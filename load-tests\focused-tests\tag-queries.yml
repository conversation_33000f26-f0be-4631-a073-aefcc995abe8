# Tag-Based Queries Test - Most Expensive Path
# Tests tag filtering which requires additional queries + intersection logic
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 2
      name: "Warm-up: Tag queries"
    - duration: 360
      arrivalRate: 6
      name: "Steady: Tag query performance test"
    - duration: 60
      arrivalRate: 2
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 4000   # Expect very slow responses
    - http.response_time.median: 1200 # Expect slow responses
    - http.codes.200: 80             # Lower success rate expected
    - http.codes.5xx: 8              # May have server errors

  http:
    timeout: 30
    pool: 12
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Tag Queries - Most Expensive Path"
    weight: 100
    flow:
      - function: "generateTagQueryFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
            state: "{{ state }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
          name: "GET /api/mugshots - Tag Queries (Most Expensive)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
      - think: 3

processor: "./load-tests/focused-tests/data-generators-focused.js"
