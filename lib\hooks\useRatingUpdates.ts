'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'

interface RatingUpdate {
  id: string
  mugshot_id: string
  rating: number
  created_at: string
  updated_at: string
}

interface UseRatingUpdatesReturn {
  isConnected: boolean
  lastUpdate: RatingUpdate | null
  updates: RatingUpdate[]
}

export function useRatingUpdates(
  mugshotId: string, 
  enabled = true
): UseRatingUpdatesReturn {
  const [isConnected, setIsConnected] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<RatingUpdate | null>(null)
  const [updates, setUpdates] = useState<RatingUpdate[]>([])

  useEffect(() => {
    if (!enabled || !mugshotId) {
      return
    }

    const supabase = createClient()
    
    // Create real-time subscription for ratings changes
    const channel = supabase
      .channel(`ratings-${mugshotId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ratings',
          filter: `mugshot_id=eq.${mugshotId}`
        },
        (payload) => {
          const update = payload.new as RatingUpdate
          if (update) {
            setLastUpdate(update)
            setUpdates(prev => [update, ...prev.slice(0, 9)]) // Keep last 10 updates
          }
        }
      )
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED')
      })

    return () => {
      supabase.removeChannel(channel)
      setIsConnected(false)
    }
  }, [mugshotId, enabled])

  return {
    isConnected,
    lastUpdate,
    updates
  }
} 