# Test 1: 10 Concurrent Users - Baseline Test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 2
      name: "Warm-up: 2 users/sec"
    - duration: 120
      arrivalRate: 5
      name: "Steady: 5 users/sec (10 concurrent)"
    - duration: 60
      arrivalRate: 2
      name: "Cool-down: 2 users/sec"
  
  # Conservative thresholds for small load
  ensure:
    - http.response_time.p95: 1000   # 95% under 1 second
    - http.response_time.median: 300 # Median under 300ms
    - http.codes.200: 98             # 98% success rate
    - http.codes.5xx: 1              # Less than 1% server errors

  http:
    timeout: 10
    pool: 15
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "10 Users - Mugshots API"
    weight: 80
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - 10 users"
      - think: 2

  - name: "10 Users - Details API"
    weight: 20
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 10 users"
      - think: 3

processor: "../scenarios/data-generators.js"
