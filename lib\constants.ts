// Tag emojis - centralized to avoid hydration errors (updated to match database tag types)
export const tagEmojis = {
  "Wild": "🤪",
  "Funny": "😂", 
  "Spooky": "😱"
};

// Legacy categoryEmojis for backward compatibility during transition
export const categoryEmojis = tagEmojis;

// Rating emojis for each tag type - blank and filled versions
export const ratingEmojis = {
  "Wild": {
    blank: "😶",
    filled: "🤪"
  },
  "Funny": {
    blank: "😐",
    filled: "😂"
  },
  "Spooky": {
    blank: "😴",
    filled: "😱"
  }
};

// Database tag types (lowercase for API consistency)
export const TAG_TYPES = {
  WILD: 'wild',
  FUNNY: 'funny', 
  SPOOKY: 'spooky'
} as const;

// UI display mapping (title case for UI)
export const TAG_TYPE_DISPLAY = {
  [TAG_TYPES.WILD]: 'Wild',
  [TAG_TYPES.FUNNY]: 'Funny',
  [TAG_TYPES.SPOOKY]: 'Spooky'
} as const;

// Tag type validation
export type TagType = typeof TAG_TYPES[keyof typeof TAG_TYPES];
export type TagTypeDisplay = typeof TAG_TYPE_DISPLAY[TagType];

// States with counties
export const states = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado",
  "Connecticut", "Delaware", "Florida", "Georgia", "Hawaii", "Idaho",
  "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana",
  "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota",
  "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada",
  "New Hampshire", "New Jersey", "New Mexico", "New York",
  "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon",
  "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota",
  "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington",
  "West Virginia", "Wisconsin", "Wyoming"
];

// Sample counties for each state
export const statesWithCounties: Record<string, string[]> = {
  "Alabama": ["Jefferson", "Mobile", "Madison", "Montgomery"],
  "Alaska": ["Anchorage", "Fairbanks", "Juneau"],
  "Arizona": ["Maricopa", "Pima", "Pinal"],
  "Arkansas": ["Pulaski", "Benton", "Washington"],
  "California": ["Los Angeles", "San Diego", "Orange", "Riverside"],
  "Colorado": ["Denver", "El Paso", "Arapahoe"],
  "Connecticut": ["Fairfield", "Hartford", "New Haven"],
  "Delaware": ["New Castle", "Kent", "Sussex"],
  "Florida": ["Miami-Dade", "Broward", "Palm Beach", "Hillsborough"],
  "Georgia": ["Fulton", "Gwinnett", "Cobb"],
  "Hawaii": ["Honolulu", "Hawaii", "Maui"],
  "Idaho": ["Ada", "Canyon", "Kootenai"],
  "Illinois": ["Cook", "DuPage", "Lake"],
  "Indiana": ["Marion", "Lake", "Allen"],
  "Iowa": ["Polk", "Linn", "Scott"],
  "Kansas": ["Johnson", "Sedgwick", "Shawnee"],
  "Kentucky": ["Jefferson", "Fayette", "Kenton"],
  "Louisiana": ["Orleans", "Jefferson", "East Baton Rouge"],
  "Maine": ["Cumberland", "York", "Penobscot"],
  "Maryland": ["Montgomery", "Prince George's", "Baltimore"],
  "Massachusetts": ["Middlesex", "Worcester", "Essex"],
  "Michigan": ["Wayne", "Oakland", "Macomb"],
  "Minnesota": ["Hennepin", "Ramsey", "Dakota"],
  "Mississippi": ["Hinds", "Harrison", "DeSoto"],
  "Missouri": ["St. Louis", "Jackson", "St. Charles"],
  "Montana": ["Yellowstone", "Missoula", "Gallatin"],
  "Nebraska": ["Douglas", "Lancaster", "Sarpy"],
  "Nevada": ["Clark", "Washoe", "Carson City"],
  "New Hampshire": ["Hillsborough", "Rockingham", "Merrimack"],
  "New Jersey": ["Bergen", "Middlesex", "Essex"],
  "New Mexico": ["Bernalillo", "Doña Ana", "Santa Fe"],
  "New York": ["Kings", "Queens", "New York", "Bronx"],
  "North Carolina": ["Mecklenburg", "Wake", "Guilford"],
  "North Dakota": ["Cass", "Burleigh", "Grand Forks"],
  "Ohio": ["Franklin", "Cuyahoga", "Hamilton"],
  "Oklahoma": ["Oklahoma", "Tulsa", "Cleveland"],
  "Oregon": ["Multnomah", "Washington", "Clackamas"],
  "Pennsylvania": ["Philadelphia", "Allegheny", "Montgomery"],
  "Rhode Island": ["Providence", "Kent", "Washington"],
  "South Carolina": ["Greenville", "Richland", "Charleston"],
  "South Dakota": ["Minnehaha", "Pennington", "Lincoln"],
  "Tennessee": ["Shelby", "Davidson", "Knox"],
  "Texas": ["Harris", "Dallas", "Tarrant", "Bexar"],
  "Utah": ["Salt Lake", "Utah", "Davis"],
  "Vermont": ["Chittenden", "Washington", "Rutland"],
  "Virginia": ["Fairfax", "Prince William", "Virginia Beach"],
  "Washington": ["King", "Pierce", "Snohomish"],
  "West Virginia": ["Kanawha", "Berkeley", "Cabell"],
  "Wisconsin": ["Milwaukee", "Dane", "Waukesha"],
  "Wyoming": ["Laramie", "Natrona", "Campbell"]
};

// Feature flags for auth state management migration
export const AUTH_MIGRATION_FLAGS = {
  // Main migration toggle
  ENABLE_NEW_AUTH_PROVIDER: process.env.NEXT_PUBLIC_AUTH_MIGRATION_ENABLED === 'true',
  
  // Individual component toggles for gradual rollout
  ENABLE_NEW_USERNAV: process.env.NEXT_PUBLIC_USERNAV_MIGRATION_ENABLED === 'true',
  ENABLE_NEW_PROFILE_PAGE: process.env.NEXT_PUBLIC_PROFILE_MIGRATION_ENABLED === 'true',
  
  // Development and debugging toggles
  ENABLE_AUTH_DEBUG_LOGGING: process.env.NODE_ENV === 'development',
  ENABLE_COMPATIBILITY_BRIDGE: true, // Always enabled during migration
} as const; 