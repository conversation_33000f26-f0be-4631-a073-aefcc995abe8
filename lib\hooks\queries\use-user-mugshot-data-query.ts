import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import type { UserMugshotResponse } from '@/lib/services/api-client'

/**
 * User-specific mugshot data query hook for authenticated user's rating/tag data
 * Fetches user's rating and tags for a specific mugshot
 * 
 * @param mugshotId - The ID of the mugshot to fetch user data for
 * @param enabled - Whether the query should run (default: true)
 * @returns TanStack Query result with user's rating and tag data
 */
export function useUserMugshotDataQuery(mugshotId: string, enabled = true) {
  const { isAuthenticated, user } = useAuthStore()
  
  return useQuery({
    queryKey: ['user', 'mugshot', mugshotId, 'data'],
    queryFn: async (): Promise<UserMugshotResponse['data']> => {
      // Don't make request if user is not authenticated
      if (!isAuthenticated || !user) {
        return { 
          userRating: null, 
          userTags: [],
          meta: {
            mugshotId: parseInt(mugshotId, 10),
            userId: '',
            fetchedAt: new Date().toISOString()
          }
        }
      }

      try {
        const response = await fetch(`/api/user/mugshot/${mugshotId}/data`)
        
        // Check if response is JSON before parsing
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          const text = await response.text()
          console.error('Non-JSON response received for user mugshot data:', text.substring(0, 200))
          throw new Error('Server returned invalid response format')
        }
        
        const result = await response.json()
        
        // Handle authentication errors with login redirect
        if (!response.ok) {
          if (response.status === 401 || result.error === 'UNAUTHENTICATED') {
            // Return empty data instead of redirecting on public pages
            console.log('User not authenticated, returning empty data')
            return { 
              userRating: null, 
              userTags: [],
              meta: {
                mugshotId: parseInt(mugshotId, 10),
                userId: '',
                fetchedAt: new Date().toISOString()
              }
            }
          } else if (response.status === 404) {
            // User hasn't interacted with this mugshot yet - return empty data
            return { 
              userRating: null, 
              userTags: [],
              meta: {
                mugshotId: parseInt(mugshotId, 10),
                userId: user?.id || '',
                fetchedAt: new Date().toISOString()
              }
            }
          } else if (response.status >= 500) {
            throw new Error('Server error occurred')
          } else if (response.status === 429) {
            throw new Error('Too many requests - please wait a moment')
          } else {
            throw new Error(result.message || 'Failed to fetch user mugshot data')
          }
        }
        
        if (!result.success) {
          throw new Error(result.message || 'Failed to fetch user mugshot data')
        }
        
        // Validate the data structure
        const data = result.data
        if (data && typeof data !== 'object') {
          throw new Error('Invalid user mugshot data format')
        }
        
        return data || { 
          userRating: null, 
          userTags: [],
          meta: {
            mugshotId: parseInt(mugshotId, 10),
            userId: user?.id || '',
            fetchedAt: new Date().toISOString()
          }
        }
        
      } catch (error) {
        // If user is not authenticated, return empty data instead of throwing error
        if (!isAuthenticated || !user) {
          return { 
            userRating: null, 
            userTags: [],
            meta: {
              mugshotId: parseInt(mugshotId, 10),
              userId: '',
              fetchedAt: new Date().toISOString()
            }
          }
        }
        
        console.error('Error fetching user mugshot data:', error)
        throw error
      }
    },
    // Only enable query if user is authenticated and enabled flag is true
    enabled: enabled && isAuthenticated && !!user,
    // Return empty data immediately for unauthenticated users
    placeholderData: { 
      userRating: null, 
      userTags: [],
      meta: {
        mugshotId: parseInt(mugshotId, 10),
        userId: user?.id || '',
        fetchedAt: new Date().toISOString()
      }
    },
    // Don't retry on auth errors for unauthenticated users
    retry: (failureCount, _error) => {
      if (!isAuthenticated || !user) return false
      return failureCount < 3
    },
    // Stale time - cache for 30 seconds
    staleTime: 30 * 1000,
    // Garbage collect after 5 minutes of inactivity
    gcTime: 5 * 60 * 1000
  })
}

/**
 * Prefetch utility for user mugshot data
 * Can be used to prefetch user-specific data when authentication is confirmed
 */
export function prefetchUserMugshotData(
  queryClient: { prefetchQuery: (config: object) => Promise<void> },
  mugshotId: string,
  isAuthenticated: boolean
) {
  if (!isAuthenticated) {
    return Promise.resolve()
  }
  
  return queryClient.prefetchQuery({
    queryKey: ['user', 'mugshot', mugshotId, 'data'],
    queryFn: async () => {
      const response = await fetch(`/api/user/mugshot/${mugshotId}/data`)
      const result = await response.json()
      
      if (!response.ok) {
        if (result.error === 'UNAUTHENTICATED') {
          return null
        }
        throw new Error(result.message || 'Failed to fetch user mugshot data')
      }
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch user mugshot data')
      }
      
      return result.data
    },
    staleTime: 30 * 1000,
  })
} 