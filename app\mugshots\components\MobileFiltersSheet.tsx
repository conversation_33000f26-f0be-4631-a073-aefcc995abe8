"use client"

import { useState, useEffect, RefObject } from "react"
import { format } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, <PERSON>etHeader, <PERSON>etTitle, SheetTrigger } from "@/components/ui/sheet"
import LocationDropdown from "@/components/LocationDropdown"
import { Filter, Calendar as CalendarIcon, Grid2X2, Grid3X3, Check, ChevronsUpDown, X, RotateCcw } from "lucide-react"
import { categoryEmojis } from "@/lib/constants"
import { cn } from "@/lib/utils"

interface MobileFiltersSheetProps {
  // Current filter values from global store
  searchTerm: string
  selectedState: string
  selectedCounty: string
  dateFrom: string
  dateTo: string
  categories: string[]
  tags: string[]
  sortBy: string
  perPage: number
  gridView: string
  
  // Loading states
  loadingFields: {
    search: boolean
    location: boolean
    date: boolean
    sort: boolean
    perPage: boolean
    gridView: boolean
    categories: boolean
  }
  
  // Handlers - only apply and clear will be called
  handleSearchChange: (value: string) => void
  handleStateChange: (state: string) => void
  handleCountyChange: (county: string) => void
  handleDateChange: (type: 'from' | 'to', date: Date | undefined) => void
  handleSortChange: (sort: string) => void
  handlePerPageChange: (perPage: number) => void
  handleGridViewChange: (view: string) => void
  toggleCategory: (category: string) => void
  clearAllFilters: () => void
  
  // Date picker states
  dateFromState: Date | undefined
  dateToState: Date | undefined
  setDateFromState: (date: Date | undefined) => void
  setDateToState: (date: Date | undefined) => void
  
  // Ref for auto-scrolling to grid
  gridContainerRef?: RefObject<HTMLDivElement | null>
}

const perPageOptions = [12, 24, 48]

export default function MobileFiltersSheet({
  // Current values from global store (these will be the initial values for temp state)
  searchTerm,
  selectedState,
  selectedCounty,
  dateFrom,
  dateTo,
  categories,
  tags,
  perPage,
  gridView,
  handleStateChange,
  handleCountyChange,
  handleDateChange,
  handlePerPageChange,
  handleGridViewChange,
  toggleCategory,
  setDateFromState,
  setDateToState,
  gridContainerRef
}: MobileFiltersSheetProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [perPageOpen, setPerPageOpen] = useState(false)
  const [fromDateOpen, setFromDateOpen] = useState(false)
  const [toDateOpen, setToDateOpen] = useState(false)
  
  // Temporary state for mobile filters (only applied when user clicks Apply)
  const [tempSelectedState, setTempSelectedState] = useState(selectedState)
  const [tempSelectedCounty, setTempSelectedCounty] = useState(selectedCounty)
  const [tempDateFrom, setTempDateFrom] = useState(dateFrom)
  const [tempDateTo, setTempDateTo] = useState(dateTo)
  const [tempCategories, setTempCategories] = useState<string[]>(categories)
  const [tempTags, setTempTags] = useState<string[]>(tags)
  const [tempPerPage, setTempPerPage] = useState(perPage)
  const [tempGridView, setTempGridView] = useState(gridView)
  
  // Temporary date picker states
  const [tempDateFromState, setTempDateFromState] = useState<Date | undefined>(
    dateFrom ? new Date(dateFrom) : undefined
  )
  const [tempDateToState, setTempDateToState] = useState<Date | undefined>(
    dateTo ? new Date(dateTo) : undefined
  )

  // Update temp state when props change (when global store updates)
  useEffect(() => {
    setTempSelectedState(selectedState)
    setTempSelectedCounty(selectedCounty)
    setTempDateFrom(dateFrom)
    setTempDateTo(dateTo)
    setTempCategories(categories)
    setTempTags(tags)
    setTempPerPage(perPage)
    setTempGridView(gridView)
    setTempDateFromState(dateFrom ? new Date(dateFrom) : undefined)
    setTempDateToState(dateTo ? new Date(dateTo) : undefined)
  }, [selectedState, selectedCounty, dateFrom, dateTo, categories, tags, perPage, gridView])

  // Count active filters based on current global state (not temp state)
  const activeFiltersCount = [
    searchTerm,
    selectedState && selectedState !== 'all-states' ? selectedState : null,
    selectedCounty && selectedCounty !== 'all-counties' ? selectedCounty : null,
    dateFrom,
    dateTo,
    categories.length > 0 ? 'categories' : null,
    tags.length > 0 ? 'tags' : null
  ].filter(Boolean).length

  // Check if there are pending changes in temp state
  const hasPendingChanges = (
    tempSelectedState !== selectedState ||
    tempSelectedCounty !== selectedCounty ||
    tempDateFrom !== dateFrom ||
    tempDateTo !== dateTo ||
    JSON.stringify(tempCategories.sort()) !== JSON.stringify(categories.sort()) ||
    JSON.stringify(tempTags.sort()) !== JSON.stringify(tags.sort()) ||
    tempPerPage !== perPage ||
    tempGridView !== gridView
  )

  // Handle temporary state changes
  const handleTempStateChange = (state: string) => {
    setTempSelectedState(state === 'all-states' ? '' : state)
    setTempSelectedCounty('') // Reset county when state changes
  }

  const handleTempCountyChange = (county: string) => {
    setTempSelectedCounty(county === 'all-counties' ? '' : county)
  }

  const handleTempDateChange = (type: 'from' | 'to', date: Date | undefined) => {
    if (type === 'from') {
      setTempDateFromState(date)
      setTempDateFrom(date ? format(date, 'yyyy-MM-dd') : '')
      
      // If setting from date and to date is not set, default to current date
      if (date && !tempDateToState) {
        const today = new Date()
        setTempDateToState(today)
        setTempDateTo(format(today, 'yyyy-MM-dd'))
      }
      
      // If from date is after to date, adjust to date to from date
      if (date && tempDateToState && date > tempDateToState) {
        setTempDateToState(date)
        setTempDateTo(format(date, 'yyyy-MM-dd'))
      }
    } else {
      // Validate to date is not before from date
      if (tempDateFromState && date && date < tempDateFromState) {
        // Silently adjust to_date to from_date if invalid
        setTempDateToState(tempDateFromState)
        setTempDateTo(format(tempDateFromState, 'yyyy-MM-dd'))
      } else {
        setTempDateToState(date)
        setTempDateTo(date ? format(date, 'yyyy-MM-dd') : '')
      }
    }
  }

  const _toggleTempCategory = (category: string) => {
    setTempCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const toggleTempTag = (tag: string) => {
    setTempTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  // Apply all temporary changes to global store
  const applyFilters = () => {
    // Apply all changes to global store
    handleStateChange(tempSelectedState === '' ? 'all-states' : tempSelectedState)
    handleCountyChange(tempSelectedCounty === '' ? 'all-counties' : tempSelectedCounty)
    
    // Apply date changes
    handleDateChange('from', tempDateFromState)
    handleDateChange('to', tempDateToState)
    setDateFromState(tempDateFromState)
    setDateToState(tempDateToState)
    
    // Apply category changes
    const currentCategories = categories
    const newCategories = tempCategories
    
    // Add new categories
    newCategories.forEach(category => {
      if (!currentCategories.includes(category)) {
        toggleCategory(category)
      }
    })
    
    // Remove deselected categories
    currentCategories.forEach(category => {
      if (!newCategories.includes(category)) {
        toggleCategory(category)
      }
    })

    // Apply tag changes - sync temp tags with global tags
    const currentTags = tags
    const newTags = tempTags
    
    // Add new tags
    newTags.forEach(tag => {
      if (!currentTags.includes(tag)) {
        toggleCategory(tag) // Use toggleCategory function for now (it handles tags)
      }
    })
    
    // Remove deselected tags
    currentTags.forEach(tag => {
      if (!newTags.includes(tag)) {
        toggleCategory(tag) // Use toggleCategory function for now (it handles tags)
      }
    })
    
    // Apply other changes
    handlePerPageChange(tempPerPage)
    handleGridViewChange(tempGridView)
    
    // Close the sheet
    setIsOpen(false)

    // Auto-scroll to grid
    if (gridContainerRef?.current) {
      gridContainerRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // Clear all temporary state
  const clearTempFilters = () => {
    setTempSelectedState('')
    setTempSelectedCounty('')
    setTempDateFrom('')
    setTempDateTo('')
    setTempCategories([])
    setTempTags([])
    setTempPerPage(12)
    setTempGridView('large')
    setTempDateFromState(undefined)
    setTempDateToState(undefined)
  }

  // Reset temp state to current global state (cancel changes)
  const resetTempState = () => {
    setTempSelectedState(selectedState)
    setTempSelectedCounty(selectedCounty)
    setTempDateFrom(dateFrom)
    setTempDateTo(dateTo)
    setTempCategories(categories)
    setTempTags(tags)
    setTempPerPage(perPage)
    setTempGridView(gridView)
    setTempDateFromState(dateFrom ? new Date(dateFrom) : undefined)
    setTempDateToState(dateTo ? new Date(dateTo) : undefined)
  }

  return (
    <Sheet open={isOpen} onOpenChange={(open) => {
      if (!open) {
        // Reset temp state when closing without applying
        resetTempState()
      }
      setIsOpen(open)
    }}>
      <SheetTrigger asChild>
        <Button variant="outline" className="relative border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/20 hover:border-cyan-400">
          <Filter className="h-4 w-4" />
          {activeFiltersCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-pink-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {activeFiltersCount}
            </span>
          )}
        </Button>
      </SheetTrigger>
      
      <SheetContent side="right" className="w-full sm:max-w-lg bg-gray-900/95 border-l border-cyan-500/30 text-white flex flex-col">
        <SheetHeader className="pb-6 flex-shrink-0">
          <SheetTitle className="text-xl font-bold text-white flex items-center gap-2">
            <Filter className="h-5 w-5 text-cyan-400" />
            Filter Mugshots
            {hasPendingChanges && (
              <span className="text-xs bg-orange-500/20 text-orange-300 px-2 py-1 rounded">
                {hasPendingChanges ? 'Changes pending' : ''}
              </span>
            )}
          </SheetTitle>
        </SheetHeader>

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto pr-2">
          <div className="space-y-6 pb-4">
            {/* Location Dropdown */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Location</h3>
              <LocationDropdown
                selectedState={tempSelectedState || 'all-states'}
                setSelectedState={handleTempStateChange}
                selectedCounty={tempSelectedCounty || 'all-counties'}
                setSelectedCounty={handleTempCountyChange}
                allStatesOption={true}
                allCountiesOption={true}
                className="w-full"
                isLoading={false} // No loading for temp state
              />
            </div>

            {/* Date Range */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Date Range</h3>
              <div className="space-y-4">
                {/* From Date */}
                <div>
                  <label className="text-sm font-medium text-gray-300 block mb-2">From Date</label>
                  <Popover open={fromDateOpen} onOpenChange={setFromDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "input-neon w-full justify-start text-left font-normal",
                          !tempDateFromState && "text-gray-400"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {tempDateFromState ? format(tempDateFromState, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl" align="start">
                      <Calendar
                        mode="single"
                        selected={tempDateFromState}
                        onSelect={(date) => {
                          handleTempDateChange('from', date)
                          setFromDateOpen(false)
                        }}
                        disabled={(date) => date > new Date()} // Disable future dates
                        initialFocus
                        className="border-none"
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* To Date */}
                <div>
                  <label className="text-sm font-medium text-gray-300 block mb-2">To Date</label>
                  <Popover open={toDateOpen} onOpenChange={setToDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        disabled={!tempDateFromState} // Disable if no from date selected
                        className={cn(
                          "input-neon w-full justify-start text-left font-normal",
                          (!tempDateToState || !tempDateFromState) && "text-gray-400",
                          !tempDateFromState && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {tempDateToState ? format(tempDateToState, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl" align="start">
                      <Calendar
                        mode="single"
                        selected={tempDateToState}
                        onSelect={(date) => {
                          handleTempDateChange('to', date)
                          setToDateOpen(false)
                        }}
                        disabled={(date) => {
                          // Disable future dates and dates before from date
                          const today = new Date()
                          if (date > today) return true
                          if (tempDateFromState && date < tempDateFromState) return true
                          return false
                        }}
                        initialFocus
                        className="border-none"
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Tags</h3>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(categoryEmojis).map(([category, emoji]) => (
                  <button
                    key={category}
                    onClick={() => toggleTempTag(category)}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg font-medium text-sm transition-all duration-300 ${
                      tempTags.includes(category)
                        ? "bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-lg shadow-pink-500/30"
                        : "bg-gray-800/90 border border-cyan-500/30 text-white hover:bg-gray-700/90 hover:border-cyan-500/50"
                    }`}
                  >
                    <span className="text-base">{emoji}</span>
                    <span className="text-xs truncate">{category}</span>
                  </button>
                ))}
              </div>
              {tempTags.length > 0 && (
                <div className="mt-3">
                  <button
                    onClick={() => setTempTags([])}
                    className="text-xs text-cyan-400 hover:text-cyan-300 transition-colors flex items-center gap-1"
                  >
                    <X className="h-3 w-3" />
                    Clear tags
                  </button>
                </div>
              )}
            </div>

            {/* Display Options */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Display Options</h3>
              <div className="space-y-4">
                {/* Per Page */}
                <div>
                  <label className="text-sm font-medium text-gray-300 block mb-2">Items per page</label>
                  <Popover open={perPageOpen} onOpenChange={setPerPageOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={perPageOpen}
                        className="input-neon w-full justify-between"
                      >
                        {tempPerPage}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl">
                      <Command className="bg-transparent">
                        <CommandList className="bg-transparent">
                          <CommandGroup className="bg-transparent">
                            {perPageOptions.map((option) => (
                              <CommandItem
                                key={option}
                                onSelect={() => {
                                  setTempPerPage(option)
                                  setPerPageOpen(false)
                                }}
                                className="text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4 text-cyan-400",
                                    tempPerPage === option ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {option}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Grid View */}
                <div>
                  <label className="text-sm font-medium text-gray-300 block mb-2">Grid View</label>
                  <div className="flex gap-2">
                    <Button
                      variant={tempGridView === "large" ? "default" : "outline"}
                      onClick={() => setTempGridView("large")}
                      className={`flex-1 ${
                        tempGridView === "large"
                          ? "bg-pink-600 hover:bg-pink-700 text-white border-pink-600"
                          : "border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/20 hover:border-cyan-400"
                      }`}
                    >
                      <Grid2X2 className="h-4 w-4 mr-2" />
                      Large
                    </Button>
                    <Button
                      variant={tempGridView === "compact" ? "default" : "outline"}
                      onClick={() => setTempGridView("compact")}
                      className={`flex-1 ${
                        tempGridView === "compact"
                          ? "bg-pink-600 hover:bg-pink-700 text-white border-pink-600"
                          : "border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/20 hover:border-cyan-400"
                      }`}
                    >
                      <Grid3X3 className="h-4 w-4 mr-2" />
                      Compact
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Fixed/Sticky Action Buttons at Bottom */}
        <div className="flex-shrink-0 border-t border-gray-700 pt-4 mt-4 bg-gray-900/95">
          <div className="space-y-3">
            {/* Clear Filters Button */}
            <Button
              variant="outline"
              onClick={clearTempFilters}
              className="w-full border-red-500/30 text-red-400 hover:bg-red-500/20 hover:border-red-400"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All Filters
            </Button>
            
            {/* Apply Filters Button */}
            <Button
              onClick={applyFilters}
              className="w-full bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-semibold shadow-lg shadow-pink-500/30"
              disabled={!hasPendingChanges}
            >
              <Check className="h-4 w-4 mr-2" />
              Apply Filters
              {hasPendingChanges && (
                <span className="ml-2 bg-white/20 px-2 py-0.5 rounded text-xs">
                  Changes
                </span>
              )}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
} 