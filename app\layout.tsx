import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, M_PLUS_Rounded_1c } from "next/font/google";
import { Suspense } from "react";
import "./globals.css";
import Header from "@/components/header";
import Footer from "@/components/footer";
import { ThemeProvider } from "@/components/theme-provider";
import AuthProvider from "@/components/AuthProvider";
import AuthProviderV2 from "@/components/AuthProvider-v2";
import { AUTH_MIGRATION_FLAGS } from "@/lib/constants";
import AccountLinkedNotification from "@/components/AccountLinkedNotification";
import { Toaster } from "@/components/ui/sonner";
import { QueryProvider } from "@/lib/providers/query-provider";
import TopProgressBar from "@/components/TopProgressBar";
import PageTransition from "@/components/PageTransition";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const mPlusRounded = M_PLUS_Rounded_1c({
  weight: "900",
  variable: "--font-m-plus-rounded",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "America's Top Mugshots - Rate & Discover Mugshots",
  description: "Discover, rate and vote on America's most interesting mugshots. Browse by location, category and popularity. Join the jury today!",
  keywords: "mugshots, arrest records, voting, rating, America, local arrests",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${mPlusRounded.variable} antialiased min-h-screen`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
                  <QueryProvider>
          {AUTH_MIGRATION_FLAGS.ENABLE_NEW_AUTH_PROVIDER ? (
            <AuthProviderV2>
              {/* Top progress bar for navigation feedback */}
              <Suspense fallback={null}>
                <TopProgressBar />
              </Suspense>
              
              <Header />
              <main className="flex-1">
                <PageTransition>
                  {children}
                </PageTransition>
              </main>
              <Footer />
            </AuthProviderV2>
          ) : (
            <AuthProvider>
              {/* Top progress bar for navigation feedback */}
              <Suspense fallback={null}>
                <TopProgressBar />
              </Suspense>
              
              <Header />
              <main className="flex-1">
                <PageTransition>
                  {children}
                </PageTransition>
              </main>
              <Footer />
            </AuthProvider>
          )}
            
            {/* Account linking notification */}
            <Suspense fallback={null}>
              <AccountLinkedNotification />
            </Suspense>
            
            {/* Toast notifications */}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
