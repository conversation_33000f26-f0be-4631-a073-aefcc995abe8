import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import WinnerManagementDashboard from '@/app/admin/winners/components/WinnerManagementDashboard'
import WinnerHistoryTracker from '@/app/admin/winners/components/WinnerHistoryTracker'
import WinnerAnalytics from '@/app/admin/winners/components/WinnerAnalytics'

// Mock the winner management service
vi.mock('@/lib/services/winner-management-service', () => ({
  getWinners: vi.fn(),
  getEligibleMugshots: vi.fn(),
  assignDailyWinner: vi.fn(),
  assignWeeklyWinner: vi.fn(),
  reverseAssignment: vi.fn(),
  getAssignmentHistory: vi.fn(),
  getWinnerStats: vi.fn(),
  exportAssignmentData: vi.fn()
}))

// Mock toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

// Mock Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => (
    <img src={src} alt={alt} {...props} />
  )
}))

import {
  getWinners,
  getEligibleMugshots,
  assignDailyWinner,
  assignWeeklyWinner,
  reverseAssignment,
  getAssignmentHistory,
  getWinnerStats,
  exportAssignmentData
} from '@/lib/services/winner-management-service'
import { toast } from 'sonner'

const mockGetWinners = getWinners as any
const mockGetEligibleMugshots = getEligibleMugshots as any
const mockAssignDailyWinner = assignDailyWinner as any
const mockAssignWeeklyWinner = assignWeeklyWinner as any
const mockReverseAssignment = reverseAssignment as any
const mockGetAssignmentHistory = getAssignmentHistory as any
const mockGetWinnerStats = getWinnerStats as any
const mockExportAssignmentData = exportAssignmentData as any

describe('Winner Management System Integration', () => {
  const mockWinners = [
    {
      id: 'winner-1',
      date: '2024-01-15',
      mugshot: {
        id: 'mugshot-1',
        firstName: 'John',
        lastName: 'Doe',
        imagePath: '/images/john.jpg',
        stateOfBooking: 'California',
        countyOfBooking: 'Los Angeles'
      },
      selectionMethod: 'automatic' as const,
      averageRating: 8.5,
      totalRatings: 42
    },
    {
      id: 'winner-2',
      date: '2024-01-14',
      mugshot: {
        id: 'mugshot-2',
        firstName: 'Jane',
        lastName: 'Smith',
        imagePath: '/images/jane.jpg',
        stateOfBooking: 'Texas',
        countyOfBooking: 'Harris'
      },
      selectionMethod: 'manual' as const,
      reason: 'Tie breaker decision',
      assignmentId: 'assignment-123'
    }
  ]

  const mockStats = {
    totalWinners: 156,
    manualAssignments: 23,
    automaticWinners: 133,
    recentActivity: 12
  }

  const mockEligibleMugshots = [
    {
      id: 'mugshot-3',
      first_name: 'Bob',
      last_name: 'Johnson',
      image_path: '/images/bob.jpg',
      state_of_booking: 'Florida',
      county_of_booking: 'Miami-Dade',
      date_of_booking: '2024-01-15'
    }
  ]

  const mockAssignmentHistory = [
    {
      id: 'assignment-1',
      competitionType: 'daily',
      competitionId: '2024-01-15',
      assignmentReason: 'Manual override due to voting irregularities',
      assignmentType: 'override',
      isReversed: false,
      createdAt: '2024-01-15T10:00:00Z',
      admin: {
        firstName: 'Admin',
        lastName: 'User'
      },
      newWinner: {
        id: 'mugshot-1',
        firstName: 'John',
        lastName: 'Doe',
        imagePath: '/images/john.jpg',
        stateOfBooking: 'California'
      }
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mocks
    mockGetWinners.mockResolvedValue(mockWinners)
    mockGetWinnerStats.mockResolvedValue(mockStats)
    mockGetEligibleMugshots.mockResolvedValue(mockEligibleMugshots)
    mockGetAssignmentHistory.mockResolvedValue(mockAssignmentHistory)
    mockExportAssignmentData.mockResolvedValue('csv,data,here')
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('WinnerManagementDashboard', () => {
    it('should render dashboard with winners list', async () => {
      render(<WinnerManagementDashboard />)
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Winner Management')).toBeInTheDocument()
      })

      // Check if winners are displayed
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      
      // Check winner details
      expect(screen.getByText('California, Los Angeles')).toBeInTheDocument()
      expect(screen.getByText('⭐ 8.5/10 (42 ratings)')).toBeInTheDocument()
      
      // Check selection method badges
      expect(screen.getByText('Automatic')).toBeInTheDocument()
      expect(screen.getByText('Manual')).toBeInTheDocument()
    })

    it('should display stats correctly', async () => {
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(screen.getByText('156')).toBeInTheDocument() // Total winners
        expect(screen.getByText('23')).toBeInTheDocument()  // Manual assignments
        expect(screen.getByText('12')).toBeInTheDocument()  // Recent activity
      })
    })

    it('should switch between competition tabs', async () => {
      const user = userEvent.setup()
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(screen.getByText('Daily Winners')).toBeInTheDocument()
      })

      // Click on weekly tab
      const weeklyTab = screen.getByText('Weekly Champions')
      await user.click(weeklyTab)
      
      expect(mockGetWinners).toHaveBeenCalledWith('weekly', 20)
      expect(mockGetWinnerStats).toHaveBeenCalledWith('weekly')
    })

    it('should open manual assignment modal', async () => {
      const user = userEvent.setup()
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(screen.getByText('Manual Assignment')).toBeInTheDocument()
      })

      // Click manual assignment button
      const assignButton = screen.getByText('Manual Assignment')
      await user.click(assignButton)
      
      // Check if modal opens
      await waitFor(() => {
        expect(screen.getByText('Manual Winner Assignment')).toBeInTheDocument()
      })
      
      expect(mockGetEligibleMugshots).toHaveBeenCalled()
    })

    it('should handle winner assignment submission', async () => {
      const user = userEvent.setup()
      mockAssignDailyWinner.mockResolvedValue({ success: true })
      
      render(<WinnerManagementDashboard />)
      
      // Open assignment modal
      await waitFor(() => {
        expect(screen.getByText('Manual Assignment')).toBeInTheDocument()
      })
      
      const assignButton = screen.getByText('Manual Assignment')
      await user.click(assignButton)
      
      await waitFor(() => {
        expect(screen.getByText('Manual Winner Assignment')).toBeInTheDocument()
      })

      // Fill out the form
      const mugshotSelect = screen.getByDisplayValue('Choose a mugshot...')
      await user.click(mugshotSelect)
      
      const reasonTextarea = screen.getByPlaceholderText(/Provide a detailed reason/)
      await user.type(reasonTextarea, 'Test assignment reason')

      // Submit the form
      const submitButton = screen.getByText('Assign Winner')
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockAssignDailyWinner).toHaveBeenCalledWith({
          competitionType: 'daily',
          mugshotId: expect.any(String),
          reason: 'Test assignment reason',
          assignmentType: 'manual_selection',
          date: expect.any(String)
        })
      })
      
      expect(toast.success).toHaveBeenCalledWith('daily winner assigned successfully')
    })

    it('should handle assignment errors', async () => {
      const user = userEvent.setup()
      mockAssignDailyWinner.mockRejectedValue(new Error('Assignment failed'))
      
      render(<WinnerManagementDashboard />)
      
      // Open modal and submit
      const assignButton = screen.getByText('Manual Assignment')
      await user.click(assignButton)
      
      await waitFor(() => {
        expect(screen.getByText('Manual Winner Assignment')).toBeInTheDocument()
      })

      const reasonTextarea = screen.getByPlaceholderText(/Provide a detailed reason/)
      await user.type(reasonTextarea, 'Test assignment')

      const submitButton = screen.getByText('Assign Winner')
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to assign winner')
      })
    })

    it('should open reversal modal for manual assignments', async () => {
      const user = userEvent.setup()
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Find and click the reverse button (should only be available for manual assignments)
      const reverseButton = screen.getByText('Reverse')
      await user.click(reverseButton)
      
      await waitFor(() => {
        expect(screen.getByText('Reverse Winner Assignment')).toBeInTheDocument()
      })
    })

    it('should handle reversal submission', async () => {
      const user = userEvent.setup()
      mockReverseAssignment.mockResolvedValue({ success: true })
      
      render(<WinnerManagementDashboard />)
      
      // Open reversal modal
      await waitFor(() => {
        expect(screen.getByText('Reverse')).toBeInTheDocument()
      })
      
      const reverseButton = screen.getByText('Reverse')
      await user.click(reverseButton)
      
      await waitFor(() => {
        expect(screen.getByText('Reverse Winner Assignment')).toBeInTheDocument()
      })

      // Fill reversal reason
      const reasonTextarea = screen.getByPlaceholderText(/Provide a detailed reason for reversing/)
      await user.type(reasonTextarea, 'Assignment made in error')

      // Submit reversal
      const submitButton = screen.getByText('Reverse Assignment')
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(mockReverseAssignment).toHaveBeenCalledWith(
          'assignment-123',
          'Assignment made in error'
        )
      })
      
      expect(toast.success).toHaveBeenCalledWith('Assignment reversed successfully')
    })

    it('should handle search functionality', async () => {
      const user = userEvent.setup()
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Search for specific winner
      const searchInput = screen.getByPlaceholderText('Search winners...')
      await user.type(searchInput, 'John')
      
      // John should still be visible, Jane should be filtered out
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
    })

    it('should export data successfully', async () => {
      const user = userEvent.setup()
      
      // Mock URL methods
      const mockURL = {
        createObjectURL: vi.fn().mockReturnValue('blob:mock-url'),
        revokeObjectURL: vi.fn()
      }
      Object.defineProperty(window, 'URL', { value: mockURL })
      
      // Mock document methods
      const mockAnchor = {
        href: '',
        download: '',
        click: vi.fn()
      }
      const mockCreateElement = vi.fn().mockReturnValue(mockAnchor)
      const mockAppendChild = vi.fn()
      const mockRemoveChild = vi.fn()
      
      Object.defineProperty(document, 'createElement', { value: mockCreateElement })
      Object.defineProperty(document.body, 'appendChild', { value: mockAppendChild })
      Object.defineProperty(document.body, 'removeChild', { value: mockRemoveChild })
      
      render(<WinnerManagementDashboard />)
      
      // Click export button
      const exportButton = screen.getByText('Export')
      await user.click(exportButton)
      
      await waitFor(() => {
        expect(mockExportAssignmentData).toHaveBeenCalled()
        expect(toast.success).toHaveBeenCalledWith('Assignment data exported successfully')
      })
    })
  })

  describe('WinnerHistoryTracker', () => {
    it('should render assignment history', async () => {
      render(<WinnerHistoryTracker />)
      
      await waitFor(() => {
        expect(screen.getByText('Winner Assignment History')).toBeInTheDocument()
      })

      // Check if assignment is displayed
      expect(screen.getByText('Daily Assignment')).toBeInTheDocument()
      expect(screen.getByText('Manual override due to voting irregularities')).toBeInTheDocument()
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })

    it('should filter assignments by type', async () => {
      const user = userEvent.setup()
      render(<WinnerHistoryTracker />)
      
      await waitFor(() => {
        expect(screen.getByText('Winner Assignment History')).toBeInTheDocument()
      })

      // Change filter
      const assignmentTypeFilter = screen.getByDisplayValue('All Types')
      await user.click(assignmentTypeFilter)
      
      const overrideOption = screen.getByText('Override')
      await user.click(overrideOption)
      
      // Should refetch with filter
      await waitFor(() => {
        expect(mockGetAssignmentHistory).toHaveBeenCalledTimes(2) // Initial load + filter change
      })
    })

    it('should open assignment details modal', async () => {
      const user = userEvent.setup()
      render(<WinnerHistoryTracker />)
      
      await waitFor(() => {
        expect(screen.getByText('Details')).toBeInTheDocument()
      })

      // Click details button
      const detailsButton = screen.getByText('Details')
      await user.click(detailsButton)
      
      await waitFor(() => {
        expect(screen.getByText('Assignment Details')).toBeInTheDocument()
        expect(screen.getByText('Assignment Information')).toBeInTheDocument()
      })
    })

    it('should search assignments', async () => {
      const user = userEvent.setup()
      render(<WinnerHistoryTracker />)
      
      await waitFor(() => {
        expect(screen.getByText('Daily Assignment')).toBeInTheDocument()
      })

      // Search for specific assignment
      const searchInput = screen.getByPlaceholderText('Search assignments...')
      await user.type(searchInput, 'voting')
      
      // Assignment with 'voting' in reason should still be visible
      expect(screen.getByText('Manual override due to voting irregularities')).toBeInTheDocument()
    })
  })

  describe('WinnerAnalytics', () => {
    const mockAnalyticsData = {
      totalWinners: 156,
      manualAssignments: 23,
      automaticWinners: 133,
      reversedAssignments: 3,
      averageTimeToAssignment: 45,
      competitionParticipation: {
        daily: 120,
        weekly: 28,
        monthly: 7,
        quarterly: 1
      },
      assignmentTypes: {
        manual_selection: 12,
        override: 8,
        tie_breaker: 2,
        dispute_resolution: 1
      },
      topStates: [
        { state: 'California', winnerCount: 24, percentage: 15.4 },
        { state: 'Texas', winnerCount: 19, percentage: 12.2 }
      ],
      adminActivity: [
        { adminName: 'John Smith', assignments: 15, reversals: 1, successRate: 93.3 }
      ]
    }

    it('should render analytics dashboard', async () => {
      render(<WinnerAnalytics />)
      
      await waitFor(() => {
        expect(screen.getByText('Winner Analytics')).toBeInTheDocument()
        expect(screen.getByText('Performance metrics and insights')).toBeInTheDocument()
      })

      // Should show loading state initially
      expect(screen.getAllByRole('generic')).toHaveLength(expect.any(Number))
    })

    it('should display performance indicators', async () => {
      render(<WinnerAnalytics />)
      
      await waitFor(() => {
        expect(screen.getByText('Assignment Success Rate')).toBeInTheDocument()
        expect(screen.getByText('Manual Assignment Ratio')).toBeInTheDocument()
        expect(screen.getByText('Average Processing Time')).toBeInTheDocument()
        expect(screen.getByText('Total Competitions')).toBeInTheDocument()
      })
    })

    it('should handle time range filter changes', async () => {
      const user = userEvent.setup()
      render(<WinnerAnalytics />)
      
      await waitFor(() => {
        expect(screen.getByDisplayValue('Last 30 days')).toBeInTheDocument()
      })

      // Change time range
      const timeRangeSelect = screen.getByDisplayValue('Last 30 days')
      await user.click(timeRangeSelect)
      
      const last7DaysOption = screen.getByText('Last 7 days')
      await user.click(last7DaysOption)
      
      // Should trigger reload with new time range
      await waitFor(() => {
        expect(timeRangeSelect).toHaveValue('7')
      })
    })

    it('should export analytics report', async () => {
      const user = userEvent.setup()
      
      // Mock URL and document methods for export
      const mockURL = {
        createObjectURL: vi.fn().mockReturnValue('blob:mock-url'),
        revokeObjectURL: vi.fn()
      }
      Object.defineProperty(window, 'URL', { value: mockURL })
      
      const mockAnchor = {
        href: '',
        download: '',
        click: vi.fn()
      }
      const mockCreateElement = vi.fn().mockReturnValue(mockAnchor)
      const mockAppendChild = vi.fn()
      const mockRemoveChild = vi.fn()
      
      Object.defineProperty(document, 'createElement', { value: mockCreateElement })
      Object.defineProperty(document.body, 'appendChild', { value: mockAppendChild })
      Object.defineProperty(document.body, 'removeChild', { value: mockRemoveChild })
      
      render(<WinnerAnalytics />)
      
      await waitFor(() => {
        expect(screen.getByText('Export')).toBeInTheDocument()
      })

      // Click export button
      const exportButton = screen.getByText('Export')
      await user.click(exportButton)
      
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Analytics report exported successfully')
      })
    })
  })

  describe('System Integration', () => {
    it('should handle service failures gracefully', async () => {
      mockGetWinners.mockRejectedValue(new Error('Service unavailable'))
      
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load winner data')
      })
    })

    it('should show loading states during data fetching', async () => {
      // Mock delayed response
      mockGetWinners.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(mockWinners), 100))
      )
      
      render(<WinnerManagementDashboard />)
      
      // Should show skeleton loading
      expect(screen.getAllByRole('generic')).toHaveLength(expect.any(Number))
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
    })

    it('should handle empty data states', async () => {
      mockGetWinners.mockResolvedValue([])
      mockGetWinnerStats.mockResolvedValue({
        totalWinners: 0,
        manualAssignments: 0,
        automaticWinners: 0,
        recentActivity: 0
      })
      
      render(<WinnerManagementDashboard />)
      
      await waitFor(() => {
        expect(screen.getByText('No daily winners found')).toBeInTheDocument()
        expect(screen.getByText('Winners will appear here as competitions conclude')).toBeInTheDocument()
      })
    })

    it('should update data after successful operations', async () => {
      mockAssignDailyWinner.mockResolvedValue({ success: true })
      
      const user = userEvent.setup()
      render(<WinnerManagementDashboard />)
      
      // Initial load
      await waitFor(() => {
        expect(mockGetWinners).toHaveBeenCalledTimes(1)
        expect(mockGetWinnerStats).toHaveBeenCalledTimes(1)
      })

      // Perform assignment
      const assignButton = screen.getByText('Manual Assignment')
      await user.click(assignButton)
      
      await waitFor(() => {
        expect(screen.getByText('Manual Winner Assignment')).toBeInTheDocument()
      })

      const reasonTextarea = screen.getByPlaceholderText(/Provide a detailed reason/)
      await user.type(reasonTextarea, 'Test assignment')

      const submitButton = screen.getByText('Assign Winner')
      await user.click(submitButton)
      
      // Should reload data after successful assignment
      await waitFor(() => {
        expect(mockGetWinners).toHaveBeenCalledTimes(2) // Initial + reload
        expect(mockGetWinnerStats).toHaveBeenCalledTimes(2) // Initial + reload
      })
    })
  })
}) 