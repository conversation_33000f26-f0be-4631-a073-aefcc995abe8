# Top-Rated Mugshots and Enhanced Filtering Implementation

## Overview

This document outlines the comprehensive improvements made to the mugshots filtering and sorting system, with special focus on fixing the top-rated functionality and implementing robust tag filtering across all sorting methods.

## Issues Fixed

### 1. **Top-Rated Sorting Problems**
- **SQL RPC Issue**: Previous code used `supabase.rpc('sql', ...)` which doesn't exist
- **SQL Injection Risk**: Building SQL strings with direct concatenation
- **Missing Aggregation**: Not properly calculating average ratings at database level
- **No Rated Filtering**: Was returning mugshots without ratings in top-rated results

### 2. **Tag Filtering Limitations**
- Tag filtering was not implemented at the database level
- Inconsistent filtering across different sorting methods
- No proper OR logic for multiple tags

## Solutions Implemented

### 1. **Unified Database Functions** (`supabase/migrations/011_filter_functions.sql`)

#### Single Comprehensive Functions
```sql
-- search_filtered_mugshots: Handles ALL sorting types with comprehensive filtering
-- count_filtered_mugshots: Universal count function for all filters
```

**Key Features:**
- **Unified Approach**: Single function handles both top-rated and newest sorting
- **Dynamic Sorting**: Uses `sort_by` parameter to switch between sorting methods
- **Smart Rating Logic**: Uses LEFT JOIN for ratings so all mugshots are included, but conditionally sorts by rating when needed
- **Tag filtering**: Consistent OR logic using EXISTS subquery with `ANY()` operator
- **Comprehensive Filtering**: All standard filters (search, location, date, tags) work across all sorting types
- **Performance Optimized**: Uses `WITH` clauses and proper indexing
- **SQL Injection Safe**: Fully parameterized queries

### 2. **Enhanced Service Layer** (`lib/services/mugshots-service-server.ts`)

#### Simplified Architecture
- **Single Method**: Uses one unified database function for all sorting types
- **Clean Interface**: No need for separate methods for different sorting types
- **Universal Fallback**: Single fallback method handles all sorting scenarios
- **Consistent Behavior**: Same filtering and user data enhancement across all sorts

#### Key Improvements
```typescript
// Unified method for all sorting and filtering
private async getMugshotsUnified(filters, sortOptions, pagination, userId) {
  // Uses search_filtered_mugshots function for ALL sorting types
  // Single function handles newest, top-rated, and most-viewed
  // Tag filtering works consistently across all sorts
  // Falls back gracefully on errors with unified fallback
}

// Single fallback method
private async getMugshotsFallback(filters, sortOptions, pagination, userId) {
  // Handles all sorting types in fallback mode
  // Special logic for top-rated sorting when database function fails
  // Consistent filtering across all scenarios
}
```

### 3. **Enhanced API Layer** (`app/api/mugshots/route.ts`)

The API layer was already properly structured and didn't require changes. It correctly:
- Parses tag filters from query parameters
- Passes filters to the service layer
- Maintains backward compatibility

### 4. **Comprehensive Testing**

#### Top-Rated Tests (`__tests__/services/top-rated-mugshots.test.ts`)
- Tests proper sorting by average rating
- Tests tie-breaking by total ratings count
- Tests filtering (search, location, date)
- Tests pagination
- Tests user-specific data inclusion
- Tests fallback behavior

#### Tag Filtering Tests (`__tests__/services/tag-filtering.test.ts`)
- Tests single tag filtering
- Tests multiple tag filtering (OR logic)
- Tests tag filtering with different sorting methods
- Tests combined filters (location + tags + search)
- Tests counting with tag filters
- Tests edge cases and error conditions

### 5. **Debug and Monitoring Tools**

#### Enhanced Debug Script (`scripts/debug-top-rated.ts`)
- Tests all database functions
- Validates data integrity
- Tests service layer functionality
- Tests tag filtering combinations
- Provides detailed error reporting

## Technical Details

### Database Function Design

#### Tag Filtering Logic
```sql
-- OR logic for multiple tags
and (
  tags_filter is null
  or exists (
    select 1 from tags t
    where t.mugshot_id = m.id
    and t.tag_type = any(tags_filter)
  )
)
```

#### Top-Rated Aggregation
```sql
with rated as (
  select
    r.mugshot_id,
    avg(r.rating)::numeric(4,2) as average_rating,
    count(*) as total_ratings
  from ratings r
  group by r.mugshot_id
)
```

### Service Layer Pattern

#### Fallback Strategy
1. **Primary**: Database function call
2. **Fallback**: Direct Supabase queries
3. **Error Handling**: Graceful degradation with logging

#### Filter Processing
```typescript
// Convert tags array to proper format for database
const tagsFilter = filters.tags && filters.tags.length > 0 ? filters.tags : null

// Call unified database function with all filters and sorting
const { data, error } = await supabase.rpc('search_filtered_mugshots', {
  search_term: filters.searchTerm || null,
  state_filter: filters.state !== 'all-states' ? filters.state : null,
  county_filter: filters.county !== 'all-counties' ? filters.county : null,
  date_from: filters.dateFrom || null,
  date_to: filters.dateTo || null,
  tags_filter: tagsFilter,
  sort_by: sortOptions.sortBy, // 'newest', 'top-rated', or 'most-viewed'
  limit_count: pagination.perPage,
  offset_count: offset
})
```

## API Usage Examples

### Basic Top-Rated Query
```
GET /api/mugshots?sortBy=top-rated&page=1&perPage=12
```

### Filtered Top-Rated Query
```
GET /api/mugshots?sortBy=top-rated&state=CA&tags=wild,funny&search=john&page=1&perPage=12
```

### Tag-Filtered Newest
```
GET /api/mugshots?sortBy=newest&tags=wild&page=1&perPage=12
```

## Performance Improvements

### Database Level
- **Efficient Aggregation**: Rating calculations done in database
- **Proper Indexing**: Indexes on `mugshot_id`, `rating`, and `tag_type`
- **Query Optimization**: Uses `WITH` clauses and proper JOINs

### Application Level
- **Batch Operations**: Rating and tag data fetched in parallel
- **Minimal Data Transfer**: Only necessary fields selected
- **Caching-Friendly**: Consistent, predictable query patterns

## Deployment Instructions

### 1. Run Database Migration
```bash
npx supabase db push
```

### 2. Test Functions
```bash
npx tsx scripts/debug-top-rated.ts
```

### 3. Run Tests
```bash
npm test __tests__/services/top-rated-mugshots.test.ts
npm test __tests__/services/tag-filtering.test.ts
```

## Expected Behavior

### Top-Rated Sorting
- **Only Rated Mugshots**: Returns only mugshots with at least 1 rating
- **Proper Ordering**: Sorted by average rating DESC, then total ratings DESC, then date DESC
- **Tag Filtering**: Filters by specified tags using OR logic
- **All Filters**: Supports search, location, date, and tag filtering

### Regular Sorting
- **Consistent Filtering**: Same tag filtering logic as top-rated
- **Backward Compatible**: Existing functionality unchanged
- **Performance**: Uses database functions when available

### API Responses
```json
{
  "success": true,
  "data": {
    "mugshots": [
      {
        "id": 123,
        "firstName": "John",
        "lastName": "Doe",
        "average_rating": 8.5,
        "total_ratings": 12,
        "wild_count": 3,
        "funny_count": 1,
        "spooky_count": 0,
        "user_rating": 9,
        "user_tags": ["wild"]
      }
    ],
    "pagination": {
      "page": 1,
      "perPage": 12,
      "totalCount": 45
    }
  }
}
```

## Future Enhancements

### 1. **Most-Viewed Sorting**
- Implement view tracking
- Add `views` column to mugshots table
- Update database functions to support view-based sorting

### 2. **Advanced Tag Logic**
- Implement AND logic for tags
- Tag exclusion filters
- Tag combination queries

### 3. **Performance Monitoring**
- Query performance metrics
- Slow query detection
- Caching strategies

## Conclusion

The enhanced filtering and top-rated system provides:
- ✅ **Accurate Top-Rated Results**: Only shows rated mugshots, properly sorted
- ✅ **Comprehensive Tag Filtering**: Works across all sorting methods
- ✅ **High Performance**: Database-level aggregation and filtering
- ✅ **Robust Error Handling**: Graceful fallbacks and comprehensive testing
- ✅ **Developer-Friendly**: Extensive testing and debugging tools

This implementation ensures that users see meaningful, accurately sorted results when using the top-rated feature, and provides powerful filtering capabilities across the entire application. 