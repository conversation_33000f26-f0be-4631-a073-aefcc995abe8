-- Migration 009: Add Top-Rated Mugshots Function
-- This migration adds a database function to efficiently get top-rated mugshots with filters

-- Function to search top-rated mugshots with filters
CREATE OR REPLACE FUNCTION public.search_top_rated_mugshots(
    search_term TEXT DEFAULT NULL,
    state_filter TEXT DEFAULT NULL,
    county_filter TEXT DEFAULT NULL,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL,
    limit_count INTEGER DEFAULT 12,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    id BIGINT,
    created_at TIMESTAMP WITH TIME ZONE,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBooking" DATE,
    "stateOfBooking" TEXT,
    "countyOfBooking" TEXT,
    "offenseDescription" TEXT,
    "additionalDetails" TEXT,
    "imagePath" TEXT,
    fb_status TEXT,
    "adsText" TEXT,
    jb_post_link TEXT,
    jb_fb_post BOOLEAN,
    average_rating DECIMAL(3,2),
    total_ratings INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.created_at,
        m."firstName",
        m."lastName",
        m."dateOfBooking",
        m."stateOfBooking",
        m."countyOfBooking",
        m."offenseDescription",
        m."additionalDetails",
        m."imagePath",
        m.fb_status,
        m."adsText",
        m.jb_post_link,
        m.jb_fb_post,
        COALESCE(AVG(r.rating), 0.0)::DECIMAL(3,2) as average_rating,
        COALESCE(COUNT(r.rating), 0)::INTEGER as total_ratings
    FROM public.mugshots m
    LEFT JOIN public.ratings r ON m.id = r.mugshot_id
    WHERE 
        (search_term IS NULL OR 
         m."firstName" ILIKE '%' || search_term || '%' OR 
         m."lastName" ILIKE '%' || search_term || '%')
    AND (state_filter IS NULL OR m."stateOfBooking" = state_filter)
    AND (county_filter IS NULL OR m."countyOfBooking" = county_filter)
    AND (date_from IS NULL OR m."dateOfBooking" >= date_from)
    AND (date_to IS NULL OR m."dateOfBooking" <= date_to)
    GROUP BY 
        m.id, m.created_at, m."firstName", m."lastName", m."dateOfBooking",
        m."stateOfBooking", m."countyOfBooking", m."offenseDescription",
        m."additionalDetails", m."imagePath", m.fb_status, m."adsText",
        m.jb_post_link, m.jb_fb_post
    HAVING COUNT(r.rating) > 0  -- Only return mugshots that have ratings
    ORDER BY 
        AVG(r.rating) DESC,
        COUNT(r.rating) DESC,
        m.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to count top-rated mugshots with filters (only those with ratings)
CREATE OR REPLACE FUNCTION public.count_top_rated_mugshots(
    search_term TEXT DEFAULT NULL,
    state_filter TEXT DEFAULT NULL,
    county_filter TEXT DEFAULT NULL,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    result_count INTEGER;
BEGIN
    SELECT COUNT(DISTINCT m.id)
    INTO result_count
    FROM public.mugshots m
    INNER JOIN public.ratings r ON m.id = r.mugshot_id
    WHERE 
        (search_term IS NULL OR 
         m."firstName" ILIKE '%' || search_term || '%' OR 
         m."lastName" ILIKE '%' || search_term || '%')
    AND (state_filter IS NULL OR m."stateOfBooking" = state_filter)
    AND (county_filter IS NULL OR m."countyOfBooking" = county_filter)
    AND (date_from IS NULL OR m."dateOfBooking" >= date_from)
    AND (date_to IS NULL OR m."dateOfBooking" <= date_to);
    
    RETURN result_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION public.search_top_rated_mugshots(TEXT, TEXT, TEXT, DATE, DATE, INTEGER, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.count_top_rated_mugshots(TEXT, TEXT, TEXT, DATE, DATE) TO anon, authenticated;

-- Add performance indexes for top-rated queries
CREATE INDEX IF NOT EXISTS idx_ratings_mugshot_rating ON public.ratings(mugshot_id, rating DESC);
CREATE INDEX IF NOT EXISTS idx_ratings_for_aggregation ON public.ratings(mugshot_id) INCLUDE (rating); 