"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from "@/components/ui/carousel"
import { <PERSON><PERSON> } from "@/components/ui/button"
import MugshotModal from "@/components/MugshotModal"
import MugshotCard from "@/components/MugshotCard"
import CategoryOverlay from "@/components/CategoryOverlay"
import { categoryEmojis } from "@/lib/constants"

type TimeFrame = "day" | "week" | "month" | "year"
// Updated to match the actual categoryEmojis keys
type Category = "Wild" | "Funny" | "Spooky"

type CarouselMugshot = {
  id: number
  name: string
  location: string
  rating: number
  image: string
  category: Category
  votes: number
  rank: number
  arrestDate: string
  birthDate: string
  offenses: string[]
}

// Mock data generator for each category - updated category names
const generateMugshots = (category: Category): CarouselMugshot[] => {
  const names = {
    Wild: ["Storm Chase", "Luna Wild", "Max Chaos", "Aria Rebel", "Jake Free", "Zoe Maverick"],
    Funny: ["Charlie Giggles", "Penny Jokes", "Max Comedy", "Luna Laughs", "Rico Smiles", "Bella Chuckles"],
    Spooky: ["Shadow Dark", "Luna Nightmare", "Max Terror", "Aria Ghost", "Jake Skull", "Raven Creep"]
  }
  
  const locations = [
    "Dallas County, Texas", "Miami-Dade County, Florida", "Los Angeles County, California",
    "Clark County, Nevada", "Manhattan County, New York", "Cook County, Illinois"
  ]
  
  return Array.from({ length: 6 }, (_, i) => {
    const id = i + 1 + (category === "Wild" ? 0 : category === "Funny" ? 100 : 200)
    return {
      id,
      name: names[category][i] || names[category][0],
      location: locations[i] || locations[0],
      rating: Number((4.8 - i * 0.1).toFixed(1)),
      image: "/images/mugshot-placeholder.png",
      category,
      votes: Math.floor(6000 - i * 300),
      rank: i + 1,
      arrestDate: new Date(Date.now() - (id * 123456789 % 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      birthDate: new Date(Date.now() - (id * 987654321 % 365) * 24 * 60 * 60 * 1000 * 25).toISOString().split('T')[0],
      offenses: ["Public Intoxication", "Disorderly Conduct"]
    }
  })
}

const timeFrameOptions = [
  { value: "day" as TimeFrame, label: "Daily" },
  { value: "week" as TimeFrame, label: "Weekly" },
  { value: "month" as TimeFrame, label: "Monthly" },
  { value: "year" as TimeFrame, label: "Yearly" }
]

interface TopMugshotsCarouselProps {
  timeFrame?: TimeFrame
  onTimeFrameChange?: (timeFrame: TimeFrame) => void
}

export default function TopMugshotsCarousel({ 
  timeFrame = "week", 
  onTimeFrameChange 
}: TopMugshotsCarouselProps) {
  const [selectedCategory, setSelectedCategory] = useState<Category>("Wild")
  const [currentTimeFrame, setCurrentTimeFrame] = useState<TimeFrame>(timeFrame)
  const [selectedMugshot, setSelectedMugshot] = useState<CarouselMugshot | null>(null)
  const [isMugshotModalOpen, setIsMugshotModalOpen] = useState(false)
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)
  const [count, setCount] = useState(0)
  const [animationTrigger, setAnimationTrigger] = useState<Category | null>(null)

  const handleTimeFrameChange = (newTimeFrame: TimeFrame) => {
    setCurrentTimeFrame(newTimeFrame)
    onTimeFrameChange?.(newTimeFrame)
  }

  const handleCategoryChange = (newCategory: Category) => {
    // Only trigger animation if it's a different category
    if (newCategory !== selectedCategory) {
      setAnimationTrigger(newCategory)
    }
    setSelectedCategory(newCategory)
  }

  const handleAnimationComplete = () => {
    setAnimationTrigger(null)
  }

  const handleCardClick = (mugshot: CarouselMugshot) => {
    setSelectedMugshot(mugshot)
    setIsMugshotModalOpen(true)
  }

  const getTimeFrameLabel = () => {
    return timeFrameOptions.find(opt => opt.value === currentTimeFrame)?.label || "Weekly"
  }

  const mugshotsByCategory = {
    Wild: generateMugshots("Wild"),
    Funny: generateMugshots("Funny"),
    Spooky: generateMugshots("Spooky")
  }

  // Carousel pagination logic
  useEffect(() => {
    if (!api) return

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  // Reset carousel when category changes
  useEffect(() => {
    if (api) {
      api.scrollTo(0)
      setCurrent(1)
    }
  }, [selectedCategory, api])

  return (
    <div className="space-y-6">
      {/* Header with Dynamic Title and Time Frame Controls */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h2 className="text-3xl md:text-4xl font-bold">
          <span className="text-white">{getTimeFrameLabel()}</span>{" "}
          <span className="text-pink-500">Top {selectedCategory}</span>{" "}
          <span className="text-white">Mugshots</span>
        </h2>
        
        {/* Time Frame Toggle - Below title on mobile, right side on desktop */}
        <div className="flex items-center gap-1 bg-gray-900/50 p-1 rounded-lg border border-pink-500/30 w-fit">
          {timeFrameOptions.map((option) => (
            <Button
              key={option.value}
              variant={currentTimeFrame === option.value ? "default" : "ghost"}
              size="sm"
              onClick={() => handleTimeFrameChange(option.value)}
              className={`px-3 py-1 text-xs font-medium transition-all cursor-pointer ${
                currentTimeFrame === option.value
                  ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-glow"
                  : "text-gray-400 hover:text-white hover:bg-gray-800"
              }`}
            >
              {option.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Tabs and Carousel Section */}
      <Tabs 
        value={selectedCategory} 
        onValueChange={(value) => handleCategoryChange(value as Category)}
        className="w-full"
      >
        {/* Container with border and padding */}
        <div className="border border-cyan-500/30 rounded-xl p-4 md:p-6 lg:p-8 bg-gray-900/50 backdrop-blur-sm">
          {/* Main Content Area with Vertically Centered Layout */}
          <div className="flex flex-col lg:flex-row items-stretch gap-6 lg:gap-8">
            {/* Category Tabs - Vertically Centered */}
            <div className="flex justify-center lg:flex-none lg:self-center">
              <TabsList className="flex flex-row lg:flex-col h-auto bg-gray-900/50 border border-cyan-500/30 p-2 w-full lg:w-auto">
                {Object.entries(categoryEmojis).map(([category, emoji]) => (
                  <TabsTrigger
                    key={category}
                    value={category}
                    className={`flex items-center gap-2 lg:gap-3 px-3 lg:px-4 py-2 lg:py-3 rounded-lg font-medium text-xs lg:text-sm transition-all duration-300 w-full justify-center lg:justify-start ${
                      selectedCategory === category
                        ? "bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-lg shadow-pink-500/30"
                        : "text-white hover:bg-gray-700/90 hover:text-cyan-400 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-pink-600 data-[state=active]:text-white"
                    }`}
                  >
                    <span className="text-lg lg:text-xl">{emoji}</span>
                    <span className="hidden sm:inline lg:inline">{category}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            {/* Carousel Content - Takes remaining space */}
            <div className="flex-1 flex flex-col justify-center min-w-0">
              {Object.entries(categoryEmojis).map(([category]) => (
                <TabsContent key={category} value={category} className="mt-0 min-w-0">
                  <div className="relative">
                    <Carousel
                      opts={{
                        align: "start",
                        loop: false, // Disable infinite loop
                      }}
                      className="w-full max-w-full overflow-hidden"
                      setApi={setApi}
                    >
                      <CarouselContent className="-ml-2 md:-ml-4 max-w-full">
                        {(mugshotsByCategory[category as Category] || []).map((mugshot) => (
                          <CarouselItem key={mugshot.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3 xl:basis-1/4">
                            <MugshotCard
                              mugshot={mugshot}
                              onClick={(m) => handleCardClick(m as CarouselMugshot)}
                              cardSize="medium"
                              showRankBadge={true}
                              ratingStats={{
                                averageRating: mugshot.rating,
                                totalRatings: mugshot.votes
                              }}
                            />
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                      <CarouselPrevious className="left-2 bg-gray-800/90 border-cyan-500/30 text-white hover:bg-gray-700/90 hover:border-cyan-500/50 disabled:opacity-50 disabled:cursor-not-allowed" />
                      <CarouselNext className="right-2 bg-gray-800/90 border-cyan-500/30 text-white hover:bg-gray-700/90 hover:border-cyan-500/50 disabled:opacity-50 disabled:cursor-not-allowed" />
                    </Carousel>
                    
                    {/* Dot Pagination */}
                    {count > 1 && (
                      <div className="flex justify-center mt-3 gap-2">
                        {Array.from({ length: count }).map((_, index) => (
                          <button
                            key={index}
                            className={`w-2 h-2 rounded-full transition-all cursor-pointer ${
                              index === current - 1 
                                ? 'bg-pink-500 shadow-lg shadow-pink-500/30' 
                                : 'bg-gray-600 hover:bg-gray-500'
                            }`}
                            onClick={() => api?.scrollTo(index)}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </TabsContent>
              ))}
            </div>
          </div>
        </div>
      </Tabs>

      {/* Mugshot Modal */}
      {selectedMugshot && (
        <MugshotModal
          mugshot={selectedMugshot}
          isOpen={isMugshotModalOpen}
          onClose={() => setIsMugshotModalOpen(false)}
          preloadedRatingStats={{
            averageRating: selectedMugshot.rating,
            totalRatings: selectedMugshot.votes
          }}
        />
      )}

      {/* Category Animation Overlay */}
      <CategoryOverlay 
        trigger={animationTrigger === "Spooky" ? "Scary" : animationTrigger} 
        onComplete={handleAnimationComplete}
      />
    </div>
  )
} 