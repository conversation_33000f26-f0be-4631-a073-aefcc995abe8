import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  submitRating, 
  getUserRatings, 
  getCategoryAverages, 
  getOverallRating,
  checkTimeGate,
  getMugshotsWithRatings,
  deleteRating
} from '../../lib/services/rating-service'

// Mock Supabase client
const mockSupabase = {
  auth: {
    getUser: vi.fn(),
  },
  from: vi.fn(() => mockSupabase),
  select: vi.fn(() => mockSupabase),
  eq: vi.fn(() => mockSupabase),
  single: vi.fn(),
  delete: vi.fn(() => mockSupabase),
  order: vi.fn(() => mockSupabase),
  limit: vi.fn(() => mockSupabase),
  rpc: vi.fn(),
}

vi.mock('../../lib/supabase/server', () => ({
  createClient: vi.fn(() => Promise.resolve(mockSupabase)),
}))

// Mock next/cache
vi.mock('next/cache', () => ({
  revalidatePath: vi.fn(),
}))

describe('Rating Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('submitRating', () => {
    const mockUser = { id: 'test-user-id' }
    const mockMugshot = { dateOfBooking: '2024-01-01' }

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
      mockSupabase.single.mockResolvedValue({
        data: mockMugshot,
        error: null,
      })
      mockSupabase.rpc.mockResolvedValue({
        data: true, // is_rating_allowed returns true
        error: null,
      })
    })

    it('should successfully submit a valid rating', async () => {
      mockSupabase.rpc
        .mockResolvedValueOnce({ data: true, error: null }) // checkTimeGate
        .mockResolvedValueOnce({ data: { id: 'rating-id' }, error: null }) // upsert_rating

      const result = await submitRating(1, 'Hot', 5)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Rating saved successfully!')
      expect(mockSupabase.rpc).toHaveBeenCalledWith('upsert_rating', {
        user_id_param: mockUser.id,
        mugshot_id_param: 1,
        category_param: 'Hot',
        rating_param: 5,
      })
    })

    it('should reject unauthenticated users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const result = await submitRating(1, 'Hot', 5)

      expect(result.success).toBe(false)
      expect(result.message).toBe('Authentication required to submit ratings')
      expect(result.error).toBe('UNAUTHENTICATED')
    })

    it('should reject invalid rating values', async () => {
      const result = await submitRating(1, 'Hot', 6)

      expect(result.success).toBe(false)
      expect(result.message).toBe('Rating must be between 1 and 5')
      expect(result.error).toBe('INVALID_RATING')
    })

    it('should reject invalid categories', async () => {
      // @ts-ignore - testing invalid category
      const result = await submitRating(1, 'Invalid', 3)

      expect(result.success).toBe(false)
      expect(result.message).toBe('Invalid rating category')
      expect(result.error).toBe('INVALID_CATEGORY')
    })

    it('should enforce time gate restrictions', async () => {
      // Mock time gate check to return false
      mockSupabase.rpc
        .mockResolvedValueOnce({ data: false, error: null }) // is_rating_allowed returns false

      const result = await submitRating(1, 'Hot', 3)

      expect(result.success).toBe(false)
      expect(result.error).toBe('TIME_GATE_ACTIVE')
    })

    it('should handle mugshot not found', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: { message: 'Not found' },
      })

      const result = await submitRating(999, 'Hot', 3)

      expect(result.success).toBe(false)
      expect(result.message).toBe('Mugshot not found')
      expect(result.error).toBe('MUGSHOT_NOT_FOUND')
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.rpc
        .mockResolvedValueOnce({ data: true, error: null }) // checkTimeGate
        .mockResolvedValueOnce({ data: null, error: { message: 'Database error' } }) // upsert_rating

      const result = await submitRating(1, 'Hot', 3)

      expect(result.success).toBe(false)
      expect(result.message).toBe('Failed to save rating. Please try again.')
      expect(result.error).toBe('DATABASE_ERROR')
    })
  })

  describe('getUserRatings', () => {
    const mockUser = { id: 'test-user-id' }

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
    })

    it('should return user ratings for authenticated user', async () => {
      const mockRatings = [
        { category: 'Hot', rating: 4 },
        { category: 'Funny', rating: 3 },
      ]

      mockSupabase.rpc.mockResolvedValue({
        data: mockRatings,
        error: null,
      })

      const result = await getUserRatings(1)

      expect(result).toEqual(mockRatings)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_user_ratings', {
        user_id_param: mockUser.id,
        mugshot_id_param: 1,
      })
    })

    it('should return empty array for unauthenticated users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const result = await getUserRatings(1)

      expect(result).toEqual([])
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const result = await getUserRatings(1)

      expect(result).toEqual([])
    })
  })

  describe('getCategoryAverages', () => {
    it('should return category averages for a mugshot', async () => {
      const mockAverages = [
        { category: 'Hot', average_rating: 4.2, total_ratings: 10 },
        { category: 'Funny', average_rating: 3.8, total_ratings: 8 },
      ]

      mockSupabase.rpc.mockResolvedValue({
        data: mockAverages,
        error: null,
      })

      const result = await getCategoryAverages(1)

      expect(result).toEqual(mockAverages)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_category_averages', {
        mugshot_id_param: 1,
      })
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const result = await getCategoryAverages(1)

      expect(result).toEqual([])
    })
  })

  describe('getOverallRating', () => {
    it('should return overall rating for a mugshot', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: 4.2,
        error: null,
      })

      const result = await getOverallRating(1)

      expect(result).toBe(4.2)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('calculate_overall_rating', {
        mugshot_id_param: 1,
      })
    })

    it('should return 0 for database errors', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const result = await getOverallRating(1)

      expect(result).toBe(0)
    })
  })

  describe('checkTimeGate', () => {
    it('should allow rating for mugshots older than 24 hours', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null,
      })

      const result = await checkTimeGate('2024-01-01')

      expect(result.allowed).toBe(true)
      expect(result.message).toBe('Rating is available')
    })

    it('should block rating for recent mugshots', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: false,
        error: null,
      })

      const result = await checkTimeGate('2024-12-19') // Recent date

      expect(result.allowed).toBe(false)
      expect(result.message).toContain('Rating will be available in')
      expect(result.hoursRemaining).toBeGreaterThan(0)
    })

    it('should handle missing booking date', async () => {
      const result = await checkTimeGate(null)

      expect(result.allowed).toBe(false)
      expect(result.message).toBe('Booking date not available for this mugshot')
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const result = await checkTimeGate('2024-01-01')

      expect(result.allowed).toBe(false)
      expect(result.message).toBe('Unable to verify rating eligibility')
    })
  })

  describe('getMugshotsWithRatings', () => {
    it('should return mugshots sorted by rating', async () => {
      const mockMugshots = [
        { mugshot_id: 1, overall_rating: 4.5, total_ratings: 20 },
        { mugshot_id: 2, overall_rating: 4.2, total_ratings: 15 },
      ]

      mockSupabase.order.mockReturnThis()
      mockSupabase.limit.mockResolvedValue({
        data: mockMugshots,
        error: null,
      })

      const result = await getMugshotsWithRatings(10)

      expect(result).toEqual(mockMugshots)
      expect(mockSupabase.from).toHaveBeenCalledWith('mugshot_ratings_summary')
      expect(mockSupabase.order).toHaveBeenCalledWith('overall_rating', { ascending: false })
      expect(mockSupabase.limit).toHaveBeenCalledWith(10)
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.order.mockReturnThis()
      mockSupabase.limit.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const result = await getMugshotsWithRatings(10)

      expect(result).toEqual([])
    })
  })

  describe('deleteRating', () => {
    const mockUser = { id: 'test-user-id' }

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })
    })

    it('should successfully delete a rating', async () => {
      mockSupabase.eq.mockReturnThis()
      mockSupabase.delete.mockReturnThis()
      
      const result = await deleteRating(1, 'Hot')

      expect(result.success).toBe(true)
      expect(result.message).toBe('Rating deleted successfully')
      expect(mockSupabase.from).toHaveBeenCalledWith('mugshot_ratings')
      expect(mockSupabase.delete).toHaveBeenCalled()
    })

    it('should reject unauthenticated users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const result = await deleteRating(1, 'Hot')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Authentication required')
      expect(result.error).toBe('UNAUTHENTICATED')
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.eq.mockReturnThis()
      mockSupabase.delete.mockRejectedValue(new Error('Database error'))

      const result = await deleteRating(1, 'Hot')

      expect(result.success).toBe(false)
      expect(result.message).toBe('An unexpected error occurred')
      expect(result.error).toBe('UNEXPECTED_ERROR')
    })
  })
}) 