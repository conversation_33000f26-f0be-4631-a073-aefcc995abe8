/**
 * TypeScript interfaces for TanStack Query system
 * Provides type safety for all query hooks and mutations
 */

import type { 
  MugshotFilters, 
  MugshotsSortOptions, 
  MugshotsPaginationOptions,
  MugshotsResponse,
  SingleMugshotResponse,
  UserMugshotResponse,
  RatingSubmissionResult
} from '@/lib/services/api-client'

// Query Hook Return Types
export interface UseMugshotsQueryOptions {
  filters?: MugshotFilters
  sortOptions?: MugshotsSortOptions
  pagination?: MugshotsPaginationOptions
  enabled?: boolean
}

export interface UseMugshotDetailQueryOptions {
  mugshotId: string
  enabled?: boolean
}

export interface UseUserMugshotDataQueryOptions {
  mugshotId: string
  enabled?: boolean
}

// Mutation Types
export interface RatingMutationData {
  mugshotId: string
  rating: number
}

export interface TagMutationData {
  mugshotId: string
  tagType: 'wild' | 'funny' | 'spooky'
}

// Error Types
export interface QueryError {
  message: string
  status?: number
  code?: string
}

export interface AuthenticationError extends QueryError {
  code: 'UNAUTHENTICATED'
  requiresRedirect: boolean
  redirectUrl?: string
}

export interface ValidationError extends QueryError {
  code: 'VALIDATION_ERROR'
  field?: string
  value?: any
}

// Cache Management Types
export interface CacheOptions {
  staleTime?: number
  gcTime?: number
  retry?: number | boolean | ((failureCount: number, error: any) => boolean)
  retryDelay?: number | ((attemptIndex: number) => number)
  refetchOnWindowFocus?: boolean
  refetchOnMount?: boolean | 'always'
  enabled?: boolean
}

export interface OptimisticUpdateContext {
  previousMugshot?: any
  previousUser?: any
  mugshotId: string
  timestamp: number
}

// Query State Types
export interface QueryState<TData = any> {
  data?: TData
  error?: QueryError
  isLoading: boolean
  isSuccess: boolean
  isError: boolean
  isFetching: boolean
  isStale: boolean
}

export interface MutationState<TData = any, TVariables = any> {
  data?: TData
  error?: QueryError
  isLoading: boolean
  isSuccess: boolean
  isError: boolean
  variables?: TVariables
}

// Specific Query Result Types
export type MugshotsQueryResult = QueryState<MugshotsResponse['data']>
export type MugshotDetailQueryResult = QueryState<SingleMugshotResponse['data']>
export type UserMugshotDataQueryResult = QueryState<UserMugshotResponse['data']>

// Specific Mutation Result Types
export type RatingMutationResult = MutationState<RatingSubmissionResult, number>
export type TagMutationResult = MutationState<any, string>

// Authentication State Integration
export interface AuthenticatedQueryOptions {
  requiresAuth: boolean
  redirectOnUnauthenticated?: boolean
  preserveUrl?: boolean
}

// Real-time Update Types
export interface RealtimeUpdateData {
  mugshotId: string
  type: 'rating' | 'tag' | 'new_mugshot'
  data: any
  timestamp: number
  userId?: string
}

export interface RealtimeSubscriptionOptions {
  enabled: boolean
  mugshotId?: string
  userId?: string
  onUpdate?: (data: RealtimeUpdateData) => void
  onError?: (error: QueryError) => void
  onConnect?: () => void
  onDisconnect?: () => void
}

// Prefetch Types
export interface PrefetchOptions {
  queryKey: readonly any[]
  queryFn: () => Promise<any>
  staleTime?: number
  gcTime?: number
}

// Query Invalidation Types
export interface InvalidationPattern {
  queryKey: readonly any[]
  exact?: boolean
  refetchActive?: boolean
  refetchInactive?: boolean
}

// Bulk Operations Types
export interface BulkQueryOptions {
  queries: Array<{
    queryKey: readonly any[]
    queryFn: () => Promise<any>
    options?: CacheOptions
  }>
  batchSize?: number
  delayBetweenBatches?: number
}

// Error Boundary Integration
export interface QueryErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: QueryError; resetError: () => void }>
  onError?: (error: QueryError, errorInfo: any) => void
  children: React.ReactNode
}

// Development & Debugging Types
export interface QueryDevtoolsConfig {
  initialIsOpen?: boolean
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  panelProps?: any
  closeButtonProps?: any
  toggleButtonProps?: any
}

export interface QueryLogger {
  onQueryStart?: (queryKey: readonly any[]) => void
  onQuerySuccess?: (queryKey: readonly any[], data: any, duration: number) => void
  onQueryError?: (queryKey: readonly any[], error: QueryError, duration: number) => void
  onMutationStart?: (variables: any) => void
  onMutationSuccess?: (data: any, variables: any, duration: number) => void
  onMutationError?: (error: QueryError, variables: any, duration: number) => void
}

// Performance Monitoring Types
export interface QueryMetrics {
  queryKey: readonly any[]
  duration: number
  cacheHit: boolean
  dataSize?: number
  timestamp: number
  status: 'success' | 'error' | 'loading'
}

export interface MutationMetrics {
  variables: any
  duration: number
  optimisticUpdate: boolean
  rollbackRequired?: boolean
  timestamp: number
  status: 'success' | 'error' | 'loading'
}

// Configuration Types
export interface QueryClientConfig {
  defaultOptions?: {
    queries?: CacheOptions
    mutations?: {
      retry?: number | boolean
      retryDelay?: number | ((attemptIndex: number) => number)
    }
  }
  queryCache?: any
  mutationCache?: any
  logger?: QueryLogger
  errorHandler?: (error: QueryError) => void
} 