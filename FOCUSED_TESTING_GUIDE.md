# 🎯 Focused Mugshots API Performance Testing Guide

## 🔍 **Performance Analysis Based on Your Native Service**

After analyzing your `mugshots-native-service.ts`, I've identified distinct performance characteristics:

### **⚡ FAST Queries (No Joins):**
- **`newest` sort** - Simple ORDER BY dateOfBooking, created_at
- **`most-viewed` sort** - Simple ORDER BY 
- **Basic filters** - Direct column filters (state, county, date, search)

### **🐌 SLOW Queries (Complex Operations):**
- **`top-rated` sort** - Fetches ALL ratings, calculates averages in TypeScript, filters top 1000
- **Tag filtering** - Additional query to tags table + intersection logic
- **Combined complex filters** - Multiple table queries with filtering

## 🚀 **Focused Test Suite**

I've created 5 specialized tests targeting different performance scenarios:

### **1. Baseline Performance Test**
```bash
npm run load-test:baseline
```
- **Focus:** Absolute fastest path (no filters, newest sort only)
- **Expected:** 200ms median, 800ms p95
- **Purpose:** Establish your performance baseline

### **2. Fast Queries Test**
```bash
npm run load-test:fast
```
- **Focus:** Simple filters with newest/most-viewed sort (no joins)
- **Filters:** State, county, search, date range
- **Expected:** 300ms median, 1200ms p95
- **Purpose:** Test your optimized query paths

### **3. Top-Rated Queries Test**
```bash
npm run load-test:top-rated
```
- **Focus:** Most expensive sort option (rating calculations)
- **Expected:** 800ms median, 3000ms p95
- **Purpose:** Test your complex calculation performance

### **4. Tag Queries Test**
```bash
npm run load-test:tags
```
- **Focus:** Most expensive filter path (tag table queries)
- **Expected:** 1200ms median, 4000ms p95
- **Purpose:** Test your most complex query scenario

### **5. Realistic Mixed Test**
```bash
npm run load-test:mixed
```
- **Focus:** Real user behavior (80% fast, 20% slow queries)
- **Expected:** 500ms median, 2000ms p95
- **Purpose:** Test realistic production usage

## 📊 **Recommended Testing Sequence**

### **Phase 1: Establish Baseline**
```bash
# Test 1: Pure baseline (fastest possible)
npm run load-test:baseline
node load-tests/analyze-results.js

# Expected: Should be very fast, 95%+ success rate
```

### **Phase 2: Test Fast Query Paths**
```bash
# Test 2: Fast queries with simple filters
npm run load-test:fast
node load-tests/analyze-results.js

# Expected: Should be reasonably fast, 90%+ success rate
```

### **Phase 3: Test Complex Query Paths**
```bash
# Test 3: Top-rated queries (complex calculations)
npm run load-test:top-rated
node load-tests/analyze-results.js

# Expected: Slower but should work, 80%+ success rate
```

### **Phase 4: Test Most Expensive Path**
```bash
# Test 4: Tag queries (most expensive)
npm run load-test:tags
node load-tests/analyze-results.js

# Expected: Slowest, may show stress, 70%+ success rate
```

### **Phase 5: Test Realistic Usage**
```bash
# Test 5: Mixed realistic usage
npm run load-test:mixed
node load-tests/analyze-results.js

# Expected: Mixed performance, 85%+ success rate
```

## 🎯 **Performance Expectations by Test Type**

### **Baseline Test (Fastest)**
- **Load:** 15 concurrent users
- **Duration:** 5 minutes
- **Expected Results:**
  - ✅ Median: 150-300ms
  - ✅ P95: 500-800ms
  - ✅ Success Rate: 98-100%

### **Fast Queries Test**
- **Load:** 20 concurrent users
- **Duration:** 6 minutes
- **Expected Results:**
  - ✅ Median: 250-400ms
  - ⚠️ P95: 800-1200ms
  - ✅ Success Rate: 90-98%

### **Top-Rated Test (Slow)**
- **Load:** 8 concurrent users
- **Duration:** 7 minutes
- **Expected Results:**
  - ⚠️ Median: 600-1000ms
  - ❌ P95: 2000-3000ms
  - ⚠️ Success Rate: 80-90%

### **Tag Queries Test (Slowest)**
- **Load:** 6 concurrent users
- **Duration:** 8 minutes
- **Expected Results:**
  - ❌ Median: 1000-1500ms
  - ❌ P95: 3000-4000ms
  - ❌ Success Rate: 70-85%

### **Mixed Test (Realistic)**
- **Load:** 12 concurrent users
- **Duration:** 7 minutes
- **Expected Results:**
  - ⚠️ Median: 400-600ms
  - ⚠️ P95: 1500-2000ms
  - ✅ Success Rate: 85-95%

## 🔧 **Optimization Priorities Based on Results**

### **If Baseline Test Fails:**
1. **Database Connection Issues** - Check Supabase connection limits
2. **Basic Query Optimization** - Add indexes on dateOfBooking, created_at
3. **Server Resource Issues** - Monitor CPU/memory usage

### **If Fast Queries Test Fails:**
1. **Add Indexes:**
   ```sql
   CREATE INDEX idx_mugshots_state ON mugshots(stateOfBooking);
   CREATE INDEX idx_mugshots_county ON mugshots(countyOfBooking);
   CREATE INDEX idx_mugshots_date_range ON mugshots(dateOfBooking);
   ```
2. **Optimize Search Queries** - Full-text search indexes
3. **Connection Pooling** - Optimize database connections

### **If Top-Rated Test Fails:**
1. **Cache Rating Calculations** - Pre-calculate and cache top-rated results
2. **Optimize Rating Query** - Use database aggregation instead of TypeScript
3. **Limit Result Set** - Reduce the 1000 limit for top-rated queries

### **If Tag Queries Test Fails:**
1. **Optimize Tag Intersection** - Use database JOINs instead of separate queries
2. **Cache Tag Results** - Cache frequently accessed tag combinations
3. **Add Tag Indexes** - Optimize tag table queries

## 📈 **Performance Monitoring Strategy**

### **Key Metrics to Track:**
1. **Response Time by Query Type:**
   - Baseline vs Fast vs Slow vs Tag queries
2. **Success Rate by Complexity:**
   - Simple filters vs Complex calculations
3. **Database Query Performance:**
   - Individual query execution times
4. **Resource Usage:**
   - CPU, memory, database connections

### **Continuous Testing:**
```bash
# Weekly performance regression testing
npm run load-test:baseline && npm run load-test:mixed

# Before/after optimization comparison
npm run load-test:top-rated  # Before optimization
# ... make optimizations ...
npm run load-test:top-rated  # After optimization

# Compare results
node load-tests/analyze-results.js results/before.json results/after.json
```

## 🎯 **Success Criteria**

### **Minimum Acceptable Performance:**
- **Baseline:** < 500ms median, 95%+ success
- **Fast Queries:** < 800ms median, 90%+ success
- **Top-Rated:** < 2000ms median, 80%+ success
- **Tag Queries:** < 3000ms median, 70%+ success
- **Mixed:** < 1000ms median, 85%+ success

### **Excellent Performance Goals:**
- **Baseline:** < 200ms median, 99%+ success
- **Fast Queries:** < 400ms median, 95%+ success
- **Top-Rated:** < 1000ms median, 90%+ success
- **Tag Queries:** < 1500ms median, 85%+ success
- **Mixed:** < 600ms median, 95%+ success

## 🚀 **Ready to Start?**

Begin with the baseline test to establish your performance foundation:

```bash
npm run load-test:baseline
```

This focused testing approach will give you precise insights into which parts of your native service perform well and which need optimization!
