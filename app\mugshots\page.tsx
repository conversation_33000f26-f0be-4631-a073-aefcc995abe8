import { Suspense } from 'react'
import MugshotsPageClient from './components/MugshotsPageClient'
import MugshotsLoading from './components/MugshotsLoading'

// Types for search parameters - Next.js 15 pattern
interface SearchParams {
  search?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  categories?: string
  tags?: string
  sort?: string
  perPage?: string
  gridView?: string
  page?: string
}

interface PageProps {
  searchParams: Promise<SearchParams>
}

// Server Component that extracts URL params and passes to client
async function MugshotsContent({ searchParams }: PageProps) {
  const params = await searchParams
  
  // Parse initial parameters for faster first render
  const initialSearchTerm = params.search || ''
  const initialSelectedState = params.state || 'all-states'
  const initialSelectedCounty = params.county || 'all-counties'
  const initialDateFrom = params.dateFrom || ''
  const initialDateTo = params.dateTo || ''
  const initialCategories = params.categories?.split(',').filter(Boolean) || []
  const initialTags = params.tags?.split(',').filter(Boolean) || []
  const initialSortBy = params.sort || 'newest'
  const initialPerPage = parseInt(params.perPage || '12', 10)
  const initialGridView = (params.gridView || 'large') as 'large' | 'compact'
  const initialCurrentPage = parseInt(params.page || '1', 10)

  return (
    <MugshotsPageClient
      initialSearchTerm={initialSearchTerm}
      initialSelectedState={initialSelectedState}
      initialSelectedCounty={initialSelectedCounty}
      initialDateFrom={initialDateFrom}
      initialDateTo={initialDateTo}
      initialCategories={initialCategories}
      initialTags={initialTags}
      initialSortBy={initialSortBy}
      initialPerPage={initialPerPage}
      initialGridView={initialGridView}
      initialCurrentPage={initialCurrentPage}
    />
  )
}

// Main Server Component - Next.js 15 pattern
export default function MugshotsPage({ searchParams }: PageProps) {
  return (
    <Suspense fallback={<MugshotsLoading />}>
      <MugshotsContent searchParams={searchParams} />
    </Suspense>
  )
} 