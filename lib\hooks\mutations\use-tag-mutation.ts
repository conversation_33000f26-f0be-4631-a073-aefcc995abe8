import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'

/**
 * Tag mutation hook with optimistic updates for instant tag toggle feedback
 * Handles authentication, optimistic updates, and error rollback
 * Uses TWO-CACHE UPDATE STRATEGY: updates both detail cache AND grid caches
 * 
 * @param mugshotId - The ID of the mugshot to tag
 * @returns TanStack Query mutation for tag toggle
 */
export function useTagMutation(mugshotId: string) {
  const queryClient = useQueryClient()
  const { isAuthenticated, user } = useAuthStore()
  
  return useMutation({
    mutationFn: async (tagType: string) => {
      // Immediate authentication check - redirect if not authenticated
      if (!isAuthenticated || !user) {
        // Redirect to login with current URL for post-login redirect
        const currentUrl = window.location.href
        window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
        throw new Error('Authentication required')
      }
      
      // Validate tag type
      if (!['wild', 'funny', 'spooky'].includes(tagType)) {
        throw new Error('Invalid tag type. Must be wild, funny, or spooky')
      }
      
      const response = await fetch('/api/tags/toggle', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          mugshotId: parseInt(mugshotId), 
          tagType 
        })
      })
      
      const result = await response.json()
      
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to toggle tag')
      }
      
      return result.data
    },
    
    onMutate: async (tagType: string) => {
      // Only proceed with optimistic updates for authenticated users
      if (!isAuthenticated || !user) {
        return
      }
      
      // ✅ STEP 1: Cancel outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: ['mugshot', mugshotId] })
      await queryClient.cancelQueries({ queryKey: ['user', 'mugshot', mugshotId] })
      await queryClient.cancelQueries({ queryKey: ['mugshots'] }) // Cancel all grid queries
      
      // ✅ STEP 2: Snapshot previous data for rollback
      const previousMugshot = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
      const previousUser = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
      
      // ✅ STEP 3: Update detail cache (already working)
      // Toggle user tag state immediately
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], (old: unknown) => {
        if (!old) return old
        
        const oldData = old as { userTags?: string[] }
        const currentTags = oldData.userTags || []
        const hasTag = currentTags.includes(tagType)
        
        return {
          ...oldData,
          userTags: hasTag 
            ? currentTags.filter((t: string) => t !== tagType)
            : [...currentTags, tagType]
        }
      })
      
      // Calculate new tag statistics
      const hasTag = (previousUser as { userTags?: string[] })?.userTags?.includes(tagType) || false
      const oldMugshotData = previousMugshot as { tags?: { tagCounts: Record<string, number> } }
      const currentCount = oldMugshotData?.tags?.tagCounts[tagType] || 0
      const newCount = Math.max(0, hasTag ? currentCount - 1 : currentCount + 1)
      
      // Update tag statistics optimistically
      queryClient.setQueryData(['mugshot', mugshotId, 'detail'], (old: unknown) => {
        const oldData = old as { tags?: { tagCounts: Record<string, number> } }
        if (!oldData?.tags) return old
        
        return {
          ...oldData,
          tags: {
            ...oldData.tags,
            tagCounts: {
              ...oldData.tags.tagCounts,
              [tagType]: newCount
            }
          }
        }
      })
      
      // ✅ STEP 4: Update ALL grid caches - THE MAGIC HAPPENS HERE!
      // Get all ['mugshots', ...] query cache entries and update the specific mugshot
      const mugshotCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
      const previousGridCaches: Array<{ queryKey: readonly unknown[], data: unknown }> = []
      
      mugshotCaches.forEach(([queryKey, data]) => {
        if (!data || typeof data !== 'object') return
        
        // Save original data for rollback
        previousGridCaches.push({ queryKey, data })
        
        const gridData = data as { mugshots?: Array<{ id: number, tag_counts?: Record<string, number>, [key: string]: unknown }> }
        if (!gridData.mugshots || !Array.isArray(gridData.mugshots)) return
        
        // Find and update the specific mugshot in this grid cache
        const updatedMugshots = gridData.mugshots.map(mugshot => {
          if (mugshot.id === parseInt(mugshotId, 10)) {
            // Update this mugshot's tag data in the grid
            const currentTagCounts = mugshot.tag_counts || {}
            const updatedTagCounts = {
              ...currentTagCounts,
              [tagType]: newCount
            }
            
            return {
              ...mugshot,
              // Update raw tag_counts (JSONB from SQL function)
              tag_counts: updatedTagCounts,
              // Update UI-expected camelCase properties
              wildCount: updatedTagCounts.wild || 0,
              funnyCount: updatedTagCounts.funny || 0,
              spookyCount: updatedTagCounts.spooky || 0,
              // Update database format snake_case properties
              wild_count: updatedTagCounts.wild || 0,
              funny_count: updatedTagCounts.funny || 0,
              spooky_count: updatedTagCounts.spooky || 0,
              // Keep all other mugshot properties unchanged
            }
          }
          return mugshot // Leave other mugshots unchanged
        })
        
        // Update the cache with the modified grid data
        queryClient.setQueryData(queryKey, {
          ...gridData,
          mugshots: updatedMugshots
        })
      })
      
      return { 
        previousMugshot, 
        previousUser, 
        previousGridCaches 
      }
    },
    
    onError: (error, _tagType, context) => {
      console.error('Tag mutation error:', error)
      console.error('Failed tag operation:', { mugshotId })
      
      // ✅ STEP 5: Rollback ALL optimistic updates
      if (context?.previousMugshot) {
        queryClient.setQueryData(['mugshot', mugshotId, 'detail'], context.previousMugshot)
      }
      if (context?.previousUser) {
        queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], context.previousUser)
      }
      
      // Rollback all grid cache updates
      if (context?.previousGridCaches) {
        context.previousGridCaches.forEach(({ queryKey, data }) => {
          queryClient.setQueryData(queryKey, data)
        })
      }
    },
    
    onSuccess: (result, _tagType) => {
      // Tag submitted successfully - just log success
      console.log('Tag submitted successfully:', result)
      
      // Don't invalidate mugshots queries here - it causes page re-render and closes popover
      // The optimistic updates already handle the UI changes instantly
    },

    onSettled: () => {
      // Invalidate other related queries for consistency
      queryClient.invalidateQueries({ queryKey: ['mugshot', mugshotId] })
      queryClient.invalidateQueries({ queryKey: ['user', 'mugshot', mugshotId] })
      
      // Invalidate global tag statistics cache if applicable
      queryClient.invalidateQueries({ queryKey: ['tags', 'statistics'] })
    }
  })
}

/**
 * Utility to check if user can tag a mugshot
 * Returns whether the tag mutation should be enabled
 */
export function useCanTag(mugshotId: string) {
  const { isAuthenticated, user } = useAuthStore()
  
  // Validate mugshotId is provided
  if (!mugshotId || mugshotId.trim() === '') {
    return false
  }
  
  // Future: Add per-mugshot tagging rules here
  // Future: Check if user has reached tagging limits for this mugshot
  
  return isAuthenticated && !!user
}

/**
 * Utility to handle unauthenticated tag click
 * Redirects to login with current URL preserved
 */
export function handleUnauthenticatedTag() {
  const currentUrl = window.location.href
  window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
}

/**
 * Hook to get current user's tags for a mugshot
 * Useful for determining tag button states
 */
export function useUserTags(mugshotId: string) {
  const queryClient = useQueryClient()
  
  const userData = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data']) as { userTags?: string[] } | undefined
  
  return userData?.userTags || []
}

/**
 * Hook to get current tag counts for a mugshot
 * Useful for displaying tag statistics
 */
export function useTagCounts(mugshotId: string) {
  const queryClient = useQueryClient()
  
  const mugshotData = queryClient.getQueryData(['mugshot', mugshotId, 'detail']) as { tags?: { tagCounts: Record<string, number> } } | undefined
  
  return mugshotData?.tags?.tagCounts || { wild: 0, funny: 0, spooky: 0 }
} 