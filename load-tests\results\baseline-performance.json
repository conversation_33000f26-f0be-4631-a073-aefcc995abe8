{"aggregate": {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 3300, "vusers.created": 3300, "errors.Undefined function \"generateBaselineFilters\"": 3300, "http.requests": 3300, "http.codes.200": 2271, "http.responses": 2271, "http.downloaded_bytes": 20547976, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 2271, "errors.Failed capture or match": 2271, "vusers.failed": 3300, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 1029, "errors.ETIMEDOUT": 1029}, "rates": {"http.request_rate": 8}, "firstCounterAt": 1753540106839, "firstHistogramAt": 1753540110252, "lastCounterAt": 1753540406727, "lastHistogramAt": 1753540406727, "firstMetricAt": 1753540106839, "lastMetricAt": 1753540406727, "period": 1753540400000, "summaries": {"http.response_time": {"min": 574, "max": 9987, "count": 2271, "mean": 2994.9, "p50": 2671, "median": 2671, "p75": 3534.1, "p90": 6312.2, "p95": 8520.7, "p99": 9801.2, "p999": 9999.2}, "http.response_time.2xx": {"min": 574, "max": 9987, "count": 2271, "mean": 2994.9, "p50": 2671, "median": 2671, "p75": 3534.1, "p90": 6312.2, "p95": 8520.7, "p99": 9801.2, "p999": 9999.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 574, "max": 9987, "count": 2271, "mean": 2994.9, "p50": 2671, "median": 2671, "p75": 3534.1, "p90": 6312.2, "p95": 8520.7, "p99": 9801.2, "p999": 9999.2}}, "histograms": {"http.response_time": {"min": 574, "max": 9987, "count": 2271, "mean": 2994.9, "p50": 2671, "median": 2671, "p75": 3534.1, "p90": 6312.2, "p95": 8520.7, "p99": 9801.2, "p999": 9999.2}, "http.response_time.2xx": {"min": 574, "max": 9987, "count": 2271, "mean": 2994.9, "p50": 2671, "median": 2671, "p75": 3534.1, "p90": 6312.2, "p95": 8520.7, "p99": 9801.2, "p999": 9999.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 574, "max": 9987, "count": 2271, "mean": 2994.9, "p50": 2671, "median": 2671, "p75": 3534.1, "p90": 6312.2, "p95": 8520.7, "p99": 9801.2, "p999": 9999.2}}}, "intermediate": [{"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 18, "vusers.created": 18, "errors.Undefined function \"generateBaselineFilters\"": 18, "http.requests": 18}, "rates": {"http.request_rate": 7}, "http.request_rate": null, "firstCounterAt": 1753540106839, "lastCounterAt": 1753540109963, "firstMetricAt": 1753540106839, "lastMetricAt": 1753540109963, "period": "1753540100000", "summaries": {}, "histograms": {}}, {"counters": {"http.codes.200": 64, "http.responses": 64, "http.downloaded_bytes": 579044, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 64, "errors.Failed capture or match": 64, "vusers.failed": 64, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540110022, "firstHistogramAt": 1753540110252, "lastCounterAt": 1753540119962, "lastHistogramAt": 1753540119619, "firstMetricAt": 1753540110022, "lastMetricAt": 1753540119962, "period": "1753540110000", "summaries": {"http.response_time": {"min": 575, "max": 3521, "count": 64, "mean": 1233, "p50": 1064.4, "median": 1064.4, "p75": 1556.5, "p90": 2322.1, "p95": 3197.8, "p99": 3328.3, "p999": 3328.3}, "http.response_time.2xx": {"min": 575, "max": 3521, "count": 64, "mean": 1233, "p50": 1064.4, "median": 1064.4, "p75": 1556.5, "p90": 2322.1, "p95": 3197.8, "p99": 3328.3, "p999": 3328.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 575, "max": 3521, "count": 64, "mean": 1233, "p50": 1064.4, "median": 1064.4, "p75": 1556.5, "p90": 2322.1, "p95": 3197.8, "p99": 3328.3, "p999": 3328.3}}, "histograms": {"http.response_time": {"min": 575, "max": 3521, "count": 64, "mean": 1233, "p50": 1064.4, "median": 1064.4, "p75": 1556.5, "p90": 2322.1, "p95": 3197.8, "p99": 3328.3, "p999": 3328.3}, "http.response_time.2xx": {"min": 575, "max": 3521, "count": 64, "mean": 1233, "p50": 1064.4, "median": 1064.4, "p75": 1556.5, "p90": 2322.1, "p95": 3197.8, "p99": 3328.3, "p999": 3328.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 575, "max": 3521, "count": 64, "mean": 1233, "p50": 1064.4, "median": 1064.4, "p75": 1556.5, "p90": 2322.1, "p95": 3197.8, "p99": 3328.3, "p999": 3328.3}}}, {"counters": {"http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 461426, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 51, "errors.Failed capture or match": 51, "vusers.failed": 51, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540120022, "firstHistogramAt": 1753540120187, "lastCounterAt": 1753540129989, "lastHistogramAt": 1753540129989, "firstMetricAt": 1753540120022, "lastMetricAt": 1753540129989, "period": "1753540120000", "summaries": {"http.response_time": {"min": 581, "max": 1370, "count": 51, "mean": 685.8, "p50": 608, "median": 608, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 581, "max": 1370, "count": 51, "mean": 685.8, "p50": 608, "median": 608, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 581, "max": 1370, "count": 51, "mean": 685.8, "p50": 608, "median": 608, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}}, "histograms": {"http.response_time": {"min": 581, "max": 1370, "count": 51, "mean": 685.8, "p50": 608, "median": 608, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 581, "max": 1370, "count": 51, "mean": 685.8, "p50": 608, "median": 608, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 581, "max": 1370, "count": 51, "mean": 685.8, "p50": 608, "median": 608, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 452408, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 50, "errors.Failed capture or match": 50, "vusers.failed": 50, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540130022, "firstHistogramAt": 1753540130437, "lastCounterAt": 1753540139962, "lastHistogramAt": 1753540139700, "firstMetricAt": 1753540130022, "lastMetricAt": 1753540139962, "period": "1753540130000", "summaries": {"http.response_time": {"min": 581, "max": 2106, "count": 50, "mean": 873.3, "p50": 727.9, "median": 727.9, "p75": 1064.4, "p90": 1353.1, "p95": 1436.8, "p99": 1587.9, "p999": 1587.9}, "http.response_time.2xx": {"min": 581, "max": 2106, "count": 50, "mean": 873.3, "p50": 727.9, "median": 727.9, "p75": 1064.4, "p90": 1353.1, "p95": 1436.8, "p99": 1587.9, "p999": 1587.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 581, "max": 2106, "count": 50, "mean": 873.3, "p50": 727.9, "median": 727.9, "p75": 1064.4, "p90": 1353.1, "p95": 1436.8, "p99": 1587.9, "p999": 1587.9}}, "histograms": {"http.response_time": {"min": 581, "max": 2106, "count": 50, "mean": 873.3, "p50": 727.9, "median": 727.9, "p75": 1064.4, "p90": 1353.1, "p95": 1436.8, "p99": 1587.9, "p999": 1587.9}, "http.response_time.2xx": {"min": 581, "max": 2106, "count": 50, "mean": 873.3, "p50": 727.9, "median": 727.9, "p75": 1064.4, "p90": 1353.1, "p95": 1436.8, "p99": 1587.9, "p999": 1587.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 581, "max": 2106, "count": 50, "mean": 873.3, "p50": 727.9, "median": 727.9, "p75": 1064.4, "p90": 1353.1, "p95": 1436.8, "p99": 1587.9, "p999": 1587.9}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 452384, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 50, "errors.Failed capture or match": 50, "vusers.failed": 50, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540140022, "firstHistogramAt": 1753540140448, "lastCounterAt": 1753540149962, "lastHistogramAt": 1753540149707, "firstMetricAt": 1753540140022, "lastMetricAt": 1753540149962, "period": "1753540140000", "summaries": {"http.response_time": {"min": 574, "max": 1064, "count": 50, "mean": 615.1, "p50": 596, "median": 596, "p75": 608, "p90": 632.8, "p95": 645.6, "p99": 699.4, "p999": 699.4}, "http.response_time.2xx": {"min": 574, "max": 1064, "count": 50, "mean": 615.1, "p50": 596, "median": 596, "p75": 608, "p90": 632.8, "p95": 645.6, "p99": 699.4, "p999": 699.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 574, "max": 1064, "count": 50, "mean": 615.1, "p50": 596, "median": 596, "p75": 608, "p90": 632.8, "p95": 645.6, "p99": 699.4, "p999": 699.4}}, "histograms": {"http.response_time": {"min": 574, "max": 1064, "count": 50, "mean": 615.1, "p50": 596, "median": 596, "p75": 608, "p90": 632.8, "p95": 645.6, "p99": 699.4, "p999": 699.4}, "http.response_time.2xx": {"min": 574, "max": 1064, "count": 50, "mean": 615.1, "p50": 596, "median": 596, "p75": 608, "p90": 632.8, "p95": 645.6, "p99": 699.4, "p999": 699.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 574, "max": 1064, "count": 50, "mean": 615.1, "p50": 596, "median": 596, "p75": 608, "p90": 632.8, "p95": 645.6, "p99": 699.4, "p999": 699.4}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 452395, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 50, "errors.Failed capture or match": 50, "vusers.failed": 50, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540150022, "firstHistogramAt": 1753540150478, "lastCounterAt": 1753540159962, "lastHistogramAt": 1753540159676, "firstMetricAt": 1753540150022, "lastMetricAt": 1753540159962, "period": "1753540150000", "summaries": {"http.response_time": {"min": 576, "max": 1106, "count": 50, "mean": 685.3, "p50": 608, "median": 608, "p75": 620.3, "p90": 1022.7, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "http.response_time.2xx": {"min": 576, "max": 1106, "count": 50, "mean": 685.3, "p50": 608, "median": 608, "p75": 620.3, "p90": 1022.7, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 576, "max": 1106, "count": 50, "mean": 685.3, "p50": 608, "median": 608, "p75": 620.3, "p90": 1022.7, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}}, "histograms": {"http.response_time": {"min": 576, "max": 1106, "count": 50, "mean": 685.3, "p50": 608, "median": 608, "p75": 620.3, "p90": 1022.7, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "http.response_time.2xx": {"min": 576, "max": 1106, "count": 50, "mean": 685.3, "p50": 608, "median": 608, "p75": 620.3, "p90": 1022.7, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 576, "max": 1106, "count": 50, "mean": 685.3, "p50": 608, "median": 608, "p75": 620.3, "p90": 1022.7, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}}}, {"counters": {"http.codes.200": 76, "http.responses": 76, "http.downloaded_bytes": 687652, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 76, "errors.Failed capture or match": 76, "vusers.failed": 76, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 84, "vusers.created": 84, "errors.Undefined function \"generateBaselineFilters\"": 84, "http.requests": 84}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753540160022, "firstHistogramAt": 1753540160447, "lastCounterAt": 1753540169962, "lastHistogramAt": 1753540169941, "firstMetricAt": 1753540160022, "lastMetricAt": 1753540169962, "period": "1753540160000", "summaries": {"http.response_time": {"min": 589, "max": 1149, "count": 76, "mean": 681.5, "p50": 632.8, "median": 632.8, "p75": 727.9, "p90": 804.5, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 589, "max": 1149, "count": 76, "mean": 681.5, "p50": 632.8, "median": 632.8, "p75": 727.9, "p90": 804.5, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 589, "max": 1149, "count": 76, "mean": 681.5, "p50": 632.8, "median": 632.8, "p75": 727.9, "p90": 804.5, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}}, "histograms": {"http.response_time": {"min": 589, "max": 1149, "count": 76, "mean": 681.5, "p50": 632.8, "median": 632.8, "p75": 727.9, "p90": 804.5, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 589, "max": 1149, "count": 76, "mean": 681.5, "p50": 632.8, "median": 632.8, "p75": 727.9, "p90": 804.5, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 589, "max": 1149, "count": 76, "mean": 681.5, "p50": 632.8, "median": 632.8, "p75": 727.9, "p90": 804.5, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}}}, {"counters": {"http.codes.200": 132, "http.responses": 132, "http.downloaded_bytes": 1194344, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 132, "errors.Failed capture or match": 132, "vusers.failed": 132, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540170023, "firstHistogramAt": 1753540170135, "lastCounterAt": 1753540179962, "lastHistogramAt": 1753540179381, "firstMetricAt": 1753540170023, "lastMetricAt": 1753540179962, "period": "1753540170000", "summaries": {"http.response_time": {"min": 618, "max": 3295, "count": 132, "mean": 1474.2, "p50": 1556.5, "median": 1556.5, "p75": 1901.1, "p90": 2322.1, "p95": 2566.3, "p99": 3197.8, "p999": 3197.8}, "http.response_time.2xx": {"min": 618, "max": 3295, "count": 132, "mean": 1474.2, "p50": 1556.5, "median": 1556.5, "p75": 1901.1, "p90": 2322.1, "p95": 2566.3, "p99": 3197.8, "p999": 3197.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 618, "max": 3295, "count": 132, "mean": 1474.2, "p50": 1556.5, "median": 1556.5, "p75": 1901.1, "p90": 2322.1, "p95": 2566.3, "p99": 3197.8, "p999": 3197.8}}, "histograms": {"http.response_time": {"min": 618, "max": 3295, "count": 132, "mean": 1474.2, "p50": 1556.5, "median": 1556.5, "p75": 1901.1, "p90": 2322.1, "p95": 2566.3, "p99": 3197.8, "p999": 3197.8}, "http.response_time.2xx": {"min": 618, "max": 3295, "count": 132, "mean": 1474.2, "p50": 1556.5, "median": 1556.5, "p75": 1901.1, "p90": 2322.1, "p95": 2566.3, "p99": 3197.8, "p999": 3197.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 618, "max": 3295, "count": 132, "mean": 1474.2, "p50": 1556.5, "median": 1556.5, "p75": 1901.1, "p90": 2322.1, "p95": 2566.3, "p99": 3197.8, "p999": 3197.8}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 145, "http.responses": 145, "http.downloaded_bytes": 1311958, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 145, "errors.Failed capture or match": 145, "vusers.failed": 145}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540180022, "firstHistogramAt": 1753540180939, "lastCounterAt": 1753540189962, "lastHistogramAt": 1753540189482, "firstMetricAt": 1753540180022, "lastMetricAt": 1753540189962, "period": "1753540180000", "summaries": {"http.response_time": {"min": 1933, "max": 3914, "count": 145, "mean": 2611.9, "p50": 2618.1, "median": 2618.1, "p75": 2780, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3752.7}, "http.response_time.2xx": {"min": 1933, "max": 3914, "count": 145, "mean": 2611.9, "p50": 2618.1, "median": 2618.1, "p75": 2780, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3752.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1933, "max": 3914, "count": 145, "mean": 2611.9, "p50": 2618.1, "median": 2618.1, "p75": 2780, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3752.7}}, "histograms": {"http.response_time": {"min": 1933, "max": 3914, "count": 145, "mean": 2611.9, "p50": 2618.1, "median": 2618.1, "p75": 2780, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3752.7}, "http.response_time.2xx": {"min": 1933, "max": 3914, "count": 145, "mean": 2611.9, "p50": 2618.1, "median": 2618.1, "p75": 2780, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3752.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1933, "max": 3914, "count": 145, "mean": 2611.9, "p50": 2618.1, "median": 2618.1, "p75": 2780, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3752.7}}}, {"counters": {"http.codes.200": 119, "http.responses": 119, "http.downloaded_bytes": 1076706, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 119, "errors.Failed capture or match": 119, "vusers.failed": 119, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540190022, "firstHistogramAt": 1753540190112, "lastCounterAt": 1753540199993, "lastHistogramAt": 1753540199993, "firstMetricAt": 1753540190022, "lastMetricAt": 1753540199993, "period": "1753540190000", "summaries": {"http.response_time": {"min": 2064, "max": 4819, "count": 119, "mean": 3211.4, "p50": 3134.5, "median": 3134.5, "p75": 3464.1, "p90": 4492.8, "p95": 4583.6, "p99": 4583.6, "p999": 4676.2}, "http.response_time.2xx": {"min": 2064, "max": 4819, "count": 119, "mean": 3211.4, "p50": 3134.5, "median": 3134.5, "p75": 3464.1, "p90": 4492.8, "p95": 4583.6, "p99": 4583.6, "p999": 4676.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 2064, "max": 4819, "count": 119, "mean": 3211.4, "p50": 3134.5, "median": 3134.5, "p75": 3464.1, "p90": 4492.8, "p95": 4583.6, "p99": 4583.6, "p999": 4676.2}}, "histograms": {"http.response_time": {"min": 2064, "max": 4819, "count": 119, "mean": 3211.4, "p50": 3134.5, "median": 3134.5, "p75": 3464.1, "p90": 4492.8, "p95": 4583.6, "p99": 4583.6, "p999": 4676.2}, "http.response_time.2xx": {"min": 2064, "max": 4819, "count": 119, "mean": 3211.4, "p50": 3134.5, "median": 3134.5, "p75": 3464.1, "p90": 4492.8, "p95": 4583.6, "p99": 4583.6, "p999": 4676.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 2064, "max": 4819, "count": 119, "mean": 3211.4, "p50": 3134.5, "median": 3134.5, "p75": 3464.1, "p90": 4492.8, "p95": 4583.6, "p99": 4583.6, "p999": 4676.2}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 171, "http.responses": 171, "http.downloaded_bytes": 1547215, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 171, "errors.Failed capture or match": 171, "vusers.failed": 171}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540200022, "firstHistogramAt": 1753540200075, "lastCounterAt": 1753540209971, "lastHistogramAt": 1753540209970, "firstMetricAt": 1753540200022, "lastMetricAt": 1753540209971, "period": "1753540200000", "summaries": {"http.response_time": {"min": 2908, "max": 5484, "count": 171, "mean": 3910.4, "p50": 3984.7, "median": 3984.7, "p75": 4231.1, "p90": 4583.6, "p95": 4676.2, "p99": 5065.6, "p999": 5272.4}, "http.response_time.2xx": {"min": 2908, "max": 5484, "count": 171, "mean": 3910.4, "p50": 3984.7, "median": 3984.7, "p75": 4231.1, "p90": 4583.6, "p95": 4676.2, "p99": 5065.6, "p999": 5272.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 2908, "max": 5484, "count": 171, "mean": 3910.4, "p50": 3984.7, "median": 3984.7, "p75": 4231.1, "p90": 4583.6, "p95": 4676.2, "p99": 5065.6, "p999": 5272.4}}, "histograms": {"http.response_time": {"min": 2908, "max": 5484, "count": 171, "mean": 3910.4, "p50": 3984.7, "median": 3984.7, "p75": 4231.1, "p90": 4583.6, "p95": 4676.2, "p99": 5065.6, "p999": 5272.4}, "http.response_time.2xx": {"min": 2908, "max": 5484, "count": 171, "mean": 3910.4, "p50": 3984.7, "median": 3984.7, "p75": 4231.1, "p90": 4583.6, "p95": 4676.2, "p99": 5065.6, "p999": 5272.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 2908, "max": 5484, "count": 171, "mean": 3910.4, "p50": 3984.7, "median": 3984.7, "p75": 4231.1, "p90": 4583.6, "p95": 4676.2, "p99": 5065.6, "p999": 5272.4}}}, {"counters": {"http.codes.200": 165, "http.responses": 165, "http.downloaded_bytes": 1492920, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 165, "errors.Failed capture or match": 165, "vusers.failed": 165, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540210021, "firstHistogramAt": 1753540210021, "lastCounterAt": 1753540219963, "lastHistogramAt": 1753540219830, "firstMetricAt": 1753540210021, "lastMetricAt": 1753540219963, "period": "1753540210000", "summaries": {"http.response_time": {"min": 1805, "max": 3566, "count": 165, "mean": 2530.8, "p50": 2566.3, "median": 2566.3, "p75": 2836.2, "p90": 2951.9, "p95": 3072.4, "p99": 3328.3, "p999": 3395.5}, "http.response_time.2xx": {"min": 1805, "max": 3566, "count": 165, "mean": 2530.8, "p50": 2566.3, "median": 2566.3, "p75": 2836.2, "p90": 2951.9, "p95": 3072.4, "p99": 3328.3, "p999": 3395.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1805, "max": 3566, "count": 165, "mean": 2530.8, "p50": 2566.3, "median": 2566.3, "p75": 2836.2, "p90": 2951.9, "p95": 3072.4, "p99": 3328.3, "p999": 3395.5}}, "histograms": {"http.response_time": {"min": 1805, "max": 3566, "count": 165, "mean": 2530.8, "p50": 2566.3, "median": 2566.3, "p75": 2836.2, "p90": 2951.9, "p95": 3072.4, "p99": 3328.3, "p999": 3395.5}, "http.response_time.2xx": {"min": 1805, "max": 3566, "count": 165, "mean": 2530.8, "p50": 2566.3, "median": 2566.3, "p75": 2836.2, "p90": 2951.9, "p95": 3072.4, "p99": 3328.3, "p999": 3395.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1805, "max": 3566, "count": 165, "mean": 2530.8, "p50": 2566.3, "median": 2566.3, "p75": 2836.2, "p90": 2951.9, "p95": 3072.4, "p99": 3328.3, "p999": 3395.5}}}, {"counters": {"http.codes.200": 131, "http.responses": 131, "http.downloaded_bytes": 1185274, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 131, "errors.Failed capture or match": 131, "vusers.failed": 131, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540220022, "firstHistogramAt": 1753540220052, "lastCounterAt": 1753540229963, "lastHistogramAt": 1753540229730, "firstMetricAt": 1753540220022, "lastMetricAt": 1753540229963, "period": "1753540220000", "summaries": {"http.response_time": {"min": 1640, "max": 4329, "count": 131, "mean": 2706.5, "p50": 2893.5, "median": 2893.5, "p75": 3395.5, "p90": 3828.5, "p95": 3905.8, "p99": 3984.7, "p999": 4065.2}, "http.response_time.2xx": {"min": 1640, "max": 4329, "count": 131, "mean": 2706.5, "p50": 2893.5, "median": 2893.5, "p75": 3395.5, "p90": 3828.5, "p95": 3905.8, "p99": 3984.7, "p999": 4065.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1640, "max": 4329, "count": 131, "mean": 2706.5, "p50": 2893.5, "median": 2893.5, "p75": 3395.5, "p90": 3828.5, "p95": 3905.8, "p99": 3984.7, "p999": 4065.2}}, "histograms": {"http.response_time": {"min": 1640, "max": 4329, "count": 131, "mean": 2706.5, "p50": 2893.5, "median": 2893.5, "p75": 3395.5, "p90": 3828.5, "p95": 3905.8, "p99": 3984.7, "p999": 4065.2}, "http.response_time.2xx": {"min": 1640, "max": 4329, "count": 131, "mean": 2706.5, "p50": 2893.5, "median": 2893.5, "p75": 3395.5, "p90": 3828.5, "p95": 3905.8, "p99": 3984.7, "p999": 4065.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1640, "max": 4329, "count": 131, "mean": 2706.5, "p50": 2893.5, "median": 2893.5, "p75": 3395.5, "p90": 3828.5, "p95": 3905.8, "p99": 3984.7, "p999": 4065.2}}}, {"counters": {"http.codes.200": 162, "http.responses": 162, "http.downloaded_bytes": 1465821, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 162, "errors.Failed capture or match": 162, "vusers.failed": 162, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540230023, "firstHistogramAt": 1753540230090, "lastCounterAt": 1753540239988, "lastHistogramAt": 1753540239988, "firstMetricAt": 1753540230023, "lastMetricAt": 1753540239988, "period": "1753540230000", "summaries": {"http.response_time": {"min": 2257, "max": 3908, "count": 162, "mean": 2946.4, "p50": 2951.9, "median": 2951.9, "p75": 3134.5, "p90": 3328.3, "p95": 3395.5, "p99": 3534.1, "p999": 3605.5}, "http.response_time.2xx": {"min": 2257, "max": 3908, "count": 162, "mean": 2946.4, "p50": 2951.9, "median": 2951.9, "p75": 3134.5, "p90": 3328.3, "p95": 3395.5, "p99": 3534.1, "p999": 3605.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 2257, "max": 3908, "count": 162, "mean": 2946.4, "p50": 2951.9, "median": 2951.9, "p75": 3134.5, "p90": 3328.3, "p95": 3395.5, "p99": 3534.1, "p999": 3605.5}}, "histograms": {"http.response_time": {"min": 2257, "max": 3908, "count": 162, "mean": 2946.4, "p50": 2951.9, "median": 2951.9, "p75": 3134.5, "p90": 3328.3, "p95": 3395.5, "p99": 3534.1, "p999": 3605.5}, "http.response_time.2xx": {"min": 2257, "max": 3908, "count": 162, "mean": 2946.4, "p50": 2951.9, "median": 2951.9, "p75": 3134.5, "p90": 3328.3, "p95": 3395.5, "p99": 3534.1, "p999": 3605.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 2257, "max": 3908, "count": 162, "mean": 2946.4, "p50": 2951.9, "median": 2951.9, "p75": 3134.5, "p90": 3328.3, "p95": 3395.5, "p99": 3534.1, "p999": 3605.5}}}, {"counters": {"http.codes.200": 158, "http.responses": 158, "http.downloaded_bytes": 1429592, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 158, "errors.Failed capture or match": 158, "vusers.failed": 158, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540240000, "firstHistogramAt": 1753540240000, "lastCounterAt": 1753540249962, "lastHistogramAt": 1753540249435, "firstMetricAt": 1753540240000, "lastMetricAt": 1753540249962, "period": "1753540240000", "summaries": {"http.response_time": {"min": 1242, "max": 3307, "count": 158, "mean": 2007.5, "p50": 1939.5, "median": 1939.5, "p75": 2416.8, "p90": 2566.3, "p95": 2618.1, "p99": 2725, "p999": 2725}, "http.response_time.2xx": {"min": 1242, "max": 3307, "count": 158, "mean": 2007.5, "p50": 1939.5, "median": 1939.5, "p75": 2416.8, "p90": 2566.3, "p95": 2618.1, "p99": 2725, "p999": 2725}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1242, "max": 3307, "count": 158, "mean": 2007.5, "p50": 1939.5, "median": 1939.5, "p75": 2416.8, "p90": 2566.3, "p95": 2618.1, "p99": 2725, "p999": 2725}}, "histograms": {"http.response_time": {"min": 1242, "max": 3307, "count": 158, "mean": 2007.5, "p50": 1939.5, "median": 1939.5, "p75": 2416.8, "p90": 2566.3, "p95": 2618.1, "p99": 2725, "p999": 2725}, "http.response_time.2xx": {"min": 1242, "max": 3307, "count": 158, "mean": 2007.5, "p50": 1939.5, "median": 1939.5, "p75": 2416.8, "p90": 2566.3, "p95": 2618.1, "p99": 2725, "p999": 2725}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1242, "max": 3307, "count": 158, "mean": 2007.5, "p50": 1939.5, "median": 1939.5, "p75": 2416.8, "p90": 2566.3, "p95": 2618.1, "p99": 2725, "p999": 2725}}}, {"counters": {"http.codes.200": 126, "http.responses": 126, "http.downloaded_bytes": 1139998, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 125, "errors.Failed capture or match": 125, "vusers.failed": 125, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540250023, "firstHistogramAt": 1753540250379, "lastCounterAt": 1753540259999, "lastHistogramAt": 1753540259999, "firstMetricAt": 1753540250023, "lastMetricAt": 1753540259999, "period": "1753540250000", "summaries": {"http.response_time": {"min": 1992, "max": 3657, "count": 126, "mean": 2850.9, "p50": 2780, "median": 2780, "p75": 3197.8, "p90": 3395.5, "p95": 3534.1, "p99": 3605.5, "p999": 3678.4}, "http.response_time.2xx": {"min": 1992, "max": 3657, "count": 126, "mean": 2850.9, "p50": 2780, "median": 2780, "p75": 3197.8, "p90": 3395.5, "p95": 3534.1, "p99": 3605.5, "p999": 3678.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1992, "max": 3657, "count": 125, "mean": 2844.9, "p50": 2780, "median": 2780, "p75": 3197.8, "p90": 3395.5, "p95": 3534.1, "p99": 3605.5, "p999": 3678.4}}, "histograms": {"http.response_time": {"min": 1992, "max": 3657, "count": 126, "mean": 2850.9, "p50": 2780, "median": 2780, "p75": 3197.8, "p90": 3395.5, "p95": 3534.1, "p99": 3605.5, "p999": 3678.4}, "http.response_time.2xx": {"min": 1992, "max": 3657, "count": 126, "mean": 2850.9, "p50": 2780, "median": 2780, "p75": 3197.8, "p90": 3395.5, "p95": 3534.1, "p99": 3605.5, "p999": 3678.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 1992, "max": 3657, "count": 125, "mean": 2844.9, "p50": 2780, "median": 2780, "p75": 3197.8, "p90": 3395.5, "p95": 3534.1, "p99": 3605.5, "p999": 3678.4}}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 137, "errors.Failed capture or match": 137, "vusers.failed": 137, "http.codes.200": 136, "http.responses": 136, "http.downloaded_bytes": 1230540, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540260000, "firstHistogramAt": 1753540260000, "lastCounterAt": 1753540269963, "lastHistogramAt": 1753540269695, "firstMetricAt": 1753540260000, "lastMetricAt": 1753540269963, "period": "1753540260000", "summaries": {"plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 3000, "max": 4870, "count": 137, "mean": 3702.3, "p50": 3678.4, "median": 3678.4, "p75": 3905.8, "p90": 4316.6, "p95": 4403.8, "p99": 4770.6, "p999": 4770.6}, "http.response_time": {"min": 3000, "max": 4870, "count": 136, "mean": 3703, "p50": 3678.4, "median": 3678.4, "p75": 3905.8, "p90": 4316.6, "p95": 4403.8, "p99": 4770.6, "p999": 4770.6}, "http.response_time.2xx": {"min": 3000, "max": 4870, "count": 136, "mean": 3703, "p50": 3678.4, "median": 3678.4, "p75": 3905.8, "p90": 4316.6, "p95": 4403.8, "p99": 4770.6, "p999": 4770.6}}, "histograms": {"plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 3000, "max": 4870, "count": 137, "mean": 3702.3, "p50": 3678.4, "median": 3678.4, "p75": 3905.8, "p90": 4316.6, "p95": 4403.8, "p99": 4770.6, "p999": 4770.6}, "http.response_time": {"min": 3000, "max": 4870, "count": 136, "mean": 3703, "p50": 3678.4, "median": 3678.4, "p75": 3905.8, "p90": 4316.6, "p95": 4403.8, "p99": 4770.6, "p999": 4770.6}, "http.response_time.2xx": {"min": 3000, "max": 4870, "count": 136, "mean": 3703, "p50": 3678.4, "median": 3678.4, "p75": 3905.8, "p90": 4316.6, "p95": 4403.8, "p99": 4770.6, "p999": 4770.6}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 114, "http.responses": 114, "http.downloaded_bytes": 1031473, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 114, "errors.Failed capture or match": 114, "vusers.failed": 114}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540270023, "firstHistogramAt": 1753540270656, "lastCounterAt": 1753540279992, "lastHistogramAt": 1753540279992, "firstMetricAt": 1753540270023, "lastMetricAt": 1753540279992, "period": "1753540270000", "summaries": {"http.response_time": {"min": 5136, "max": 7452, "count": 114, "mean": 6120.9, "p50": 6187.2, "median": 6187.2, "p75": 6439.7, "p90": 6702.6, "p95": 6702.6, "p99": 6976.1, "p999": 6976.1}, "http.response_time.2xx": {"min": 5136, "max": 7452, "count": 114, "mean": 6120.9, "p50": 6187.2, "median": 6187.2, "p75": 6439.7, "p90": 6702.6, "p95": 6702.6, "p99": 6976.1, "p999": 6976.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 5136, "max": 7452, "count": 114, "mean": 6120.9, "p50": 6187.2, "median": 6187.2, "p75": 6439.7, "p90": 6702.6, "p95": 6702.6, "p99": 6976.1, "p999": 6976.1}}, "histograms": {"http.response_time": {"min": 5136, "max": 7452, "count": 114, "mean": 6120.9, "p50": 6187.2, "median": 6187.2, "p75": 6439.7, "p90": 6702.6, "p95": 6702.6, "p99": 6976.1, "p999": 6976.1}, "http.response_time.2xx": {"min": 5136, "max": 7452, "count": 114, "mean": 6120.9, "p50": 6187.2, "median": 6187.2, "p75": 6439.7, "p90": 6702.6, "p95": 6702.6, "p99": 6976.1, "p999": 6976.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 5136, "max": 7452, "count": 114, "mean": 6120.9, "p50": 6187.2, "median": 6187.2, "p75": 6439.7, "p90": 6702.6, "p95": 6702.6, "p99": 6976.1, "p999": 6976.1}}}, {"counters": {"http.codes.200": 102, "http.responses": 102, "http.downloaded_bytes": 922877, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 102, "errors.Failed capture or match": 102, "vusers.failed": 104, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 2, "errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540280016, "firstHistogramAt": 1753540280016, "lastCounterAt": 1753540289997, "lastHistogramAt": 1753540289997, "firstMetricAt": 1753540280016, "lastMetricAt": 1753540289997, "period": "1753540280000", "summaries": {"http.response_time": {"min": 6629, "max": 9971, "count": 102, "mean": 8605.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9801.2, "p99": 9999.2, "p999": 9999.2}, "http.response_time.2xx": {"min": 6629, "max": 9971, "count": 102, "mean": 8605.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9801.2, "p99": 9999.2, "p999": 9999.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 6629, "max": 9971, "count": 102, "mean": 8605.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9801.2, "p99": 9999.2, "p999": 9999.2}}, "histograms": {"http.response_time": {"min": 6629, "max": 9971, "count": 102, "mean": 8605.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9801.2, "p99": 9999.2, "p999": 9999.2}, "http.response_time.2xx": {"min": 6629, "max": 9971, "count": 102, "mean": 8605.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9801.2, "p99": 9999.2, "p999": 9999.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 6629, "max": 9971, "count": 102, "mean": 8605.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9801.2, "p99": 9999.2, "p999": 9999.2}}}, {"counters": {"http.codes.200": 49, "http.responses": 49, "http.downloaded_bytes": 443353, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 49, "errors.Failed capture or match": 49, "vusers.failed": 148, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 99, "errors.ETIMEDOUT": 99}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540290022, "firstHistogramAt": 1753540290148, "lastCounterAt": 1753540299975, "lastHistogramAt": 1753540297263, "firstMetricAt": 1753540290022, "lastMetricAt": 1753540299975, "period": "1753540290000", "summaries": {"http.response_time": {"min": 8892, "max": 9987, "count": 49, "mean": 9650.1, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9999.2, "p95": 9999.2, "p99": 9999.2, "p999": 9999.2}, "http.response_time.2xx": {"min": 8892, "max": 9987, "count": 49, "mean": 9650.1, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9999.2, "p95": 9999.2, "p99": 9999.2, "p999": 9999.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 8892, "max": 9987, "count": 49, "mean": 9650.1, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9999.2, "p95": 9999.2, "p99": 9999.2, "p999": 9999.2}}, "histograms": {"http.response_time": {"min": 8892, "max": 9987, "count": 49, "mean": 9650.1, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9999.2, "p95": 9999.2, "p99": 9999.2, "p999": 9999.2}, "http.response_time.2xx": {"min": 8892, "max": 9987, "count": 49, "mean": 9650.1, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9999.2, "p95": 9999.2, "p99": 9999.2, "p999": 9999.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 8892, "max": 9987, "count": 49, "mean": 9650.1, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9999.2, "p95": 9999.2, "p99": 9999.2, "p999": 9999.2}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540300022, "lastCounterAt": 1753540309963, "firstMetricAt": 1753540300022, "lastMetricAt": 1753540309963, "period": "1753540300000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540310023, "lastCounterAt": 1753540319974, "firstMetricAt": 1753540310023, "lastMetricAt": 1753540319974, "period": "1753540310000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540320022, "lastCounterAt": 1753540329963, "firstMetricAt": 1753540320022, "lastMetricAt": 1753540329963, "period": "1753540320000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540330022, "lastCounterAt": 1753540339963, "firstMetricAt": 1753540330022, "lastMetricAt": 1753540339963, "period": "1753540330000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 116, "vusers.created": 116, "errors.Undefined function \"generateBaselineFilters\"": 116, "http.requests": 116, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753540340022, "lastCounterAt": 1753540349964, "firstMetricAt": 1753540340022, "lastMetricAt": 1753540349964, "period": "1753540340000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 116, "errors.ETIMEDOUT": 116, "vusers.failed": 116, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540350023, "lastCounterAt": 1753540359964, "firstMetricAt": 1753540350023, "lastMetricAt": 1753540359964, "period": "1753540350000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540360023, "lastCounterAt": 1753540369964, "firstMetricAt": 1753540360023, "lastMetricAt": 1753540369964, "period": "1753540360000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).errors.ETIMEDOUT": 12, "errors.ETIMEDOUT": 12, "vusers.failed": 97, "http.codes.200": 85, "http.responses": 85, "http.downloaded_bytes": 769083, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 85, "errors.Failed capture or match": 85}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540370023, "firstHistogramAt": 1753540372571, "lastCounterAt": 1753540379964, "lastHistogramAt": 1753540379782, "firstMetricAt": 1753540370023, "lastMetricAt": 1753540379964, "period": "1753540370000", "summaries": {"http.response_time": {"min": 605, "max": 9735, "count": 85, "mean": 3963.7, "p50": 3197.8, "median": 3197.8, "p75": 6439.7, "p90": 8692.8, "p95": 8868.4, "p99": 9607.1, "p999": 9607.1}, "http.response_time.2xx": {"min": 605, "max": 9735, "count": 85, "mean": 3963.7, "p50": 3197.8, "median": 3197.8, "p75": 6439.7, "p90": 8692.8, "p95": 8868.4, "p99": 9607.1, "p999": 9607.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 605, "max": 9735, "count": 85, "mean": 3963.7, "p50": 3197.8, "median": 3197.8, "p75": 6439.7, "p90": 8692.8, "p95": 8868.4, "p99": 9607.1, "p999": 9607.1}}, "histograms": {"http.response_time": {"min": 605, "max": 9735, "count": 85, "mean": 3963.7, "p50": 3197.8, "median": 3197.8, "p75": 6439.7, "p90": 8692.8, "p95": 8868.4, "p99": 9607.1, "p999": 9607.1}, "http.response_time.2xx": {"min": 605, "max": 9735, "count": 85, "mean": 3963.7, "p50": 3197.8, "median": 3197.8, "p75": 6439.7, "p90": 8692.8, "p95": 8868.4, "p99": 9607.1, "p999": 9607.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 605, "max": 9735, "count": 85, "mean": 3963.7, "p50": 3197.8, "median": 3197.8, "p75": 6439.7, "p90": 8692.8, "p95": 8868.4, "p99": 9607.1, "p999": 9607.1}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 452430, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 50, "errors.Failed capture or match": 50, "vusers.failed": 50, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540380023, "firstHistogramAt": 1753540380459, "lastCounterAt": 1753540389963, "lastHistogramAt": 1753540389744, "firstMetricAt": 1753540380023, "lastMetricAt": 1753540389963, "period": "1753540380000", "summaries": {"http.response_time": {"min": 605, "max": 952, "count": 50, "mean": 668.9, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 757.6, "p95": 804.5, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 605, "max": 952, "count": 50, "mean": 668.9, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 757.6, "p95": 804.5, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 605, "max": 952, "count": 50, "mean": 668.9, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 757.6, "p95": 804.5, "p99": 871.5, "p999": 871.5}}, "histograms": {"http.response_time": {"min": 605, "max": 952, "count": 50, "mean": 668.9, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 757.6, "p95": 804.5, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 605, "max": 952, "count": 50, "mean": 668.9, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 757.6, "p95": 804.5, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 605, "max": 952, "count": 50, "mean": 668.9, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 757.6, "p95": 804.5, "p99": 871.5, "p999": 871.5}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 452387, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 50, "errors.Failed capture or match": 50, "vusers.failed": 50, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540390023, "firstHistogramAt": 1753540390455, "lastCounterAt": 1753540399963, "lastHistogramAt": 1753540399749, "firstMetricAt": 1753540390023, "lastMetricAt": 1753540399963, "period": "1753540390000", "summaries": {"http.response_time": {"min": 600, "max": 713, "count": 50, "mean": 646.4, "p50": 645.6, "median": 645.6, "p75": 658.6, "p90": 671.9, "p95": 685.5, "p99": 699.4, "p999": 699.4}, "http.response_time.2xx": {"min": 600, "max": 713, "count": 50, "mean": 646.4, "p50": 645.6, "median": 645.6, "p75": 658.6, "p90": 671.9, "p95": 685.5, "p99": 699.4, "p999": 699.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 600, "max": 713, "count": 50, "mean": 646.4, "p50": 645.6, "median": 645.6, "p75": 658.6, "p90": 671.9, "p95": 685.5, "p99": 699.4, "p999": 699.4}}, "histograms": {"http.response_time": {"min": 600, "max": 713, "count": 50, "mean": 646.4, "p50": 645.6, "median": 645.6, "p75": 658.6, "p90": 671.9, "p95": 685.5, "p99": 699.4, "p999": 699.4}, "http.response_time.2xx": {"min": 600, "max": 713, "count": 50, "mean": 646.4, "p50": 645.6, "median": 645.6, "p75": 658.6, "p90": 671.9, "p95": 685.5, "p99": 699.4, "p999": 699.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 600, "max": 713, "count": 50, "mean": 646.4, "p50": 645.6, "median": 645.6, "p75": 658.6, "p90": 671.9, "p95": 685.5, "p99": 699.4, "p999": 699.4}}}, {"counters": {"http.codes.200": 35, "http.responses": 35, "http.downloaded_bytes": 316696, "plugins.metrics-by-endpoint.GET /api/mugshots - Baseline (No Filters).codes.200": 35, "errors.Failed capture or match": 35, "vusers.failed": 35, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 32, "vusers.created": 32, "errors.Undefined function \"generateBaselineFilters\"": 32, "http.requests": 32}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753540400023, "firstHistogramAt": 1753540400459, "lastCounterAt": 1753540406727, "lastHistogramAt": 1753540406727, "firstMetricAt": 1753540400023, "lastMetricAt": 1753540406727, "period": "1753540400000", "summaries": {"http.response_time": {"min": 613, "max": 685, "count": 35, "mean": 636.4, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 658.6, "p95": 658.6, "p99": 658.6, "p999": 658.6}, "http.response_time.2xx": {"min": 613, "max": 685, "count": 35, "mean": 636.4, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 658.6, "p95": 658.6, "p99": 658.6, "p999": 658.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 613, "max": 685, "count": 35, "mean": 636.4, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 658.6, "p95": 658.6, "p99": 658.6, "p999": 658.6}}, "histograms": {"http.response_time": {"min": 613, "max": 685, "count": 35, "mean": 636.4, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 658.6, "p95": 658.6, "p99": 658.6, "p999": 658.6}, "http.response_time.2xx": {"min": 613, "max": 685, "count": 35, "mean": 636.4, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 658.6, "p95": 658.6, "p99": 658.6, "p999": 658.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Baseline (No Filters)": {"min": 613, "max": 685, "count": 35, "mean": 636.4, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 658.6, "p95": 658.6, "p99": 658.6, "p999": 658.6}}}]}