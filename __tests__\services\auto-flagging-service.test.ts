import { describe, it, expect, beforeEach } from 'vitest'
import { AutoFlaggingService, FlaggingRule, ContentToAnalyze } from '@/lib/services/auto-flagging-service'

describe('AutoFlaggingService', () => {
  let service: AutoFlaggingService
  
  const mockRules: FlaggingRule[] = [
    {
      id: 'test_explicit',
      rule_name: 'Test Explicit Content',
      content_type: 'mugshot',
      rule_type: 'keyword',
      rule_config: {
        keywords: ['explicit', 'inappropriate', 'nsfw'],
        threshold: 0.8,
        severity_weights: {
          'explicit': 1.0,
          'inappropriate': 0.9,
          'nsfw': 1.0
        }
      },
      priority: 'high',
      is_active: true
    },
    {
      id: 'test_harassment',
      rule_name: 'Test Harassment Detection',
      content_type: 'user_comment',
      rule_type: 'keyword',
      rule_config: {
        keywords: ['harassment', 'abuse', 'threats'],
        threshold: 0.9,
        severity_weights: {
          'harassment': 0.9,
          'abuse': 0.9,
          'threats': 1.0
        }
      },
      priority: 'urgent',
      is_active: true
    },
    {
      id: 'test_inactive',
      rule_name: 'Test Inactive Rule',
      content_type: 'user_tag',
      rule_type: 'keyword',
      rule_config: {
        keywords: ['spam'],
        threshold: 0.7
      },
      priority: 'medium',
      is_active: false
    }
  ]

  beforeEach(() => {
    service = new AutoFlaggingService(mockRules)
  })

  describe('Constructor and initialization', () => {
    it('should initialize with custom rules', () => {
      const activeRules = service.getActiveRules()
      expect(activeRules).toHaveLength(2) // Only active rules
      expect(activeRules.every(rule => rule.is_active)).toBe(true)
    })

    it('should initialize with default rules when no custom rules provided', () => {
      const defaultService = new AutoFlaggingService()
      const rules = defaultService.getActiveRules()
      expect(rules.length).toBeGreaterThan(0)
    })
  })

  describe('analyzeContent', () => {
    it('should flag content with explicit keywords', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_1',
        text_content: 'This is explicit content that should be flagged'
      }

      const result = await service.analyzeContent(content)

      expect(result.should_flag).toBe(true)
      expect(result.confidence_score).toBeGreaterThan(0.8)
      expect(result.severity).toBe('high')
      expect(result.matched_rules).toContain('test_explicit')
      expect(result.reasons).toContain(expect.stringContaining('explicit'))
    })

    it('should flag urgent content for immediate removal', async () => {
      const content: ContentToAnalyze = {
        content_type: 'user_comment',
        content_id: 'test_2',
        text_content: 'This contains threats and harassment'
      }

      const result = await service.analyzeContent(content)

      expect(result.should_flag).toBe(true)
      expect(result.severity).toBe('urgent')
      expect(result.auto_action).toBe('quarantine')
      expect(result.matched_rules).toContain('test_harassment')
    })

    it('should not flag content without matching keywords', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_3',
        text_content: 'This is perfectly normal content'
      }

      const result = await service.analyzeContent(content)

      expect(result.should_flag).toBe(false)
      expect(result.confidence_score).toBe(0)
      expect(result.matched_rules).toHaveLength(0)
    })

    it('should not apply inactive rules', async () => {
      const content: ContentToAnalyze = {
        content_type: 'user_tag',
        content_id: 'test_4',
        text_content: 'spam content here'
      }

      const result = await service.analyzeContent(content)

      expect(result.should_flag).toBe(false)
      expect(result.matched_rules).not.toContain('test_inactive')
    })

    it('should only apply rules for matching content type', async () => {
      const content: ContentToAnalyze = {
        content_type: 'user_profile',
        content_id: 'test_5',
        text_content: 'explicit inappropriate content'
      }

      const result = await service.analyzeContent(content)

      expect(result.should_flag).toBe(false) // No rules for user_profile in mock data
      expect(result.matched_rules).toHaveLength(0)
    })

    it('should determine correct auto action based on confidence and severity', async () => {
      // High confidence + urgent severity should trigger remove action
      const urgentContent: ContentToAnalyze = {
        content_type: 'user_comment',
        content_id: 'test_urgent',
        text_content: 'threats and harassment with high confidence'
      }

      const urgentResult = await service.analyzeContent(urgentContent)
      expect(urgentResult.auto_action).toBe('quarantine') // Based on current thresholds

      // Lower confidence should trigger review action
      const reviewContent: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_review',
        text_content: 'inappropriate content' // Lower weight than "explicit"
      }

      const reviewResult = await service.analyzeContent(reviewContent)
      if (reviewResult.should_flag) {
        expect(['review', 'quarantine']).toContain(reviewResult.auto_action)
      }
    })
  })

  describe('Rule management', () => {
    it('should add new rules', () => {
      const newRule: FlaggingRule = {
        id: 'new_rule',
        rule_name: 'New Test Rule',
        content_type: 'user_tag',
        rule_type: 'keyword',
        rule_config: { keywords: ['test'], threshold: 0.5 },
        priority: 'low',
        is_active: true
      }

      service.addRule(newRule)
      const rules = service.getActiveRules()
      expect(rules).toContain(newRule)
    })

    it('should update existing rules', () => {
      const updated = service.updateRule('test_explicit', {
        priority: 'urgent',
        is_active: false
      })

      expect(updated).toBe(true)
      const activeRules = service.getActiveRules()
      expect(activeRules.find(r => r.id === 'test_explicit')).toBeUndefined()
    })

    it('should return false when updating non-existent rule', () => {
      const updated = service.updateRule('non_existent', { priority: 'high' })
      expect(updated).toBe(false)
    })

    it('should remove rules', () => {
      const removed = service.removeRule('test_explicit')
      expect(removed).toBe(true)
      
      const rules = service.getActiveRules()
      expect(rules.find(r => r.id === 'test_explicit')).toBeUndefined()
    })

    it('should return false when removing non-existent rule', () => {
      const removed = service.removeRule('non_existent')
      expect(removed).toBe(false)
    })

    it('should get rules by content type', () => {
      const mugshotRules = service.getRulesByContentType('mugshot')
      expect(mugshotRules).toHaveLength(1)
      expect(mugshotRules[0].id).toBe('test_explicit')

      const commentRules = service.getRulesByContentType('user_comment')
      expect(commentRules).toHaveLength(1)
      expect(commentRules[0].id).toBe('test_harassment')
    })
  })

  describe('Keyword rule application', () => {
    it('should handle case-insensitive matching', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_case',
        text_content: 'This contains EXPLICIT content in UPPERCASE'
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(true)
    })

    it('should apply severity weights correctly', async () => {
      const highWeightContent: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_weight_high',
        text_content: 'explicit content' // Weight 1.0
      }

      const lowWeightContent: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_weight_low',
        text_content: 'inappropriate content' // Weight 0.9
      }

      const highResult = await service.analyzeContent(highWeightContent)
      const lowResult = await service.analyzeContent(lowWeightContent)

      expect(highResult.confidence_score).toBeGreaterThan(lowResult.confidence_score)
    })

    it('should respect threshold settings', async () => {
      // Add a rule with high threshold
      const highThresholdRule: FlaggingRule = {
        id: 'high_threshold',
        rule_name: 'High Threshold Rule',
        content_type: 'user_comment',
        rule_type: 'keyword',
        rule_config: {
          keywords: ['mild'],
          threshold: 0.95,
          severity_weights: { 'mild': 0.5 }
        },
        priority: 'low',
        is_active: true
      }

      service.addRule(highThresholdRule)

      const content: ContentToAnalyze = {
        content_type: 'user_comment',
        content_id: 'test_threshold',
        text_content: 'mild content'
      }

      const result = await service.analyzeContent(content)
      
      // Should not flag because confidence (0.5) is below threshold (0.95)
      const highThresholdMatch = result.matched_rules.includes('high_threshold')
      expect(highThresholdMatch).toBe(false)
    })
  })

  describe('Pattern matching', () => {
    beforeEach(() => {
      const patternRule: FlaggingRule = {
        id: 'pattern_test',
        rule_name: 'Pattern Test Rule',
        content_type: 'user_comment',
        rule_type: 'keyword',
        rule_config: {
          keywords: [],
          patterns: [
            /\b\d{3}-\d{3}-\d{4}\b/, // Phone number pattern
            /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/ // Email pattern
          ],
          threshold: 0.8
        },
        priority: 'high',
        is_active: true
      }

      service.addRule(patternRule)
    })

    it('should detect phone number patterns', async () => {
      const content: ContentToAnalyze = {
        content_type: 'user_comment',
        content_id: 'test_phone',
        text_content: 'Call me at ************ for more info'
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(true)
      expect(result.matched_rules).toContain('pattern_test')
    })

    it('should detect email patterns', async () => {
      const content: ContentToAnalyze = {
        content_type: 'user_comment',
        content_id: 'test_email',
        text_content: 'Contact <NAME_EMAIL>'
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(true)
      expect(result.matched_rules).toContain('pattern_test')
    })
  })

  describe('Multiple rule interactions', () => {
    it('should use highest confidence score when multiple rules match', async () => {
      // Add another rule for the same content type with different weight
      const additionalRule: FlaggingRule = {
        id: 'additional_test',
        rule_name: 'Additional Test Rule',
        content_type: 'mugshot',
        rule_type: 'keyword',
        rule_config: {
          keywords: ['inappropriate'],
          threshold: 0.6,
          severity_weights: { 'inappropriate': 0.7 }
        },
        priority: 'medium',
        is_active: true
      }

      service.addRule(additionalRule)

      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_multiple',
        text_content: 'This is explicit and inappropriate content'
      }

      const result = await service.analyzeContent(content)
      
      expect(result.should_flag).toBe(true)
      expect(result.matched_rules).toContain('test_explicit')
      expect(result.matched_rules).toContain('additional_test')
      // Should use highest confidence (explicit = 1.0 > inappropriate = 0.7)
      expect(result.confidence_score).toBe(1.0)
      // Should use highest severity (high > medium)
      expect(result.severity).toBe('high')
    })

    it('should combine reasons from multiple matching rules', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_reasons',
        text_content: 'explicit inappropriate content'
      }

      const result = await service.analyzeContent(content)
      
      if (result.matched_rules.length > 1) {
        expect(result.reasons.length).toBeGreaterThan(1)
      }
    })
  })

  describe('Edge cases', () => {
    it('should handle empty content gracefully', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_empty',
        text_content: ''
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(false)
      expect(result.confidence_score).toBe(0)
    })

    it('should handle content with only whitespace', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_whitespace',
        text_content: '   \n\t   '
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(false)
    })

    it('should handle undefined text content', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_undefined'
        // text_content is undefined
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(false)
    })

    it('should handle content with special characters', async () => {
      const content: ContentToAnalyze = {
        content_type: 'mugshot',
        content_id: 'test_special',
        text_content: 'This has explicit!@#$%^&*() content with symbols'
      }

      const result = await service.analyzeContent(content)
      expect(result.should_flag).toBe(true) // Should still detect "explicit"
    })
  })
}) 