# Baseline Performance Test - No Filters, Newest Sort Only
# This tests the absolute fastest query path in your native service
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm-up: Baseline queries"
    - duration: 180
      arrivalRate: 15
      name: "Steady: Baseline performance test"
    - duration: 60
      arrivalRate: 5
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 800    # Should be very fast
    - http.response_time.median: 200 # Should be very fast
    - http.codes.200: 98             # High success rate expected
    - http.codes.5xx: 1              # Minimal server errors

  http:
    timeout: 10
    pool: 20
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Baseline - No Filters (Fastest Path)"
    weight: 100
    flow:
      - function: "generateBaselineFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "baseline"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
          name: "GET /api/mugshots-fast - Baseline (Ultra Fast)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 1

processor: "./load-tests/focused-tests/data-generators-focused.js"
