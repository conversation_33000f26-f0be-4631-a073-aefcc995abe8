{"aggregate": {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 5760, "vusers.created": 5760, "errors.Undefined function \"generateFastQueryFilters\"": 5760, "http.requests": 5760, "http.codes.200": 677, "http.responses": 677, "http.downloaded_bytes": 2848165, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 677, "vusers.failed": 5083, "vusers.completed": 677, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 5083, "errors.ETIMEDOUT": 5083}, "rates": {"http.request_rate": 10}, "firstCounterAt": 1753661165052, "firstHistogramAt": 1753661165965, "lastCounterAt": 1753661539465, "lastHistogramAt": 1753661248271, "firstMetricAt": 1753661165052, "lastMetricAt": 1753661539465, "period": 1753661530000, "summaries": {"http.response_time": {"min": 343, "max": 14504, "count": 677, "mean": 2871.4, "p50": 487.9, "median": 487.9, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}, "http.response_time.2xx": {"min": 343, "max": 14504, "count": 677, "mean": 2871.4, "p50": 487.9, "median": 487.9, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 343, "max": 14504, "count": 677, "mean": 2871.4, "p50": 487.9, "median": 487.9, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}, "vusers.session_length": {"min": 351.3, "max": 14513.2, "count": 677, "mean": 2882.4, "p50": 497.8, "median": 497.8, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}}, "histograms": {"http.response_time": {"min": 343, "max": 14504, "count": 677, "mean": 2871.4, "p50": 487.9, "median": 487.9, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}, "http.response_time.2xx": {"min": 343, "max": 14504, "count": 677, "mean": 2871.4, "p50": 487.9, "median": 487.9, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 343, "max": 14504, "count": 677, "mean": 2871.4, "p50": 487.9, "median": 487.9, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}, "vusers.session_length": {"min": 351.3, "max": 14513.2, "count": 677, "mean": 2882.4, "p50": 497.8, "median": 497.8, "p75": 1720.2, "p90": 12213.1, "p95": 13230.3, "p99": 14332.3, "p999": 14332.3}}}, "intermediate": [{"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 40, "vusers.created": 40, "errors.Undefined function \"generateFastQueryFilters\"": 40, "http.requests": 40, "http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 168280, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661165052, "firstHistogramAt": 1753661165965, "lastCounterAt": 1753661169863, "lastHistogramAt": 1753661169863, "firstMetricAt": 1753661165052, "lastMetricAt": 1753661169863, "period": "1753661160000", "summaries": {"http.response_time": {"min": 351, "max": 914, "count": 40, "mean": 495.1, "p50": 399.5, "median": 399.5, "p75": 415.8, "p90": 889.1, "p95": 907, "p99": 907, "p999": 907}, "http.response_time.2xx": {"min": 351, "max": 914, "count": 40, "mean": 495.1, "p50": 399.5, "median": 399.5, "p75": 415.8, "p90": 889.1, "p95": 907, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 351, "max": 914, "count": 40, "mean": 495.1, "p50": 399.5, "median": 399.5, "p75": 415.8, "p90": 889.1, "p95": 907, "p99": 907, "p999": 907}, "vusers.session_length": {"min": 368.7, "max": 969.1, "count": 40, "mean": 514.3, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 944, "p95": 963.1, "p99": 963.1, "p999": 963.1}}, "histograms": {"http.response_time": {"min": 351, "max": 914, "count": 40, "mean": 495.1, "p50": 399.5, "median": 399.5, "p75": 415.8, "p90": 889.1, "p95": 907, "p99": 907, "p999": 907}, "http.response_time.2xx": {"min": 351, "max": 914, "count": 40, "mean": 495.1, "p50": 399.5, "median": 399.5, "p75": 415.8, "p90": 889.1, "p95": 907, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 351, "max": 914, "count": 40, "mean": 495.1, "p50": 399.5, "median": 399.5, "p75": 415.8, "p90": 889.1, "p95": 907, "p99": 907, "p999": 907}, "vusers.session_length": {"min": 368.7, "max": 969.1, "count": 40, "mean": 514.3, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 944, "p95": 963.1, "p99": 963.1, "p999": 963.1}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "http.codes.200": 80, "http.responses": 80, "http.downloaded_bytes": 336560, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 80, "vusers.failed": 0, "vusers.completed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661170050, "firstHistogramAt": 1753661170635, "lastCounterAt": 1753661179883, "lastHistogramAt": 1753661179883, "firstMetricAt": 1753661170050, "lastMetricAt": 1753661179883, "period": "1753661170000", "summaries": {"http.response_time": {"min": 362, "max": 1020, "count": 80, "mean": 519.3, "p50": 441.5, "median": 441.5, "p75": 518.1, "p90": 837.3, "p95": 889.1, "p99": 982.6, "p999": 982.6}, "http.response_time.2xx": {"min": 362, "max": 1020, "count": 80, "mean": 519.3, "p50": 441.5, "median": 441.5, "p75": 518.1, "p90": 837.3, "p95": 889.1, "p99": 982.6, "p999": 982.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 362, "max": 1020, "count": 80, "mean": 519.3, "p50": 441.5, "median": 441.5, "p75": 518.1, "p90": 837.3, "p95": 889.1, "p99": 982.6, "p999": 982.6}, "vusers.session_length": {"min": 368.5, "max": 1025.2, "count": 80, "mean": 529.1, "p50": 450.4, "median": 450.4, "p75": 539.2, "p90": 854.2, "p95": 907, "p99": 982.6, "p999": 982.6}}, "histograms": {"http.response_time": {"min": 362, "max": 1020, "count": 80, "mean": 519.3, "p50": 441.5, "median": 441.5, "p75": 518.1, "p90": 837.3, "p95": 889.1, "p99": 982.6, "p999": 982.6}, "http.response_time.2xx": {"min": 362, "max": 1020, "count": 80, "mean": 519.3, "p50": 441.5, "median": 441.5, "p75": 518.1, "p90": 837.3, "p95": 889.1, "p99": 982.6, "p999": 982.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 362, "max": 1020, "count": 80, "mean": 519.3, "p50": 441.5, "median": 441.5, "p75": 518.1, "p90": 837.3, "p95": 889.1, "p99": 982.6, "p999": 982.6}, "vusers.session_length": {"min": 368.5, "max": 1025.2, "count": 80, "mean": 529.1, "p50": 450.4, "median": 450.4, "p75": 539.2, "p90": 854.2, "p95": 907, "p99": 982.6, "p999": 982.6}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "http.codes.200": 77, "http.responses": 77, "http.downloaded_bytes": 323939, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 77, "vusers.failed": 0, "vusers.completed": 77}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661180050, "firstHistogramAt": 1753661180397, "lastCounterAt": 1753661189945, "lastHistogramAt": 1753661189945, "firstMetricAt": 1753661180050, "lastMetricAt": 1753661189945, "period": "1753661180000", "summaries": {"http.response_time": {"min": 344, "max": 1358, "count": 77, "mean": 481.7, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 685.5, "p95": 889.1, "p99": 1249.1, "p999": 1249.1}, "http.response_time.2xx": {"min": 344, "max": 1358, "count": 77, "mean": 481.7, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 685.5, "p95": 889.1, "p99": 1249.1, "p999": 1249.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 344, "max": 1358, "count": 77, "mean": 481.7, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 685.5, "p95": 889.1, "p99": 1249.1, "p999": 1249.1}, "vusers.session_length": {"min": 351.3, "max": 1360.7, "count": 77, "mean": 491.1, "p50": 415.8, "median": 415.8, "p75": 468.8, "p90": 699.4, "p95": 889.1, "p99": 1274.3, "p999": 1274.3}}, "histograms": {"http.response_time": {"min": 344, "max": 1358, "count": 77, "mean": 481.7, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 685.5, "p95": 889.1, "p99": 1249.1, "p999": 1249.1}, "http.response_time.2xx": {"min": 344, "max": 1358, "count": 77, "mean": 481.7, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 685.5, "p95": 889.1, "p99": 1249.1, "p999": 1249.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 344, "max": 1358, "count": 77, "mean": 481.7, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 685.5, "p95": 889.1, "p99": 1249.1, "p999": 1249.1}, "vusers.session_length": {"min": 351.3, "max": 1360.7, "count": 77, "mean": 491.1, "p50": 415.8, "median": 415.8, "p75": 468.8, "p90": 699.4, "p95": 889.1, "p99": 1274.3, "p999": 1274.3}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "http.codes.200": 83, "http.responses": 83, "http.downloaded_bytes": 349183, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 83, "vusers.failed": 0, "vusers.completed": 83}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661190010, "firstHistogramAt": 1753661190010, "lastCounterAt": 1753661199923, "lastHistogramAt": 1753661199923, "firstMetricAt": 1753661190010, "lastMetricAt": 1753661199923, "period": "1753661190000", "summaries": {"http.response_time": {"min": 346, "max": 1429, "count": 83, "mean": 520.1, "p50": 459.5, "median": 459.5, "p75": 518.1, "p90": 671.9, "p95": 907, "p99": 1326.4, "p999": 1326.4}, "http.response_time.2xx": {"min": 346, "max": 1429, "count": 83, "mean": 520.1, "p50": 459.5, "median": 459.5, "p75": 518.1, "p90": 671.9, "p95": 907, "p99": 1326.4, "p999": 1326.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 346, "max": 1429, "count": 83, "mean": 520.1, "p50": 459.5, "median": 459.5, "p75": 518.1, "p90": 671.9, "p95": 907, "p99": 1326.4, "p999": 1326.4}, "vusers.session_length": {"min": 353.1, "max": 1437.8, "count": 83, "mean": 529.9, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 671.9, "p95": 925.4, "p99": 1353.1, "p999": 1353.1}}, "histograms": {"http.response_time": {"min": 346, "max": 1429, "count": 83, "mean": 520.1, "p50": 459.5, "median": 459.5, "p75": 518.1, "p90": 671.9, "p95": 907, "p99": 1326.4, "p999": 1326.4}, "http.response_time.2xx": {"min": 346, "max": 1429, "count": 83, "mean": 520.1, "p50": 459.5, "median": 459.5, "p75": 518.1, "p90": 671.9, "p95": 907, "p99": 1326.4, "p999": 1326.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 346, "max": 1429, "count": 83, "mean": 520.1, "p50": 459.5, "median": 459.5, "p75": 518.1, "p90": 671.9, "p95": 907, "p99": 1326.4, "p999": 1326.4}, "vusers.session_length": {"min": 353.1, "max": 1437.8, "count": 83, "mean": 529.9, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 671.9, "p95": 925.4, "p99": 1353.1, "p999": 1353.1}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "http.codes.200": 76, "http.responses": 76, "http.downloaded_bytes": 319736, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 76, "vusers.failed": 0, "vusers.completed": 76}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661200050, "firstHistogramAt": 1753661200539, "lastCounterAt": 1753661209971, "lastHistogramAt": 1753661209971, "firstMetricAt": 1753661200050, "lastMetricAt": 1753661209971, "period": "1753661200000", "summaries": {"http.response_time": {"min": 343, "max": 2162, "count": 76, "mean": 644.1, "p50": 441.5, "median": 441.5, "p75": 584.2, "p90": 1380.5, "p95": 1587.9, "p99": 1755, "p999": 1755}, "http.response_time.2xx": {"min": 343, "max": 2162, "count": 76, "mean": 644.1, "p50": 441.5, "median": 441.5, "p75": 584.2, "p90": 1380.5, "p95": 1587.9, "p99": 1755, "p999": 1755}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 343, "max": 2162, "count": 76, "mean": 644.1, "p50": 441.5, "median": 441.5, "p75": 584.2, "p90": 1380.5, "p95": 1587.9, "p99": 1755, "p999": 1755}, "vusers.session_length": {"min": 356.9, "max": 2178.4, "count": 76, "mean": 653, "p50": 450.4, "median": 450.4, "p75": 584.2, "p90": 1408.4, "p95": 1620, "p99": 1755, "p999": 1755}}, "histograms": {"http.response_time": {"min": 343, "max": 2162, "count": 76, "mean": 644.1, "p50": 441.5, "median": 441.5, "p75": 584.2, "p90": 1380.5, "p95": 1587.9, "p99": 1755, "p999": 1755}, "http.response_time.2xx": {"min": 343, "max": 2162, "count": 76, "mean": 644.1, "p50": 441.5, "median": 441.5, "p75": 584.2, "p90": 1380.5, "p95": 1587.9, "p99": 1755, "p999": 1755}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 343, "max": 2162, "count": 76, "mean": 644.1, "p50": 441.5, "median": 441.5, "p75": 584.2, "p90": 1380.5, "p95": 1587.9, "p99": 1755, "p999": 1755}, "vusers.session_length": {"min": 356.9, "max": 2178.4, "count": 76, "mean": 653, "p50": 450.4, "median": 450.4, "p75": 584.2, "p90": 1408.4, "p95": 1620, "p99": 1755, "p999": 1755}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "http.codes.200": 84, "http.responses": 84, "http.downloaded_bytes": 353388, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 84, "vusers.failed": 0, "vusers.completed": 84}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661210043, "firstHistogramAt": 1753661210043, "lastCounterAt": 1753661219863, "lastHistogramAt": 1753661219863, "firstMetricAt": 1753661210043, "lastMetricAt": 1753661219863, "period": "1753661210000", "summaries": {"http.response_time": {"min": 343, "max": 2431, "count": 84, "mean": 721.9, "p50": 450.4, "median": 450.4, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2416.8, "p999": 2416.8}, "http.response_time.2xx": {"min": 343, "max": 2431, "count": 84, "mean": 721.9, "p50": 450.4, "median": 450.4, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2416.8, "p999": 2416.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 343, "max": 2431, "count": 84, "mean": 721.9, "p50": 450.4, "median": 450.4, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2416.8, "p999": 2416.8}, "vusers.session_length": {"min": 354.9, "max": 2442.6, "count": 84, "mean": 731.1, "p50": 468.8, "median": 468.8, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2465.6, "p999": 2465.6}}, "histograms": {"http.response_time": {"min": 343, "max": 2431, "count": 84, "mean": 721.9, "p50": 450.4, "median": 450.4, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2416.8, "p999": 2416.8}, "http.response_time.2xx": {"min": 343, "max": 2431, "count": 84, "mean": 721.9, "p50": 450.4, "median": 450.4, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2416.8, "p999": 2416.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 343, "max": 2431, "count": 84, "mean": 721.9, "p50": 450.4, "median": 450.4, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2416.8, "p999": 2416.8}, "vusers.session_length": {"min": 354.9, "max": 2442.6, "count": 84, "mean": 731.1, "p50": 468.8, "median": 468.8, "p75": 727.9, "p90": 1720.2, "p95": 1901.1, "p99": 2465.6, "p999": 2465.6}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 148, "vusers.created": 148, "errors.Undefined function \"generateFastQueryFilters\"": 148, "http.requests": 148, "http.codes.200": 96, "http.responses": 96, "http.downloaded_bytes": 403872, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 96, "vusers.failed": 0, "vusers.completed": 96}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661220050, "firstHistogramAt": 1753661220554, "lastCounterAt": 1753661229970, "lastHistogramAt": 1753661229970, "firstMetricAt": 1753661220050, "lastMetricAt": 1753661229970, "period": "1753661220000", "summaries": {"http.response_time": {"min": 363, "max": 2567, "count": 96, "mean": 1019.9, "p50": 658.6, "median": 658.6, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}, "http.response_time.2xx": {"min": 363, "max": 2567, "count": 96, "mean": 1019.9, "p50": 658.6, "median": 658.6, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 363, "max": 2567, "count": 96, "mean": 1019.9, "p50": 658.6, "median": 658.6, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}, "vusers.session_length": {"min": 379.4, "max": 2573, "count": 96, "mean": 1030.1, "p50": 671.9, "median": 671.9, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}}, "histograms": {"http.response_time": {"min": 363, "max": 2567, "count": 96, "mean": 1019.9, "p50": 658.6, "median": 658.6, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}, "http.response_time.2xx": {"min": 363, "max": 2567, "count": 96, "mean": 1019.9, "p50": 658.6, "median": 658.6, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 363, "max": 2567, "count": 96, "mean": 1019.9, "p50": 658.6, "median": 658.6, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}, "vusers.session_length": {"min": 379.4, "max": 2573, "count": 96, "mean": 1030.1, "p50": 671.9, "median": 671.9, "p75": 1495.5, "p90": 2059.5, "p95": 2369, "p99": 2566.3, "p999": 2566.3}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "http.codes.200": 41, "http.responses": 41, "http.downloaded_bytes": 172494, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 41, "vusers.failed": 0, "vusers.completed": 41}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661230032, "firstHistogramAt": 1753661230105, "lastCounterAt": 1753661239991, "lastHistogramAt": 1753661239990, "firstMetricAt": 1753661230032, "lastMetricAt": 1753661239991, "period": "1753661230000", "summaries": {"http.response_time": {"min": 2642, "max": 11320, "count": 41, "mean": 7693, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}, "http.response_time.2xx": {"min": 2642, "max": 11320, "count": 41, "mean": 7693, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 2642, "max": 11320, "count": 41, "mean": 7693, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}, "vusers.session_length": {"min": 2653.5, "max": 11328.3, "count": 41, "mean": 7702.7, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}}, "histograms": {"http.response_time": {"min": 2642, "max": 11320, "count": 41, "mean": 7693, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}, "http.response_time.2xx": {"min": 2642, "max": 11320, "count": 41, "mean": 7693, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 2642, "max": 11320, "count": 41, "mean": 7693, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}, "vusers.session_length": {"min": 2653.5, "max": 11328.3, "count": 41, "mean": 7702.7, "p50": 7709.8, "median": 7709.8, "p75": 10201.2, "p90": 10407.3, "p95": 10617.5, "p99": 10617.5, "p999": 10617.5}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "http.codes.200": 100, "http.responses": 100, "http.downloaded_bytes": 420713, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 100, "vusers.failed": 10, "vusers.completed": 100, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 10, "errors.ETIMEDOUT": 10}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661240029, "firstHistogramAt": 1753661240102, "lastCounterAt": 1753661249959, "lastHistogramAt": 1753661248271, "firstMetricAt": 1753661240029, "lastMetricAt": 1753661249959, "period": "1753661240000", "summaries": {"http.response_time": {"min": 10548, "max": 14504, "count": 100, "mean": 12794, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}, "http.response_time.2xx": {"min": 10548, "max": 14504, "count": 100, "mean": 12794, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 10548, "max": 14504, "count": 100, "mean": 12794, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}, "vusers.session_length": {"min": 10564.6, "max": 14513.2, "count": 100, "mean": 12809.2, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}}, "histograms": {"http.response_time": {"min": 10548, "max": 14504, "count": 100, "mean": 12794, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}, "http.response_time.2xx": {"min": 10548, "max": 14504, "count": 100, "mean": 12794, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 10548, "max": 14504, "count": 100, "mean": 12794, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}, "vusers.session_length": {"min": 10564.6, "max": 14513.2, "count": 100, "mean": 12809.2, "p50": 12711.5, "median": 12711.5, "p75": 13497.6, "p90": 14048.5, "p95": 14332.3, "p99": 14332.3, "p999": 14332.3}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 201, "errors.ETIMEDOUT": 201, "vusers.failed": 201}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661250029, "lastCounterAt": 1753661259962, "firstMetricAt": 1753661250029, "lastMetricAt": 1753661259962, "period": "1753661250000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661260029, "lastCounterAt": 1753661269961, "firstMetricAt": 1753661260029, "lastMetricAt": 1753661269961, "period": "1753661260000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661270029, "lastCounterAt": 1753661279972, "firstMetricAt": 1753661270029, "lastMetricAt": 1753661279972, "period": "1753661270000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661280029, "lastCounterAt": 1753661289961, "firstMetricAt": 1753661280029, "lastMetricAt": 1753661289961, "period": "1753661280000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661290029, "lastCounterAt": 1753661299962, "firstMetricAt": 1753661290029, "lastMetricAt": 1753661299962, "period": "1753661290000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661300030, "lastCounterAt": 1753661309961, "firstMetricAt": 1753661300030, "lastMetricAt": 1753661309961, "period": "1753661300000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661310029, "lastCounterAt": 1753661319961, "firstMetricAt": 1753661310029, "lastMetricAt": 1753661319961, "period": "1753661310000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661320029, "lastCounterAt": 1753661329961, "firstMetricAt": 1753661320029, "lastMetricAt": 1753661329961, "period": "1753661320000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661330030, "lastCounterAt": 1753661339973, "firstMetricAt": 1753661330030, "lastMetricAt": 1753661339973, "period": "1753661330000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661340029, "lastCounterAt": 1753661349960, "firstMetricAt": 1753661340029, "lastMetricAt": 1753661349960, "period": "1753661340000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661350029, "lastCounterAt": 1753661359971, "firstMetricAt": 1753661350029, "lastMetricAt": 1753661359971, "period": "1753661350000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661360029, "lastCounterAt": 1753661369961, "firstMetricAt": 1753661360029, "lastMetricAt": 1753661369961, "period": "1753661360000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661370029, "lastCounterAt": 1753661379968, "firstMetricAt": 1753661370029, "lastMetricAt": 1753661379968, "period": "1753661370000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661380029, "lastCounterAt": 1753661389960, "firstMetricAt": 1753661380029, "lastMetricAt": 1753661389960, "period": "1753661380000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661390029, "lastCounterAt": 1753661399966, "firstMetricAt": 1753661390029, "lastMetricAt": 1753661399966, "period": "1753661390000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661400029, "lastCounterAt": 1753661409979, "firstMetricAt": 1753661400029, "lastMetricAt": 1753661409979, "period": "1753661400000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661410030, "lastCounterAt": 1753661419960, "firstMetricAt": 1753661410030, "lastMetricAt": 1753661419960, "period": "1753661410000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661420029, "lastCounterAt": 1753661429962, "firstMetricAt": 1753661420029, "lastMetricAt": 1753661429962, "period": "1753661420000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661430029, "lastCounterAt": 1753661439961, "firstMetricAt": 1753661430029, "lastMetricAt": 1753661439961, "period": "1753661430000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661440029, "lastCounterAt": 1753661449969, "firstMetricAt": 1753661440029, "lastMetricAt": 1753661449969, "period": "1753661440000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661450029, "lastCounterAt": 1753661459961, "firstMetricAt": 1753661450029, "lastMetricAt": 1753661459961, "period": "1753661450000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 132, "vusers.created": 132, "errors.Undefined function \"generateFastQueryFilters\"": 132, "http.requests": 132, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661460030, "lastCounterAt": 1753661469969, "firstMetricAt": 1753661460030, "lastMetricAt": 1753661469969, "period": "1753661460000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 192, "errors.ETIMEDOUT": 192, "vusers.failed": 192}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661470046, "lastCounterAt": 1753661479650, "firstMetricAt": 1753661470046, "lastMetricAt": 1753661479650, "period": "1753661470000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661480052, "lastCounterAt": 1753661489462, "firstMetricAt": 1753661480052, "lastMetricAt": 1753661489462, "period": "1753661480000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661490052, "lastCounterAt": 1753661499461, "firstMetricAt": 1753661490052, "lastMetricAt": 1753661499461, "period": "1753661490000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661500052, "lastCounterAt": 1753661509463, "firstMetricAt": 1753661500052, "lastMetricAt": 1753661509463, "period": "1753661500000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661510052, "lastCounterAt": 1753661519461, "firstMetricAt": 1753661510052, "lastMetricAt": 1753661519461, "period": "1753661510000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 40, "vusers.created": 40, "errors.Undefined function \"generateFastQueryFilters\"": 40, "http.requests": 40, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661520051, "lastCounterAt": 1753661529468, "firstMetricAt": 1753661520051, "lastMetricAt": 1753661529468, "period": "1753661520000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {}, "firstCounterAt": 1753661530067, "lastCounterAt": 1753661539465, "firstMetricAt": 1753661530067, "lastMetricAt": 1753661539465, "period": "1753661530000", "summaries": {}, "histograms": {}}]}