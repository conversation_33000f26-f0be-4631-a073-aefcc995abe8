'use client'

import { useEffect, useState, useCallback } from 'react'
// import Link from 'next/link' // Unused import
import { usePathname } from 'next/navigation'
import { useAuthStore } from '@/lib/stores/auth-store'
import UniversalNavLink from '@/components/UniversalNavLink'
import SignoutFeedback from '@/components/SignoutFeedback'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { User, LogOut } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'

export default function UserNav() {
  const { user, profile, isAuthenticated, isLoading, loadUserProfile, clearAuth: _clearAuth } = useAuthStore()
  const [isInitializing, setIsInitializing] = useState(true)
  const [hasTriedLoad, setHasTriedLoad] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const pathname = usePathname()

  // Memoize the profile loading function to prevent infinite loops
  const loadProfileOnce = useCallback(async () => {
    if (hasTriedLoad || isLoading) return
    
    setHasTriedLoad(true)
    try {
      await loadUserProfile()
    } catch (error) {
      console.error('Failed to load user profile:', error)
    }
  }, [hasTriedLoad, isLoading, loadUserProfile])

  // Initial auth check and profile load - run only once
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // If we're authenticated but don't have profile data, load it
        if (isAuthenticated && !profile && !hasTriedLoad) {
          await loadProfileOnce()
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
      } finally {
        setIsInitializing(false)
      }
    }

    initializeAuth()
  }, [isAuthenticated, profile, loadProfileOnce, hasTriedLoad])

  // Reset initialization state when auth state changes
  useEffect(() => {
    if (!isAuthenticated) {
      setHasTriedLoad(false)
      setIsInitializing(false)
    }
  }, [isAuthenticated])

  // Determine redirect behavior based on current route
  const getRedirectUrl = useCallback(() => {
    // Admin pages should redirect to home
    if (pathname.startsWith('/admin')) {
      return '/'
    }
    
    // Profile pages should redirect to home
    if (pathname.startsWith('/profile')) {
      return '/'
    }
    
    // All other pages (public pages) stay on same page
    return pathname
  }, [pathname])

  const handleLogout = async (e: React.MouseEvent) => {
    e.preventDefault()
    
    // Show signout feedback immediately
    setIsSigningOut(true)
    
    try {
      const supabase = createClient()
      
      // SIMPLE: Just call Supabase signOut, no timeouts or complex logic
      await supabase.auth.signOut()
      
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Supabase signout failed:', error)
      }
    }
    
    // SIMPLE: Let AuthProvider handle the clearAuth via the SIGNED_OUT event
    // No need to manually clear anything here
    
    // Brief feedback display
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Determine where to redirect
    const redirectUrl = getRedirectUrl()
    
    // Set loading to false BEFORE navigation
    setIsSigningOut(false)
    
    // Navigate to the appropriate page
    window.location.href = redirectUrl
  }

  // Show loading state while initializing or auth is loading
  if (isInitializing || isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
    )
  }

  // Show login button for unauthenticated users
  if (!isAuthenticated || !user) {
    return (
      <UniversalNavLink href="/login">
        <Button className="btn-glow-pink text-white font-medium">
          Join the Jury
        </Button>
      </UniversalNavLink>
    )
  }

  // Generate user initials safely
  const initials = profile?.full_name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase() || user.email?.[0]?.toUpperCase() || 'U'

  return (
    <>
      {/* Signout Feedback Dialog */}
      <SignoutFeedback isOpen={isSigningOut} />
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-pink-500 text-white">
                {initials}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 z-50" align="end" side="bottom" sideOffset={4}>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {profile?.full_name || 'User'}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {profile?.email || user.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <UniversalNavLink href="/profile" className="w-full">
              <div className="flex items-center cursor-pointer">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </div>
            </UniversalNavLink>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50"
            onClick={handleLogout}
            disabled={isSigningOut}
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>{isSigningOut ? 'Signing out...' : 'Sign out'}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
