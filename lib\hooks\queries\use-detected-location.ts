/**
 * Detected Location Hook
 * 
 * TanStack Query hook for IP-based location detection and nearest state calculation.
 * Provides global state management with proper caching and error handling.
 * 
 * Key Features:
 * - Global query cache with key 'detected-location'
 * - Automatic IP detection for unauthenticated users only
 * - Session-based caching with localStorage persistence
 * - Comprehensive error handling with fallback states
 * - Proper stale-time configuration for optimal performance
 */

import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import { locationDetectionService } from '@/lib/services/location-detection-service'
import { availableStatesService } from '@/lib/services/available-states-service'
import { handleLocationError } from '@/lib/utils/location-error-handling'
import type { Coordinates } from '@/lib/utils/distance-utils'

// Hook response interface
export interface DetectedLocationData {
  state: string
  coordinates: Coordinates
  distance?: number
  totalMugshots?: number
  cached: boolean
  source: 'ip-detection' | 'fallback' | 'disabled'
  error?: string
}

// Hook options interface
export interface UseDetectedLocationOptions {
  enabled?: boolean
  fallbackState?: string
  staleTime?: number
  gcTime?: number
}

/**
 * Hook for detecting user location and finding nearest available state
 * 
 * Only runs for unauthenticated users. Authenticated users should use
 * their stored location preferences from the auth store.
 * 
 * @param options Configuration options for the hook
 * @returns TanStack Query result with detected location data
 */
export function useDetectedLocation(options: UseDetectedLocationOptions = {}) {
  const {
    enabled = true,
    fallbackState = 'California',
    staleTime = 30 * 60 * 1000, // 30 minutes
    gcTime = 60 * 60 * 1000 // 1 hour
  } = options

  // Get authentication state
  const { isAuthenticated, isLoading: authLoading } = useAuthStore()

  return useQuery({
    queryKey: ['detected-location'],
    queryFn: async (): Promise<DetectedLocationData> => {
      try {
        // Step 1: Detect user location via IP
        console.log('🌍 Starting IP-based location detection...')
        const locationResult = await locationDetectionService.detectUserLocation()

        if (locationResult.error) {
          console.warn('⚠️ Location detection failed:', locationResult.error)
          // Continue with fallback coordinates
        }

        // Step 2: Find nearest available state
        console.log('📍 Finding nearest available state...', locationResult.coordinates)
        const nearestStateResult = await availableStatesService.findNearestState(
          locationResult.coordinates,
          false // Don't include all distances for performance
        )

        if (nearestStateResult.success && nearestStateResult.data) {
          const { nearestState, distance, coordinates, totalMugshots } = nearestStateResult.data
          
          console.log('✅ Detected nearest state:', {
            state: nearestState,
            distance: `${distance} miles`,
            totalMugshots,
            cached: locationResult.cached
          })

          return {
            state: nearestState,
            coordinates,
            distance,
            totalMugshots,
            cached: locationResult.cached,
            source: 'ip-detection',
            error: locationResult.error
          }
        }

        // Step 3: Fallback to default state if nearest state detection fails
        console.warn('⚠️ Nearest state detection failed, using fallback:', fallbackState)
        
        // Verify fallback state is available
        const fallbackAvailable = await availableStatesService.isStateAvailable(fallbackState)
        const finalState = fallbackAvailable ? fallbackState : 'California'
        
        const fallbackMugshotCount = await availableStatesService.getStateMugshotCount(finalState)

        return {
          state: finalState,
          coordinates: locationResult.coordinates,
          totalMugshots: fallbackMugshotCount,
          cached: locationResult.cached,
          source: 'fallback',
          error: nearestStateResult.error || locationResult.error
        }

      } catch (error) {
        console.error('❌ Location detection failed completely:', error)

        // Use comprehensive error handling for final fallback
        const { errorInfo, fallbackState: finalState, fallbackCoordinates } = handleLocationError(
          error,
          'use-detected-location'
        )

        return {
          state: finalState,
          coordinates: fallbackCoordinates,
          cached: false,
          source: 'fallback',
          error: errorInfo.userMessage
        }
      }
    },
    enabled: enabled && !authLoading && !isAuthenticated, // Only for unauthenticated users
    staleTime,
    gcTime,
    // Keep data fresh and immediately available
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    // Use cached data immediately while fetching in background
    placeholderData: (previousData) => previousData,
    retry: (failureCount, error) => {
      // Don't retry on client-side errors or after 2 attempts
      if (failureCount >= 2) return false
      
      // Don't retry on specific errors
      const errorMessage = error?.message?.toLowerCase() || ''
      if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
        return failureCount < 1 // Only retry once for network issues
      }
      
      return false
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff, max 10s
    // Removed duplicate refetch settings
  })
}

/**
 * Hook for getting detected location with authentication awareness
 * 
 * This hook provides a unified interface that:
 * - Returns detected location for unauthenticated users
 * - Returns null for authenticated users (they should use auth store location)
 * - Handles loading and error states appropriately
 */
export function useLocationForUnauthenticatedUsers(options: UseDetectedLocationOptions = {}) {
  const { isAuthenticated, isLoading: authLoading } = useAuthStore()
  
  const detectedLocationQuery = useDetectedLocation({
    ...options,
    enabled: !authLoading && !isAuthenticated
  })

  // Return appropriate data based on authentication state
  if (authLoading) {
    return {
      data: null,
      isLoading: true,
      error: null,
      isAuthenticated: false
    }
  }

  if (isAuthenticated) {
    return {
      data: null,
      isLoading: false,
      error: null,
      isAuthenticated: true
    }
  }

  return {
    data: detectedLocationQuery.data || null,
    isLoading: detectedLocationQuery.isLoading,
    error: detectedLocationQuery.error,
    isAuthenticated: false
  }
}

/**
 * Hook for clearing location detection cache
 * Useful for testing or when user wants to refresh their location
 */
export function useClearLocationCache() {
  const clearCache = () => {
    locationDetectionService.clearCache()
    // Could also invalidate the query cache here if needed
    // queryClient.invalidateQueries({ queryKey: ['detected-location'] })
  }

  const isCached = () => {
    return locationDetectionService.isCached()
  }

  return {
    clearCache,
    isCached
  }
}

/**
 * Hook for checking if location detection is available
 * Useful for showing UI indicators or enabling/disabling features
 */
export function useLocationDetectionStatus() {
  const { isAuthenticated, isLoading: authLoading } = useAuthStore()
  
  return {
    shouldDetectLocation: !authLoading && !isAuthenticated,
    isAuthenticatedUser: isAuthenticated,
    isAuthLoading: authLoading
  }
}
