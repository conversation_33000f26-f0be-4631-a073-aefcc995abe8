"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Home, Loader2, MapPin } from "lucide-react"
import { states, statesWithCounties } from "@/lib/constants"
import { useAuthStore } from "@/lib/stores/auth-store"
import { useLocationForUnauthenticatedUsers } from "@/lib/hooks/queries/use-detected-location"

interface LocationDropdownProps {
  className?: string
  selectedState: string
  setSelectedState: (state: string) => void
  selectedCounty: string
  setSelectedCounty: (county: string) => void
  stateLabel?: string
  countyLabel?: string
  statePlaceholder?: string
  countyPlaceholder?: string
  allStatesOption?: boolean
  allCountiesOption?: boolean
  isLoading?: boolean
}

export default function LocationDropdown({
  className = "",
  selectedState,
  setSelectedState,
  selectedCounty,
  setSelectedCounty,
  stateLabel = "State",
  countyLabel = "County",
  statePlaceholder = "Select state",
  countyPlaceholder = "Select county",
  allStatesOption = false,
  allCountiesOption = false,
  isLoading = false
}: LocationDropdownProps) {
  const [stateSearchTerm, setStateSearchTerm] = useState("")
  const [countySearchTerm, setCountySearchTerm] = useState("")
  const [previousState, setPreviousState] = useState<string>("")
  
  // Get user's home location from auth store
  const { getHomeLocation, hasHomeLocation, isAuthenticated, isLoading: authLoading } = useAuthStore()
  const { state: homeState, county: homeCounty } = getHomeLocation()

  // Get detected location for unauthenticated users
  const {
    data: detectedLocation,
    isLoading: locationDetecting,
    error: _locationError
  } = useLocationForUnauthenticatedUsers({
    fallbackState: 'California',
    staleTime: 30 * 60 * 1000 // 30 minutes
  })
  
  // Filter states based on search term
  const filteredStates = states.filter(state => 
    state.toLowerCase().includes(stateSearchTerm.toLowerCase())
  )

  // Get counties for selected state
  const counties = selectedState && selectedState !== "all-states"
    ? statesWithCounties[selectedState] || []
    : []
  
  // Filter counties based on search term
  const filteredCounties = counties.filter(county => 
    county.toLowerCase().includes(countySearchTerm.toLowerCase())
  )

  // Initialize with user's home location (authenticated) or detected location (unauthenticated)
  useEffect(() => {
    // For authenticated users: use home location if available
    if (
      isAuthenticated &&
      !authLoading &&
      hasHomeLocation() &&
      (!selectedState || selectedState === 'all-states') &&
      (!selectedCounty || selectedCounty === 'all-counties') &&
      homeState &&
      homeCounty
    ) {
      console.log('🏠 Pre-selecting home location for authenticated user:', { homeState, homeCounty })
      setSelectedState(homeState)
      setSelectedCounty(homeCounty)
      return
    }

    // For unauthenticated users: use detected location if available
    if (
      !isAuthenticated &&
      !authLoading &&
      !locationDetecting &&
      detectedLocation &&
      (!selectedState || selectedState === 'all-states') && // Allow override of 'all-states'
      detectedLocation.state
    ) {
      console.log('🎯 Pre-selecting detected state for unauthenticated user:', detectedLocation.state)
      setSelectedState(detectedLocation.state)
      // Note: We don't set county for detected location since we only detect state
    }
  }, [
    isAuthenticated,
    authLoading,
    hasHomeLocation,
    homeState,
    homeCounty,
    selectedState,
    selectedCounty,
    setSelectedState,
    setSelectedCounty,
    locationDetecting,
    detectedLocation
  ])

  // Reset county only when state actually changes (not during initialization)
  useEffect(() => {
    // Only clear county if the state actually changed from a previous value
    // AND the new state is different from the county's state
    if (previousState && previousState !== selectedState && selectedCounty) {
      // Check if the current county is valid for the new state
      const counties = selectedState && selectedState !== "all-states"
        ? statesWithCounties[selectedState] || []
        : []
      
      // Only clear county if it's not valid for the new state
      if (!counties.includes(selectedCounty)) {
        setSelectedCounty("")
        setCountySearchTerm("")
      }
    }
    // Update previous state tracker
    setPreviousState(selectedState)
  }, [selectedState, previousState, selectedCounty, setSelectedCounty])

  // Handle state change - NO useCallback needed
  const handleStateChange = (value: string) => {
    setSelectedState(value)
    setStateSearchTerm("")
    
    // NEW: If user selects "all-states", also clear county to ensure consistency
    if (value === "all-states") {
      setSelectedCounty("all-counties")
      setCountySearchTerm("")
    }
  }

  // Handle county change - NO useCallback needed  
  const handleCountyChange = (value: string) => {
    setSelectedCounty(value)
    setCountySearchTerm("")
  }

  return (
    <div className={`w-full ${className}`}>
      <div className="flex flex-col sm:flex-row gap-4 w-full">
        <div className="flex-1 space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300 block mb-2">
              {stateLabel}
            </label>
            {(isLoading || locationDetecting) && (
              <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
            )}
          </div>
          <Select 
            value={selectedState} 
            onValueChange={handleStateChange}
          >
            <SelectTrigger className="input-neon w-full bg-background">
              <SelectValue placeholder={statePlaceholder} />
            </SelectTrigger>
            <SelectContent className="bg-gray-800/90 border-cyan-500/30 shadow-xl">
              <div className="sticky top-0 p-2 border-b border-cyan-500/20">
                <Input 
                  placeholder="Search states..."
                  value={stateSearchTerm}
                  onChange={(e) => setStateSearchTerm(e.target.value)}
                  className="input-neon"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              
              <div className="max-h-32 overflow-y-auto">
                {allStatesOption && (
                  <SelectItem value="all-states" className="text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer">
                    All States
                  </SelectItem>
                )}
                
                {filteredStates.map((state) => {
                  const isHomeState = hasHomeLocation() && state === homeState
                  const isDetectedState = !isAuthenticated && detectedLocation?.state === state
                  return (
                    <SelectItem
                      key={state}
                      value={state}
                      className={`text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer ${
                        isHomeState ? 'bg-pink-500/10 border-l-2 border-pink-500' : ''
                      } ${
                        isDetectedState ? 'bg-cyan-500/10 border-l-2 border-cyan-500' : ''
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        {isHomeState && <Home className="w-3 h-3 text-pink-400" />}
                        {isDetectedState && <MapPin className="w-3 h-3 text-cyan-400" />}
                        <span>{state}</span>
                        {isDetectedState && detectedLocation?.distance && (
                          <span className="text-xs text-cyan-400 ml-auto">
                            {Math.round(detectedLocation.distance)} mi
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  )
                })}
                
                {filteredStates.length === 0 && stateSearchTerm && (
                  <div className="px-4 py-2 text-sm text-gray-400">No states found</div>
                )}
              </div>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300 block mb-2">
              {countyLabel}
            </label>
            {isLoading && (
              <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
            )}
          </div>
          <Select 
            value={selectedCounty} 
            onValueChange={handleCountyChange}
            disabled={!selectedState || selectedState === "all-states"}
          >
            <SelectTrigger className="input-neon w-full bg-background">
              <SelectValue placeholder={
                !selectedState || selectedState === "all-states"
                  ? "Select state first" 
                  : countyPlaceholder
              } />
            </SelectTrigger>
            <SelectContent className="bg-gray-800/90 border-cyan-500/30 shadow-xl">
              {selectedState && selectedState !== "all-states" && (
                <>
                  <div className="sticky top-0 p-2 border-b border-cyan-500/20">
                    <Input 
                      placeholder="Search counties..."
                      value={countySearchTerm}
                      onChange={(e) => setCountySearchTerm(e.target.value)}
                      className="input-neon"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                  
                  <div className="max-h-32 overflow-y-auto">
                    {allCountiesOption && (
                      <SelectItem value="all-counties" className="text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer">
                        All Counties
                      </SelectItem>
                    )}
                    
                    {filteredCounties.map((county) => {
                      const isHomeCounty = hasHomeLocation() && county === homeCounty && selectedState === homeState
                      return (
                        <SelectItem 
                          key={county} 
                          value={county} 
                          className={`text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer ${
                            isHomeCounty ? 'bg-pink-500/10 border-l-2 border-pink-500' : ''
                          }`}
                        >
                                                <div className="flex items-center gap-2">
                        {isHomeCounty && <Home className="w-3 h-3 text-pink-400" />}
                        <span>{county}</span>
                      </div>
                        </SelectItem>
                      )
                    })}
                    
                    {filteredCounties.length === 0 && countySearchTerm && (
                      <div className="px-4 py-2 text-sm text-gray-400">No counties found</div>
                    )}
                  </div>
                </>
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
} 