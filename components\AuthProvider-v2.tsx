'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { createClient } from '@/lib/supabase/client'
import { useAuthProfileSync } from '@/lib/hooks/use-auth-profile-sync'

interface AuthProviderProps {
  children: React.ReactNode
}

/**
 * Simplified AuthProvider that handles ONLY Supabase auth state changes
 * Profile loading is handled by TanStack Query hooks to eliminate race conditions
 */
export default function AuthProviderV2({ children }: AuthProviderProps) {
  const { setUser, clearAuth, setError, setLoading } = useAuthStore()
  
  // Use the profile sync hook to handle profile data via TanStack Query
  useAuthProfileSync()

  useEffect(() => {
    const supabase = createClient()
    let mounted = true

    // Get initial session - simplified without profile loading
    const getInitialSession = async () => {
      try {
        setLoading(true)
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (!mounted) return // Component unmounted
        
        if (error) {
          console.error('Session error:', error)
          setError('Failed to get session')
          setLoading(false)
          return
        }
        
        if (session?.user) {
          setUser(session.user)
          // Profile loading is now handled by useAuthProfileSync hook
        } else {
          clearAuth()
        }
        
        setLoading(false)
      } catch (error) {
        if (!mounted) return
        console.error('Initial session error:', error)
        setError('Failed to initialize authentication')
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes - simplified without profile loading
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return // Component unmounted
        
        console.log('Auth state changed:', event)
        
        try {
          if (event === 'SIGNED_IN' && session?.user) {
            setUser(session.user)
            // Profile loading is now handled by useAuthProfileSync hook
            setError(null) // Clear any previous errors
          } else if (event === 'SIGNED_OUT') {
            // Clear auth completely to fix logout issues
            clearAuth()
          } else if (event === 'TOKEN_REFRESHED' && session?.user) {
            // Just update the user, profile sync hook handles profile data
            setUser(session.user)
          }
        } catch (error) {
          if (!mounted) return
          console.error('Auth state change error:', error)
          setError('Authentication error occurred')
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [setUser, clearAuth, setError, setLoading])

  return <>{children}</>
} 