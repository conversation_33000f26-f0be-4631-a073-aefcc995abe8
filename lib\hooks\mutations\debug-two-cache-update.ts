/**
 * Debug utility to test and verify the two-cache update strategy
 * Run this in browser console to test cache updates
 */

import type { QueryClient } from '@tanstack/react-query'

export function debugCacheUpdates(mugshotId: string, queryClient: QueryClient) {
  
  console.log('🔍 [Debug] Checking cache state for mugshot:', mugshotId)
  
  // Check detail cache
  const detailCache = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
  console.log('📋 Detail cache:', detailCache)
  
  // Check user cache  
  const userCache = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
  console.log('👤 User cache:', userCache)
  
  // Check all grid caches
  const gridCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
  console.log('🗂️ Found', gridCaches.length, 'grid caches:')
  
  gridCaches.forEach(([queryKey, data], index) => {
    console.log(`  Cache ${index + 1}:`, queryKey)
    const gridData = data as { mugshots?: Array<{ id: number, [key: string]: unknown }> }
    
    if (gridData?.mugshots) {
      const targetMugshot = gridData.mugshots.find(m => m.id === parseInt(mugshotId, 10))
      if (targetMugshot) {
        console.log(`    ✅ Mugshot ${mugshotId} found:`, {
          id: targetMugshot.id,
          // Rating properties (both formats)
          average_rating: targetMugshot.average_rating,
          total_ratings: targetMugshot.total_ratings,
          averageRating: targetMugshot.averageRating,
          totalRatings: targetMugshot.totalRatings,
          rating: targetMugshot.rating,
          votes: targetMugshot.votes,
          // Tag properties (all formats)
          tag_counts: targetMugshot.tag_counts,
          wild_count: targetMugshot.wild_count,
          funny_count: targetMugshot.funny_count,
          spooky_count: targetMugshot.spooky_count,
          wildCount: targetMugshot.wildCount,
          funnyCount: targetMugshot.funnyCount,
          spookyCount: targetMugshot.spookyCount,
        })
      } else {
        console.log(`    ❌ Mugshot ${mugshotId} not found in this cache`)
      }
    } else {
      console.log(`    ⚠️ No mugshots array in cache data`)
    }
  })
}

export function simulateTagUpdate(mugshotId: string, tagType: 'wild' | 'funny' | 'spooky', increment = true, queryClient: QueryClient) {
  
  console.log('🧪 [Debug] Simulating tag update:', { mugshotId, tagType, increment })
  
  // Before state
  console.log('📊 BEFORE:')
  debugCacheUpdates(mugshotId, queryClient)
  
  // Simulate the update logic from our mutation
  const gridCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
  
  gridCaches.forEach(([queryKey, data]) => {
    if (!data || typeof data !== 'object') return
    
    const gridData = data as { mugshots?: Array<{ id: number, [key: string]: unknown }> }
    if (!gridData.mugshots || !Array.isArray(gridData.mugshots)) return
    
    const updatedMugshots = gridData.mugshots.map(mugshot => {
      if (mugshot.id === parseInt(mugshotId, 10)) {
        const currentTagCounts = (mugshot.tag_counts as Record<string, number>) || {}
        const currentCount = currentTagCounts[tagType] || 0
        const newCount = increment ? currentCount + 1 : Math.max(0, currentCount - 1)
        
        const updatedTagCounts = {
          ...currentTagCounts,
          [tagType]: newCount
        }
        
        return {
          ...mugshot,
          // Update raw tag_counts (JSONB from SQL function)
          tag_counts: updatedTagCounts,
          // Update UI-expected camelCase properties
          wildCount: updatedTagCounts.wild || 0,
          funnyCount: updatedTagCounts.funny || 0,
          spookyCount: updatedTagCounts.spooky || 0,
          // Update database format snake_case properties
          wild_count: updatedTagCounts.wild || 0,
          funny_count: updatedTagCounts.funny || 0,
          spooky_count: updatedTagCounts.spooky || 0,
        }
      }
      return mugshot
    })
    
    queryClient.setQueryData(queryKey, {
      ...gridData,
      mugshots: updatedMugshots
    })
  })
  
  // After state
  console.log('📊 AFTER:')
  debugCacheUpdates(mugshotId, queryClient)
}

export function simulateRatingUpdate(mugshotId: string, newRating: number, queryClient: QueryClient) {
  
  console.log('🧪 [Debug] Simulating rating update:', { mugshotId, newRating })
  
  // Before state
  console.log('📊 BEFORE:')
  debugCacheUpdates(mugshotId, queryClient)
  
  // Simulate new statistics calculation
  const currentStats = queryClient.getQueryData(['mugshot', mugshotId, 'rating-statistics']) as { averageRating: number, totalRatings: number } | undefined
  const currentUserRating = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'rating']) as { userRating: number | null } | undefined
  
  if (currentStats) {
    const currentTotal = currentStats.totalRatings || 0
    const currentAvg = currentStats.averageRating || 0
    const previousRating = currentUserRating?.userRating
    
    let newTotal = currentTotal
    let newSum = currentAvg * currentTotal
    
    if (previousRating !== null && previousRating !== undefined) {
      newSum = newSum - previousRating + newRating
    } else {
      newSum = newSum + newRating
      newTotal = currentTotal + 1
    }
    
    const newAvg = newTotal > 0 ? newSum / newTotal : 0
    const newStatistics = {
      averageRating: Math.round(newAvg * 100) / 100,
      totalRatings: newTotal
    }
    
    console.log('📈 Calculated new stats:', newStatistics)
    
    // Update grid caches
    const gridCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
    
    gridCaches.forEach(([queryKey, data]) => {
      if (!data || typeof data !== 'object') return
      
      const gridData = data as { mugshots?: Array<{ id: number, [key: string]: unknown }> }
      if (!gridData.mugshots || !Array.isArray(gridData.mugshots)) return
      
      const updatedMugshots = gridData.mugshots.map(mugshot => {
        if (mugshot.id === parseInt(mugshotId, 10)) {
          const newAvgRating = newStatistics.averageRating
          const newTotalRatings = newStatistics.totalRatings
          
          return {
            ...mugshot,
            // Update both database format (snake_case) AND UI format (camelCase)
            average_rating: newAvgRating,
            total_ratings: newTotalRatings,
            averageRating: newAvgRating,
            totalRatings: newTotalRatings,
            rating: newAvgRating,
            votes: newTotalRatings,
          }
        }
        return mugshot
      })
      
      queryClient.setQueryData(queryKey, {
        ...gridData,
        mugshots: updatedMugshots
      })
    })
  }
  
  // After state
  console.log('📊 AFTER:')
  debugCacheUpdates(mugshotId, queryClient)
}

// Debug functions are exported for use in React components
// To use these functions, import them and pass a QueryClient instance:
// 
// import { debugCacheUpdates } from '@/lib/hooks/mutations/debug-two-cache-update'
// import { useQueryClient } from '@tanstack/react-query'
// 
// const queryClient = useQueryClient()
// debugCacheUpdates(mugshotId, queryClient) 