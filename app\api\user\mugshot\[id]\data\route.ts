import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    
    // Log request for debugging (useful for API monitoring)
    console.log(`GET /api/user/mugshot/${id}/data - Request from:`, request.nextUrl.origin)
    
    // Validate mugshot ID
    const mugshotId = parseInt(id, 10)
    if (isNaN(mugshotId) || mugshotId <= 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid mugshot ID', error: 'INVALID_MUGSHOT_ID' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    
    // Get current user - REQUIRED for this endpoint
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, message: 'Authentication required', error: 'UNAUTHENTICATED' },
        { status: 401 }
      )
    }

    // Get user's rating for this mugshot
    const { data: userRatingData, error: ratingError } = await supabase
      .rpc('get_user_rating', {
        p_mugshot_id: mugshotId,
        p_user_id: user.id
      })

    if (ratingError) {
      console.error('Error fetching user rating:', ratingError)
      // Don't fail the request, just set to null
    }

    // Get user's tags for this mugshot
    const { data: userTagsData, error: tagsError } = await supabase
      .from('tags')
      .select('tag_type')
      .eq('mugshot_id', mugshotId)
      .eq('user_id', user.id)

    if (tagsError) {
      console.error('Error fetching user tags:', tagsError)
      // Don't fail the request, just set to empty array
    }

    // Build response with user-specific data only
    const response = {
      success: true,
      data: {
        userRating: userRatingData || null,
        userTags: userTagsData?.map(tag => tag.tag_type) || [],
        meta: {
          mugshotId,
          userId: user.id,
          fetchedAt: new Date().toISOString()
        }
      }
    }

    return NextResponse.json(response)
    
  } catch (error) {
    console.error('User mugshot data API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch user data',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
} 