# Quick validation test for Top-Rated First 12
# Use this to quickly verify the test setup before running the full load test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 10
      arrivalRate: 1
      name: "Quick validation test"
  
  # Relaxed expectations for quick test
  ensure:
    - http.response_time.p95: 3000
    - http.codes.200: 90

  http:
    timeout: 15
    pool: 5

scenarios:
  - name: "Quick Top-Rated First 12 Validation"
    weight: 100
    flow:
      - function: "generateTopRatedFirst12Filters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - Quick Validation"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
            - json: "$.data.mugshots[0].average_rating"
              as: "firstMugshotRating"
            - json: "$.data.mugshots[0].total_ratings"
              as: "firstMugshotTotalRatings"

processor: "./load-tests/focused-tests/data-generators-top-rated-12.js"
