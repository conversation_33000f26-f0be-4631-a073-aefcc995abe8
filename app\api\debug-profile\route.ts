import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        user: null,
        profile: null 
      }, { status: 401 })
    }

    // Get user profile from our profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
        user_metadata: user.user_metadata
      },
      profile: profile || null,
      profileError: profileError?.message || null
    })
    
  } catch (error) {
    // Log error for debugging purposes (consider using a proper logging service)
    console.error('Debug profile API error:', error)
    
    // Return generic error message (avoid exposing sensitive information)
    return NextResponse.json({ 
      error: 'Internal server error',
      user: null,
      profile: null 
    }, { status: 500 })
  }
} 