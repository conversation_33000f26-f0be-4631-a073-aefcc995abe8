'use client'

import { useEffect, useState, useRef } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

/**
 * Top Progress Bar Component
 * Provides smooth, continuous feedback during navigation
 * Inspired by NProgress with custom React implementation
 * Uses shadcn UI design tokens and Tailwind CSS
 */
export default function TopProgressBar() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isInitialLoad = useRef(true)

  // Function to start the progress bar
  const startProgress = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    
    setIsLoading(true)
    setProgress(10) // Start with 10%

    // Simulate progress increment
    intervalRef.current = setInterval(() => {
      setProgress(prev => {
        if (prev >= 85) {
          return prev // Cap at 85% until route completes
        }
        return prev + Math.random() * 12 // Increment by 0-12%
      })
    }, 180)
  }

  // Function to complete the progress bar
  const completeProgress = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    
    setProgress(100)
    
    // Hide progress bar after completion animation
    setTimeout(() => {
      setIsLoading(false)
      setProgress(0)
    }, 300)
  }

  // Listen for manual navigation starts
  useEffect(() => {
    // Check if navigation was marked as started
    const checkNavigationState = () => {
      if (typeof window !== 'undefined' && window.history.state?.navigationStarted) {
        startProgress()
        
        // Clear the flag
        const newState = { ...window.history.state }
        delete newState.navigationStarted
        window.history.replaceState(newState, '', window.location.href)
      }
    }

    // Check immediately and set up interval to check periodically
    checkNavigationState()
    const checkInterval = setInterval(checkNavigationState, 50)

    // Clean up
    return () => {
      clearInterval(checkInterval)
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Handle route changes (completion)
  useEffect(() => {
    // Skip the initial load
    if (isInitialLoad.current) {
      isInitialLoad.current = false
      return
    }

    // Route has changed, complete the progress
    if (isLoading) {
      completeProgress()
    }
  }, [pathname, searchParams, isLoading])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  if (!isLoading) return null

  return (
    <>
      {/* Top progress bar */}
      <div className="fixed top-0 left-0 right-0 z-[9999] h-0.5 bg-transparent">
        <div
          className="h-full bg-gradient-to-r from-pink-500 via-pink-400 to-purple-500 transition-all duration-200 ease-out relative shadow-lg"
          style={{
            width: `${progress}%`,
            boxShadow: '0 0 10px rgba(236, 72, 153, 0.6), 0 0 5px rgba(147, 51, 234, 0.4)',
          }}
        >
          {/* Animated shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse" />
          
          {/* Right edge glow */}
          <div className="absolute top-0 right-0 w-6 h-full bg-gradient-to-l from-pink-300 to-transparent opacity-80" />
        </div>
      </div>

      {/* Loading indicator */}
      <div className="fixed top-1 right-4 z-[9998]">
        <div className="flex items-center space-x-2 bg-background/95 backdrop-blur-sm border border-pink-500/20 rounded-full px-3 py-1.5 shadow-lg">
          <div className="w-3 h-3 border-2 border-pink-500 border-t-transparent rounded-full animate-spin" />
          <span className="text-xs text-foreground/70 font-medium">Loading...</span>
        </div>
      </div>
    </>
  )
} 