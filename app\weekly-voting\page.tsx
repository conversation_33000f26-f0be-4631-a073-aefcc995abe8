import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Vote, Calendar, Trophy, Users, Timer, Star } from 'lucide-react'
import Image from 'next/image'

// Mock data for weekly voting
const mockVotingStatus = {
  isActive: true,
  weekStart: '2024-12-23',
  weekEnd: '2024-12-29',
  totalVotes: 15847,
  timeRemaining: '2 days, 14 hours',
  topContenders: [
    { 
      id: 1, 
      name: '<PERSON>', 
      state: 'FL', 
      county: 'Miami-Dade',
      votes: 3245, 
      percentage: 20.5,
      mugshot: '/images/mugshot-placeholder.png',
      category: 'Wild'
    },
    { 
      id: 2, 
      name: '<PERSON>', 
      state: 'TX', 
      county: 'Harris',
      votes: 2987, 
      percentage: 18.8,
      mugshot: '/images/mugshot-placeholder.png',
      category: 'Funny'
    },
    { 
      id: 3, 
      name: '<PERSON>', 
      state: 'CA', 
      county: 'Los Angeles',
      votes: 2756, 
      percentage: 17.4,
      mugshot: '/images/mugshot-placeholder.png',
      category: 'Hot'
    },
    { 
      id: 4, 
      name: '<PERSON>', 
      state: 'NY', 
      county: 'Queens',
      votes: 2234, 
      percentage: 14.1,
      mugshot: '/images/mugshot-placeholder.png',
      category: 'Scary'
    },
    { 
      id: 5, 
      name: 'Amanda Brown', 
      state: 'GA', 
      county: 'Fulton',
      votes: 1987, 
      percentage: 12.5,
      mugshot: '/images/mugshot-placeholder.png',
      category: 'Wild'
    }
  ]
}

export default async function WeeklyVotingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            Weekly Best Voting
          </h1>
          <p className="text-xl text-gray-300 mb-6">
            Vote for this week&apos;s most legendary mugshots and help crown the weekly champion
          </p>
          <div className="flex justify-center gap-4 flex-wrap">
            <Badge variant="secondary" className="text-lg px-4 py-2">
              <Calendar className="w-4 h-4 mr-2" />
              Dec 23 - Dec 29, 2024
            </Badge>
            <Badge variant="outline" className="text-lg px-4 py-2 border-green-400 text-green-400">
              <Timer className="w-4 h-4 mr-2" />
              {mockVotingStatus.timeRemaining} left
            </Badge>
          </div>
        </div>

        {/* Voting Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="bg-gray-800/50 border-blue-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Vote className="w-5 h-5 text-blue-400" />
                Total Votes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white">
                {mockVotingStatus.totalVotes.toLocaleString()}
              </div>
              <p className="text-gray-400">votes cast this week</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Users className="w-5 h-5 text-purple-400" />
                Contenders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-white">
                {mockVotingStatus.topContenders.length}
              </div>
              <p className="text-gray-400">top contenders</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-green-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-400" />
                Prize
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold text-yellow-400">
                Weekly Champion
              </div>
              <p className="text-gray-400">title & recognition</p>
            </CardContent>
          </Card>
        </div>

        {/* Top Contenders */}
        <Card className="bg-gray-800/50 border-cyan-500/30 mb-8">
          <CardHeader>
            <CardTitle className="text-white text-2xl flex items-center gap-2">
              <Star className="w-6 h-6 text-yellow-400" />
              Top Weekly Contenders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {mockVotingStatus.topContenders.map((contender, index) => (
                <div key={contender.id} className="flex items-center gap-4 p-4 rounded-lg bg-gray-700/30 border border-gray-600/30">
                  {/* Rank Badge */}
                  <div className="flex-shrink-0">
                    <Badge 
                      variant={index === 0 ? "default" : "secondary"}
                      className={`text-lg px-3 py-1 ${
                        index === 0 ? "bg-yellow-500 text-black" :
                        index === 1 ? "bg-gray-400 text-black" :
                        index === 2 ? "bg-amber-600 text-white" : ""
                      }`}
                    >
                      #{index + 1}
                    </Badge>
                  </div>

                  {/* Mugshot */}
                  <div className="flex-shrink-0">
                    <Image
                      src={contender.mugshot}
                      alt={contender.name}
                      width={64}
                      height={80}
                      className="w-16 h-20 rounded-lg border-2 border-gray-500 object-cover"
                    />
                  </div>

                  {/* Info */}
                  <div className="flex-grow">
                    <h3 className="text-white font-semibold text-lg">{contender.name}</h3>
                    <p className="text-gray-400">{contender.county}, {contender.state}</p>
                                         <div className="flex items-center gap-2 mt-1">
                       <Badge variant="outline" className="text-xs">
                         {contender.category}
                       </Badge>
                     </div>
                  </div>

                  {/* Vote Progress */}
                  <div className="flex-shrink-0 text-right min-w-[120px]">
                    <div className="text-white font-semibold">
                      {contender.votes.toLocaleString()} votes
                    </div>
                    <div className="text-sm text-gray-400 mb-2">
                      {contender.percentage}%
                    </div>
                    <Progress 
                      value={contender.percentage} 
                      className="h-2 w-20"
                    />
                  </div>

                  {/* Vote Button */}
                  <div className="flex-shrink-0">
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black"
                    >
                      Vote
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* How to Vote */}
        <Card className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border-blue-500/30">
          <CardContent className="p-8">
            <h3 className="text-2xl font-bold text-white mb-4 text-center">
              How Weekly Voting Works
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl mb-2">🗳️</div>
                <h4 className="text-white font-semibold mb-2">Vote Daily</h4>
                <p className="text-gray-300 text-sm">
                  Cast your vote for your favorite mugshots throughout the week
                </p>
              </div>
              <div>
                <div className="text-3xl mb-2">⭐</div>
                <h4 className="text-white font-semibold mb-2">Rate & Tag</h4>
                <p className="text-gray-300 text-sm">
                  Rate mugshots and add tags to boost their weekly score
                </p>
              </div>
              <div>
                <div className="text-3xl mb-2">🏆</div>
                <h4 className="text-white font-semibold mb-2">Weekly Winner</h4>
                <p className="text-gray-300 text-sm">
                  Top voted mugshot becomes the weekly champion
                </p>
              </div>
              <div>
                <div className="text-3xl mb-2">👑</div>
                <h4 className="text-white font-semibold mb-2">Monthly Compete</h4>
                <p className="text-gray-300 text-sm">
                  Weekly winners compete in monthly championships
                </p>
              </div>
            </div>
            
            <div className="text-center mt-6">
              <Badge variant="outline" className="text-blue-300 border-blue-300">
                Voting ends every Sunday at midnight
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 