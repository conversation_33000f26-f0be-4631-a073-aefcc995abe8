import { SupabaseClient } from '@supabase/supabase-js'

interface Profile {
  id: string
  user_id: string
  email: string
  full_name: string | null
  state: string | null
  county: string | null
  role: string
  created_at: string
  updated_at: string
}

interface OAuthUser {
  id: string
  email?: string
  user_metadata?: {
    full_name?: string
    name?: string
  }
  app_metadata?: {
    provider?: string
  }
}

interface AccountLinkingResult {
  success: boolean
  action: 'linked' | 'existing_oauth' | 'new_account' | 'error'
  profile?: Profile
  error?: string
  message?: string
}

/**
 * Handles account linking for OAuth users who might have existing email/password accounts
 */
export async function handleAccountLinking(
  supabase: SupabaseClient,
  oauthUser: OAuthUser
): Promise<AccountLinkingResult> {
  try {
    const email = oauthUser.email?.toLowerCase()
    
    if (!email) {
      return {
        success: false,
        action: 'error',
        error: 'No email provided by OAuth provider'
      }
    }

    console.log('Checking for existing accounts with email:', email)

    // Step 1: Check if this OAuth user already has a profile
    const { data: existingOAuthProfile, error: oauthProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', oauthUser.id)
      .single()

    if (existingOAuthProfile && !oauthProfileError) {
      console.log('OAuth user already has a profile:', existingOAuthProfile.id)
      return {
        success: true,
        action: 'existing_oauth',
        profile: existingOAuthProfile,
        message: 'Existing OAuth account found'
      }
    }

    // Step 2: Check if there's an existing profile with the same email (from email/password signup)
    const { data: existingEmailProfile, error: emailProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .neq('user_id', oauthUser.id) // Not the current OAuth user
      .single()

    if (existingEmailProfile && !emailProfileError) {
      console.log('Found existing email/password account for:', email)
      console.log('Existing profile:', existingEmailProfile.id, 'OAuth user:', oauthUser.id)

      // Step 3: Link the accounts by updating the existing profile
      const { data: linkedProfile, error: linkError } = await supabase
        .from('profiles')
        .update({
          // Update to use the new OAuth user_id but preserve all existing data
          user_id: oauthUser.id,
          // Add OAuth-specific fields
          avatar_url: (oauthUser.user_metadata as { avatar_url?: string })?.avatar_url || existingEmailProfile.avatar_url,
          // Preserve all existing data (state, county, preferences, etc.)
          full_name: existingEmailProfile.full_name, // Keep original name unless empty
          // Update metadata
          updated_at: new Date().toISOString()
        })
        .eq('id', existingEmailProfile.id)
        .select()
        .single()

      if (linkError) {
        console.error('Error linking accounts:', linkError)
        return {
          success: false,
          action: 'error',
          error: 'Failed to link accounts: ' + linkError.message
        }
      }

      console.log('Successfully linked OAuth account to existing profile:', linkedProfile.id)
      
      return {
        success: true,
        action: 'linked',
        profile: linkedProfile,
        message: `Linked Google account to existing account for ${email}`
      }
    }

    // Step 4: No existing account found - create new profile for OAuth user
    console.log('No existing account found, creating new OAuth profile for:', email)
    
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        user_id: oauthUser.id,
        email: email,
        full_name: oauthUser.user_metadata?.full_name || 'User',
        avatar_url: (oauthUser.user_metadata as { avatar_url?: string })?.avatar_url || null,
        state: null, // OAuth users start without location
        county: null,
        role: 'user'
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating new OAuth profile:', createError)
      return {
        success: false,
        action: 'error',
        error: 'Failed to create profile: ' + createError.message
      }
    }

    console.log('Created new OAuth profile:', newProfile.id)
    
    return {
      success: true,
      action: 'new_account',
      profile: newProfile,
      message: 'Created new Google account'
    }

  } catch (error) {
    console.error('Account linking error:', error)
    return {
      success: false,
      action: 'error',
      error: error instanceof Error ? error.message : 'Unknown error during account linking'
    }
  }
}

/**
 * Get user profile with account linking support
 */
export async function getOrCreateProfileWithLinking(
  supabase: SupabaseClient,
  user: OAuthUser
): Promise<{ profile: Profile | null; wasLinked: boolean; message?: string }> {
  try {
    // First try normal profile lookup
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (profile && !error) {
      return { profile, wasLinked: false }
    }

    // If no profile found and this is an OAuth user, try account linking
    if (user.app_metadata?.provider === 'google') {
      console.log('OAuth user without profile, attempting account linking...')
      
      const linkingResult = await handleAccountLinking(supabase, user)
      
      if (linkingResult.success && linkingResult.profile) {
        return { 
          profile: linkingResult.profile, 
          wasLinked: linkingResult.action === 'linked',
          message: linkingResult.message
        }
      } else {
        console.error('Account linking failed:', linkingResult.error)
      }
    }

    return { profile: null, wasLinked: false }

  } catch (error) {
    console.error('Error in getOrCreateProfileWithLinking:', error)
    return { profile: null, wasLinked: false }
  }
} 