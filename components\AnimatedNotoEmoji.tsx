"use client"

import { useState } from "react"
import Image from "next/image"

interface AnimatedNotoEmojiProps {
  emoji: string
  size?: number
  className?: string
  isHovered?: boolean
}

// Map common emojis to their Google Noto animated emoji URLs
const emojiUrlMap: Record<string, { animated: string; static: string }> = {
  "🥵": {
    animated: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f975/512.gif",
    static: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f975/emoji.svg"
  },
  "😂": {
    animated: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f602/512.gif", 
    static: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f602/emoji.svg"
  },
  "🤪": {
    animated: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f92a/512.gif",
    static: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f92a/emoji.svg"
  },
  "😱": {
    animated: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f631/512.gif",
    static: "https://fonts.gstatic.com/s/e/notoemoji/latest/1f631/emoji.svg"
  }
}

export default function AnimatedNotoEmoji({ 
  emoji, 
  size = 64, 
  className = "",
  isHovered = false 
}: AnimatedNotoEmojiProps) {
  const [hasError, setHasError] = useState(false)

  const emojiUrls = emojiUrlMap[emoji]

  // Fallback to regular emoji if no URL mapping or error
  if (!emojiUrls || hasError) {
    return (
      <span 
        className={`inline-block text-4xl ${className} transition-all duration-300 ${
          isHovered ? "scale-110 drop-shadow-lg" : "scale-100"
        }`}
        style={{ 
          fontSize: `${size}px`,
          transformOrigin: "center"
        }}
      >
        {emoji}
      </span>
    )
  }

  return (
    <Image
      src={isHovered ? emojiUrls.animated : emojiUrls.static}
      alt={emoji}
      width={size}
      height={size}
      className={`inline-block transition-all duration-300 ${className} ${
        isHovered ? "scale-110 drop-shadow-lg" : "scale-100"
      }`}
      style={{
        transformOrigin: "center",
      }}
      onError={() => setHasError(true)}
    />
  )
} 