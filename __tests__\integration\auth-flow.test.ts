import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createClient } from '@/lib/supabase/client'
import { signUpUser, signInUser } from '@/lib/auth-utils'

// Mock the Supabase client
vi.mock('@/lib/supabase/client', () => ({
  createClient: vi.fn()
}))

describe('Authentication Integration Flow', () => {
  let mockSupabase: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockSupabase = {
      auth: {
        signUp: vi.fn(),
        signInWithPassword: vi.fn(),
        getUser: vi.fn(),
        getSession: vi.fn()
      }
    }
    
    vi.mocked(createClient).mockReturnValue(mockSupabase)
  })

  describe('User Registration Flow', () => {
    it('should successfully register a new user with profile data', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        fullName: '<PERSON>',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            user_metadata: {
              full_name: 'John Doe',
              state: 'CA',
              county: 'Los Angeles'
            }
          }
        },
        error: null
      })

      // Act
      const result = await signUpUser(mockSupabase, userData)

      // Assert
      expect(result.success).toBe(true)
      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'Password123!',
        options: {
          data: {
            full_name: 'John Doe',
            state: 'CA',
            county: 'Los Angeles'
          }
        }
      })
    })

    it('should handle registration errors gracefully', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        fullName: 'John Doe',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: null,
        error: {
          message: 'User already registered'
        }
      })

      // Act
      const result = await signUpUser(mockSupabase, userData)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toBe('User already registered')
    })
  })

  describe('User Login Flow', () => {
    it('should successfully log in an existing user', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!'
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          },
          session: {
            access_token: 'token-123'
          }
        },
        error: null
      })

      // Act
      const result = await signInUser(mockSupabase, credentials)

      // Assert
      expect(result.success).toBe(true)
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'Password123!'
      })
    })

    it('should handle login errors gracefully', async () => {
      // Arrange
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: null,
        error: {
          message: 'Invalid login credentials'
        }
      })

      // Act
      const result = await signInUser(mockSupabase, credentials)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid login credentials')
    })
  })

  describe('Supabase Client Creation', () => {
    it('should create client with environment variables', () => {
      // Act
      const client = createClient()

      // Assert
      expect(createClient).toHaveBeenCalled()
      expect(client).toBeDefined()
    })
  })
}) 