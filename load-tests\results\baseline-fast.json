{"aggregate": {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 3300, "vusers.created": 3300, "errors.Undefined function \"generateBaselineFilters\"": 3300, "http.requests": 3300, "http.codes.200": 756, "http.responses": 756, "http.downloaded_bytes": 3170683, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 756, "vusers.failed": 2544, "vusers.completed": 756, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 2544, "errors.ETIMEDOUT": 2544}, "rates": {"http.request_rate": 8}, "firstCounterAt": 1753659681408, "firstHistogramAt": 1753659682277, "lastCounterAt": 1753659990637, "lastHistogramAt": 1753659783760, "firstMetricAt": 1753659681408, "lastMetricAt": 1753659990637, "period": 1753659990000, "summaries": {"http.response_time": {"min": 331, "max": 9885, "count": 756, "mean": 1521.3, "p50": 713.5, "median": 713.5, "p75": 1408.4, "p90": 3828.5, "p95": 5826.9, "p99": 9607.1, "p999": 9801.2}, "http.response_time.2xx": {"min": 331, "max": 9885, "count": 756, "mean": 1521.3, "p50": 713.5, "median": 713.5, "p75": 1408.4, "p90": 3828.5, "p95": 5826.9, "p99": 9607.1, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 331, "max": 9885, "count": 756, "mean": 1521.3, "p50": 713.5, "median": 713.5, "p75": 1408.4, "p90": 3828.5, "p95": 5826.9, "p99": 9607.1, "p999": 9801.2}, "vusers.session_length": {"min": 1332.1, "max": 10901.8, "count": 756, "mean": 2532.3, "p50": 1720.2, "median": 1720.2, "p75": 2416.8, "p90": 4867, "p95": 6838, "p99": 10617.5, "p999": 10832}}, "histograms": {"http.response_time": {"min": 331, "max": 9885, "count": 756, "mean": 1521.3, "p50": 713.5, "median": 713.5, "p75": 1408.4, "p90": 3828.5, "p95": 5826.9, "p99": 9607.1, "p999": 9801.2}, "http.response_time.2xx": {"min": 331, "max": 9885, "count": 756, "mean": 1521.3, "p50": 713.5, "median": 713.5, "p75": 1408.4, "p90": 3828.5, "p95": 5826.9, "p99": 9607.1, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 331, "max": 9885, "count": 756, "mean": 1521.3, "p50": 713.5, "median": 713.5, "p75": 1408.4, "p90": 3828.5, "p95": 5826.9, "p99": 9607.1, "p999": 9801.2}, "vusers.session_length": {"min": 1332.1, "max": 10901.8, "count": 756, "mean": 2532.3, "p50": 1720.2, "median": 1720.2, "p75": 2416.8, "p90": 4867, "p95": 6838, "p99": 10617.5, "p999": 10832}}}, "intermediate": [{"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 45, "vusers.created": 45, "errors.Undefined function \"generateBaselineFilters\"": 45, "http.requests": 45, "http.codes.200": 43, "http.responses": 43, "http.downloaded_bytes": 180342, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 43, "vusers.failed": 0, "vusers.completed": 37}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659681408, "firstHistogramAt": 1753659682277, "lastCounterAt": 1753659689970, "lastHistogramAt": 1753659689970, "firstMetricAt": 1753659681408, "lastMetricAt": 1753659689970, "period": "1753659680000", "summaries": {"http.response_time": {"min": 350, "max": 1265, "count": 43, "mean": 557.1, "p50": 415.8, "median": 415.8, "p75": 528.6, "p90": 925.4, "p95": 1130.2, "p99": 1249.1, "p999": 1249.1}, "http.response_time.2xx": {"min": 350, "max": 1265, "count": 43, "mean": 557.1, "p50": 415.8, "median": 415.8, "p75": 528.6, "p90": 925.4, "p95": 1130.2, "p99": 1249.1, "p999": 1249.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 350, "max": 1265, "count": 43, "mean": 557.1, "p50": 415.8, "median": 415.8, "p75": 528.6, "p90": 925.4, "p95": 1130.2, "p99": 1249.1, "p999": 1249.1}, "vusers.session_length": {"min": 1357.8, "max": 2276.2, "count": 37, "mean": 1570.2, "p50": 1408.4, "median": 1408.4, "p75": 1525.7, "p90": 1978.7, "p95": 2186.8, "p99": 2276.1, "p999": 2276.1}}, "histograms": {"http.response_time": {"min": 350, "max": 1265, "count": 43, "mean": 557.1, "p50": 415.8, "median": 415.8, "p75": 528.6, "p90": 925.4, "p95": 1130.2, "p99": 1249.1, "p999": 1249.1}, "http.response_time.2xx": {"min": 350, "max": 1265, "count": 43, "mean": 557.1, "p50": 415.8, "median": 415.8, "p75": 528.6, "p90": 925.4, "p95": 1130.2, "p99": 1249.1, "p999": 1249.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 350, "max": 1265, "count": 43, "mean": 557.1, "p50": 415.8, "median": 415.8, "p75": 528.6, "p90": 925.4, "p95": 1130.2, "p99": 1249.1, "p999": 1249.1}, "vusers.session_length": {"min": 1357.8, "max": 2276.2, "count": 37, "mean": 1570.2, "p50": 1408.4, "median": 1408.4, "p75": 1525.7, "p90": 1978.7, "p95": 2186.8, "p99": 2276.1, "p999": 2276.1}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 209700, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 50, "vusers.failed": 0, "vusers.completed": 48, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659690038, "firstHistogramAt": 1753659690038, "lastCounterAt": 1753659699957, "lastHistogramAt": 1753659699957, "firstMetricAt": 1753659690038, "lastMetricAt": 1753659699957, "period": "1753659690000", "summaries": {"http.response_time": {"min": 338, "max": 893, "count": 50, "mean": 475.9, "p50": 399.5, "median": 399.5, "p75": 459.5, "p90": 788.5, "p95": 837.3, "p99": 854.2, "p999": 854.2}, "http.response_time.2xx": {"min": 338, "max": 893, "count": 50, "mean": 475.9, "p50": 399.5, "median": 399.5, "p75": 459.5, "p90": 788.5, "p95": 837.3, "p99": 854.2, "p999": 854.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 338, "max": 893, "count": 50, "mean": 475.9, "p50": 399.5, "median": 399.5, "p75": 459.5, "p90": 788.5, "p95": 837.3, "p99": 854.2, "p999": 854.2}, "vusers.session_length": {"min": 1341.9, "max": 1938.6, "count": 48, "mean": 1467, "p50": 1408.4, "median": 1408.4, "p75": 1465.9, "p90": 1755, "p95": 1826.6, "p99": 1901.1, "p999": 1901.1}}, "histograms": {"http.response_time": {"min": 338, "max": 893, "count": 50, "mean": 475.9, "p50": 399.5, "median": 399.5, "p75": 459.5, "p90": 788.5, "p95": 837.3, "p99": 854.2, "p999": 854.2}, "http.response_time.2xx": {"min": 338, "max": 893, "count": 50, "mean": 475.9, "p50": 399.5, "median": 399.5, "p75": 459.5, "p90": 788.5, "p95": 837.3, "p99": 854.2, "p999": 854.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 338, "max": 893, "count": 50, "mean": 475.9, "p50": 399.5, "median": 399.5, "p75": 459.5, "p90": 788.5, "p95": 837.3, "p99": 854.2, "p999": 854.2}, "vusers.session_length": {"min": 1341.9, "max": 1938.6, "count": 48, "mean": 1467, "p50": 1408.4, "median": 1408.4, "p75": 1465.9, "p90": 1755, "p95": 1826.6, "p99": 1901.1, "p999": 1901.1}}}, {"counters": {"http.codes.200": 47, "http.responses": 47, "http.downloaded_bytes": 197118, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 47, "vusers.failed": 0, "vusers.completed": 54, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659700015, "firstHistogramAt": 1753659700015, "lastCounterAt": 1753659709974, "lastHistogramAt": 1753659709974, "firstMetricAt": 1753659700015, "lastMetricAt": 1753659709974, "period": "1753659700000", "summaries": {"http.response_time": {"min": 331, "max": 534, "count": 47, "mean": 390.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 432.7, "p95": 459.5, "p99": 507.8, "p999": 507.8}, "http.response_time.2xx": {"min": 331, "max": 534, "count": 47, "mean": 390.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 432.7, "p95": 459.5, "p99": 507.8, "p999": 507.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 331, "max": 534, "count": 47, "mean": 390.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 432.7, "p95": 459.5, "p99": 507.8, "p999": 507.8}, "vusers.session_length": {"min": 1335.8, "max": 1908.6, "count": 54, "mean": 1441.3, "p50": 1408.4, "median": 1408.4, "p75": 1436.8, "p90": 1525.7, "p95": 1790.4, "p99": 1863.5, "p999": 1863.5}}, "histograms": {"http.response_time": {"min": 331, "max": 534, "count": 47, "mean": 390.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 432.7, "p95": 459.5, "p99": 507.8, "p999": 507.8}, "http.response_time.2xx": {"min": 331, "max": 534, "count": 47, "mean": 390.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 432.7, "p95": 459.5, "p99": 507.8, "p999": 507.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 331, "max": 534, "count": 47, "mean": 390.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 432.7, "p95": 459.5, "p99": 507.8, "p999": 507.8}, "vusers.session_length": {"min": 1335.8, "max": 1908.6, "count": 54, "mean": 1441.3, "p50": 1408.4, "median": 1408.4, "p75": 1436.8, "p90": 1525.7, "p95": 1790.4, "p99": 1863.5, "p999": 1863.5}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 51, "http.codes.200": 54, "http.responses": 54, "http.downloaded_bytes": 226476, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 54, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659710004, "firstHistogramAt": 1753659710004, "lastCounterAt": 1753659719986, "lastHistogramAt": 1753659719986, "firstMetricAt": 1753659710004, "lastMetricAt": 1753659719986, "period": "1753659710000", "summaries": {"vusers.session_length": {"min": 1345.9, "max": 1872.4, "count": 51, "mean": 1432.6, "p50": 1380.5, "median": 1380.5, "p75": 1408.4, "p90": 1436.8, "p95": 1826.6, "p99": 1863.5, "p999": 1863.5}, "http.response_time": {"min": 334, "max": 866, "count": 54, "mean": 420.1, "p50": 383.8, "median": 383.8, "p75": 399.5, "p90": 424.2, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "http.response_time.2xx": {"min": 334, "max": 866, "count": 54, "mean": 420.1, "p50": 383.8, "median": 383.8, "p75": 399.5, "p90": 424.2, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 334, "max": 866, "count": 54, "mean": 420.1, "p50": 383.8, "median": 383.8, "p75": 399.5, "p90": 424.2, "p95": 788.5, "p99": 854.2, "p999": 854.2}}, "histograms": {"vusers.session_length": {"min": 1345.9, "max": 1872.4, "count": 51, "mean": 1432.6, "p50": 1380.5, "median": 1380.5, "p75": 1408.4, "p90": 1436.8, "p95": 1826.6, "p99": 1863.5, "p999": 1863.5}, "http.response_time": {"min": 334, "max": 866, "count": 54, "mean": 420.1, "p50": 383.8, "median": 383.8, "p75": 399.5, "p90": 424.2, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "http.response_time.2xx": {"min": 334, "max": 866, "count": 54, "mean": 420.1, "p50": 383.8, "median": 383.8, "p75": 399.5, "p90": 424.2, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 334, "max": 866, "count": 54, "mean": 420.1, "p50": 383.8, "median": 383.8, "p75": 399.5, "p90": 424.2, "p95": 788.5, "p99": 854.2, "p999": 854.2}}}, {"counters": {"http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 213894, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 51, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659720005, "firstHistogramAt": 1753659720005, "lastCounterAt": 1753659729992, "lastHistogramAt": 1753659729992, "firstMetricAt": 1753659720005, "lastMetricAt": 1753659729992, "period": "1753659720000", "summaries": {"http.response_time": {"min": 331, "max": 793, "count": 51, "mean": 399.2, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 415.8, "p95": 713.5, "p99": 788.5, "p999": 788.5}, "http.response_time.2xx": {"min": 331, "max": 793, "count": 51, "mean": 399.2, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 415.8, "p95": 713.5, "p99": 788.5, "p999": 788.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 331, "max": 793, "count": 51, "mean": 399.2, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 415.8, "p95": 713.5, "p99": 788.5, "p999": 788.5}, "vusers.session_length": {"min": 1336.2, "max": 1799.2, "count": 50, "mean": 1408.9, "p50": 1380.5, "median": 1380.5, "p75": 1380.5, "p90": 1436.8, "p95": 1720.2, "p99": 1790.4, "p999": 1790.4}}, "histograms": {"http.response_time": {"min": 331, "max": 793, "count": 51, "mean": 399.2, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 415.8, "p95": 713.5, "p99": 788.5, "p999": 788.5}, "http.response_time.2xx": {"min": 331, "max": 793, "count": 51, "mean": 399.2, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 415.8, "p95": 713.5, "p99": 788.5, "p999": 788.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 331, "max": 793, "count": 51, "mean": 399.2, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 415.8, "p95": 713.5, "p99": 788.5, "p999": 788.5}, "vusers.session_length": {"min": 1336.2, "max": 1799.2, "count": 50, "mean": 1408.9, "p50": 1380.5, "median": 1380.5, "p75": 1380.5, "p90": 1436.8, "p95": 1720.2, "p99": 1790.4, "p999": 1790.4}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 209700, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 50, "vusers.failed": 0, "vusers.completed": 45}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659730406, "firstHistogramAt": 1753659730739, "lastCounterAt": 1753659739995, "lastHistogramAt": 1753659739995, "firstMetricAt": 1753659730406, "lastMetricAt": 1753659739995, "period": "1753659730000", "summaries": {"http.response_time": {"min": 332, "max": 917, "count": 50, "mean": 432.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 550.1, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "http.response_time.2xx": {"min": 332, "max": 917, "count": 50, "mean": 432.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 550.1, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 332, "max": 917, "count": 50, "mean": 432.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 550.1, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "vusers.session_length": {"min": 1332.1, "max": 1561, "count": 45, "mean": 1397.9, "p50": 1380.5, "median": 1380.5, "p75": 1408.4, "p90": 1436.8, "p95": 1465.9, "p99": 1556.5, "p999": 1556.5}}, "histograms": {"http.response_time": {"min": 332, "max": 917, "count": 50, "mean": 432.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 550.1, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "http.response_time.2xx": {"min": 332, "max": 917, "count": 50, "mean": 432.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 550.1, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 332, "max": 917, "count": 50, "mean": 432.4, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 550.1, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "vusers.session_length": {"min": 1332.1, "max": 1561, "count": 45, "mean": 1397.9, "p50": 1380.5, "median": 1380.5, "p75": 1408.4, "p90": 1436.8, "p95": 1465.9, "p99": 1556.5, "p999": 1556.5}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 122, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 142, "vusers.created": 142, "errors.Undefined function \"generateBaselineFilters\"": 142, "http.requests": 142, "http.codes.200": 130, "http.responses": 130, "http.downloaded_bytes": 545222, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 130}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659740304, "firstHistogramAt": 1753659740304, "lastCounterAt": 1753659749967, "lastHistogramAt": 1753659749962, "firstMetricAt": 1753659740304, "lastMetricAt": 1753659749967, "period": "1753659740000", "summaries": {"vusers.session_length": {"min": 1345.1, "max": 2410.9, "count": 122, "mean": 1623.2, "p50": 1556.5, "median": 1556.5, "p75": 1720.2, "p90": 1939.5, "p95": 2101.1, "p99": 2276.1, "p999": 2416.8}, "http.response_time": {"min": 343, "max": 1407, "count": 130, "mean": 644, "p50": 608, "median": 608, "p75": 742.6, "p90": 944, "p95": 1085.9, "p99": 1380.5, "p999": 1408.4}, "http.response_time.2xx": {"min": 343, "max": 1407, "count": 130, "mean": 644, "p50": 608, "median": 608, "p75": 742.6, "p90": 944, "p95": 1085.9, "p99": 1380.5, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 343, "max": 1407, "count": 130, "mean": 644, "p50": 608, "median": 608, "p75": 742.6, "p90": 944, "p95": 1085.9, "p99": 1380.5, "p999": 1408.4}}, "histograms": {"vusers.session_length": {"min": 1345.1, "max": 2410.9, "count": 122, "mean": 1623.2, "p50": 1556.5, "median": 1556.5, "p75": 1720.2, "p90": 1939.5, "p95": 2101.1, "p99": 2276.1, "p999": 2416.8}, "http.response_time": {"min": 343, "max": 1407, "count": 130, "mean": 644, "p50": 608, "median": 608, "p75": 742.6, "p90": 944, "p95": 1085.9, "p99": 1380.5, "p999": 1408.4}, "http.response_time.2xx": {"min": 343, "max": 1407, "count": 130, "mean": 644, "p50": 608, "median": 608, "p75": 742.6, "p90": 944, "p95": 1085.9, "p99": 1380.5, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 343, "max": 1407, "count": 130, "mean": 644, "p50": 608, "median": 608, "p75": 742.6, "p90": 944, "p95": 1085.9, "p99": 1380.5, "p999": 1408.4}}}, {"counters": {"http.codes.200": 152, "http.responses": 152, "http.downloaded_bytes": 637488, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 152, "vusers.failed": 0, "vusers.completed": 147, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659750002, "firstHistogramAt": 1753659750002, "lastCounterAt": 1753659759990, "lastHistogramAt": 1753659759990, "firstMetricAt": 1753659750002, "lastMetricAt": 1753659759990, "period": "1753659750000", "summaries": {"http.response_time": {"min": 463, "max": 1946, "count": 152, "mean": 1277.1, "p50": 1353.1, "median": 1353.1, "p75": 1465.9, "p90": 1587.9, "p95": 1686.1, "p99": 1755, "p999": 1755}, "http.response_time.2xx": {"min": 463, "max": 1946, "count": 152, "mean": 1277.1, "p50": 1353.1, "median": 1353.1, "p75": 1465.9, "p90": 1587.9, "p95": 1686.1, "p99": 1755, "p999": 1755}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 463, "max": 1946, "count": 152, "mean": 1277.1, "p50": 1353.1, "median": 1353.1, "p75": 1465.9, "p90": 1587.9, "p95": 1686.1, "p99": 1755, "p999": 1755}, "vusers.session_length": {"min": 1529.5, "max": 2953.1, "count": 147, "mean": 2308.9, "p50": 2369, "median": 2369, "p75": 2465.6, "p90": 2618.1, "p95": 2725, "p99": 2780, "p999": 2780}}, "histograms": {"http.response_time": {"min": 463, "max": 1946, "count": 152, "mean": 1277.1, "p50": 1353.1, "median": 1353.1, "p75": 1465.9, "p90": 1587.9, "p95": 1686.1, "p99": 1755, "p999": 1755}, "http.response_time.2xx": {"min": 463, "max": 1946, "count": 152, "mean": 1277.1, "p50": 1353.1, "median": 1353.1, "p75": 1465.9, "p90": 1587.9, "p95": 1686.1, "p99": 1755, "p999": 1755}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 463, "max": 1946, "count": 152, "mean": 1277.1, "p50": 1353.1, "median": 1353.1, "p75": 1465.9, "p90": 1587.9, "p95": 1686.1, "p99": 1755, "p999": 1755}, "vusers.session_length": {"min": 1529.5, "max": 2953.1, "count": 147, "mean": 2308.9, "p50": 2369, "median": 2369, "p75": 2465.6, "p90": 2618.1, "p95": 2725, "p99": 2780, "p999": 2780}}}, {"counters": {"http.codes.200": 102, "http.responses": 102, "http.downloaded_bytes": 427791, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 102, "vusers.failed": 0, "vusers.completed": 111, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659760024, "firstHistogramAt": 1753659760060, "lastCounterAt": 1753659769968, "lastHistogramAt": 1753659769944, "firstMetricAt": 1753659760024, "lastMetricAt": 1753659769968, "period": "1753659760000", "summaries": {"http.response_time": {"min": 361, "max": 4287, "count": 102, "mean": 2246.4, "p50": 2369, "median": 2369, "p75": 3395.5, "p90": 3605.5, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}, "http.response_time.2xx": {"min": 361, "max": 4287, "count": 102, "mean": 2246.4, "p50": 2369, "median": 2369, "p75": 3395.5, "p90": 3605.5, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 361, "max": 4287, "count": 102, "mean": 2246.4, "p50": 2369, "median": 2369, "p75": 3395.5, "p90": 3605.5, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}, "vusers.session_length": {"min": 1375.5, "max": 5290.8, "count": 111, "mean": 2792.8, "p50": 2276.1, "median": 2276.1, "p75": 3984.7, "p90": 4492.8, "p95": 4676.2, "p99": 4770.6, "p999": 4867}}, "histograms": {"http.response_time": {"min": 361, "max": 4287, "count": 102, "mean": 2246.4, "p50": 2369, "median": 2369, "p75": 3395.5, "p90": 3605.5, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}, "http.response_time.2xx": {"min": 361, "max": 4287, "count": 102, "mean": 2246.4, "p50": 2369, "median": 2369, "p75": 3395.5, "p90": 3605.5, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 361, "max": 4287, "count": 102, "mean": 2246.4, "p50": 2369, "median": 2369, "p75": 3395.5, "p90": 3605.5, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}, "vusers.session_length": {"min": 1375.5, "max": 5290.8, "count": 111, "mean": 2792.8, "p50": 2276.1, "median": 2276.1, "p75": 3984.7, "p90": 4492.8, "p95": 4676.2, "p99": 4770.6, "p999": 4867}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 63, "http.responses": 63, "http.downloaded_bytes": 264236, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 63, "vusers.failed": 0, "vusers.completed": 71}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659770024, "firstHistogramAt": 1753659770052, "lastCounterAt": 1753659779972, "lastHistogramAt": 1753659779844, "firstMetricAt": 1753659770024, "lastMetricAt": 1753659779972, "period": "1753659770000", "summaries": {"http.response_time": {"min": 3617, "max": 9370, "count": 63, "mean": 5995.2, "p50": 5487.5, "median": 5487.5, "p75": 6439.7, "p90": 8868.4, "p95": 9047.6, "p99": 9230.4, "p999": 9230.4}, "http.response_time.2xx": {"min": 3617, "max": 9370, "count": 63, "mean": 5995.2, "p50": 5487.5, "median": 5487.5, "p75": 6439.7, "p90": 8868.4, "p95": 9047.6, "p99": 9230.4, "p999": 9230.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 3617, "max": 9370, "count": 63, "mean": 5995.2, "p50": 5487.5, "median": 5487.5, "p75": 6439.7, "p90": 8868.4, "p95": 9047.6, "p99": 9230.4, "p999": 9230.4}, "vusers.session_length": {"min": 4423.9, "max": 9888.4, "count": 71, "mean": 6257.9, "p50": 6312.2, "median": 6312.2, "p75": 6838, "p90": 8352, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}}, "histograms": {"http.response_time": {"min": 3617, "max": 9370, "count": 63, "mean": 5995.2, "p50": 5487.5, "median": 5487.5, "p75": 6439.7, "p90": 8868.4, "p95": 9047.6, "p99": 9230.4, "p999": 9230.4}, "http.response_time.2xx": {"min": 3617, "max": 9370, "count": 63, "mean": 5995.2, "p50": 5487.5, "median": 5487.5, "p75": 6439.7, "p90": 8868.4, "p95": 9047.6, "p99": 9230.4, "p999": 9230.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 3617, "max": 9370, "count": 63, "mean": 5995.2, "p50": 5487.5, "median": 5487.5, "p75": 6439.7, "p90": 8868.4, "p95": 9047.6, "p99": 9230.4, "p999": 9230.4}, "vusers.session_length": {"min": 4423.9, "max": 9888.4, "count": 71, "mean": 6257.9, "p50": 6312.2, "median": 6312.2, "p75": 6838, "p90": 8352, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}}}, {"counters": {"http.codes.200": 14, "http.responses": 14, "http.downloaded_bytes": 58716, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).codes.200": 14, "vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "vusers.failed": 131, "vusers.completed": 20, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 131, "errors.ETIMEDOUT": 131}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659780013, "firstHistogramAt": 1753659780013, "lastCounterAt": 1753659789978, "lastHistogramAt": 1753659783760, "firstMetricAt": 1753659780013, "lastMetricAt": 1753659789978, "period": "1753659780000", "summaries": {"http.response_time": {"min": 8977, "max": 9885, "count": 14, "mean": 9620, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 8977, "max": 9885, "count": 14, "mean": 9620, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 8977, "max": 9885, "count": 14, "mean": 9620, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 9982.2, "max": 10901.8, "count": 20, "mean": 10493.6, "p50": 10407.3, "median": 10407.3, "p75": 10832, "p90": 10832, "p95": 10832, "p99": 10832, "p999": 10832}}, "histograms": {"http.response_time": {"min": 8977, "max": 9885, "count": 14, "mean": 9620, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 8977, "max": 9885, "count": 14, "mean": 9620, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Ultra Fast)": {"min": 8977, "max": 9885, "count": 14, "mean": 9620, "p50": 9607.1, "median": 9607.1, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 9982.2, "max": 10901.8, "count": 20, "mean": 10493.6, "p50": 10407.3, "median": 10407.3, "p75": 10832, "p90": 10832, "p95": 10832, "p99": 10832, "p999": 10832}}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659790024, "lastCounterAt": 1753659799973, "firstMetricAt": 1753659790024, "lastMetricAt": 1753659799973, "period": "1753659790000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659800024, "lastCounterAt": 1753659809968, "firstMetricAt": 1753659800024, "lastMetricAt": 1753659809968, "period": "1753659800000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659810024, "lastCounterAt": 1753659819980, "firstMetricAt": 1753659810024, "lastMetricAt": 1753659819980, "period": "1753659810000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659820024, "lastCounterAt": 1753659829967, "firstMetricAt": 1753659820024, "lastMetricAt": 1753659829967, "period": "1753659820000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659830024, "lastCounterAt": 1753659839970, "firstMetricAt": 1753659830024, "lastMetricAt": 1753659839970, "period": "1753659830000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659840024, "lastCounterAt": 1753659849983, "firstMetricAt": 1753659840024, "lastMetricAt": 1753659849983, "period": "1753659840000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659850024, "lastCounterAt": 1753659859968, "firstMetricAt": 1753659850024, "lastMetricAt": 1753659859968, "period": "1753659850000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659860025, "lastCounterAt": 1753659869971, "firstMetricAt": 1753659860025, "lastMetricAt": 1753659869971, "period": "1753659860000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659870024, "lastCounterAt": 1753659879972, "firstMetricAt": 1753659870024, "lastMetricAt": 1753659879972, "period": "1753659870000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659880024, "lastCounterAt": 1753659889967, "firstMetricAt": 1753659880024, "lastMetricAt": 1753659889967, "period": "1753659880000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659890024, "lastCounterAt": 1753659899968, "firstMetricAt": 1753659890024, "lastMetricAt": 1753659899968, "period": "1753659890000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659900024, "lastCounterAt": 1753659909970, "firstMetricAt": 1753659900024, "lastMetricAt": 1753659909970, "period": "1753659900000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753659910024, "lastCounterAt": 1753659919967, "firstMetricAt": 1753659910024, "lastMetricAt": 1753659919967, "period": "1753659910000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 58, "vusers.created": 58, "errors.Undefined function \"generateBaselineFilters\"": 58, "http.requests": 58, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 150, "errors.ETIMEDOUT": 150, "vusers.failed": 150}, "rates": {"http.request_rate": 11}, "http.request_rate": null, "firstCounterAt": 1753659920024, "lastCounterAt": 1753659929981, "firstMetricAt": 1753659920024, "lastMetricAt": 1753659929981, "period": "1753659920000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 58, "errors.ETIMEDOUT": 58, "vusers.failed": 58}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659930026, "lastCounterAt": 1753659939633, "firstMetricAt": 1753659930026, "lastMetricAt": 1753659939633, "period": "1753659930000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659940407, "lastCounterAt": 1753659949633, "firstMetricAt": 1753659940407, "lastMetricAt": 1753659949633, "period": "1753659940000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659950407, "lastCounterAt": 1753659959633, "firstMetricAt": 1753659950407, "lastMetricAt": 1753659959633, "period": "1753659950000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659960407, "lastCounterAt": 1753659969633, "firstMetricAt": 1753659960407, "lastMetricAt": 1753659969633, "period": "1753659960000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659970407, "lastCounterAt": 1753659979633, "firstMetricAt": 1753659970407, "lastMetricAt": 1753659979633, "period": "1753659970000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - No Filters (Fastest Path)": 5, "vusers.created": 5, "errors.Undefined function \"generateBaselineFilters\"": 5, "http.requests": 5, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753659980408, "lastCounterAt": 1753659989636, "firstMetricAt": 1753659980408, "lastMetricAt": 1753659989636, "period": "1753659980000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Ultra Fast).errors.ETIMEDOUT": 5, "errors.ETIMEDOUT": 5, "vusers.failed": 5}, "rates": {}, "firstCounterAt": 1753659990420, "lastCounterAt": 1753659990637, "firstMetricAt": 1753659990420, "lastMetricAt": 1753659990637, "period": "1753659990000", "summaries": {}, "histograms": {}}]}