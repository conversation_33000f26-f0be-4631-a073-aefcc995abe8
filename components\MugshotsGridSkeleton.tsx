import { Skeleton } from "@/components/ui/skeleton"

interface MugshotsGridSkeletonProps {
  gridView?: 'large' | 'compact'
  count?: number
}

export default function MugshotsGridSkeleton({ 
  gridView = 'large', 
  count = 12 
}: MugshotsGridSkeletonProps) {
  const getGridClasses = () => {
    switch (gridView) {
      case "large":
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      case "compact":
        return "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
      default:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    }
  }

  const getCardHeight = () => {
    return gridView === 'large' ? 'h-80' : 'h-64'
  }

  return (
    <div className={`grid ${getGridClasses()} gap-6 mb-8`}>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="space-y-3">
          {/* Mugshot image skeleton */}
          <Skeleton className={`w-full ${getCardHeight()} bg-gray-700 rounded-lg`} />
          
          {/* Name skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4 bg-gray-700 mx-auto" />
            <Skeleton className="h-3 w-1/2 bg-gray-700 mx-auto" />
          </div>
          
          {/* Details skeleton */}
          <div className="space-y-1">
            <Skeleton className="h-3 w-full bg-gray-700" />
            <Skeleton className="h-3 w-2/3 bg-gray-700" />
          </div>
          
          {/* Rating skeleton */}
          <div className="flex justify-center space-x-1">
            {Array.from({ length: 5 }).map((_, starIndex) => (
              <Skeleton key={starIndex} className="h-4 w-4 bg-gray-700 rounded-full" />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
} 