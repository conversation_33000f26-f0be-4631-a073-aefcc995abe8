import { describe, it, expect } from 'vitest'
import { transformDBMugshotToUI, transformDBMugshotsToUI, calculatePagination, UIMugshot } from '@/lib/utils/mugshot-transforms'
import type { DatabaseMugshot } from '@/lib/services/mugshots-service'

// Type alias for test compatibility  
type DBMugshot = DatabaseMugshot

// Import types for test compatibility
export interface PaginationParams {
  page: number
  perPage: number
}

// Helper function to create test mugshot with all required fields
function createTestMugshot(overrides: Partial<DBMugshot>): DBMugshot {
  return {
    id: 1,
    created_at: '2024-01-01T00:00:00Z',
    firstName: 'Test',
    lastName: 'User',
    dateOfBooking: '2024-01-01',
    stateOfBooking: 'California',
    countyOfBooking: 'Los Angeles',
    offenseDescription: 'Test offense',
    additionalDetails: null,
    imagePath: '/images/test.jpg',
    fb_status: null,
    adsText: null,
    jb_post_link: null,
    jb_fb_post: false,
    ...overrides
  }
}

describe('Mugshot Data Transformation', () => {
  it('should transform database mugshot to UI format', () => {
    const dbMugshot: DBMugshot = {
      id: 1,
      created_at: '2024-01-01T00:00:00Z',
      firstName: 'John',
      lastName: 'Doe',
      dateOfBooking: '2024-01-01',
      stateOfBooking: 'California',
      countyOfBooking: 'Los Angeles',
      offenseDescription: 'Public intoxication, Disorderly conduct',
      additionalDetails: 'Additional info here',
      imagePath: '/images/mugshot-1.jpg'
    }

    const uiMugshot = transformDBMugshotToUI(dbMugshot)

    expect(uiMugshot.id).toBe(1)
    expect(uiMugshot.name).toBe('John Doe')
    expect(uiMugshot.location).toBe('Los Angeles, California')
    expect(uiMugshot.state).toBe('California')
    expect(uiMugshot.county).toBe('Los Angeles')
    expect(uiMugshot.arrestDate).toBe('2024-01-01')
    expect(uiMugshot.image).toBe('/images/mugshot-1.jpg')
    expect(uiMugshot.offenses).toEqual(['Public intoxication', 'Disorderly conduct'])
  })

  it('should handle null/empty database fields gracefully', () => {
    const dbMugshot: DBMugshot = {
      id: 2,
      created_at: '2024-01-01T00:00:00Z',
      firstName: null,
      lastName: null,
      dateOfBooking: null,
      stateOfBooking: null,
      countyOfBooking: null,
      offenseDescription: null,
      additionalDetails: null,
      imagePath: null
    }

    const uiMugshot = transformDBMugshotToUI(dbMugshot)

    expect(uiMugshot.name).toBe('Unknown')
    expect(uiMugshot.location).toBe('Unknown')
    expect(uiMugshot.arrestDate).toBe('')
    expect(uiMugshot.image).toBe('/images/mugshot-placeholder.png')
    expect(uiMugshot.offenses).toEqual([])
  })

  it('should handle partial name data', () => {
    const dbMugshot: DBMugshot = {
      id: 3,
      created_at: '2024-01-01T00:00:00Z',
      firstName: 'Jane',
      lastName: null,
      dateOfBooking: '2024-01-01',
      stateOfBooking: 'Texas',
      countyOfBooking: null,
      offenseDescription: 'DUI',
      additionalDetails: null,
      imagePath: '/images/mugshot-3.jpg'
    }

    const uiMugshot = transformDBMugshotToUI(dbMugshot)

    expect(uiMugshot.name).toBe('Jane')
    expect(uiMugshot.location).toBe('Texas')
  })

  it('should parse JSON offense descriptions', () => {
    const dbMugshot: DBMugshot = {
      id: 4,
      created_at: '2024-01-01T00:00:00Z',
      firstName: 'Bob',
      lastName: 'Smith',
      dateOfBooking: '2024-01-01',
      stateOfBooking: 'Florida',
      countyOfBooking: 'Miami-Dade',
      offenseDescription: '["Theft", "Burglary", "Resisting arrest"]',
      additionalDetails: null,
      imagePath: '/images/mugshot-4.jpg'
    }

    const uiMugshot = transformDBMugshotToUI(dbMugshot)

    expect(uiMugshot.offenses).toEqual(['Theft', 'Burglary', 'Resisting arrest'])
  })

  it('should transform array of database mugshots', () => {
    const dbMugshots: DBMugshot[] = [
      {
        id: 1,
        created_at: '2024-01-01T00:00:00Z',
        firstName: 'Alice',
        lastName: 'Johnson',
        dateOfBooking: '2024-01-01',
        stateOfBooking: 'California',
        countyOfBooking: 'San Francisco',
        offenseDescription: 'Vandalism',
        additionalDetails: null,
        imagePath: '/images/mugshot-1.jpg'
      },
      {
        id: 2,
        created_at: '2024-01-02T00:00:00Z',
        firstName: 'Robert',
        lastName: 'Wilson',
        dateOfBooking: '2024-01-02',
        stateOfBooking: 'New York',
        countyOfBooking: 'Manhattan',
        offenseDescription: 'Public disturbance',
        additionalDetails: null,
        imagePath: '/images/mugshot-2.jpg'
      }
    ]

    const uiMugshots = transformDBMugshotsToUI(dbMugshots)

    expect(uiMugshots).toHaveLength(2)
    expect(uiMugshots[0].name).toBe('Alice Johnson')
    expect(uiMugshots[1].name).toBe('Robert Wilson')
  })

  it('should calculate pagination correctly', () => {
    const totalCount = 100
    const params = { page: 3, perPage: 12 }
    
    const pagination = calculatePagination(totalCount, params)

    expect(pagination.totalPages).toBe(9) // Math.ceil(100/12) = 9
    expect(pagination.offset).toBe(24) // (3-1) * 12 = 24
    expect(pagination.hasNext).toBe(true)
    expect(pagination.hasPrevious).toBe(true)
  })

  it('should handle edge cases in pagination', () => {
    // First page
    const firstPage = calculatePagination(50, { page: 1, perPage: 12 })
    expect(firstPage.offset).toBe(0)
    expect(firstPage.hasPrevious).toBe(false)
    expect(firstPage.hasNext).toBe(true)

    // Last page
    const lastPage = calculatePagination(50, { page: 5, perPage: 12 })
    expect(lastPage.hasNext).toBe(false)
    expect(lastPage.hasPrevious).toBe(true)

    // Empty results
    const empty = calculatePagination(0, { page: 1, perPage: 12 })
    expect(empty.totalPages).toBe(0)
    expect(empty.hasNext).toBe(false)
    expect(empty.hasPrevious).toBe(false)
  })

  it('should parse offenses with dash and slash separators', () => {
    const dbMugshot: DBMugshot = createTestMugshot({
      id: 5,
      firstName: 'John',
      lastName: 'Doe',
      stateOfBooking: 'Texas',
      countyOfBooking: 'Harris',
      offenseDescription: 'Public Disorderly Conduct - Trespassing / Entering Premises After Warning Or Refusing To Leave On Request - A / Open Container Of Beer Or Wine In Motor Vehicle',
      imagePath: '/images/mugshot-5.jpg'
    })

    const uiMugshot = transformDBMugshotToUI(dbMugshot)
    
    expect(uiMugshot.offenses).toEqual([
      'Public Disorderly Conduct',
      'Trespassing',
      'Entering Premises After Warning Or Refusing To Leave On Request',
      'Open Container Of Beer Or Wine In Motor Vehicle'
    ])
  })

  it('should parse complex mixed separator offenses', () => {
    const dbMugshot: DBMugshot = createTestMugshot({
      id: 6,
      firstName: 'Jane',
      lastName: 'Smith',
      stateOfBooking: 'California',
      countyOfBooking: 'Los Angeles',
      offenseDescription: 'DRIVING UNDER THE INFLUENCE - DUI / RECKLESS DRIVING - SPEEDING / FAILURE TO MAINTAIN LANE',
      imagePath: '/images/mugshot-6.jpg'
    })

    const uiMugshot = transformDBMugshotToUI(dbMugshot)
    
    expect(uiMugshot.offenses).toEqual([
      'Driving Under The Influence',
      'Dui',
      'Reckless Driving',
      'Speeding',
      'Failure To Maintain Lane'
    ])
  })

  it('should handle newline-separated offenses with broken monetary values', () => {
    const dbMugshot: DBMugshot = createTestMugshot({
      id: 7,
      firstName: 'Bob',
      lastName: 'Wilson',
      stateOfBooking: 'Texas',
      countyOfBooking: 'Harris',
      offenseDescription: 'Carry Or Possess Firearm By Convicted Felon\nMalicious Injury Or Destruction Of Property Less Than $1\n000\nDestroying Property - Private Or Public\nProtective Order Violation',
      imagePath: '/images/mugshot-7.jpg'
    })

    const uiMugshot = transformDBMugshotToUI(dbMugshot)
    
    expect(uiMugshot.offenses).toEqual([
      'Carry Or Possess Firearm By Convicted Felon',
      'Malicious Injury Or Destruction Of Property Less Than $1000',
      'Destroying Property - Private Or Public', 
      'Protective Order Violation'
    ])
  })

  it('should handle semicolon-separated offenses with monetary values', () => {
    const dbMugshot: DBMugshot = createTestMugshot({
      id: 8,
      firstName: 'Sarah',
      lastName: 'Johnson',
      stateOfBooking: 'Florida',
      countyOfBooking: 'Miami-Dade',
      offenseDescription: 'carry or possess firearm by convicted felon; malicious injury or destruction of property less than $1,000; destroying property - private or public; PROTECTIVE ORDER VIOLATION',
      imagePath: '/images/mugshot-8.jpg'
    })

    const uiMugshot = transformDBMugshotToUI(dbMugshot)
    
    expect(uiMugshot.offenses).toEqual([
      'Carry Or Possess Firearm By Convicted Felon',
      'Malicious Injury Or Destruction Of Property Less Than $1,000',
      'Destroying Property - Private Or Public', 
      'Protective Order Violation'
    ])
  })
}) 