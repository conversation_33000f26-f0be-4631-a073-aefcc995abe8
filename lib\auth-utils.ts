import { SupabaseClient } from '@supabase/supabase-js'

export interface AuthFormData {
  email: string
  password: string
  confirmPassword?: string
  fullName?: string
  selectedState?: string
  selectedCounty?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

interface AuthUser {
  id: string
  email?: string
  user_metadata?: Record<string, unknown>
  app_metadata?: Record<string, unknown>
  created_at?: string
}

export interface AuthResult {
  success: boolean
  error?: string
  user?: AuthUser
}

export interface SignUpData {
  email: string
  password: string
  fullName: string
  selectedState: string
  selectedCounty: string
}

export interface SignInData {
  email: string
  password: string
}

export function validateAuthForm(
  data: AuthFormData,
  mode: 'signin' | 'signup'
): ValidationResult {
  const errors: Record<string, string> = {}

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!data.email || !emailRegex.test(data.email)) {
    errors.email = 'Please enter a valid email address'
  }

  // Password validation
  if (!data.password || data.password.length < 8) {
    errors.password = 'Password must be at least 8 characters long'
  } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(data.password)) {
    errors.password = 'Password must contain uppercase, lowercase, number, and special character'
  }

  if (mode === 'signup') {
    // Confirm password validation
    if (!data.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (data.password !== data.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    // Full name validation
    if (!data.fullName || data.fullName.trim().length < 2) {
      errors.fullName = 'Please enter your full name'
    }

    // State validation
    if (!data.selectedState) {
      errors.selectedState = 'Please select your state'
    }

    // County validation
    if (!data.selectedCounty) {
      errors.selectedCounty = 'Please select your county'
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

export async function signUpUser(
  supabase: SupabaseClient,
  userData: SignUpData
): Promise<AuthResult> {
  try {
    const { data, error } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.fullName,
          state: userData.selectedState,
          county: userData.selectedCounty
        }
      }
    })

    if (error) {
      return {
        success: false,
        error: error.message
      }
    }

    // Store profile data in user metadata for later profile creation
    // Profile will be created after email confirmation in the callback route
    // User metadata is stored in Supabase auth.users table automatically

    return {
      success: true,
      user: data.user || undefined
    }
  } catch {
    return {
      success: false,
      error: 'An unexpected error occurred during signup'
    }
  }
}

export async function signInUser(
  supabase: SupabaseClient,
  credentials: SignInData
): Promise<AuthResult> {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password
    })

    if (error) {
      return {
        success: false,
        error: error.message
      }
    }

    return {
      success: true,
      user: data.user
    }
  } catch {
    return {
      success: false,
      error: 'An unexpected error occurred during signin'
    }
  }
}

// Google OAuth interfaces and functions
interface OAuthError {
  message: string
  details?: string
}

interface OAuthSession {
  access_token: string
  refresh_token?: string
  user: AuthUser
}

export interface OAuthResult {
  data: { provider: string; url: string | null } | null
  error: OAuthError | null
}

export interface OAuthCallbackResult {
  session: OAuthSession | null
  error: OAuthError | null
}

export async function handleGoogleAuth(
  returnUrl: string = '/mugshots'
): Promise<OAuthResult> {
  try {
    const { createClient } = await import('./supabase/client')
    const supabase = createClient()
    
    // Construct the full redirect URL with return URL
    // For Google OAuth, we want to always check if user needs location setup
    const baseUrl = typeof window !== 'undefined' 
      ? window.location.origin 
      : process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    
    const redirectTo = `${baseUrl}/auth/callback?returnUrl=${encodeURIComponent(returnUrl)}`
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    })

    return { data, error }
  } catch {
    return {
      data: null,
      error: { message: 'Failed to initialize Google OAuth' }
    }
  }
}

export async function handleOAuthCallback(): Promise<OAuthCallbackResult> {
  try {
    const { createClient } = await import('./supabase/client')
    const supabase = createClient()
    
    const { data, error } = await supabase.auth.getSession()
    
    return {
      session: data?.session || null,
      error
    }
  } catch {
    return {
      session: null,
      error: { message: 'Failed to process OAuth callback' }
    }
  }
} 