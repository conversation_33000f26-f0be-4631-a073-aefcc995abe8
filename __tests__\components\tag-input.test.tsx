import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import TagInput from '@/components/TagInput'
import { useAuthStore } from '@/lib/stores/auth-store'
import { addTagToMugshot, getPopularTags } from '@/lib/services/tag-service'
import { toast } from 'sonner'

// Mock dependencies
vi.mock('@/lib/stores/auth-store')
vi.mock('@/lib/services/tag-service')
vi.mock('sonner')

// Mock next/router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))

describe('TagInput Component', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>'
  }

  const mockOnTagAdded = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock authenticated user by default
    vi.mocked(useAuthStore).mockReturnValue({
      user: mockUser,
      isLoading: false,
      // Add other required properties with default values
      profile: null,
      isInitialized: true,
      setUser: vi.fn(),
      setProfile: vi.fn(),
      initialize: vi.fn(),
      clearAuth: vi.fn(),
      getHomeLocation: vi.fn().mockReturnValue({ state: '', county: '' })
    })

    // Mock successful tag suggestions
    vi.mocked(getPopularTags).mockResolvedValue([
      {
        id: 'tag-1',
        name: 'Funny',
        slug: 'funny',
        usage_count: 50,
        is_trending: true
      },
      {
        id: 'tag-2',
        name: 'Wild',
        slug: 'wild',
        usage_count: 30,
        is_trending: false
      }
    ])

    // Mock successful tag addition
    vi.mocked(addTagToMugshot).mockResolvedValue({
      success: true,
      message: 'Tag added successfully!',
      tag: {
        id: 'tag-123',
        name: 'Test Tag',
        slug: 'test-tag',
        usage_count: 1
      }
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Rendering and Basic Functionality', () => {
    it('should render tag input for authenticated users', () => {
      render(<TagInput mugshotId={1} />)
      
      expect(screen.getByText('Add Community Tag')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Add a tag...')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /add/i })).toBeInTheDocument()
    })

    it('should render login prompt for unauthenticated users', () => {
      vi.mocked(useAuthStore).mockReturnValue({
        user: null,
        isLoading: false,
        profile: null,
        isInitialized: true,
        setUser: vi.fn(),
        setProfile: vi.fn(),
        initialize: vi.fn(),
        clearAuth: vi.fn(),
        getHomeLocation: vi.fn().mockReturnValue({ state: '', county: '' })
      })

      render(<TagInput mugshotId={1} />)
      
      expect(screen.getByText('Login to add tags')).toBeInTheDocument()
      expect(screen.queryByPlaceholderText('Add a tag...')).not.toBeInTheDocument()
    })

    it('should render loading state', () => {
      vi.mocked(useAuthStore).mockReturnValue({
        user: null,
        isLoading: true,
        profile: null,
        isInitialized: false,
        setUser: vi.fn(),
        setProfile: vi.fn(),
        initialize: vi.fn(),
        clearAuth: vi.fn(),
        getHomeLocation: vi.fn().mockReturnValue({ state: '', county: '' })
      })

      render(<TagInput mugshotId={1} />)
      
      expect(screen.getByText('Loading ratings...')).toBeInTheDocument()
    })

    it('should use custom placeholder', () => {
      render(<TagInput mugshotId={1} placeholder="Custom placeholder..." />)
      
      expect(screen.getByPlaceholderText('Custom placeholder...')).toBeInTheDocument()
    })

    it('should apply compact styling', () => {
      const { container } = render(<TagInput mugshotId={1} compact={true} />)
      
      // Check for compact padding class
      expect(container.querySelector('.p-3')).toBeInTheDocument()
    })
  })

  describe('Input Validation', () => {
    it('should show character count', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Test')
      
      expect(screen.getByText('4/20')).toBeInTheDocument()
    })

    it('should validate minimum length', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'A')
      await user.click(addButton)
      
      expect(screen.getByText('Tag must be at least 2 characters long')).toBeInTheDocument()
      expect(addTagToMugshot).not.toHaveBeenCalled()
    })

    it('should validate maximum length', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'This is a very long tag name that exceeds twenty characters')
      await user.click(addButton)
      
      expect(screen.getByText('Tag must be no more than 20 characters long')).toBeInTheDocument()
      expect(addTagToMugshot).not.toHaveBeenCalled()
    })

    it('should validate character format', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      
      await user.type(input, 'Tag@#$')
      
      expect(screen.getByText('Letters, numbers, spaces, hyphens only')).toBeInTheDocument()
    })

    it('should disable add button for invalid input', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'A')
      
      expect(addButton).toBeDisabled()
    })
  })

  describe('Autocomplete Functionality', () => {
    it('should show suggestions when typing', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Fun')
      
      await waitFor(() => {
        expect(getPopularTags).toHaveBeenCalledWith('Fun', 10)
      })
      
      await waitFor(() => {
        expect(screen.getByText('Funny')).toBeInTheDocument()
      })
    })

    it('should hide suggestions when input is too short', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'F')
      
      // Should not call getPopularTags for single character
      await waitFor(() => {
        expect(getPopularTags).not.toHaveBeenCalled()
      })
    })

    it('should highlight trending tags', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Fun')
      
      await waitFor(() => {
        expect(screen.getByText('Trending')).toBeInTheDocument()
      })
    })

    it('should show usage counts', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Fun')
      
      await waitFor(() => {
        expect(screen.getByText('50 uses')).toBeInTheDocument()
      })
    })

    it('should select suggestion with click', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} onTagAdded={mockOnTagAdded} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Fun')
      
      await waitFor(() => {
        expect(screen.getByText('Funny')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('Funny'))
      
      expect(addTagToMugshot).toHaveBeenCalledWith(1, 'Funny')
    })
  })

  describe('Keyboard Navigation', () => {
    it('should submit tag with Enter key', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} onTagAdded={mockOnTagAdded} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Test Tag{enter}')
      
      expect(addTagToMugshot).toHaveBeenCalledWith(1, 'Test Tag')
    })

    it('should navigate suggestions with arrow keys', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Fun')
      
      await waitFor(() => {
        expect(screen.getByText('Funny')).toBeInTheDocument()
      })
      
      // Arrow down should highlight first suggestion
      await user.keyboard('{ArrowDown}')
      
      // Enter should select highlighted suggestion
      await user.keyboard('{Enter}')
      
      expect(addTagToMugshot).toHaveBeenCalledWith(1, 'Funny')
    })

    it('should close suggestions with Escape key', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      await user.type(input, 'Fun')
      
      await waitFor(() => {
        expect(screen.getByText('Funny')).toBeInTheDocument()
      })
      
      await user.keyboard('{Escape}')
      
      await waitFor(() => {
        expect(screen.queryByText('Funny')).not.toBeInTheDocument()
      })
    })
  })

  describe('Tag Submission', () => {
    it('should successfully submit a tag', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} onTagAdded={mockOnTagAdded} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'Test Tag')
      await user.click(addButton)
      
      expect(addTagToMugshot).toHaveBeenCalledWith(1, 'Test Tag')
      
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Tag added successfully!')
        expect(mockOnTagAdded).toHaveBeenCalledWith({
          id: 'tag-123',
          name: 'Test Tag',
          slug: 'test-tag'
        })
      })
      
      // Input should be cleared
      expect(input).toHaveValue('')
    })

    it('should handle submission errors', async () => {
      vi.mocked(addTagToMugshot).mockResolvedValue({
        success: false,
        message: 'Tag already exists',
        error: 'DUPLICATE_TAG'
      })
      
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'Existing Tag')
      await user.click(addButton)
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Tag already exists')
        expect(screen.getByText('Tag already exists')).toBeInTheDocument()
      })
    })

    it('should show loading state during submission', async () => {
      // Make addTagToMugshot return a pending promise
      let resolvePromise: (value: any) => void
      const pendingPromise = new Promise((resolve) => {
        resolvePromise = resolve
      })
      vi.mocked(addTagToMugshot).mockReturnValue(pendingPromise)
      
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'Test Tag')
      await user.click(addButton)
      
      // Should show loading spinner
      expect(screen.getByTestId('loading-spinner') || document.querySelector('.animate-spin')).toBeInTheDocument()
      
      // Resolve the promise
      await act(async () => {
        resolvePromise!({
          success: true,
          message: 'Tag added successfully!',
          tag: { id: 'tag-123', name: 'Test Tag', slug: 'test-tag' }
        })
      })
    })

    it('should prevent submission for unauthenticated users', async () => {
      vi.mocked(useAuthStore).mockReturnValue({
        user: null,
        isLoading: false,
        profile: null,
        isInitialized: true,
        setUser: vi.fn(),
        setProfile: vi.fn(),
        initialize: vi.fn(),
        clearAuth: vi.fn(),
        getHomeLocation: vi.fn().mockReturnValue({ state: '', county: '' })
      })
      
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      // Should show login prompt instead of input
      expect(screen.getByText('Login to add tags')).toBeInTheDocument()
      expect(screen.queryByPlaceholderText('Add a tag...')).not.toBeInTheDocument()
    })
  })

  describe('Visual Feedback', () => {
    it('should show success indicator', async () => {
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'Test Tag')
      await user.click(addButton)
      
      await waitFor(() => {
        expect(document.querySelector('.text-green-400')).toBeInTheDocument()
      })
    })

    it('should show error indicator', async () => {
      vi.mocked(addTagToMugshot).mockResolvedValue({
        success: false,
        message: 'Error occurred',
        error: 'SERVER_ERROR'
      })
      
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'Test Tag')
      await user.click(addButton)
      
      await waitFor(() => {
        expect(document.querySelector('.text-red-400')).toBeInTheDocument()
      })
    })

    it('should clear status indicators after timeout', async () => {
      vi.useFakeTimers()
      
      const user = userEvent.setup()
      render(<TagInput mugshotId={1} />)
      
      const input = screen.getByPlaceholderText('Add a tag...')
      const addButton = screen.getByRole('button', { name: /add/i })
      
      await user.type(input, 'Test Tag')
      await user.click(addButton)
      
      await waitFor(() => {
        expect(document.querySelector('.text-green-400')).toBeInTheDocument()
      })
      
      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(2000)
      })
      
      await waitFor(() => {
        expect(document.querySelector('.text-green-400')).not.toBeInTheDocument()
      })
      
      vi.useRealTimers()
    })
  })
}) 