import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface WeeklyVotingNotificationPayload {
  competitionId: string
  votingStartDate: string
  votingEndDate: string
  candidates: Array<{
    id: string
    mugshot_id: string
    mugshot: {
      id: string
      first_name: string
      last_name: string
      image_path: string
      state_of_booking: string
      county_of_booking: string
    }
    daily_stats: {
      average_rating: number
      total_ratings: number
      win_date: string
    }
  }>
}

interface NotificationRecipient {
  user_id?: string
  email: string
  first_name?: string
  state?: string
  county?: string
  unsubscribe_token: string
}

serve(async (req: Request) => {
  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const payload: WeeklyVotingNotificationPayload = await req.json()
    
    // Get notification recipients
    const { data: recipients, error: recipientsError } = await supabase
      .rpc('get_notification_recipients', {
        p_notification_type: 'weekly_voting'
      })

    if (recipientsError) {
      console.error('Error getting recipients:', recipientsError)
      throw recipientsError
    }

    if (!recipients || recipients.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        totalRecipients: 0,
        successCount: 0,
        errorCount: 0,
        message: 'No recipients found for weekly voting notifications'
      }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      })
    }

    let successCount = 0
    let errorCount = 0

    // Send email to each recipient
    for (const recipient of recipients as NotificationRecipient[]) {
      try {
        const emailContent = generateWeeklyVotingEmail(payload, recipient)
        
        // Send email using Resend
        const emailResponse = await sendEmail({
          to: recipient.email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        })

        // Log successful send
        await supabase.rpc('log_email_notification', {
          p_user_id: recipient.user_id,
          p_email: recipient.email,
          p_notification_type: 'weekly_voting',
          p_template_name: 'weekly-voting-open',
          p_subject: emailContent.subject,
          p_event_id: payload.competitionId,
          p_event_type: 'weekly_voting',
          p_external_message_id: emailResponse.id
        })

        successCount++
      } catch (emailError) {
        console.error(`Failed to send email to ${recipient.email}:`, emailError)
        
        // Log failed send
        await supabase.from('email_notifications_log').insert({
          user_id: recipient.user_id,
          email: recipient.email,
          notification_type: 'weekly_voting',
          template_name: 'weekly-voting-open',
          subject: 'Weekly Voting Notification',
          event_id: payload.competitionId,
          event_type: 'weekly_voting',
          delivery_status: 'failed',
          error_message: emailError instanceof Error ? emailError.message : 'Unknown error'
        })

        errorCount++
      }
    }

    return new Response(JSON.stringify({
      success: true,
      totalRecipients: recipients.length,
      successCount,
      errorCount,
      competitionId: payload.competitionId
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    })

  } catch (error) {
    console.error('Weekly voting notification error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500
    })
  }
})

function generateWeeklyVotingEmail(payload: WeeklyVotingNotificationPayload, recipient: NotificationRecipient) {
  const baseUrl = Deno.env.get('SITE_URL') || 'https://americas-top-mugshots.com'
  const votingUrl = `${baseUrl}/weekly-voting?utm_source=email&utm_campaign=weekly_voting`
  const unsubscribeUrl = `${baseUrl}/unsubscribe?token=${recipient.unsubscribe_token}`
  const preferencesUrl = `${baseUrl}/preferences?token=${recipient.unsubscribe_token}`
  
  const subject = `🗳️ Weekly Voting is Now Open! - ${payload.candidates.length} Champions Competing`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Weekly Voting is Open!</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
          margin: 0; 
          padding: 0; 
          background-color: #0a0a0a; 
          line-height: 1.6;
        }
        .container { 
          max-width: 600px; 
          margin: 0 auto; 
          background-color: #1a1a1a; 
        }
        .header { 
          background: linear-gradient(135deg, #ec4899, #8b5cf6); 
          padding: 30px 20px; 
          text-align: center; 
        }
        .logo { 
          color: white; 
          font-size: 28px; 
          font-weight: bold; 
          text-decoration: none; 
          margin-bottom: 10px;
          display: block;
        }
        .header h1 {
          color: white; 
          margin: 10px 0 0 0; 
          font-size: 24px;
          font-weight: bold;
        }
        .content { 
          padding: 30px 20px; 
          color: #ffffff; 
        }
        .greeting {
          font-size: 18px;
          margin-bottom: 20px;
        }
        .candidate { 
          background-color: #262626; 
          border: 1px solid #ec4899; 
          border-radius: 12px; 
          padding: 20px; 
          margin: 20px 0; 
        }
        .candidate h4 {
          margin: 0 0 8px 0; 
          color: #ffffff;
          font-size: 18px;
        }
        .candidate-location {
          color: #888;
          margin: 5px 0;
          font-size: 14px;
        }
        .candidate-stats {
          color: #06b6d4;
          margin: 8px 0;
          font-weight: bold;
        }
        .candidate-date {
          color: #888;
          font-size: 12px;
          margin: 5px 0;
        }
        .cta-button { 
          display: inline-block; 
          background: linear-gradient(135deg, #ec4899, #8b5cf6); 
          color: white; 
          padding: 16px 32px; 
          text-decoration: none; 
          border-radius: 8px; 
          font-weight: bold; 
          font-size: 16px;
          margin: 25px 0;
          text-align: center;
        }
        .cta-section {
          text-align: center;
          margin: 30px 0;
        }
        .voting-period {
          background-color: #262626;
          border: 1px solid #8b5cf6;
          border-radius: 8px;
          padding: 15px;
          margin: 25px 0;
          text-align: center;
        }
        .voting-period strong {
          color: #8b5cf6;
        }
        .footer { 
          background-color: #262626; 
          padding: 25px 20px; 
          text-align: center; 
          color: #888; 
          font-size: 12px; 
          line-height: 1.5;
        }
        .footer p {
          margin: 8px 0;
        }
        .unsubscribe { 
          color: #888; 
          text-decoration: none; 
        }
        .unsubscribe:hover {
          color: #ec4899;
        }
        @media (max-width: 600px) {
          .content { padding: 20px 15px; }
          .candidate { padding: 15px; }
          .header { padding: 20px 15px; }
          .logo { font-size: 24px; }
          .header h1 { font-size: 20px; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <a href="${baseUrl}" class="logo">America's Top Mugshots</a>
          <h1>🗳️ Weekly Voting is Now Open!</h1>
        </div>
        
        <div class="content">
          <p class="greeting">Hey ${recipient.first_name || 'Voter'}!</p>
          
          <p>The weekly competition has begun! <strong>${payload.candidates.length} daily winners</strong> are competing for the ultimate weekly crown. Your vote decides who becomes this week's champion!</p>
          
          <div class="cta-section">
            <a href="${votingUrl}" class="cta-button">🗳️ Vote Now</a>
          </div>
          
          <h3 style="color: #ec4899; margin-top: 30px;">This Week's Candidates:</h3>
          
          ${payload.candidates.map(candidate => `
            <div class="candidate">
              <h4>${candidate.mugshot.first_name} ${candidate.mugshot.last_name}</h4>
              <p class="candidate-location">${candidate.mugshot.state_of_booking}, ${candidate.mugshot.county_of_booking}</p>
              <p class="candidate-stats">★ ${candidate.daily_stats.average_rating}/10 (${candidate.daily_stats.total_ratings} ratings)</p>
              <p class="candidate-date">Daily Winner: ${new Date(candidate.daily_stats.win_date).toLocaleDateString()}</p>
            </div>
          `).join('')}
          
          <div class="voting-period">
            <p><strong>Voting Window:</strong> ${new Date(payload.votingStartDate).toLocaleDateString()} - ${new Date(payload.votingEndDate).toLocaleDateString()}</p>
          </div>
          
          <p>Remember: You can only vote once, so choose wisely! The winner will advance to compete in this month's championship.</p>
          
          <div class="cta-section">
            <a href="${votingUrl}" class="cta-button">Cast Your Vote Now →</a>
          </div>
        </div>
        
        <div class="footer">
          <p>You're receiving this because you signed up for weekly voting alerts.</p>
          <p>
            <a href="${preferencesUrl}" class="unsubscribe">Manage Preferences</a> | 
            <a href="${unsubscribeUrl}" class="unsubscribe">Unsubscribe</a>
          </p>
          <p>© 2024 America's Top Mugshots. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Weekly Voting is Now Open!

Hey ${recipient.first_name || 'Voter'}!

The weekly competition has begun! ${payload.candidates.length} daily winners are competing for the ultimate weekly crown.

Vote now at: ${votingUrl}

This Week's Candidates:
${payload.candidates.map(candidate => 
  `- ${candidate.mugshot.first_name} ${candidate.mugshot.last_name} (${candidate.mugshot.state_of_booking}) - ${candidate.daily_stats.average_rating}/10 rating`
).join('\n')}

Voting Period: ${new Date(payload.votingStartDate).toLocaleDateString()} - ${new Date(payload.votingEndDate).toLocaleDateString()}

Manage your email preferences: ${preferencesUrl}
Unsubscribe: ${unsubscribeUrl}

© 2024 America's Top Mugshots. All rights reserved.
  `

  return { html, text, subject }
}

async function sendEmail(emailData: {
  to: string
  subject: string
  html: string
  text: string
}) {
  const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
  
  if (!RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY environment variable is not set')
  }

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      from: 'America\'s Top Mugshots <<EMAIL>>',
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text
    })
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`Email service error: ${response.status} ${response.statusText} - ${errorText}`)
  }

  return await response.json()
} 