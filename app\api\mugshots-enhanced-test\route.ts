import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { mugshotsEnhancedFastService } from '@/lib/services/mugshots-enhanced-fast-service'
import { transformDBMugshotToUI } from '@/lib/utils/mugshot-transforms'
import type { TagType } from '@/lib/constants'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse pagination
    const page = parseInt(searchParams.get('page') || '1')
    const perPage = parseInt(searchParams.get('perPage') || '12')
    
    // Parse sorting
    const sortBy = searchParams.get('sortBy') || 'newest'
    
    // Parse filters
    const filters = {
      searchTerm: searchParams.get('search') || undefined,
      state: searchParams.get('state') || undefined,
      county: searchParams.get('county') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      tags: searchParams.get('tags') || undefined, // comma-separated
      sortBy: sortBy as 'newest' | 'top-rated' | 'most-viewed'
    }

    // Get user ID for personalized data
    let userId: string | undefined
    try {
      const supabase = await createClient()
      const { data: { user } } = await supabase.auth.getUser()
      userId = user?.id
    } catch (error) {
      console.log('No authenticated user, proceeding without user-specific data')
    }

    console.log('🧪 [ENHANCED TEST] Testing enhanced fast service with filters:', filters)
    const startTime = Date.now()

    // Fetch mugshots using enhanced fast service
    const dbMugshots = await mugshotsEnhancedFastService.getMugshots(
      filters,
      { page, perPage },
      userId
    )

    const queryDuration = Date.now() - startTime
    console.log(`🧪 [ENHANCED TEST] Query completed in ${queryDuration}ms`)

    // Transform to UI format
    const mugshots = dbMugshots.map(transformDBMugshotToUI)

    // Get total count (using fast estimation)
    const totalCount = await mugshotsEnhancedFastService.getCount(filters)
    const totalPages = Math.ceil(totalCount / perPage)

    return NextResponse.json({
      success: true,
      data: {
        mugshots,
        pagination: {
          page,
          perPage,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        },
        filters,
        metadata: {
          queryDuration,
          resultCount: mugshots.length,
          service: 'enhanced-fast',
          userId: userId ? 'authenticated' : 'anonymous',
          timestamp: new Date().toISOString()
        }
      }
    })

  } catch (error) {
    console.error('❌ [ENHANCED TEST] API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch mugshots',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
