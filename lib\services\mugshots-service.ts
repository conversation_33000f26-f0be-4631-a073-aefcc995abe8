/**
 * DEPRECATED: This service has been replaced by API-based services
 * 
 * This file exists only for backward compatibility with tests.
 * All new code should use the API client from lib/services/api-client.ts
 * which properly goes through our API layer instead of direct Supabase calls.
 */

import { getMugshots, getSingleMugshot } from './api-client'
import type { UIMugshot } from '@/lib/utils/mugshot-transforms'
import type { TagType } from '@/lib/constants'

// Legacy interfaces for backward compatibility
export interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  categories?: string[]
  tags?: string[]
}

export interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

export interface PaginationOptions {
  page: number
  perPage: number
}

export interface DatabaseMugshot {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
}

/**
 * @deprecated Use API client instead
 * This service is maintained only for test compatibility
 */
class MugshotsService {
  async healthCheck(): Promise<boolean> {
    try {
      // Test with a simple API call
      const response = await getMugshots({}, {}, { page: 1, perPage: 1 })
      return response.success
    } catch {
      return false
    }
  }

  async getMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 }
  ): Promise<DatabaseMugshot[]> {
    try {
      // Use API client instead of direct Supabase calls
      const response = await getMugshots(
        {
          search: filters.searchTerm,
          state: filters.state,
          county: filters.county,
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          categories: filters.categories,
          tags: filters.tags as TagType[] | undefined
        },
        { sortBy: sortOptions.sortBy },
        {
          page: pagination.page,
          perPage: pagination.perPage,
          includeTotal: false
        }
      )

      if (response.success && response.data) {
        // Convert UI mugshots back to database format for compatibility
        return response.data.mugshots.map((mugshot: UIMugshot): DatabaseMugshot => ({
          id: mugshot.id,
          created_at: new Date().toISOString(), // Placeholder
          firstName: mugshot.name.split(' ')[0] || '',
          lastName: mugshot.name.split(' ').slice(1).join(' ') || '',
          dateOfBooking: mugshot.arrestDate || null,
          stateOfBooking: mugshot.state || null,
          countyOfBooking: mugshot.county || null,
          offenseDescription: mugshot.offenses?.join(', ') || null,
          additionalDetails: null,
          imagePath: mugshot.image || null,
          fb_status: null,
          adsText: null,
          jb_post_link: null,
          jb_fb_post: false
        }))
      } else {
        throw new Error(response.message || 'Failed to fetch mugshots')
      }
    } catch (error) {
      console.error('Error fetching mugshots:', error)
      throw error
    }
  }

  async getMugshotCount(filters: MugshotFilters = {}): Promise<number> {
    try {
      // Use API client to get total count
      const response = await getMugshots(
        {
          search: filters.searchTerm,
          state: filters.state,
          county: filters.county,
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          categories: filters.categories,
          tags: filters.tags as TagType[] | undefined
        },
        { sortBy: 'newest' },
        { page: 1, perPage: 1, includeTotal: true }
      )

      if (response.success && response.data) {
        return response.data.pagination?.totalCount || 0
      } else {
        throw new Error(response.message || 'Failed to fetch mugshot count')
      }
    } catch (error) {
      console.error('Error fetching mugshot count:', error)
      throw error
    }
  }

  async getMugshotById(id: number): Promise<DatabaseMugshot | null> {
    try {
      const response = await getSingleMugshot(id)
      
      if (response.success && response.data) {
        const mugshot = response.data.mugshot
        // Convert UI mugshot back to database format for compatibility
        return {
          id: mugshot.id,
          created_at: new Date().toISOString(), // Placeholder
          firstName: mugshot.name.split(' ')[0] || '',
          lastName: mugshot.name.split(' ').slice(1).join(' ') || '',
          dateOfBooking: mugshot.arrestDate || null,
          stateOfBooking: mugshot.state || null,
          countyOfBooking: mugshot.county || null,
          offenseDescription: mugshot.offenses?.join(', ') || null,
          additionalDetails: null,
          imagePath: mugshot.image || null,
          fb_status: null,
          adsText: null,
          jb_post_link: null,
          jb_fb_post: false
        }
      } else {
        return null
      }
    } catch (error) {
      console.error('Error fetching mugshot by ID:', error)
      return null
    }
  }
}

// Export a single instance for backward compatibility
export const mugshotsService = new MugshotsService()
