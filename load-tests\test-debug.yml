config:
  target: 'http://localhost:3000'
  phases:
    # Short test to debug the configuration
    - duration: 30
      arrivalRate: 5
      name: "Debug test phase"
  
  # Basic thresholds
  ensure:
    - http.response_time.p95: 3000
    - http.codes.200: 90

  http:
    timeout: 10
    pool: 10
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  # Test both endpoints quickly
  - name: "Debug Mugshots API"
    weight: 80
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - Debug"
          expect:
            - statusCode: 200
      - think: 1

  - name: "Debug Mugshot Details"
    weight: 20
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - Debug"
      - think: 1

processor: "./load-tests/scenarios/data-generators.js"
