
-- ===========================================
-- DROP FUNCTIONS IF THEY EXIST (safe cleanup)
-- ===========================================
drop function if exists public.search_filtered_mugshots(
  text, text, text, date, date, text[], text, integer, integer
);

drop function if exists public.count_filtered_mugshots(
  text, text, text, date, date, text[]
);

-- ===========================================
-- CREATE SEARCH FUNCTION (Unified & Optimized)
-- ===========================================
create or replace function public.search_filtered_mugshots(
  search_term text default null,
  state_filter text default null,
  county_filter text default null,
  date_from date default null,
  date_to date default null,
  tags_filter text[] default null,
  sort_by text default 'newest', -- 'top-rated' or 'newest'
  limit_count integer default 12,
  offset_count integer default 0
)
returns table (
  id bigint,
  created_at timestamp with time zone,
  "firstName" text,
  "lastName" text,
  "dateOfBooking" date,
  "stateOfBooking" text,
  "countyOfBooking" text,
  "offenseDescription" text,
  "additionalDetails" text,
  "imagePath" text,
  fb_status text,
  "adsText" text,
  jb_post_link text,
  jb_fb_post boolean,
  average_rating numeric,
  total_ratings integer,
  tag_counts jsonb
)
language sql
as $$
  with mugshots_filtered as (
    select m.*
    from mugshots m
    where
      (search_term is null or m."firstName" ilike '%' || search_term || '%' or m."lastName" ilike '%' || search_term || '%')
      and (state_filter is null or m."stateOfBooking" = state_filter)
      and (county_filter is null or m."countyOfBooking" = county_filter)
      and (date_from is null or m."dateOfBooking" >= date_from)
      and (date_to is null or m."dateOfBooking" <= date_to)
      and (
        tags_filter is null
        or exists (
          select 1 from tags t
          where t.mugshot_id = m.id
          and t.tag_type = any(tags_filter)
        )
      )
  ),
  ratings_agg as (
    select 
      mugshot_id,
      round(avg(rating)::numeric, 2) as avg_rating,
      count(*) as rating_count
    from ratings
    group by mugshot_id
  ),
  tags_agg as (
    select 
      mugshot_id,
      jsonb_object_agg(tag_type, tag_count) as tag_counts
    from (
      select 
        mugshot_id,
        tag_type,
        count(*) as tag_count
      from tags
      group by mugshot_id, tag_type
    ) tag_summary
    group by mugshot_id
  )
  select
    m.id,
    m.created_at,
    m."firstName",
    m."lastName",
    m."dateOfBooking",
    m."stateOfBooking",
    m."countyOfBooking",
    m."offenseDescription",
    m."additionalDetails",
    m."imagePath",
    m.fb_status,
    m."adsText",
    m.jb_post_link,
    m.jb_fb_post,
    coalesce(r.avg_rating, 0) as average_rating,
    coalesce(r.rating_count::integer, 0) as total_ratings,
    coalesce(t.tag_counts, '{}'::jsonb) as tag_counts
  from mugshots_filtered m
  left join ratings_agg r on r.mugshot_id = m.id
  left join tags_agg t on t.mugshot_id = m.id
  order by
    case when sort_by = 'top-rated' then 
      case when r.rating_count is not null then 1 else 0 end
    end desc,
    case when sort_by = 'top-rated' then r.avg_rating end desc nulls last,
    case when sort_by = 'top-rated' then r.rating_count end desc nulls last,
    m."dateOfBooking" desc nulls last,
    m.created_at desc
  limit limit_count
  offset offset_count;
$$;

-- ===========================================
-- CREATE COUNT FUNCTION (matching filter logic)
-- ===========================================
create or replace function public.count_filtered_mugshots(
  search_term text default null,
  state_filter text default null,
  county_filter text default null,
  date_from date default null,
  date_to date default null,
  tags_filter text[] default null
)
returns integer
language sql
as $$
  select count(*)
  from mugshots m
  where
    (search_term is null or m."firstName" ilike '%' || search_term || '%' or m."lastName" ilike '%' || search_term || '%')
    and (state_filter is null or m."stateOfBooking" = state_filter)
    and (county_filter is null or m."countyOfBooking" = county_filter)
    and (date_from is null or m."dateOfBooking" >= date_from)
    and (date_to is null or m."dateOfBooking" <= date_to)
    and (
      tags_filter is null
      or exists (
        select 1 from tags t
        where t.mugshot_id = m.id
        and t.tag_type = any(tags_filter)
      )
    );
$$;

-- ===========================================
-- GRANT EXECUTE PERMISSIONS
-- ===========================================
grant execute on function public.search_filtered_mugshots(
  text, text, text, date, date, text[], text, integer, integer
) to anon, authenticated;

grant execute on function public.count_filtered_mugshots(
  text, text, text, date, date, text[]
) to anon, authenticated;

-- ===========================================
-- PERFORMANCE-CRITICAL INDEXES
-- ===========================================

-- Tags filtering: support EXISTS and tag-type filtering
create index concurrently if not exists idx_tags_mugshot_type_performance 
  on public.tags (mugshot_id, tag_type) include (user_id, created_at);

create index concurrently if not exists idx_tags_type_array_performance 
  on public.tags (tag_type, mugshot_id);

-- Ratings aggregation performance
create index concurrently if not exists idx_ratings_mugshot_performance 
  on public.ratings (mugshot_id) include (rating, created_at);

-- Mugshots sort performance
create index concurrently if not exists idx_mugshots_booking_date_performance 
  on public.mugshots ("dateOfBooking" desc nulls last, created_at desc);

-- Top-rated sort fallback
create index concurrently if not exists idx_mugshots_rating_sort 
  on public.mugshots (id) include ("dateOfBooking", created_at, "firstName", "lastName");


------- FALLBACK

create or replace function public.fast_mugshot_fallback(
  search_term text default null,
  state_filter text default null,
  county_filter text default null,
  date_from date default null,
  date_to date default null,
  sort_by text default 'newest',
  limit_count integer default 12,
  offset_count integer default 0
)
returns table (
  id bigint,
  created_at timestamp with time zone,
  "firstName" text,
  "lastName" text,
  "dateOfBooking" date,
  "stateOfBooking" text,
  "countyOfBooking" text,
  "offenseDescription" text,
  "additionalDetails" text,
  "imagePath" text,
  fb_status text,
  "adsText" text,
  jb_post_link text,
  jb_fb_post boolean
)
language sql
as $$
  select
    m.id,
    m.created_at,
    m."firstName",
    m."lastName",
    m."dateOfBooking",
    m."stateOfBooking",
    m."countyOfBooking",
    m."offenseDescription",
    m."additionalDetails",
    m."imagePath",
    m.fb_status,
    m."adsText",
    m.jb_post_link,
    m.jb_fb_post
  from mugshots m
  where
    (search_term is null or m."firstName" ilike '%' || search_term || '%' or m."lastName" ilike '%' || search_term || '%')
    and (state_filter is null or m."stateOfBooking" = state_filter)
    and (county_filter is null or m."countyOfBooking" = county_filter)
    and (date_from is null or m."dateOfBooking" >= date_from)
    and (date_to is null or m."dateOfBooking" <= date_to)
  order by
    case when sort_by = 'newest' then m."dateOfBooking" end desc nulls last,
    m.created_at desc
  limit limit_count
  offset offset_count
$$;


create or replace function public.count_fast_mugshot_fallback(
  search_term text default null,
  state_filter text default null,
  county_filter text default null,
  date_from date default null,
  date_to date default null
)
returns integer
language sql
as $$
  select count(*)
  from mugshots m
  where
    (search_term is null or m."firstName" ilike '%' || search_term || '%' or m."lastName" ilike '%' || search_term || '%')
    and (state_filter is null or m."stateOfBooking" = state_filter)
    and (county_filter is null or m."countyOfBooking" = county_filter)
    and (date_from is null or m."dateOfBooking" >= date_from)
    and (date_to is null or m."dateOfBooking" <= date_to)
$$;


grant execute on function public.fast_mugshot_fallback(
  text, text, text, date, date, text, integer, integer
) to anon, authenticated;

grant execute on function public.count_fast_mugshot_fallback(
  text, text, text, date, date
) to anon, authenticated;
