# Test 3: 50 Concurrent Users - Medium Load Test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm-up: 10 users/sec"
    - duration: 240
      arrivalRate: 25
      name: "Steady: 25 users/sec (50 concurrent)"
    - duration: 60
      arrivalRate: 10
      name: "Cool-down: 10 users/sec"
  
  ensure:
    - http.response_time.p95: 2000   # 95% under 2 seconds
    - http.response_time.median: 500 # Median under 500ms
    - http.codes.200: 90             # 90% success rate
    - http.codes.5xx: 5              # Less than 5% server errors

  http:
    timeout: 20
    pool: 60
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "50 Users - Mugshots API"
    weight: 70
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - 50 users"
      - think: 1.5

  - name: "50 Users - Details API"
    weight: 30
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 50 users"
      - think: 2

processor: "../scenarios/data-generators.js"
