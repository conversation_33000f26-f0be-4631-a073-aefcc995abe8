/**
 * Available States Service
 * 
 * Service for querying the available_states_with_coordinates view
 * and integrating with distance calculations to find the nearest
 * available state with mugshot data.
 */

import { createClient } from '@/lib/supabase/client'
import type { AvailableStateWithCoordinates, Coordinates, StateDistance } from '@/lib/utils/distance-utils'
import { findNearestAvailableState, calculateDistancesToAllStates, validateCoordinates } from '@/lib/utils/distance-utils'
import { handleLocationError } from '@/lib/utils/location-error-handling'

// Service response interfaces
export interface AvailableStatesResponse {
  success: boolean
  data: AvailableStateWithCoordinates[]
  error?: string
}

export interface NearestStateResponse {
  success: boolean
  data: {
    nearestState: string
    distance: number
    coordinates: Coordinates
    totalMugshots: number
    allStatesWithDistances?: StateDistance[]
  } | null
  error?: string
}

/**
 * Available States Service Class
 */
export class AvailableStatesService {
  private static instance: AvailableStatesService
  private supabase = createClient()

  private constructor() {}

  static getInstance(): AvailableStatesService {
    if (!AvailableStatesService.instance) {
      AvailableStatesService.instance = new AvailableStatesService()
    }
    return AvailableStatesService.instance
  }

  /**
   * Get all available states with coordinates by querying tables directly
   * This bypasses the potentially problematic view
   */
  async getAvailableStates(): Promise<AvailableStatesResponse> {
    try {
      console.log('🔍 Fetching available states directly from state_coordinates table...')

      // First, get all state coordinates
      const { data: stateCoords, error: coordsError } = await this.supabase
        .from('state_coordinates')
        .select('state_name, latitude, longitude')
        .order('state_name')

      if (coordsError) {
        console.error('Error fetching state coordinates:', coordsError)
        return {
          success: false,
          data: [],
          error: `State coordinates error: ${coordsError.message}`
        }
      }

      if (!stateCoords || stateCoords.length === 0) {
        console.warn('No state coordinates found')
        return {
          success: false,
          data: [],
          error: 'No state coordinates available'
        }
      }

      console.log(`📍 Found ${stateCoords.length} states with coordinates`)

      // Get mugshot counts for each state
      const statesWithCounts: AvailableStateWithCoordinates[] = []

      for (const state of stateCoords) {
        try {
          const { count, error: countError } = await this.supabase
            .from('mugshots')
            .select('*', { count: 'exact', head: true })
            .eq('stateOfBooking', state.state_name)

          const mugshotCount = countError ? 0 : (count || 0)

          // Only include states that have mugshots
          if (mugshotCount > 0) {
            statesWithCounts.push({
              state_name: state.state_name,
              latitude: Number(state.latitude),
              longitude: Number(state.longitude),
              total_mugshots: mugshotCount
            })
          }
        } catch (error) {
          console.warn(`Failed to get count for ${state.state_name}:`, error)
          // Include state with 0 count as fallback
          statesWithCounts.push({
            state_name: state.state_name,
            latitude: Number(state.latitude),
            longitude: Number(state.longitude),
            total_mugshots: 0
          })
        }
      }

      console.log(`✅ Found ${statesWithCounts.length} states with mugshot data`)

      return {
        success: true,
        data: statesWithCounts
      }

    } catch (error) {
      const { errorInfo } = handleLocationError(error, 'available-states-service')
      return {
        success: false,
        data: [],
        error: errorInfo.userMessage
      }
    }
  }

  /**
   * Find the nearest available state based on user coordinates
   */
  async findNearestState(
    userCoordinates: Coordinates,
    includeAllDistances = false
  ): Promise<NearestStateResponse> {
    try {
      // Validate user coordinates
      if (!validateCoordinates(userCoordinates)) {
        return {
          success: false,
          data: null,
          error: 'Invalid user coordinates'
        }
      }

      // Get available states
      const statesResponse = await this.getAvailableStates()
      if (!statesResponse.success || statesResponse.data.length === 0) {
        return {
          success: false,
          data: null,
          error: statesResponse.error || 'No available states found'
        }
      }

      // Find nearest state
      const nearestState = findNearestAvailableState(userCoordinates, statesResponse.data)
      if (!nearestState) {
        return {
          success: false,
          data: null,
          error: 'Could not determine nearest state'
        }
      }

      // Optionally calculate distances to all states
      let allStatesWithDistances: StateDistance[] | undefined
      if (includeAllDistances) {
        allStatesWithDistances = calculateDistancesToAllStates(userCoordinates, statesResponse.data)
      }

      return {
        success: true,
        data: {
          nearestState: nearestState.state_name,
          distance: nearestState.distance_miles,
          coordinates: nearestState.coordinates,
          totalMugshots: nearestState.total_mugshots,
          allStatesWithDistances
        }
      }

    } catch (error) {
      const { errorInfo } = handleLocationError(error, 'find-nearest-state')
      return {
        success: false,
        data: null,
        error: errorInfo.userMessage
      }
    }
  }

  /**
   * Get the nearest state with fallback handling
   * This method provides a robust fallback chain for production use
   */
  async getNearestStateWithFallback(
    userCoordinates: Coordinates,
    fallbackState = 'California'
  ): Promise<string> {
    try {
      const result = await this.findNearestState(userCoordinates)
      
      if (result.success && result.data) {
        return result.data.nearestState
      }

      // First fallback: check if fallback state is available
      const statesResponse = await this.getAvailableStates()
      if (statesResponse.success) {
        const fallbackExists = statesResponse.data.find(
          state => state.state_name === fallbackState
        )
        if (fallbackExists) {
          console.warn(`Using fallback state: ${fallbackState}`)
          return fallbackState
        }

        // Second fallback: use first available state
        if (statesResponse.data.length > 0) {
          const firstAvailable = statesResponse.data[0].state_name
          console.warn(`Using first available state: ${firstAvailable}`)
          return firstAvailable
        }
      }

      // Final fallback: hardcoded default
      console.error('All fallbacks failed, using hardcoded default')
      return fallbackState

    } catch (error) {
      console.error('Error in getNearestStateWithFallback:', error)
      return fallbackState
    }
  }

  /**
   * Check if a specific state is available (has mugshot data)
   */
  async isStateAvailable(stateName: string): Promise<boolean> {
    try {
      // Check if state exists in state_coordinates
      const { data: stateExists, error: stateError } = await this.supabase
        .from('state_coordinates')
        .select('state_name')
        .eq('state_name', stateName)
        .single()

      if (stateError || !stateExists) {
        return false
      }

      // Check if state has mugshots
      const { count, error: countError } = await this.supabase
        .from('mugshots')
        .select('*', { count: 'exact', head: true })
        .eq('stateOfBooking', stateName)

      return !countError && (count || 0) > 0
    } catch {
      return false
    }
  }

  /**
   * Get total mugshot count for a specific state
   */
  async getStateMugshotCount(stateName: string): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('mugshots')
        .select('*', { count: 'exact', head: true })
        .eq('stateOfBooking', stateName)

      if (error) {
        console.warn(`Error getting count for ${stateName}:`, error)
        return 0
      }

      return count || 0
    } catch {
      return 0
    }
  }

  /**
   * Get states within a certain distance radius
   */
  async getStatesWithinRadius(
    userCoordinates: Coordinates,
    radiusMiles: number
  ): Promise<StateDistance[]> {
    try {
      const statesResponse = await this.getAvailableStates()
      if (!statesResponse.success) {
        return []
      }

      const allDistances = calculateDistancesToAllStates(userCoordinates, statesResponse.data)
      return allDistances.filter(state => state.distance_miles <= radiusMiles)

    } catch (error) {
      console.error('Error getting states within radius:', error)
      return []
    }
  }
}

// Export singleton instance
export const availableStatesService = AvailableStatesService.getInstance()
