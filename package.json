{"name": "americas-top-mugshot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "e2e": "playwright test", "load-test": "artillery run load-tests/artillery.yml --output load-tests/results/full-test.json", "load-test:quick": "artillery run load-tests/quick-test.yml --output load-tests/results/quick-test.json", "load-test:report": "artillery run load-tests/artillery.yml --output load-tests/results/report.json && artillery report load-tests/results/report.json", "load-test:010": "artillery run load-tests/graduated-tests/test-010-users.yml --output load-tests/results/test-010-users.json", "load-test:025": "artillery run load-tests/graduated-tests/test-025-users.yml --output load-tests/results/test-025-users.json", "load-test:050": "artillery run load-tests/graduated-tests/test-050-users.yml --output load-tests/results/test-050-users.json", "load-test:100": "artillery run load-tests/graduated-tests/test-100-users.yml --output load-tests/results/test-100-users.json", "load-test:250": "artillery run load-tests/graduated-tests/test-250-users.yml --output load-tests/results/test-250-users.json", "load-test:500": "artillery run load-tests/graduated-tests/test-500-users.yml --output load-tests/results/test-500-users.json", "load-test:1000": "artillery run load-tests/graduated-tests/test-1000-users.yml --output load-tests/results/test-1000-users.json", "load-test:10000": "artillery run load-tests/graduated-tests/test-10000-users.yml --output load-tests/results/test-10000-users.json", "load-test:baseline": "artillery run load-tests/focused-tests/baseline-performance.yml --output load-tests/results/baseline-performance.json", "load-test:fast": "artillery run load-tests/focused-tests/fast-queries.yml --output load-tests/results/fast-queries.json", "load-test:top-rated": "artillery run load-tests/focused-tests/top-rated-queries.yml --output load-tests/results/top-rated-queries.json", "load-test:tags": "artillery run load-tests/focused-tests/tag-queries.yml --output load-tests/results/tag-queries.json", "load-test:mixed": "artillery run load-tests/focused-tests/realistic-mixed.yml --output load-tests/results/realistic-mixed.json", "load-test:baseline-fast": "artillery run load-tests/focused-tests/baseline-performance.yml --output load-tests/results/baseline-fast.json", "load-test:fast-optimized": "artillery run load-tests/focused-tests/fast-queries.yml --output load-tests/results/fast-optimized.json"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@lottiefiles/dotlottie-react": "^0.14.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@tanstack/query-sync-storage-persister": "^5.83.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-query-persist-client": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.19.2", "jsdom": "^26.1.0", "lucide-react": "^0.523.0", "next": "15.2.4", "next-themes": "^0.4.6", "nprogress": "^0.2.0", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@supabase/supabase-js": "^2.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.7.0", "artillery": "^2.0.21", "eslint": "^9", "eslint-config-next": "15.2.4", "msw": "^2.10.4", "playwright": "^1.54.1", "supertest": "^7.1.3", "tailwindcss": "^4.1.11", "ts-mockito": "^2.6.1", "tw-animate-css": "^1.3.4", "typescript": "^5", "vitest": "^3.2.4", "zod": "^4.0.5"}}