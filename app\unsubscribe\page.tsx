import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Mail, CheckCircle, Settings, Unplug } from 'lucide-react'

export default function UnsubscribePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Manage Email Preferences
            </h1>
            <p className="text-gray-400 text-lg">
              Update your notification settings or unsubscribe from our emails
            </p>
          </div>
          
          <div className="space-y-6">
            {/* Unsubscribe Section */}
            <Card className="bg-gray-800/50 border-red-500/30">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Unplug className="w-5 h-5 text-red-400" />
                  Complete Unsubscribe
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-300">
                  Sorry to see you go! Enter your email address to unsubscribe from all notifications.
                </p>
                <div className="space-y-3">
                  <Label htmlFor="unsubscribe-email" className="text-white">
                    Email Address
                  </Label>
                  <Input
                    id="unsubscribe-email"
                    type="email"
                    placeholder="Enter your email"
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                  <Button className="w-full bg-red-600 hover:bg-red-700">
                    Unsubscribe from All Emails
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Preferences Section */}
            <Card className="bg-gray-800/50 border-cyan-500/30">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Settings className="w-5 h-5 text-cyan-400" />
                  Update Preferences
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <p className="text-gray-300">
                  Or adjust your email preferences to receive only what you want.
                </p>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white">Daily Winners</Label>
                      <p className="text-sm text-gray-400">Get notified about daily contest winners</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white">Weekly Voting Alerts</Label>
                      <p className="text-sm text-gray-400">Weekly reminders to vote on your favorites</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white">Monthly Championships</Label>
                      <p className="text-sm text-gray-400">Updates about monthly competitions</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white">Local Updates</Label>
                      <p className="text-sm text-gray-400">Mugshots from your state/county</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>

                <Button className="w-full bg-cyan-600 hover:bg-cyan-700">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Save Preferences
                </Button>
              </CardContent>
            </Card>

            {/* Contact Section */}
            <Card className="bg-gray-800/50 border-gray-500/30">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Mail className="w-5 h-5 text-gray-400" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Having trouble with your preferences? Contact our support team.
                </p>
                <div className="text-sm text-gray-400 space-y-1">
                  <p>📧 Email: <EMAIL></p>
                  <p>🕒 Response time: Usually within 24 hours</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 