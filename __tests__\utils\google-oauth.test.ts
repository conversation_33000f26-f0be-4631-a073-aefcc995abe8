import { describe, it, expect, vi, beforeEach } from 'vitest'
import { handleGoogleAuth, handleOAuthCallback } from '../../lib/auth-utils'

// Mock Supabase client
const mockSupabase = {
  auth: {
    signInWithOAuth: vi.fn(),
    getSession: vi.fn(),
    getUser: vi.fn()
  }
}

vi.mock('../../lib/supabase/client', () => ({
  createClient: vi.fn(() => mockSupabase)
}))

describe('Google OAuth Authentication', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('handleGoogleAuth', () => {
    it('should initiate Google OAuth flow with correct parameters', async () => {
      const mockResponse = { data: { url: 'https://google.oauth.url' }, error: null }
      mockSupabase.auth.signInWithOAuth.mockResolvedValue(mockResponse)

      const result = await handleGoogleAuth('http://localhost:3000/mugshots')

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: 'http://localhost:3000/auth/callback?returnUrl=http%3A%2F%2Flocalhost%3A3000%2Fmugshots',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })
      expect(result).toEqual(mockResponse)
    })

    it('should handle default redirect URL when none provided', async () => {
      const mockResponse = { data: { url: 'https://google.oauth.url' }, error: null }
      mockSupabase.auth.signInWithOAuth.mockResolvedValue(mockResponse)

      await handleGoogleAuth()

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: 'http://localhost:3000/auth/callback?returnUrl=%2Fmugshots',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })
    })

    it('should handle OAuth initialization errors', async () => {
      const mockError = { message: 'OAuth provider not configured' }
      const mockResponse = { data: null, error: mockError }
      mockSupabase.auth.signInWithOAuth.mockResolvedValue(mockResponse)

      const result = await handleGoogleAuth()

      expect(result.error).toBe(mockError)
    })
  })

  describe('handleOAuthCallback', () => {
    it('should successfully process OAuth callback and return user session', async () => {
      const mockSession = {
        access_token: 'access_token',
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'John Doe',
            avatar_url: 'https://avatar.url'
          }
        }
      }
      
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null
      })

      const result = await handleOAuthCallback()

      expect(mockSupabase.auth.getSession).toHaveBeenCalled()
      expect(result.session).toBe(mockSession)
      expect(result.error).toBeNull()
    })

    it('should handle callback errors gracefully', async () => {
      const mockError = { message: 'Invalid OAuth state' }
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: mockError
      })

      const result = await handleOAuthCallback()

      expect(result.session).toBeNull()
      expect(result.error).toBe(mockError)
    })
  })
}) 