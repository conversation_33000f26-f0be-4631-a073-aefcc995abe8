'use client'

import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, ArrowLeft } from 'lucide-react'

export default function ErrorPage() {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <Card className="bg-gray-900 border border-red-500/30 text-white max-w-md w-full">
        <CardHeader className="bg-gradient-to-r from-red-900 to-pink-900 text-center">
          <div className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-8 h-8 text-red-400" />
          </div>
          <CardTitle className="text-xl font-bold">Authentication Error</CardTitle>
        </CardHeader>
        
        <CardContent className="p-6 text-center space-y-4">
          <p className="text-gray-300">
            Something went wrong during the authentication process. This could be due to:
          </p>
          
          <ul className="text-left text-gray-400 text-sm space-y-2">
            <li>• An expired or invalid confirmation link</li>
            <li>• Network connectivity issues</li>
            <li>• Server configuration problems</li>
          </ul>
          
          <div className="space-y-3 pt-4">
            <Button asChild className="w-full bg-blue-600 hover:bg-blue-700">
              <Link href="/login">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Try Again
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="w-full border-gray-600 text-gray-300 hover:bg-gray-800">
              <Link href="/">
                Return Home
              </Link>
            </Button>
          </div>
          
          <p className="text-xs text-gray-500 pt-4">
            If the problem persists, please contact support.
          </p>
        </CardContent>
      </Card>
    </div>
  )
} 