# Architecture: State Refactor

## Design Goals
- Decouple client vs server state
- SSR-compliant TanStack setup
- Smooth realtime update propagation
- Migration with feature flag fallback

## Modules

### Zustand Stores (Client-only)
- `auth-store.ts` – session state only
- `client-preferences-store.ts` – theme, layout, settings

### TanStack Hooks (Server data)
- `useMugshotsQuery(filters, page)`
- `useMugshotDetailQuery(id)`
- `useUserMugshotDataQuery(userId)`
- `useRatingMutation()`
- `useTagMutation()`

### Supabase Listeners
- `useRealtimeRatings()`
- `useRealtimeTags()`
- `useRealtimeWithFallback()`

### Server-Side Integration
- Next.js Server Components + data hydration
- SSR prefetch logic
- SEO Meta tag rendering

## Migration Plan
1. Introduce TanStack client and queries
2. Bridge Zustand props to TanStack
3. Gradually flip dependencies inside components
4. Remove Zustand server state logic

## Rollback
- Legacy mode toggle via ENV flag
- Fallback Zustand mocks

## Testing Strategy
- Snapshot diff for UI
- Cache hit/miss timing
- Realtime update lag
- Optimistic UX consistency
