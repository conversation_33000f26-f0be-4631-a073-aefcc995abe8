# Gentle Fast Queries Test - Conservative Load for Filtered Queries
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 30
      arrivalRate: 2
      name: "Warm-up: Gentle start"
    - duration: 120
      arrivalRate: 4
      name: "Steady: Conservative filtered queries"
    - duration: 30
      arrivalRate: 2
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 1500   # Allow higher for filtered queries
    - http.response_time.median: 600 # Based on filter complexity
    - http.codes.200: 92             # High success rate expected
    - http.codes.5xx: 3              # Minimal errors

  http:
    timeout: 15
    pool: 10  # Moderate pool for filtered queries
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Fast Queries - Gentle Load"
    weight: 100
    flow:
      - function: "generateFastFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "fast"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            sortBy: "{{ sortBy }}"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
          name: "GET /api/mugshots-fast - Fast Queries (Gentle)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 2  # Moderate think time

processor: "./load-tests/focused-tests/data-generators-focused.js"
