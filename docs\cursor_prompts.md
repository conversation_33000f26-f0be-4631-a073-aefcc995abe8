[optional] **<PERSON><PERSON>LOW THE SPECS** - Reference requirements.md, design.md, and task files for guidance

**MANDATORY CURSOR RULES - READ AND FOLLOW EXACTLY:**
1. **ANALYZE FIRST** - Always understand existing code before making changes
2. **NO EXISTING UI CHANGES** - Don't modify existing components/styles unless explicitly asked
3. **WORK SPECIFICALLY** - Focus only on the requested task/file/feature
4. **ASK WHEN UNCLEAR** - If anything is ambiguous, ask questions instead of assuming
5. **SHOW YOUR WORK** - Explain what you're doing and show code changes clearly
6. **TEST EXISTING FUNCTIONALITY** - Verify current features still work after changes
7. **NO SHORTCUTS** - Complete the full implementation, don't skip steps

**ADDITIONAL STRICT REQUIREMENTS:**
8. **ZERO ESLINT ERRORS** - All code must pass ESLint with no errors or warnings
   - Use proper TypeScript types for all variables, functions, and props
   - Import types correctly (use `import type` for type-only imports)
   - Define interfaces/types for all objects and API responses
   - Use strict null checks and handle undefined/null cases
   - Follow consistent naming conventions (camelCase, PascalCase, etc.)

9. **SCHEMA VALIDATION MANDATORY** - Always verify database schema accuracy
   - **MUST CHECK**: Review `supabase/migrations/*.sql` files for latest schema
   - **MUST MATCH**: Ensure all database queries match current table structure
   - **MUST VALIDATE**: Column names, types, constraints, and relationships
   - **NO ASSUMPTIONS**: Don't guess schema - always reference migration files
   - **GENERATE TYPES**: Create/update TypeScript interfaces that match DB schema exactly

10. **TYPE SAFETY FIRST** - Implement strict type checking
    - Define return types for all functions
    - Use generic types where appropriate
    - Handle async operations with proper Promise types
    - Validate API response types match expected interfaces
    - Use type guards for runtime type checking

**When in doubt, ASK before making changes.**

Now, following these rules, ....



## new task prompts

@requirements.md @design.md @tasks.md

I need to create a detailed task-1.md file that breaks down the first task from my implementation plan into a comprehensive, granular checklist with complete implementation details.

**Objective:**
Create a detailed breakdown of Task 1 that transforms the high-level task description into specific, actionable steps that a developer can follow to complete implementation.

**Requirements for task-1.md:**

Create a comprehensive task file with the following structure:

# Task 1: [Task Title from tasks.md]

## Overview
- Brief description of what this task accomplishes
- Why this task is critical for the overall project
- How it fits into the broader implementation strategy

## Prerequisites
- Any setup requirements before starting
- Knowledge or tools needed
- Dependencies that must be completed first

## Detailed Implementation Checklist

**Format each section as checkboxes that can be marked complete:**

- [ ] 1.1 [Specific Implementation Area]
  - [Detailed implementation step with exact file paths and technical details]
  - [Another specific step referencing design.md patterns]
  - [Include exact function names, configuration values, and code examples]
  - _Requirements: [Reference specific requirements from requirements.md]_

- [ ] 1.2 [Next Implementation Area]
  - [Specific actionable step with measurable outcome]
  - [Technical implementation detail with file paths]
  - [Integration or testing requirement]
  - _Requirements: [Reference specific requirements from requirements.md]_

- [ ] 1.3 [Continue pattern for all major areas]
  - [Break down into specific, actionable items]
  - [Include verification steps for each item]
  - [Reference design.md technical decisions]
  - _Requirements: [Reference specific requirements from requirements.md]_

**Checklist Requirements:**
- Each main checkbox (1.1, 1.2, etc.) represents a major implementation area
- Sub-bullets under each checkbox are specific implementation steps
- Each section ends with _Requirements:_ referencing relevant requirements.md sections
- All checklist items should be specific, measurable, and actionable
- Include exact file paths, function names, and configuration details
- Reference specific technical decisions from design.md

## Acceptance Criteria Verification
- [ ] All checklist items above are completed and verified
- [ ] Implementation satisfies requirements referenced in each section
- [ ] Code follows design.md architectural patterns
- [ ] All created/modified files are properly tested

## Files to Create/Modify
- List all files that will be created during this task
- List all existing files that will be modified
- Include brief descriptions of changes for each file

## Implementation Notes
- Include any code snippets or configuration examples needed
- Reference specific patterns or approaches from design.md
- Note any potential challenges or considerations

## Testing Checklist
- [ ] [Specific test case or validation step]
- [ ] [Another verification requirement]
- [ ] [Performance or functionality benchmark to meet]

## Next Steps
- What subsequent tasks depend on this foundation
- How this task enables future implementation phases

**Instructions:**
1. Use the task description from tasks.md as the foundation
2. Reference the technical architecture and patterns from design.md to inform implementation details
3. Map all work back to specific requirements from requirements.md
4. Make every checklist item actionable and specific with proper checkbox format
5. Include exact technical details like file paths, function signatures, and configuration values
6. Ensure the breakdown follows the checkbox pattern used in tasks.md
7. Each major implementation area gets its own numbered checkbox (1.1, 1.2, etc.)
8. Sub-bullets under each checkbox are specific implementation steps
9. End each section with _Requirements:_ reference

Please generate this detailed task-1.md file now using the checkbox format shown above.


## Task execution prompt

@requirements.md @design.md @tasks.md @task-1.md

I need to implement the detailed implementation checklist outlined in task-1.md. This is a critical foundation phase that must be executed with absolute precision to ensure zero impact on existing functionality.

**Critical Implementation Constraints:**

**ZERO MODIFICATION RULE:** This implementation must be completely additive. No existing UI components, styles, user flows, or functionality should be altered in any way. All changes must be additions alongside existing code, never replacements or modifications.

**EXISTING FUNCTIONALITY PRESERVATION:** Every current feature, interaction, and user experience must work exactly as it does today. Users should notice no changes in behavior, appearance, or performance from their perspective.

**BACKWARD COMPATIBILITY:** All existing imports, exports, component interfaces, and API integrations must remain unchanged. New code should integrate seamlessly without breaking any existing patterns.

**Implementation Approach:**

**Context Analysis:**
- Review requirements.md to understand what specific business needs this task addresses
- Use design.md to understand the technical architecture and implementation patterns to follow  
- Reference tasks.md to understand how this task fits into the broader implementation strategy
- Follow task-1.md checklist items systematically to ensure complete implementation

**Implementation Strategy:**
- Create new files and configurations as specified in the task checklist
- Install and configure new dependencies without affecting existing functionality
- Set up new infrastructure components as separate additions to the current codebase
- Document and analyze existing patterns without modifying them
- Establish new development tools and utilities as supplementary additions

**Quality Assurance Requirements:**
- Each checklist item in task-1.md must be completed thoroughly and verified
- All new code must follow the technical specifications from design.md
- Implementation must satisfy the acceptance criteria outlined in requirements.md
- Existing functionality must be tested and confirmed to work identically after each major change
- New additions must be properly integrated without disrupting current workflows

**Verification Steps:**
After implementation, I should be able to:
- Use the application exactly as before with zero observable differences
- Confirm all existing user flows work without any changes
- Verify that new infrastructure is properly set up and ready for future phases
- See that all existing tests continue to pass
- Validate that the development experience remains consistent

**Technical Requirements:**
- Follow exact implementation details specified in task-1.md
- Adhere to architectural decisions and patterns outlined in design.md
- Meet all acceptance criteria defined in requirements.md
- Maintain compatibility with existing codebase structure and conventions
- Ensure new additions are properly documented and integrated

**Step-by-Step Execution:**
Please work through task-1.md systematically:
1. Complete each checklist item in the specified order
2. Explain what you're implementing and why it's needed
3. Show the code changes or file additions you're making
4. Verify that existing functionality remains unaffected
5. Confirm each step meets the requirements and design specifications
6. Test that new additions work correctly without breaking existing features

**Final Validation:**
Before considering this task complete:
- Verify every checklist item in task-1.md has been implemented
- Confirm all requirements from requirements.md are satisfied
- Ensure the implementation follows design.md specifications
- Test that existing functionality works identically to before
- Validate that new infrastructure is ready for subsequent implementation phases

Please proceed with implementing task-1.md step by step, maintaining absolute preservation of existing functionality while building the foundation for future enhancements.