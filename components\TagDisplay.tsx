'use client'

import { useState, startTransition } from 'react'
import { Hash, X, Users, Flag, Loader2 } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/lib/stores/auth-store'
import { 
  removeTagFromMugshot, 
  reportTag,
  type MugshotTag,
  type TagRemovalResult
} from '@/lib/services/tag-service'
import { toast } from 'sonner'

interface TagDisplayProps {
  mugshotId: number
  tags: MugshotTag[]
  onTagRemoved?: (tagId: string) => void
  onTagClick?: (tag: MugshotTag) => void
  className?: string
  compact?: boolean
  showCounts?: boolean
  interactive?: boolean
}

export default function TagDisplay({ 
  mugshotId,
  tags,
  onTagRemoved,
  onTagClick,
  className = '',
  compact = false,
  showCounts = true,
  interactive = true
}: TagDisplayProps) {
  const { user, isLoading: authLoading } = useAuthStore()
  const [removingTags, setRemovingTags] = useState<Set<string>>(new Set())
  const [reportingTags, setReportingTags] = useState<Set<string>>(new Set())

  // Handle tag removal
  const handleRemoveTag = async (tag: MugshotTag) => {
    if (!user) {
      toast.error('Please log in to remove tags')
      return
    }

    if (!tag.current_user_tagged) {
      toast.error('You can only remove tags you added')
      return
    }

    setRemovingTags(prev => new Set(prev).add(tag.id))

    startTransition(async () => {
      try {
                 const result: TagRemovalResult = await removeTagFromMugshot(mugshotId, tag.tagType)
        
        if (result.success) {
          toast.success(result.message)
          
          // Notify parent component
          if (onTagRemoved) {
            onTagRemoved(tag.id)
          }
        } else {
          throw new Error(result.message || 'Failed to remove tag')
        }
      } catch (error) {
        console.error('Tag removal error:', error)
        toast.error(error instanceof Error ? error.message : 'Failed to remove tag')
      } finally {
        setRemovingTags(prev => {
          const newSet = new Set(prev)
          newSet.delete(tag.id)
          return newSet
        })
      }
    })
  }

  // Handle tag reporting
  const handleReportTag = async (tag: MugshotTag, reason: 'spam' | 'inappropriate' | 'duplicate' | 'misleading') => {
    if (!user) {
      toast.error('Please log in to report tags')
      return
    }

    setReportingTags(prev => new Set(prev).add(tag.id))

    startTransition(async () => {
      try {
        const result = await reportTag(tag.id, reason)
        
        if (result.success) {
          toast.success(result.message)
        } else {
          throw new Error(result.message || 'Failed to report tag')
        }
      } catch (error) {
        console.error('Tag reporting error:', error)
        toast.error(error instanceof Error ? error.message : 'Failed to report tag')
      } finally {
        setReportingTags(prev => {
          const newSet = new Set(prev)
          newSet.delete(tag.id)
          return newSet
        })
      }
    })
  }

  // Handle tag click for filtering
  const handleTagClick = (tag: MugshotTag) => {
    if (onTagClick) {
      onTagClick(tag)
    }
  }

  // Render loading state
  if (authLoading && tags.length === 0) {
    return (
      <Card className={`bg-gray-900/90 border-cyan-500/30 ${className}`}>
        <CardContent className={compact ? 'p-3' : 'p-4'}>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div className="flex flex-wrap gap-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-6 w-16" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Don't render if no tags
  if (tags.length === 0) {
    return null
  }

  return (
    <Card className={`bg-gray-900/90 border-cyan-500/30 ${className}`}>
      <CardContent className={compact ? 'p-3' : 'p-4'}>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Hash className="h-4 w-4 text-cyan-400 flex-shrink-0" />
            <span className={`font-medium text-white ${compact ? 'text-sm' : 'text-base'}`}>
              Community Tags
            </span>
            {showCounts && (
              <Badge variant="secondary" className="text-xs px-2 py-0 bg-cyan-500/20 text-cyan-400">
                {tags.length}
              </Badge>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => {
              const isRemoving = removingTags.has(tag.id)
              const isReporting = reportingTags.has(tag.id)
              const isUserTag = tag.current_user_tagged
              
              return (
                <div key={tag.id} className="relative group">
                  <Badge
                    variant="secondary"
                    className={`
                      text-sm px-3 py-1 transition-all duration-200
                      ${isUserTag 
                        ? 'bg-cyan-600/30 text-cyan-300 border border-cyan-500/50' 
                        : 'bg-gray-700/50 text-gray-300 border border-gray-600/50'
                      }
                      ${interactive && !isRemoving && !isReporting
                        ? 'hover:bg-gray-600/50 cursor-pointer hover:scale-105' 
                        : ''
                      }
                      ${isRemoving || isReporting ? 'opacity-50' : ''}
                    `}
                    onClick={() => interactive && !isRemoving && !isReporting && handleTagClick(tag)}
                  >
                                         <div className="flex items-center space-x-2">
                       <span>{tag.name || tag.tagType}</span>
                      
                      {showCounts && (
                                                 <div className="flex items-center space-x-1 text-xs opacity-75">
                           <Users className="h-3 w-3" />
                           <span>{tag.user_count || 0}</span>
                         </div>
                      )}
                      
                      {tag.is_trending && (
                        <div className="w-2 h-2 rounded-full bg-orange-400 animate-pulse" />
                      )}
                    </div>
                  </Badge>
                  
                  {/* Remove button for user's own tags */}
                  {interactive && user && isUserTag && !isRemoving && !isReporting && (
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleRemoveTag(tag)
                      }}
                      className="absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                  
                  {/* Loading indicator */}
                  {(isRemoving || isReporting) && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded">
                      <Loader2 className="h-3 w-3 animate-spin text-white" />
                    </div>
                  )}
                  
                  {/* Report dropdown for other users' tags */}
                  {interactive && user && !isUserTag && !isRemoving && !isReporting && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          type="button"
                          size="sm"
                          variant="ghost"
                          className="absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full bg-gray-600 hover:bg-gray-500 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Flag className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-gray-800 border-gray-600">
                        <DropdownMenuItem 
                          onClick={() => handleReportTag(tag, 'spam')}
                          className="text-gray-300 hover:bg-gray-700 cursor-pointer"
                        >
                          Report as Spam
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleReportTag(tag, 'inappropriate')}
                          className="text-gray-300 hover:bg-gray-700 cursor-pointer"
                        >
                          Report as Inappropriate
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleReportTag(tag, 'duplicate')}
                          className="text-gray-300 hover:bg-gray-700 cursor-pointer"
                        >
                          Report as Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleReportTag(tag, 'misleading')}
                          className="text-gray-300 hover:bg-gray-700 cursor-pointer"
                        >
                          Report as Misleading
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              )
            })}
          </div>
          
          {/* Empty state message for compact view */}
          {compact && tags.length === 0 && (
            <div className="text-gray-400 text-xs text-center">
              No community tags yet
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 