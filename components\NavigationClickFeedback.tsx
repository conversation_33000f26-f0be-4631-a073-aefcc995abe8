'use client'

import { <PERSON>actNode, ReactElement, cloneElement, isValidElement, HTMLAttributes } from 'react'
import { useNavigationProgress } from '@/hooks/useNavigationProgress'

// Type for React elements that can receive onClick and className props
type ClickableElement = ReactElement<HTMLAttributes<HTMLElement> & { 
  onClick?: (e: React.MouseEvent) => void
  className?: string
  style?: React.CSSProperties
}>

interface NavigationClickFeedbackProps {
  children: ReactNode
  href: string
  className?: string
  buttonId?: string
}

/**
 * Navigation Click Feedback Component
 * Wraps navigation elements to provide immediate visual feedback on click
 * Now uses simplified progress system with top progress bar
 */
export function NavigationClickFeedback({ 
  children, 
  href, 
  className = '', 
  buttonId 
}: NavigationClickFeedbackProps) {
  const { navigateWithProgress, isElementClicked } = useNavigationProgress()
  
  // Generate unique ID if not provided
  const elementId = buttonId || `nav-${href.replace(/[^a-zA-Z0-9]/g, '-')}`
  const isCurrentlyClicked = isElementClicked(elementId)

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    navigateWithProgress(href, elementId)
  }

  // Clone the child element to add click handler and feedback classes
  if (isValidElement(children)) {
    const clickableChild = children as ClickableElement
    return cloneElement(clickableChild, {
      onClick: handleClick,
      className: `${clickableChild.props?.className || ''} ${className} ${
        isCurrentlyClicked 
          ? 'scale-95 opacity-75 transition-all duration-100 ease-out' 
          : 'transition-all duration-100 ease-out hover:scale-[1.02]'
      }`.trim(),
      style: {
        ...clickableChild.props?.style,
        ...(isCurrentlyClicked && {
          transform: 'scale(0.95)',
          opacity: 0.75
        })
      }
    })
  }

  // Fallback wrapper for non-React elements
  return (
    <div
      onClick={handleClick}
      className={`${className} ${
        isCurrentlyClicked 
          ? 'scale-95 opacity-75 transition-all duration-100 ease-out' 
          : 'transition-all duration-100 ease-out hover:scale-[1.02]'
      } cursor-pointer`}
    >
      {children}
    </div>
  )
}

/**
 * Navigation Link with Immediate Feedback
 * Simplified version with better visual feedback
 */
interface NavLinkProps {
  href: string
  children: ReactNode
  className?: string
  buttonId?: string
  onClick?: () => void
}

export function NavLink({ href, children, className = '', buttonId, onClick }: NavLinkProps) {
  const { navigateWithProgress, isElementClicked } = useNavigationProgress()
  
  const elementId = buttonId || `link-${href.replace(/[^a-zA-Z0-9]/g, '-')}`
  const isCurrentlyClicked = isElementClicked(elementId)

  const handleClick = () => {
    onClick?.() // Call the optional onClick handler first
    navigateWithProgress(href, elementId)
  }

  return (
    <button
      onClick={handleClick}
      className={`${className} ${
        isCurrentlyClicked 
          ? 'scale-95 opacity-75 bg-pink-600/30' 
          : 'hover:bg-pink-500/10 hover:scale-[1.02]'
      } transition-all duration-100 ease-out relative overflow-hidden group`}
    >
      {/* Immediate feedback overlay */}
      {isCurrentlyClicked && (
        <div className="absolute inset-0 bg-pink-500/20 animate-pulse" />
      )}
      
      {/* Hover glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-pink-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      
      <span className="relative z-10">{children}</span>
    </button>
  )
}

/**
 * Enhanced Button with Navigation Feedback
 * Beautiful button component with loading states
 */
interface NavButtonProps {
  href: string
  children: ReactNode
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  buttonId?: string
}

export function NavButton({ 
  href, 
  children, 
  variant = 'primary',
  size = 'md',
  className = '',
  buttonId 
}: NavButtonProps) {
  const { navigateWithProgress, isElementClicked } = useNavigationProgress()
  
  const elementId = buttonId || `btn-${href.replace(/[^a-zA-Z0-9]/g, '-')}`
  const isCurrentlyClicked = isElementClicked(elementId)

  const baseClasses = 'relative font-medium transition-all duration-150 ease-out focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 focus:ring-offset-background group overflow-hidden'
  
  const variantClasses = {
    primary: 'bg-pink-600 hover:bg-pink-700 text-white shadow-lg hover:shadow-xl',
    secondary: 'bg-secondary hover:bg-secondary/80 text-secondary-foreground border border-border',
    ghost: 'hover:bg-pink-500/10 text-pink-400 hover:text-pink-300'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-md',
    md: 'px-4 py-2 text-base rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-xl'
  }

  const clickedClasses = isCurrentlyClicked 
    ? 'scale-95 opacity-80' 
    : 'hover:scale-[1.02]'

  return (
    <button
      onClick={() => navigateWithProgress(href, elementId)}
      disabled={isCurrentlyClicked}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${clickedClasses} ${className}`}
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
      
      {/* Loading overlay */}
      {isCurrentlyClicked && (
        <div className="absolute inset-0 bg-pink-500/20 animate-pulse" />
      )}
      
      {/* Content */}
      <span className={`relative z-10 flex items-center justify-center gap-2 ${isCurrentlyClicked ? 'opacity-70' : ''}`}>
        {children}
        {isCurrentlyClicked && (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        )}
      </span>
    </button>
  )
} 