'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { signUpUser, signInUser, validateAuthForm } from '@/lib/auth-utils'

export interface AuthResult {
  success: boolean
  error?: string
  redirectTo?: string
}

interface OAuthUser {
  id: string
  email?: string
  user_metadata?: {
    full_name?: string
    name?: string
  }
  app_metadata?: {
    provider?: string
  }
}

export async function loginAction(formData: FormData) {
  const supabase = await createClient()

  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Validate form data
  const validation = validateAuthForm({ email, password }, 'signin')
  
  if (!validation.isValid) {
    // Return validation errors
    return {
      success: false,
      errors: validation.errors
    }
  }

  const result = await signInUser(supabase, { email, password })

  if (result.success) {
    revalidatePath('/', 'layout')
    redirect('/mugshots')
  } else {
    return {
      success: false,
      error: result.error
    }
  }
}

export async function signupAction(formData: FormData) {
  const supabase = await createClient()

  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string
  const fullName = formData.get('fullName') as string
  const selectedState = formData.get('selectedState') as string
  const selectedCounty = formData.get('selectedCounty') as string

  // Validate form data
  const validation = validateAuthForm({
    email,
    password,
    confirmPassword,
    fullName,
    selectedState,
    selectedCounty
  }, 'signup')
  
  if (!validation.isValid) {
    // Return validation errors
    return {
      success: false,
      errors: validation.errors
    }
  }

  const result = await signUpUser(supabase, {
    email,
    password,
    fullName,
    selectedState,
    selectedCounty
  })

  if (result.success) {
    // Don't redirect immediately for signup - user needs to confirm email
    return {
      success: true,
      message: 'Please check your email to confirm your account'
    }
  } else {
    return {
      success: false,
      error: result.error
    }
  }
}

export async function logoutAction() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  revalidatePath('/', 'layout')
  redirect('/login')
} 

export async function updateUserLocationAction(
  userId: string,
  state: string,
  county: string
): Promise<AuthResult> {
  try {
    const supabase = await createClient()
    
    const { error } = await supabase
      .from('profiles')
      .update({
        state,
        county,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      return {
        success: false,
        error: error.message
      }
    }

    // Revalidate profile page
    revalidatePath('/profile')
    
    return {
      success: true
    }
  } catch (error) {
    console.error('Location update error:', error)
    return {
      success: false,
      error: 'An unexpected error occurred while updating location'
    }
  }
}

export async function createOAuthProfileAction(user: OAuthUser): Promise<AuthResult> {
  try {
    const supabase = await createClient()
    
    console.log('Creating OAuth profile for user:', user.id, user.email)
    
    // Check if profile already exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    // If profile exists, return success
    if (existingProfile && !fetchError) {
      console.log('Profile already exists for OAuth user:', existingProfile.id)
      return {
        success: true
      }
    }

    // If error is not "not found", return the error
    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error checking existing profile:', fetchError)
      return {
        success: false,
        error: fetchError.message
      }
    }

    console.log('Creating new profile for OAuth user')
    
    // Create new profile for OAuth user
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        user_id: user.id,
        email: user.email || null,
        full_name: user.user_metadata?.full_name || 'User',
        avatar_url: (user.user_metadata as { avatar_url?: string })?.avatar_url || null,
        state: null, // OAuth users start without location
        county: null, // OAuth users start without location
        role: 'user'
      })
      .select()
      .single()

    if (createError) {
      console.error('OAuth profile creation error:', createError)
      return {
        success: false,
        error: createError.message
      }
    }

    console.log('OAuth profile created successfully:', newProfile.id)
    return {
      success: true
    }
  } catch (error) {
    console.error('OAuth profile creation exception:', error)
    return {
      success: false,
      error: 'An unexpected error occurred while creating OAuth profile'
    }
  }
} 