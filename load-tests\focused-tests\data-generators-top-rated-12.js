/**
 * Data Generator for Top-Rated First 12 Test
 * 
 * This generator creates the most optimized query parameters for testing
 * the top-rated mugshots functionality. Focus is on getting exactly 12
 * top-rated mugshots with minimal overhead.
 */

/**
 * Generate filters specifically for top-rated first 12 test
 * This test focuses on:
 * - Only ratings table queries
 * - Get mugshot_id, avg_rating, total_ratings
 * - Limit to 12 results max
 * - Sort by avg_rating DESC, then total_ratings DESC for ties
 * - Only return mugshots that have ratings
 */
function generateTopRatedFirst12Filters(context, events, done) {
  const filters = {};
  
  // Always use top-rated sort - this is what we're testing
  filters.sortBy = "top-rated";
  
  // Always first page to get the top 12
  filters.page = 1;
  
  // Always 12 per page - this is our target
  filters.perPage = 12;
  
  // No additional filters to focus purely on ratings performance
  // This ensures we're testing the core ratings aggregation logic
  // without any additional WHERE clauses that could affect performance
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

/**
 * Generate filters for testing edge cases with top-rated first 12
 * This includes scenarios where there might be fewer than 12 rated mugshots
 */
function generateTopRatedFirst12EdgeCases(context, events, done) {
  const filters = {};
  
  // Always use top-rated sort
  filters.sortBy = "top-rated";
  
  // Test different page sizes to see behavior when less than 12 exist
  const pageSizes = [1, 6, 12, 24]; // Include sizes smaller and larger than 12
  filters.perPage = pageSizes[Math.floor(Math.random() * pageSizes.length)];
  
  // Always first page for consistency
  filters.page = 1;
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

/**
 * Generate filters for testing pagination behavior with top-rated
 * This tests what happens when we go beyond the first 12 results
 */
function generateTopRatedPaginationTest(context, events, done) {
  const filters = {};
  
  // Always use top-rated sort
  filters.sortBy = "top-rated";
  
  // Test different pages to see pagination behavior
  const pages = [1, 2, 3]; // First 3 pages
  filters.page = pages[Math.floor(Math.random() * pages.length)];
  
  // Always 12 per page
  filters.perPage = 12;
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

// Export functions for Artillery to use
module.exports = {
  generateTopRatedFirst12Filters,
  generateTopRatedFirst12EdgeCases,
  generateTopRatedPaginationTest
};
