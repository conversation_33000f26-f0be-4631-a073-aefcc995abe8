/**
 * Optimized Mugshots Service
 * 
 * Combines the high-performance fast service with the feature-rich native service
 * Uses the fast path for simple queries and the full-featured path for complex queries
 */

import { mugshotsEnhancedFastService } from './mugshots-enhanced-fast-service'
import { mugshotsNativeService } from './mugshots-native-service'
import type { DatabaseMugshot, MugshotFilters, SortOptions, PaginationOptions } from './mugshots-native-service'

/**
 * Determines if a query should use enhanced fast service vs native service
 */
function shouldUseEnhancedFast(
  filters: MugshotFilters,
  sortOptions: SortOptions
): boolean {
  // Use enhanced fast for most common queries
  if (sortOptions.sortBy === 'newest') {
    return true;
  }

  // Use enhanced fast for simple top-rated queries (no complex categories)
  if (sortOptions.sortBy === 'top-rated' && !filters.category?.length) {
    return true;
  }

  // Use enhanced fast for basic tag filtering (up to 3 tags)
  if (filters.tags?.length && filters.tags.length <= 3) {
    return true;
  }

  // Use native service for complex edge cases
  if (filters.category?.length) {
    return false; // Categories require native service
  }

  // Default to enhanced fast for better performance
  return true;
}

/**
 * Convert between filter formats for enhanced fast service
 */
function convertToEnhancedFilters(filters: MugshotFilters) {
  return {
    searchTerm: filters.searchTerm,
    state: filters.state,
    county: filters.county,
    dateFrom: filters.dateFrom,
    dateTo: filters.dateTo,
    tags: filters.tags?.join(','),
    sortBy: undefined // Will be set separately
  };
}

class MugshotsOptimizedService {
  /**
   * Get mugshots with intelligent routing between fast and native services
   */
  async getMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      // Determine which service to use
      if (shouldUseEnhancedFast(filters, sortOptions)) {
        console.log('🚀 [OPTIMIZED] Using enhanced fast service for optimal performance')
        console.log('📊 [OPTIMIZED] Filters:', filters)
        console.log('📊 [OPTIMIZED] Sort:', sortOptions)

        // Convert filters to enhanced fast service format
        const enhancedFilters = convertToEnhancedFilters(filters);

        // Use enhanced fast service (includes all data needed by UI)
        return await mugshotsEnhancedFastService.getMugshots(
          {
            ...enhancedFilters,
            sortBy: sortOptions.sortBy as 'newest' | 'top-rated' | 'most-viewed'
          },
          {
            page: pagination.page,
            perPage: pagination.perPage
          },
          userId
        );
      } else {
        console.log('📊 [OPTIMIZED] Using native service for complex query (categories, etc.)')
        console.log('📊 [OPTIMIZED] Complex filters:', filters)
        console.log('📊 [OPTIMIZED] Sort:', sortOptions)

        // Use native service for complex queries that enhanced fast can't handle
        return await mugshotsNativeService.getMugshots(
          filters,
          sortOptions,
          pagination,
          userId
        );
      }
    } catch (error) {
      console.error('❌ [OPTIMIZED] Error fetching mugshots:', error)
      return []
    }
  }
  
  /**
   * Get count with intelligent routing
   */
  async getCount(filters: MugshotFilters = {}): Promise<number> {
    try {
      // Use enhanced fast count for most queries
      if (!filters.category?.length) {
        console.log('🚀 [OPTIMIZED] Using enhanced fast count')
        return await mugshotsEnhancedFastService.getCount(convertToEnhancedFilters(filters));
      } else {
        console.log('📊 [OPTIMIZED] Using native count for complex filters')
        return await mugshotsNativeService.getMugshotCount(filters);
      }
    } catch (error) {
      console.error('❌ [OPTIMIZED] Error getting count:', error)
      return 0
    }
  }
}

export const mugshotsOptimizedService = new MugshotsOptimizedService()
