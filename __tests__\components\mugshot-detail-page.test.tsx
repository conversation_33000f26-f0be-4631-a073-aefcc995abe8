import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { act } from 'react'
import MugshotDetailClient from '../../app/mugshots/[id]/components/MugshotDetailClient'
import MugshotDetailLoading from '../../app/mugshots/[id]/components/MugshotDetailLoading'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock clipboard API
const mockClipboard = {
  writeText: vi.fn().mockResolvedValue(undefined),
}
Object.assign(navigator, {
  clipboard: mockClipboard,
})

// Mock window.open
const mockWindowOpen = vi.fn()
window.open = mockWindowOpen

describe('MugshotDetailClient', () => {
  const mockMugshot = {
    id: 1,
    name: '<PERSON>',
    location: 'Los Angeles, California',
    arrestDate: '2024-01-01',
    image: '/images/mugshot-1.jpg',
    category: 'Hot',
    offenses: ['Public Intoxication', 'Disorderly Conduct'],
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render share button with dropdown menu', () => {
    render(<MugshotDetailClient mugshot={mockMugshot} />)
    
    expect(screen.getByText('Share')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /share/i })).toBeInTheDocument()
  })

  it('should copy link to clipboard when copy option is clicked', async () => {
    render(<MugshotDetailClient mugshot={mockMugshot} />)
    
    // Open dropdown menu
    const shareButton = screen.getByRole('button', { name: /share/i })
    fireEvent.click(shareButton)
    
    // Click copy link option
    const copyOption = screen.getByText('Copy Link')
    fireEvent.click(copyOption)
    
    await waitFor(() => {
      expect(mockClipboard.writeText).toHaveBeenCalledTimes(1)
      expect(screen.getByText('Copied!')).toBeInTheDocument()
    })
  })

  it('should open Twitter share when Twitter option is clicked', async () => {
    render(<MugshotDetailClient mugshot={mockMugshot} />)
    
    // Open dropdown menu
    const shareButton = screen.getByRole('button', { name: /share/i })
    fireEvent.click(shareButton)
    
    // Click Twitter option
    const twitterOption = screen.getByText('Share on X')
    fireEvent.click(twitterOption)
    
    expect(mockWindowOpen).toHaveBeenCalledWith(
      expect.stringContaining('twitter.com/intent/tweet'),
      '_blank',
      'noopener,noreferrer'
    )
  })

  it('should open Facebook share when Facebook option is clicked', async () => {
    render(<MugshotDetailClient mugshot={mockMugshot} />)
    
    // Open dropdown menu
    const shareButton = screen.getByRole('button', { name: /share/i })
    fireEvent.click(shareButton)
    
    // Click Facebook option
    const facebookOption = screen.getByText('Share on Facebook')
    fireEvent.click(facebookOption)
    
    expect(mockWindowOpen).toHaveBeenCalledWith(
      expect.stringContaining('facebook.com/sharer'),
      '_blank',
      'noopener,noreferrer'
    )
  })

  it('should handle mugshot with no offenses gracefully', () => {
    const mugshotWithoutOffenses = {
      ...mockMugshot,
      offenses: undefined,
    }
    
    render(<MugshotDetailClient mugshot={mugshotWithoutOffenses} />)
    
    expect(screen.getByText('Share')).toBeInTheDocument()
  })
})

describe('MugshotDetailLoading', () => {
  it('should render loading skeletons', () => {
    render(<MugshotDetailLoading />)
    
    // Check for key loading elements
    expect(screen.getByText('Back to Mugshots')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /back to mugshots/i })).toBeDisabled()
    
    // Check for skeleton placeholders (using class selectors since Skeleton components use divs)
    const skeletons = document.querySelectorAll('.bg-gray-800')
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('should have proper accessibility attributes', () => {
    render(<MugshotDetailLoading />)
    
    const backButton = screen.getByRole('button', { name: /back to mugshots/i })
    expect(backButton).toBeDisabled()
  })
})

describe('Social Sharing Integration', () => {
  const mockMugshot = {
    id: 1,
    name: 'John Doe',
    location: 'Los Angeles, California',
    arrestDate: '2024-01-01',
    image: '/images/mugshot-1.jpg',
    category: 'Hot',
    offenses: ['Public Intoxication'],
  }

  beforeEach(() => {
    // Mock window.location for URL generation
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost:3000/mugshots/1',
      },
      writable: true,
    })
  })

  it('should generate correct share URLs', async () => {
    render(<MugshotDetailClient mugshot={mockMugshot} />)
    
    const shareButton = screen.getByRole('button', { name: /share/i })
    fireEvent.click(shareButton)
    
    // Test Twitter URL generation
    const twitterOption = screen.getByText('Share on X')
    fireEvent.click(twitterOption)
    
    const twitterCall = mockWindowOpen.mock.calls.find(call => 
      call[0].includes('twitter.com')
    )
    expect(twitterCall).toBeDefined()
    expect(twitterCall![0]).toContain(encodeURIComponent('John Doe - Los Angeles, California Mugshot'))
    expect(twitterCall![0]).toContain(encodeURIComponent('http://localhost:3000/mugshots/1'))
  })

  it('should handle native sharing API when available', async () => {
    const mockShare = vi.fn().mockResolvedValue(undefined)
    Object.assign(navigator, {
      share: mockShare,
    })

    render(<MugshotDetailClient mugshot={mockMugshot} />)
    
    const shareButton = screen.getByRole('button', { name: /share/i })
    fireEvent.click(shareButton)
    
    // Native sharing option should be available
    const nativeOption = screen.getByText('Share...')
    expect(nativeOption).toBeInTheDocument()
    
    fireEvent.click(nativeOption)
    
    await waitFor(() => {
      expect(mockShare).toHaveBeenCalledWith({
        title: 'John Doe - Los Angeles, California Mugshot',
        text: "View John Doe's arrest record from Los Angeles, California.",
        url: 'http://localhost:3000/mugshots/1',
      })
    })
  })
}) 