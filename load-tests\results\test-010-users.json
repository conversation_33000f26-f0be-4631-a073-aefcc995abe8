{"aggregate": {"counters": {"vusers.created_by_name.10 Users - Mugshots API": 684, "vusers.created": 840, "errors.Undefined function \"generateRandomFilters\"": 684, "http.requests": 840, "http.codes.200": 684, "http.responses": 840, "http.downloaded_bytes": 6057273, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 684, "vusers.failed": 0, "vusers.completed": 840, "vusers.created_by_name.10 Users - Details API": 156, "errors.Undefined function \"generateRandomMugshotId\"": 156, "http.codes.400": 156, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 156}, "rates": {"http.request_rate": 3}, "firstCounterAt": 1753538862951, "firstHistogramAt": 1753538865669, "lastCounterAt": 1753539104611, "lastHistogramAt": 1753539104611, "firstMetricAt": 1753538862951, "lastMetricAt": 1753539104611, "period": 1753539100000, "summaries": {"http.response_time": {"min": 54, "max": 2695, "count": 840, "mean": 616.4, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1130.2, "p99": 1620, "p999": 2671}, "http.response_time.2xx": {"min": 569, "max": 2695, "count": 684, "mean": 729.1, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 1107.9, "p95": 1153.1, "p99": 1826.6, "p999": 2671}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 569, "max": 2695, "count": 684, "mean": 729.1, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 1107.9, "p95": 1153.1, "p99": 1826.6, "p999": 2671}, "vusers.session_length": {"min": 2578.9, "max": 4735.7, "count": 840, "mean": 2811.2, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3197.8, "p99": 3752.7, "p999": 4676.2}, "http.response_time.4xx": {"min": 54, "max": 1066, "count": 156, "mean": 122.1, "p50": 80.6, "median": 80.6, "p75": 111.1, "p90": 165.7, "p95": 441.5, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 54, "max": 1066, "count": 156, "mean": 122.1, "p50": 80.6, "median": 80.6, "p75": 111.1, "p90": 165.7, "p95": 441.5, "p99": 772.9, "p999": 772.9}}, "histograms": {"http.response_time": {"min": 54, "max": 2695, "count": 840, "mean": 616.4, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1130.2, "p99": 1620, "p999": 2671}, "http.response_time.2xx": {"min": 569, "max": 2695, "count": 684, "mean": 729.1, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 1107.9, "p95": 1153.1, "p99": 1826.6, "p999": 2671}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 569, "max": 2695, "count": 684, "mean": 729.1, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 1107.9, "p95": 1153.1, "p99": 1826.6, "p999": 2671}, "vusers.session_length": {"min": 2578.9, "max": 4735.7, "count": 840, "mean": 2811.2, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3197.8, "p99": 3752.7, "p999": 4676.2}, "http.response_time.4xx": {"min": 54, "max": 1066, "count": 156, "mean": 122.1, "p50": 80.6, "median": 80.6, "p75": 111.1, "p90": 165.7, "p95": 441.5, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 54, "max": 1066, "count": 156, "mean": 122.1, "p50": 80.6, "median": 80.6, "p75": 111.1, "p90": 165.7, "p95": 441.5, "p99": 772.9, "p999": 772.9}}}, "intermediate": [{"counters": {"vusers.created_by_name.10 Users - Mugshots API": 13, "vusers.created": 15, "errors.Undefined function \"generateRandomFilters\"": 13, "http.requests": 15, "http.codes.200": 12, "http.responses": 13, "http.downloaded_bytes": 106124, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 12, "vusers.failed": 0, "vusers.completed": 9, "vusers.created_by_name.10 Users - Details API": 2, "errors.Undefined function \"generateRandomMugshotId\"": 2, "http.codes.400": 1, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 1}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753538862951, "firstHistogramAt": 1753538865669, "lastCounterAt": 1753538869950, "lastHistogramAt": 1753538869637, "firstMetricAt": 1753538862951, "lastMetricAt": 1753538869950, "period": "1753538860000", "summaries": {"http.response_time": {"min": 631, "max": 2695, "count": 13, "mean": 1654.2, "p50": 1652.8, "median": 1652.8, "p75": 1901.1, "p90": 2059.5, "p95": 2671, "p99": 2671, "p999": 2671}, "http.response_time.2xx": {"min": 631, "max": 2695, "count": 12, "mean": 1703.3, "p50": 1652.8, "median": 1652.8, "p75": 1901.1, "p90": 2059.5, "p95": 2671, "p99": 2671, "p999": 2671}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 631, "max": 2695, "count": 12, "mean": 1703.3, "p50": 1652.8, "median": 1652.8, "p75": 1901.1, "p90": 2059.5, "p95": 2671, "p99": 2671, "p999": 2671}, "vusers.session_length": {"min": 2648.7, "max": 4735.7, "count": 9, "mean": 3862.2, "p50": 3828.5, "median": 3828.5, "p75": 4065.2, "p90": 4676.2, "p95": 4676.2, "p99": 4676.2, "p999": 4676.2}, "http.response_time.4xx": {"min": 1066, "max": 1066, "count": 1, "mean": 1066, "p50": 1064.4, "median": 1064.4, "p75": 1064.4, "p90": 1064.4, "p95": 1064.4, "p99": 1064.4, "p999": 1064.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 1066, "max": 1066, "count": 1, "mean": 1066, "p50": 1064.4, "median": 1064.4, "p75": 1064.4, "p90": 1064.4, "p95": 1064.4, "p99": 1064.4, "p999": 1064.4}}, "histograms": {"http.response_time": {"min": 631, "max": 2695, "count": 13, "mean": 1654.2, "p50": 1652.8, "median": 1652.8, "p75": 1901.1, "p90": 2059.5, "p95": 2671, "p99": 2671, "p999": 2671}, "http.response_time.2xx": {"min": 631, "max": 2695, "count": 12, "mean": 1703.3, "p50": 1652.8, "median": 1652.8, "p75": 1901.1, "p90": 2059.5, "p95": 2671, "p99": 2671, "p999": 2671}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 631, "max": 2695, "count": 12, "mean": 1703.3, "p50": 1652.8, "median": 1652.8, "p75": 1901.1, "p90": 2059.5, "p95": 2671, "p99": 2671, "p999": 2671}, "vusers.session_length": {"min": 2648.7, "max": 4735.7, "count": 9, "mean": 3862.2, "p50": 3828.5, "median": 3828.5, "p75": 4065.2, "p90": 4676.2, "p95": 4676.2, "p99": 4676.2, "p999": 4676.2}, "http.response_time.4xx": {"min": 1066, "max": 1066, "count": 1, "mean": 1066, "p50": 1064.4, "median": 1064.4, "p75": 1064.4, "p90": 1064.4, "p95": 1064.4, "p99": 1064.4, "p999": 1064.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 1066, "max": 1066, "count": 1, "mean": 1066, "p50": 1064.4, "median": 1064.4, "p75": 1064.4, "p90": 1064.4, "p95": 1064.4, "p99": 1064.4, "p999": 1064.4}}}, {"counters": {"http.codes.400": 2, "http.responses": 20, "http.downloaded_bytes": 159234, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 2, "http.codes.200": 18, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 18, "vusers.created_by_name.10 Users - Mugshots API": 19, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 19, "http.requests": 20, "vusers.failed": 0, "vusers.completed": 19, "vusers.created_by_name.10 Users - Details API": 1, "errors.Undefined function \"generateRandomMugshotId\"": 1}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753538870001, "firstHistogramAt": 1753538870015, "lastCounterAt": 1753538879950, "lastHistogramAt": 1753538879548, "firstMetricAt": 1753538870001, "lastMetricAt": 1753538879950, "period": "1753538870000", "summaries": {"http.response_time": {"min": 65, "max": 1133, "count": 20, "mean": 807.4, "p50": 645.6, "median": 645.6, "p75": 1107.9, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "http.response_time.4xx": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}, "http.response_time.2xx": {"min": 597, "max": 1133, "count": 18, "mean": 889.5, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 597, "max": 1133, "count": 18, "mean": 889.5, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 2607.2, "max": 4075.9, "count": 19, "mean": 3064.4, "p50": 3134.5, "median": 3134.5, "p75": 3134.5, "p90": 3605.5, "p95": 3678.4, "p99": 3678.4, "p999": 3678.4}}, "histograms": {"http.response_time": {"min": 65, "max": 1133, "count": 20, "mean": 807.4, "p50": 645.6, "median": 645.6, "p75": 1107.9, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "http.response_time.4xx": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}, "http.response_time.2xx": {"min": 597, "max": 1133, "count": 18, "mean": 889.5, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 597, "max": 1133, "count": 18, "mean": 889.5, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 2607.2, "max": 4075.9, "count": 19, "mean": 3064.4, "p50": 3134.5, "median": 3134.5, "p75": 3134.5, "p90": 3605.5, "p95": 3678.4, "p99": 3678.4, "p999": 3678.4}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 22, "http.codes.200": 19, "http.responses": 21, "http.downloaded_bytes": 168083, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 19, "vusers.created_by_name.10 Users - Mugshots API": 18, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 18, "http.requests": 20, "vusers.created_by_name.10 Users - Details API": 2, "errors.Undefined function \"generateRandomMugshotId\"": 2, "http.codes.400": 2, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 2}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753538880001, "firstHistogramAt": 1753538880049, "lastCounterAt": 1753538889951, "lastHistogramAt": 1753538889605, "firstMetricAt": 1753538880001, "lastMetricAt": 1753538889951, "period": "1753538880000", "summaries": {"vusers.session_length": {"min": 2604.3, "max": 3125.4, "count": 22, "mean": 2820.9, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3072.4, "p95": 3072.4, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 65, "max": 1115, "count": 21, "mean": 676.4, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 597, "max": 1115, "count": 19, "mean": 740.4, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 597, "max": 1115, "count": 19, "mean": 740.4, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "http.response_time.4xx": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}}, "histograms": {"vusers.session_length": {"min": 2604.3, "max": 3125.4, "count": 22, "mean": 2820.9, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3072.4, "p95": 3072.4, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 65, "max": 1115, "count": 21, "mean": 676.4, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 597, "max": 1115, "count": 19, "mean": 740.4, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 597, "max": 1115, "count": 19, "mean": 740.4, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 1085.9, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "http.response_time.4xx": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 71, "count": 2, "mean": 68, "p50": 64.7, "median": 64.7, "p75": 64.7, "p90": 64.7, "p95": 64.7, "p99": 64.7, "p999": 64.7}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 19, "http.codes.200": 18, "http.responses": 19, "http.downloaded_bytes": 159172, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 18, "vusers.created_by_name.10 Users - Mugshots API": 19, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 19, "http.requests": 20, "vusers.created_by_name.10 Users - Details API": 1, "errors.Undefined function \"generateRandomMugshotId\"": 1, "http.codes.400": 1, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 1}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753538890001, "firstHistogramAt": 1753538890560, "lastCounterAt": 1753538899950, "lastHistogramAt": 1753538899627, "firstMetricAt": 1753538890001, "lastMetricAt": 1753538899950, "period": "1753538890000", "summaries": {"vusers.session_length": {"min": 2602.5, "max": 3133.8, "count": 19, "mean": 2695.9, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 2836.2, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time": {"min": 70, "max": 1128, "count": 19, "mean": 659.2, "p50": 620.3, "median": 620.3, "p75": 620.3, "p90": 854.2, "p95": 1085.9, "p99": 1085.9, "p999": 1085.9}, "http.response_time.2xx": {"min": 593, "max": 1128, "count": 18, "mean": 691.9, "p50": 620.3, "median": 620.3, "p75": 620.3, "p90": 854.2, "p95": 1085.9, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 593, "max": 1128, "count": 18, "mean": 691.9, "p50": 620.3, "median": 620.3, "p75": 620.3, "p90": 854.2, "p95": 1085.9, "p99": 1085.9, "p999": 1085.9}, "http.response_time.4xx": {"min": 70, "max": 70, "count": 1, "mean": 70, "p50": 70.1, "median": 70.1, "p75": 70.1, "p90": 70.1, "p95": 70.1, "p99": 70.1, "p999": 70.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 70, "max": 70, "count": 1, "mean": 70, "p50": 70.1, "median": 70.1, "p75": 70.1, "p90": 70.1, "p95": 70.1, "p99": 70.1, "p999": 70.1}}, "histograms": {"vusers.session_length": {"min": 2602.5, "max": 3133.8, "count": 19, "mean": 2695.9, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 2836.2, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time": {"min": 70, "max": 1128, "count": 19, "mean": 659.2, "p50": 620.3, "median": 620.3, "p75": 620.3, "p90": 854.2, "p95": 1085.9, "p99": 1085.9, "p999": 1085.9}, "http.response_time.2xx": {"min": 593, "max": 1128, "count": 18, "mean": 691.9, "p50": 620.3, "median": 620.3, "p75": 620.3, "p90": 854.2, "p95": 1085.9, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 593, "max": 1128, "count": 18, "mean": 691.9, "p50": 620.3, "median": 620.3, "p75": 620.3, "p90": 854.2, "p95": 1085.9, "p99": 1085.9, "p999": 1085.9}, "http.response_time.4xx": {"min": 70, "max": 70, "count": 1, "mean": 70, "p50": 70.1, "median": 70.1, "p75": 70.1, "p90": 70.1, "p95": 70.1, "p99": 70.1, "p999": 70.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 70, "max": 70, "count": 1, "mean": 70, "p50": 70.1, "median": 70.1, "p75": 70.1, "p90": 70.1, "p95": 70.1, "p99": 70.1, "p999": 70.1}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 21, "http.codes.200": 17, "http.responses": 21, "http.downloaded_bytes": 150542, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 17, "vusers.created_by_name.10 Users - Mugshots API": 15, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 15, "http.requests": 20, "vusers.created_by_name.10 Users - Details API": 5, "errors.Undefined function \"generateRandomMugshotId\"": 5, "http.codes.400": 4, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 4}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753538900001, "firstHistogramAt": 1753538900026, "lastCounterAt": 1753538909949, "lastHistogramAt": 1753538909605, "firstMetricAt": 1753538900001, "lastMetricAt": 1753538909949, "period": "1753538900000", "summaries": {"vusers.session_length": {"min": 2598.3, "max": 3152.7, "count": 21, "mean": 2712.3, "p50": 2618.1, "median": 2618.1, "p75": 2671, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time": {"min": 65, "max": 1139, "count": 21, "mean": 533.4, "p50": 608, "median": 608, "p75": 620.3, "p90": 632.8, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 595, "max": 1139, "count": 17, "mean": 642.2, "p50": 608, "median": 608, "p75": 620.3, "p90": 632.8, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 595, "max": 1139, "count": 17, "mean": 642.2, "p50": 608, "median": 608, "p75": 620.3, "p90": 632.8, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.4xx": {"min": 65, "max": 80, "count": 4, "mean": 71, "p50": 64.7, "median": 64.7, "p75": 74.4, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 80, "count": 4, "mean": 71, "p50": 64.7, "median": 64.7, "p75": 74.4, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}}, "histograms": {"vusers.session_length": {"min": 2598.3, "max": 3152.7, "count": 21, "mean": 2712.3, "p50": 2618.1, "median": 2618.1, "p75": 2671, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time": {"min": 65, "max": 1139, "count": 21, "mean": 533.4, "p50": 608, "median": 608, "p75": 620.3, "p90": 632.8, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 595, "max": 1139, "count": 17, "mean": 642.2, "p50": 608, "median": 608, "p75": 620.3, "p90": 632.8, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 595, "max": 1139, "count": 17, "mean": 642.2, "p50": 608, "median": 608, "p75": 620.3, "p90": 632.8, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.4xx": {"min": 65, "max": 80, "count": 4, "mean": 71, "p50": 64.7, "median": 64.7, "p75": 74.4, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 80, "count": 4, "mean": 71, "p50": 64.7, "median": 64.7, "p75": 74.4, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}}}, {"counters": {"http.codes.400": 6, "http.responses": 18, "http.downloaded_bytes": 106530, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 6, "vusers.created_by_name.10 Users - Mugshots API": 15, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 15, "http.requests": 20, "vusers.failed": 0, "vusers.completed": 18, "http.codes.200": 12, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 12, "vusers.created_by_name.10 Users - Details API": 5, "errors.Undefined function \"generateRandomMugshotId\"": 5}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753538910001, "firstHistogramAt": 1753538910022, "lastCounterAt": 1753538919950, "lastHistogramAt": 1753538919087, "firstMetricAt": 1753538910001, "lastMetricAt": 1753538919950, "period": "1753538910000", "summaries": {"http.response_time": {"min": 65, "max": 1180, "count": 18, "mean": 634.3, "p50": 608, "median": 608, "p75": 1085.9, "p90": 1107.9, "p95": 1153.1, "p99": 1153.1, "p999": 1153.1}, "http.response_time.4xx": {"min": 65, "max": 236, "count": 6, "mean": 99.2, "p50": 71.5, "median": 71.5, "p75": 74.4, "p90": 76, "p95": 76, "p99": 76, "p999": 76}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 236, "count": 6, "mean": 99.2, "p50": 71.5, "median": 71.5, "p75": 74.4, "p90": 76, "p95": 76, "p99": 76, "p999": 76}, "vusers.session_length": {"min": 2598.5, "max": 3246.2, "count": 18, "mean": 2939.8, "p50": 3072.4, "median": 3072.4, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time.2xx": {"min": 594, "max": 1180, "count": 12, "mean": 901.9, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1107.9, "p95": 1153.1, "p99": 1153.1, "p999": 1153.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 594, "max": 1180, "count": 12, "mean": 901.9, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1107.9, "p95": 1153.1, "p99": 1153.1, "p999": 1153.1}}, "histograms": {"http.response_time": {"min": 65, "max": 1180, "count": 18, "mean": 634.3, "p50": 608, "median": 608, "p75": 1085.9, "p90": 1107.9, "p95": 1153.1, "p99": 1153.1, "p999": 1153.1}, "http.response_time.4xx": {"min": 65, "max": 236, "count": 6, "mean": 99.2, "p50": 71.5, "median": 71.5, "p75": 74.4, "p90": 76, "p95": 76, "p99": 76, "p999": 76}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 236, "count": 6, "mean": 99.2, "p50": 71.5, "median": 71.5, "p75": 74.4, "p90": 76, "p95": 76, "p99": 76, "p999": 76}, "vusers.session_length": {"min": 2598.5, "max": 3246.2, "count": 18, "mean": 2939.8, "p50": 3072.4, "median": 3072.4, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time.2xx": {"min": 594, "max": 1180, "count": 12, "mean": 901.9, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1107.9, "p95": 1153.1, "p99": 1153.1, "p999": 1153.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 594, "max": 1180, "count": 12, "mean": 901.9, "p50": 1064.4, "median": 1064.4, "p75": 1107.9, "p90": 1107.9, "p95": 1153.1, "p99": 1153.1, "p999": 1153.1}}}, {"counters": {"http.codes.200": 32, "http.responses": 41, "http.downloaded_bytes": 283525, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 32, "vusers.failed": 0, "vusers.completed": 34, "vusers.created_by_name.10 Users - Mugshots API": 32, "vusers.created": 41, "errors.Undefined function \"generateRandomFilters\"": 32, "http.requests": 41, "vusers.created_by_name.10 Users - Details API": 9, "errors.Undefined function \"generateRandomMugshotId\"": 9, "http.codes.400": 9, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 9}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538920001, "firstHistogramAt": 1753538920012, "lastCounterAt": 1753538929951, "lastHistogramAt": 1753538929668, "firstMetricAt": 1753538920001, "lastMetricAt": 1753538929951, "period": "1753538920000", "summaries": {"http.response_time": {"min": 67, "max": 1493, "count": 41, "mean": 715.7, "p50": 632.8, "median": 632.8, "p75": 1085.9, "p90": 1107.9, "p95": 1130.2, "p99": 1274.3, "p999": 1274.3}, "http.response_time.2xx": {"min": 591, "max": 1493, "count": 32, "mean": 856.2, "p50": 632.8, "median": 632.8, "p75": 1085.9, "p90": 1107.9, "p95": 1130.2, "p99": 1274.3, "p999": 1274.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 591, "max": 1493, "count": 32, "mean": 856.2, "p50": 632.8, "median": 632.8, "p75": 1085.9, "p90": 1107.9, "p95": 1130.2, "p99": 1274.3, "p999": 1274.3}, "vusers.session_length": {"min": 2597, "max": 3287.5, "count": 34, "mean": 2895.9, "p50": 3072.4, "median": 3072.4, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "http.response_time.4xx": {"min": 67, "max": 527, "count": 9, "mean": 215.9, "p50": 85.6, "median": 85.6, "p75": 441.5, "p90": 507.8, "p95": 507.8, "p99": 507.8, "p999": 507.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 67, "max": 527, "count": 9, "mean": 215.9, "p50": 85.6, "median": 85.6, "p75": 441.5, "p90": 507.8, "p95": 507.8, "p99": 507.8, "p999": 507.8}}, "histograms": {"http.response_time": {"min": 67, "max": 1493, "count": 41, "mean": 715.7, "p50": 632.8, "median": 632.8, "p75": 1085.9, "p90": 1107.9, "p95": 1130.2, "p99": 1274.3, "p999": 1274.3}, "http.response_time.2xx": {"min": 591, "max": 1493, "count": 32, "mean": 856.2, "p50": 632.8, "median": 632.8, "p75": 1085.9, "p90": 1107.9, "p95": 1130.2, "p99": 1274.3, "p999": 1274.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 591, "max": 1493, "count": 32, "mean": 856.2, "p50": 632.8, "median": 632.8, "p75": 1085.9, "p90": 1107.9, "p95": 1130.2, "p99": 1274.3, "p999": 1274.3}, "vusers.session_length": {"min": 2597, "max": 3287.5, "count": 34, "mean": 2895.9, "p50": 3072.4, "median": 3072.4, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "http.response_time.4xx": {"min": 67, "max": 527, "count": 9, "mean": 215.9, "p50": 85.6, "median": 85.6, "p75": 441.5, "p90": 507.8, "p95": 507.8, "p99": 507.8, "p999": 507.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 67, "max": 527, "count": 9, "mean": 215.9, "p50": 85.6, "median": 85.6, "p75": 441.5, "p90": 507.8, "p95": 507.8, "p99": 507.8, "p999": 507.8}}}, {"counters": {"http.codes.200": 39, "http.responses": 50, "http.downloaded_bytes": 345549, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 39, "vusers.created_by_name.10 Users - Mugshots API": 39, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 39, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 50, "vusers.created_by_name.10 Users - Details API": 11, "errors.Undefined function \"generateRandomMugshotId\"": 11, "http.codes.400": 11, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 11}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538930002, "firstHistogramAt": 1753538930079, "lastCounterAt": 1753538939952, "lastHistogramAt": 1753538939863, "firstMetricAt": 1753538930002, "lastMetricAt": 1753538939952, "period": "1753538930000", "summaries": {"http.response_time": {"min": 54, "max": 1207, "count": 50, "mean": 562, "p50": 608, "median": 608, "p75": 645.6, "p90": 982.6, "p95": 1085.9, "p99": 1153.1, "p999": 1153.1}, "http.response_time.2xx": {"min": 581, "max": 1207, "count": 39, "mean": 698.5, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1153.1, "p999": 1153.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 581, "max": 1207, "count": 39, "mean": 698.5, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1153.1, "p999": 1153.1}, "vusers.session_length": {"min": 2588.6, "max": 3528.6, "count": 50, "mean": 2853.5, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3464.1, "p99": 3534.1, "p999": 3534.1}, "http.response_time.4xx": {"min": 54, "max": 148, "count": 11, "mean": 77.8, "p50": 74.4, "median": 74.4, "p75": 80.6, "p90": 87.4, "p95": 87.4, "p99": 87.4, "p999": 87.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 54, "max": 148, "count": 11, "mean": 77.8, "p50": 74.4, "median": 74.4, "p75": 80.6, "p90": 87.4, "p95": 87.4, "p99": 87.4, "p999": 87.4}}, "histograms": {"http.response_time": {"min": 54, "max": 1207, "count": 50, "mean": 562, "p50": 608, "median": 608, "p75": 645.6, "p90": 982.6, "p95": 1085.9, "p99": 1153.1, "p999": 1153.1}, "http.response_time.2xx": {"min": 581, "max": 1207, "count": 39, "mean": 698.5, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1153.1, "p999": 1153.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 581, "max": 1207, "count": 39, "mean": 698.5, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1153.1, "p999": 1153.1}, "vusers.session_length": {"min": 2588.6, "max": 3528.6, "count": 50, "mean": 2853.5, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3464.1, "p99": 3534.1, "p999": 3534.1}, "http.response_time.4xx": {"min": 54, "max": 148, "count": 11, "mean": 77.8, "p50": 74.4, "median": 74.4, "p75": 80.6, "p90": 87.4, "p95": 87.4, "p99": 87.4, "p999": 87.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 54, "max": 148, "count": 11, "mean": 77.8, "p50": 74.4, "median": 74.4, "p75": 80.6, "p90": 87.4, "p95": 87.4, "p99": 87.4, "p999": 87.4}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 51, "http.codes.200": 41, "http.responses": 52, "http.downloaded_bytes": 363199, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 41, "vusers.created_by_name.10 Users - Details API": 11, "vusers.created": 50, "errors.Undefined function \"generateRandomMugshotId\"": 11, "http.requests": 50, "http.codes.400": 11, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 11, "vusers.created_by_name.10 Users - Mugshots API": 39, "errors.Undefined function \"generateRandomFilters\"": 39}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538940002, "firstHistogramAt": 1753538940101, "lastCounterAt": 1753538949951, "lastHistogramAt": 1753538949742, "firstMetricAt": 1753538940002, "lastMetricAt": 1753538949951, "period": "1753538940000", "summaries": {"vusers.session_length": {"min": 2596.9, "max": 3216.9, "count": 51, "mean": 2811.4, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 57, "max": 1137, "count": 52, "mean": 594.9, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 590, "max": 1137, "count": 41, "mean": 734.4, "p50": 632.8, "median": 632.8, "p75": 713.5, "p90": 1107.9, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 590, "max": 1137, "count": 41, "mean": 734.4, "p50": 632.8, "median": 632.8, "p75": 713.5, "p90": 1107.9, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "http.response_time.4xx": {"min": 57, "max": 97, "count": 11, "mean": 74.6, "p50": 68.7, "median": 68.7, "p75": 80.6, "p90": 92.8, "p95": 92.8, "p99": 92.8, "p999": 92.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 57, "max": 97, "count": 11, "mean": 74.6, "p50": 68.7, "median": 68.7, "p75": 80.6, "p90": 92.8, "p95": 92.8, "p99": 92.8, "p999": 92.8}}, "histograms": {"vusers.session_length": {"min": 2596.9, "max": 3216.9, "count": 51, "mean": 2811.4, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 57, "max": 1137, "count": 52, "mean": 594.9, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 590, "max": 1137, "count": 41, "mean": 734.4, "p50": 632.8, "median": 632.8, "p75": 713.5, "p90": 1107.9, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 590, "max": 1137, "count": 41, "mean": 734.4, "p50": 632.8, "median": 632.8, "p75": 713.5, "p90": 1107.9, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "http.response_time.4xx": {"min": 57, "max": 97, "count": 11, "mean": 74.6, "p50": 68.7, "median": 68.7, "p75": 80.6, "p90": 92.8, "p95": 92.8, "p99": 92.8, "p999": 92.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 57, "max": 97, "count": 11, "mean": 74.6, "p50": 68.7, "median": 68.7, "p75": 80.6, "p90": 92.8, "p95": 92.8, "p99": 92.8, "p999": 92.8}}}, {"counters": {"http.codes.200": 43, "http.responses": 50, "http.downloaded_bytes": 380580, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 43, "vusers.failed": 0, "vusers.completed": 50, "vusers.created_by_name.10 Users - Mugshots API": 43, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 43, "http.requests": 50, "vusers.created_by_name.10 Users - Details API": 7, "errors.Undefined function \"generateRandomMugshotId\"": 7, "http.codes.400": 7, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 7}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538950002, "firstHistogramAt": 1753538950068, "lastCounterAt": 1753538959952, "lastHistogramAt": 1753538959742, "firstMetricAt": 1753538950002, "lastMetricAt": 1753538959952, "period": "1753538950000", "summaries": {"http.response_time": {"min": 65, "max": 1147, "count": 50, "mean": 616, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 587, "max": 1147, "count": 43, "mean": 703.8, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 587, "max": 1147, "count": 43, "mean": 703.8, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 2589.7, "max": 3163.3, "count": 50, "mean": 2760.9, "p50": 2618.1, "median": 2618.1, "p75": 2725, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time.4xx": {"min": 65, "max": 97, "count": 7, "mean": 76.7, "p50": 71.5, "median": 71.5, "p75": 82.3, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 97, "count": 7, "mean": 76.7, "p50": 71.5, "median": 71.5, "p75": 82.3, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}}, "histograms": {"http.response_time": {"min": 65, "max": 1147, "count": 50, "mean": 616, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 587, "max": 1147, "count": 43, "mean": 703.8, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 587, "max": 1147, "count": 43, "mean": 703.8, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1085.9, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 2589.7, "max": 3163.3, "count": 50, "mean": 2760.9, "p50": 2618.1, "median": 2618.1, "p75": 2725, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time.4xx": {"min": 65, "max": 97, "count": 7, "mean": 76.7, "p50": 71.5, "median": 71.5, "p75": 82.3, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 97, "count": 7, "mean": 76.7, "p50": 71.5, "median": 71.5, "p75": 82.3, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 49, "http.codes.200": 41, "http.responses": 50, "http.downloaded_bytes": 363064, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 41, "vusers.created_by_name.10 Users - Mugshots API": 40, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 40, "http.requests": 50, "vusers.created_by_name.10 Users - Details API": 10, "errors.Undefined function \"generateRandomMugshotId\"": 10, "http.codes.400": 9, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 9}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538960002, "firstHistogramAt": 1753538960104, "lastCounterAt": 1753538969951, "lastHistogramAt": 1753538969801, "firstMetricAt": 1753538960002, "lastMetricAt": 1753538969951, "period": "1753538960000", "summaries": {"vusers.session_length": {"min": 2587.4, "max": 3930.2, "count": 49, "mean": 2849.6, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3395.5, "p99": 3464.1, "p999": 3464.1}, "http.response_time": {"min": 67, "max": 1928, "count": 50, "mean": 667.9, "p50": 632.8, "median": 632.8, "p75": 713.5, "p90": 1107.9, "p95": 1176.4, "p99": 1436.8, "p999": 1436.8}, "http.response_time.2xx": {"min": 583, "max": 1928, "count": 41, "mean": 784.6, "p50": 658.6, "median": 658.6, "p75": 820.7, "p90": 1130.2, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 583, "max": 1928, "count": 41, "mean": 784.6, "p50": 658.6, "median": 658.6, "p75": 820.7, "p90": 1130.2, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "http.response_time.4xx": {"min": 67, "max": 421, "count": 9, "mean": 136.4, "p50": 113.3, "median": 113.3, "p75": 115.6, "p90": 127.8, "p95": 127.8, "p99": 127.8, "p999": 127.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 67, "max": 421, "count": 9, "mean": 136.4, "p50": 113.3, "median": 113.3, "p75": 115.6, "p90": 127.8, "p95": 127.8, "p99": 127.8, "p999": 127.8}}, "histograms": {"vusers.session_length": {"min": 2587.4, "max": 3930.2, "count": 49, "mean": 2849.6, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3134.5, "p95": 3395.5, "p99": 3464.1, "p999": 3464.1}, "http.response_time": {"min": 67, "max": 1928, "count": 50, "mean": 667.9, "p50": 632.8, "median": 632.8, "p75": 713.5, "p90": 1107.9, "p95": 1176.4, "p99": 1436.8, "p999": 1436.8}, "http.response_time.2xx": {"min": 583, "max": 1928, "count": 41, "mean": 784.6, "p50": 658.6, "median": 658.6, "p75": 820.7, "p90": 1130.2, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 583, "max": 1928, "count": 41, "mean": 784.6, "p50": 658.6, "median": 658.6, "p75": 820.7, "p90": 1130.2, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "http.response_time.4xx": {"min": 67, "max": 421, "count": 9, "mean": 136.4, "p50": 113.3, "median": 113.3, "p75": 115.6, "p90": 127.8, "p95": 127.8, "p99": 127.8, "p999": 127.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 67, "max": 421, "count": 9, "mean": 136.4, "p50": 113.3, "median": 113.3, "p75": 115.6, "p90": 127.8, "p95": 127.8, "p99": 127.8, "p999": 127.8}}}, {"counters": {"http.codes.400": 5, "http.responses": 50, "http.downloaded_bytes": 398083, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 5, "vusers.failed": 0, "vusers.completed": 53, "vusers.created_by_name.10 Users - Mugshots API": 46, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 46, "http.requests": 50, "http.codes.200": 45, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 45, "vusers.created_by_name.10 Users - Details API": 4, "errors.Undefined function \"generateRandomMugshotId\"": 4}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538970002, "firstHistogramAt": 1753538970012, "lastCounterAt": 1753538979951, "lastHistogramAt": 1753538979816, "firstMetricAt": 1753538970002, "lastMetricAt": 1753538979951, "period": "1753538970000", "summaries": {"http.response_time": {"min": 61, "max": 1161, "count": 50, "mean": 615.4, "p50": 608, "median": 608, "p75": 645.6, "p90": 742.6, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "http.response_time.4xx": {"min": 61, "max": 92, "count": 5, "mean": 76.6, "p50": 80.6, "median": 80.6, "p75": 85.6, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 92, "count": 5, "mean": 76.6, "p50": 80.6, "median": 80.6, "p75": 85.6, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}, "vusers.session_length": {"min": 2595.4, "max": 3184, "count": 53, "mean": 2775.6, "p50": 2618.1, "median": 2618.1, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "http.response_time.2xx": {"min": 578, "max": 1161, "count": 45, "mean": 675.3, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 742.6, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 578, "max": 1161, "count": 45, "mean": 675.3, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 742.6, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}}, "histograms": {"http.response_time": {"min": 61, "max": 1161, "count": 50, "mean": 615.4, "p50": 608, "median": 608, "p75": 645.6, "p90": 742.6, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "http.response_time.4xx": {"min": 61, "max": 92, "count": 5, "mean": 76.6, "p50": 80.6, "median": 80.6, "p75": 85.6, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 92, "count": 5, "mean": 76.6, "p50": 80.6, "median": 80.6, "p75": 85.6, "p90": 85.6, "p95": 85.6, "p99": 85.6, "p999": 85.6}, "vusers.session_length": {"min": 2595.4, "max": 3184, "count": 53, "mean": 2775.6, "p50": 2618.1, "median": 2618.1, "p75": 3072.4, "p90": 3134.5, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "http.response_time.2xx": {"min": 578, "max": 1161, "count": 45, "mean": 675.3, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 742.6, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 578, "max": 1161, "count": 45, "mean": 675.3, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 742.6, "p95": 1064.4, "p99": 1085.9, "p999": 1085.9}}}, {"counters": {"http.codes.200": 39, "http.responses": 50, "http.downloaded_bytes": 345533, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 39, "vusers.created_by_name.10 Users - Mugshots API": 39, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 39, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 48, "vusers.created_by_name.10 Users - Details API": 11, "errors.Undefined function \"generateRandomMugshotId\"": 11, "http.codes.400": 11, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 11}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538980002, "firstHistogramAt": 1753538980252, "lastCounterAt": 1753538989951, "lastHistogramAt": 1753538989805, "firstMetricAt": 1753538980002, "lastMetricAt": 1753538989951, "period": "1753538980000", "summaries": {"http.response_time": {"min": 76, "max": 1192, "count": 50, "mean": 572.5, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 772.9, "p95": 1043.3, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 586, "max": 1192, "count": 39, "mean": 696.8, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 788.5, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 586, "max": 1192, "count": 39, "mean": 696.8, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 788.5, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 2592.2, "max": 3214.4, "count": 48, "mean": 2775.3, "p50": 2671, "median": 2671, "p75": 2780, "p90": 3134.5, "p95": 3197.8, "p99": 3197.8, "p999": 3197.8}, "http.response_time.4xx": {"min": 76, "max": 203, "count": 11, "mean": 131.9, "p50": 127.8, "median": 127.8, "p75": 156, "p90": 190.6, "p95": 190.6, "p99": 190.6, "p999": 190.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 76, "max": 203, "count": 11, "mean": 131.9, "p50": 127.8, "median": 127.8, "p75": 156, "p90": 190.6, "p95": 190.6, "p99": 190.6, "p999": 190.6}}, "histograms": {"http.response_time": {"min": 76, "max": 1192, "count": 50, "mean": 572.5, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 772.9, "p95": 1043.3, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 586, "max": 1192, "count": 39, "mean": 696.8, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 788.5, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 586, "max": 1192, "count": 39, "mean": 696.8, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 788.5, "p95": 1107.9, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 2592.2, "max": 3214.4, "count": 48, "mean": 2775.3, "p50": 2671, "median": 2671, "p75": 2780, "p90": 3134.5, "p95": 3197.8, "p99": 3197.8, "p999": 3197.8}, "http.response_time.4xx": {"min": 76, "max": 203, "count": 11, "mean": 131.9, "p50": 127.8, "median": 127.8, "p75": 156, "p90": 190.6, "p95": 190.6, "p99": 190.6, "p999": 190.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 76, "max": 203, "count": 11, "mean": 131.9, "p50": 127.8, "median": 127.8, "p75": 156, "p90": 190.6, "p95": 190.6, "p99": 190.6, "p999": 190.6}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 49, "http.codes.200": 38, "http.responses": 50, "http.downloaded_bytes": 336773, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 38, "vusers.created_by_name.10 Users - Mugshots API": 37, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 37, "http.requests": 50, "vusers.created_by_name.10 Users - Details API": 13, "errors.Undefined function \"generateRandomMugshotId\"": 13, "http.codes.400": 12, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 12}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753538990002, "firstHistogramAt": 1753538990165, "lastCounterAt": 1753538999951, "lastHistogramAt": 1753538999751, "firstMetricAt": 1753538990002, "lastMetricAt": 1753538999951, "period": "1753538990000", "summaries": {"vusers.session_length": {"min": 2594.2, "max": 3444.5, "count": 49, "mean": 2777, "p50": 2671, "median": 2671, "p75": 2893.5, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 63, "max": 1295, "count": 50, "mean": 554.6, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 854.2, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 579, "max": 1295, "count": 38, "mean": 695, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 854.2, "p95": 963.1, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 579, "max": 1295, "count": 38, "mean": 695, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 854.2, "p95": 963.1, "p99": 1107.9, "p999": 1107.9}, "http.response_time.4xx": {"min": 63, "max": 438, "count": 12, "mean": 109.9, "p50": 82.3, "median": 82.3, "p75": 90.9, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 63, "max": 438, "count": 12, "mean": 109.9, "p50": 82.3, "median": 82.3, "p75": 90.9, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}}, "histograms": {"vusers.session_length": {"min": 2594.2, "max": 3444.5, "count": 49, "mean": 2777, "p50": 2671, "median": 2671, "p75": 2893.5, "p90": 3134.5, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 63, "max": 1295, "count": 50, "mean": 554.6, "p50": 632.8, "median": 632.8, "p75": 645.6, "p90": 854.2, "p95": 854.2, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 579, "max": 1295, "count": 38, "mean": 695, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 854.2, "p95": 963.1, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 579, "max": 1295, "count": 38, "mean": 695, "p50": 632.8, "median": 632.8, "p75": 658.6, "p90": 854.2, "p95": 963.1, "p99": 1107.9, "p999": 1107.9}, "http.response_time.4xx": {"min": 63, "max": 438, "count": 12, "mean": 109.9, "p50": 82.3, "median": 82.3, "p75": 90.9, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 63, "max": 438, "count": 12, "mean": 109.9, "p50": 82.3, "median": 82.3, "p75": 90.9, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}}}, {"counters": {"http.codes.400": 16, "http.responses": 50, "http.downloaded_bytes": 301727, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 16, "vusers.failed": 0, "vusers.completed": 49, "vusers.created_by_name.10 Users - Mugshots API": 35, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 35, "http.requests": 50, "http.codes.200": 34, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 34, "vusers.created_by_name.10 Users - Details API": 15, "errors.Undefined function \"generateRandomMugshotId\"": 15}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753539000002, "firstHistogramAt": 1753539000028, "lastCounterAt": 1753539009983, "lastHistogramAt": 1753539009983, "firstMetricAt": 1753539000002, "lastMetricAt": 1753539009983, "period": "1753539000000", "summaries": {"http.response_time": {"min": 76, "max": 1331, "count": 50, "mean": 572.9, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 837.3, "p95": 1107.9, "p99": 1200.1, "p999": 1200.1}, "http.response_time.4xx": {"min": 76, "max": 776, "count": 16, "mean": 209.1, "p50": 117.9, "median": 117.9, "p75": 127.8, "p90": 314.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 76, "max": 776, "count": 16, "mean": 209.1, "p50": 117.9, "median": 117.9, "p75": 127.8, "p90": 314.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "vusers.session_length": {"min": 2584.5, "max": 3790, "count": 49, "mean": 2919.1, "p50": 2836.2, "median": 2836.2, "p75": 3134.5, "p90": 3197.8, "p95": 3328.3, "p99": 3752.7, "p999": 3752.7}, "http.response_time.2xx": {"min": 592, "max": 1331, "count": 34, "mean": 744.1, "p50": 645.6, "median": 645.6, "p75": 713.5, "p90": 1107.9, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 592, "max": 1331, "count": 34, "mean": 744.1, "p50": 645.6, "median": 645.6, "p75": 713.5, "p90": 1107.9, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}}, "histograms": {"http.response_time": {"min": 76, "max": 1331, "count": 50, "mean": 572.9, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 837.3, "p95": 1107.9, "p99": 1200.1, "p999": 1200.1}, "http.response_time.4xx": {"min": 76, "max": 776, "count": 16, "mean": 209.1, "p50": 117.9, "median": 117.9, "p75": 127.8, "p90": 314.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 76, "max": 776, "count": 16, "mean": 209.1, "p50": 117.9, "median": 117.9, "p75": 127.8, "p90": 314.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "vusers.session_length": {"min": 2584.5, "max": 3790, "count": 49, "mean": 2919.1, "p50": 2836.2, "median": 2836.2, "p75": 3134.5, "p90": 3197.8, "p95": 3328.3, "p99": 3752.7, "p999": 3752.7}, "http.response_time.2xx": {"min": 592, "max": 1331, "count": 34, "mean": 744.1, "p50": 645.6, "median": 645.6, "p75": 713.5, "p90": 1107.9, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 592, "max": 1331, "count": 34, "mean": 744.1, "p50": 645.6, "median": 645.6, "p75": 713.5, "p90": 1107.9, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 53, "http.codes.200": 46, "http.responses": 49, "http.downloaded_bytes": 406787, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 46, "vusers.created_by_name.10 Users - Mugshots API": 47, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 47, "http.requests": 50, "vusers.created_by_name.10 Users - Details API": 3, "errors.Undefined function \"generateRandomMugshotId\"": 3, "http.codes.400": 3, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 3}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753539010002, "firstHistogramAt": 1753539010014, "lastCounterAt": 1753539019951, "lastHistogramAt": 1753539019866, "firstMetricAt": 1753539010002, "lastMetricAt": 1753539019951, "period": "1753539010000", "summaries": {"vusers.session_length": {"min": 2593.4, "max": 3326.9, "count": 53, "mean": 2701.9, "p50": 2671, "median": 2671, "p75": 2671, "p90": 2836.2, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 112, "max": 1512, "count": 49, "mean": 687.1, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 699.4, "p95": 1408.4, "p99": 1495.5, "p999": 1495.5}, "http.response_time.2xx": {"min": 582, "max": 1512, "count": 46, "mean": 724, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 699.4, "p95": 1408.4, "p99": 1495.5, "p999": 1495.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 582, "max": 1512, "count": 46, "mean": 724, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 699.4, "p95": 1408.4, "p99": 1495.5, "p999": 1495.5}, "http.response_time.4xx": {"min": 112, "max": 129, "count": 3, "mean": 120.3, "p50": 120.3, "median": 120.3, "p75": 120.3, "p90": 120.3, "p95": 120.3, "p99": 120.3, "p999": 120.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 112, "max": 129, "count": 3, "mean": 120.3, "p50": 120.3, "median": 120.3, "p75": 120.3, "p90": 120.3, "p95": 120.3, "p99": 120.3, "p999": 120.3}}, "histograms": {"vusers.session_length": {"min": 2593.4, "max": 3326.9, "count": 53, "mean": 2701.9, "p50": 2671, "median": 2671, "p75": 2671, "p90": 2836.2, "p95": 3134.5, "p99": 3134.5, "p999": 3134.5}, "http.response_time": {"min": 112, "max": 1512, "count": 49, "mean": 687.1, "p50": 632.8, "median": 632.8, "p75": 671.9, "p90": 699.4, "p95": 1408.4, "p99": 1495.5, "p999": 1495.5}, "http.response_time.2xx": {"min": 582, "max": 1512, "count": 46, "mean": 724, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 699.4, "p95": 1408.4, "p99": 1495.5, "p999": 1495.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 582, "max": 1512, "count": 46, "mean": 724, "p50": 645.6, "median": 645.6, "p75": 671.9, "p90": 699.4, "p95": 1408.4, "p99": 1495.5, "p999": 1495.5}, "http.response_time.4xx": {"min": 112, "max": 129, "count": 3, "mean": 120.3, "p50": 120.3, "median": 120.3, "p75": 120.3, "p90": 120.3, "p95": 120.3, "p99": 120.3, "p999": 120.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 112, "max": 129, "count": 3, "mean": 120.3, "p50": 120.3, "median": 120.3, "p75": 120.3, "p90": 120.3, "p95": 120.3, "p99": 120.3, "p999": 120.3}}}, {"counters": {"http.codes.200": 41, "http.responses": 51, "http.downloaded_bytes": 363136, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 41, "vusers.created_by_name.10 Users - Mugshots API": 40, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 40, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 50, "vusers.created_by_name.10 Users - Details API": 10, "errors.Undefined function \"generateRandomMugshotId\"": 10, "http.codes.400": 10, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 10}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753539020002, "firstHistogramAt": 1753539020190, "lastCounterAt": 1753539029951, "lastHistogramAt": 1753539029876, "firstMetricAt": 1753539020002, "lastMetricAt": 1753539029951, "period": "1753539020000", "summaries": {"http.response_time": {"min": 65, "max": 1185, "count": 51, "mean": 566.2, "p50": 608, "median": 608, "p75": 632.8, "p90": 772.9, "p95": 1064.4, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 585, "max": 1185, "count": 41, "mean": 677.8, "p50": 620.3, "median": 620.3, "p75": 645.6, "p90": 804.5, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 585, "max": 1185, "count": 41, "mean": 677.8, "p50": 620.3, "median": 620.3, "p75": 645.6, "p90": 804.5, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "vusers.session_length": {"min": 2587.1, "max": 3528.6, "count": 50, "mean": 2844.5, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3534.1}, "http.response_time.4xx": {"min": 65, "max": 216, "count": 10, "mean": 108.7, "p50": 82.3, "median": 82.3, "p75": 89.1, "p90": 179.5, "p95": 179.5, "p99": 179.5, "p999": 179.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 216, "count": 10, "mean": 108.7, "p50": 82.3, "median": 82.3, "p75": 89.1, "p90": 179.5, "p95": 179.5, "p99": 179.5, "p999": 179.5}}, "histograms": {"http.response_time": {"min": 65, "max": 1185, "count": 51, "mean": 566.2, "p50": 608, "median": 608, "p75": 632.8, "p90": 772.9, "p95": 1064.4, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 585, "max": 1185, "count": 41, "mean": 677.8, "p50": 620.3, "median": 620.3, "p75": 645.6, "p90": 804.5, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 585, "max": 1185, "count": 41, "mean": 677.8, "p50": 620.3, "median": 620.3, "p75": 645.6, "p90": 804.5, "p95": 1107.9, "p99": 1107.9, "p999": 1107.9}, "vusers.session_length": {"min": 2587.1, "max": 3528.6, "count": 50, "mean": 2844.5, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3197.8, "p95": 3395.5, "p99": 3534.1, "p999": 3534.1}, "http.response_time.4xx": {"min": 65, "max": 216, "count": 10, "mean": 108.7, "p50": 82.3, "median": 82.3, "p75": 89.1, "p90": 179.5, "p95": 179.5, "p99": 179.5, "p999": 179.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 65, "max": 216, "count": 10, "mean": 108.7, "p50": 82.3, "median": 82.3, "p75": 89.1, "p90": 179.5, "p95": 179.5, "p99": 179.5, "p999": 179.5}}}, {"counters": {"http.codes.200": 41, "http.responses": 50, "http.downloaded_bytes": 363035, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 41, "vusers.created_by_name.10 Users - Mugshots API": 41, "vusers.created": 50, "errors.Undefined function \"generateRandomFilters\"": 41, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 49, "vusers.created_by_name.10 Users - Details API": 9, "errors.Undefined function \"generateRandomMugshotId\"": 9, "http.codes.400": 9, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 9}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753539030002, "firstHistogramAt": 1753539030261, "lastCounterAt": 1753539039951, "lastHistogramAt": 1753539039831, "firstMetricAt": 1753539030002, "lastMetricAt": 1753539039951, "period": "1753539030000", "summaries": {"http.response_time": {"min": 60, "max": 1081, "count": 50, "mean": 530.5, "p50": 608, "median": 608, "p75": 632.8, "p90": 645.6, "p95": 645.6, "p99": 645.6, "p999": 645.6}, "http.response_time.2xx": {"min": 589, "max": 1081, "count": 41, "mean": 629.3, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 645.6, "p95": 645.6, "p99": 645.6, "p999": 645.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 589, "max": 1081, "count": 41, "mean": 629.3, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 645.6, "p95": 645.6, "p99": 645.6, "p999": 645.6}, "vusers.session_length": {"min": 2589.7, "max": 3103, "count": 49, "mean": 2701.2, "p50": 2618.1, "median": 2618.1, "p75": 2671, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.4xx": {"min": 60, "max": 97, "count": 9, "mean": 80.4, "p50": 77.5, "median": 77.5, "p75": 92.8, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 60, "max": 97, "count": 9, "mean": 80.4, "p50": 77.5, "median": 77.5, "p75": 92.8, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}}, "histograms": {"http.response_time": {"min": 60, "max": 1081, "count": 50, "mean": 530.5, "p50": 608, "median": 608, "p75": 632.8, "p90": 645.6, "p95": 645.6, "p99": 645.6, "p999": 645.6}, "http.response_time.2xx": {"min": 589, "max": 1081, "count": 41, "mean": 629.3, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 645.6, "p95": 645.6, "p99": 645.6, "p999": 645.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 589, "max": 1081, "count": 41, "mean": 629.3, "p50": 620.3, "median": 620.3, "p75": 632.8, "p90": 645.6, "p95": 645.6, "p99": 645.6, "p999": 645.6}, "vusers.session_length": {"min": 2589.7, "max": 3103, "count": 49, "mean": 2701.2, "p50": 2618.1, "median": 2618.1, "p75": 2671, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.4xx": {"min": 60, "max": 97, "count": 9, "mean": 80.4, "p50": 77.5, "median": 77.5, "p75": 92.8, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 60, "max": 97, "count": 9, "mean": 80.4, "p50": 77.5, "median": 77.5, "p75": 92.8, "p90": 94.6, "p95": 94.6, "p99": 94.6, "p999": 94.6}}}, {"counters": {"http.codes.200": 24, "http.responses": 29, "http.downloaded_bytes": 212505, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 24, "vusers.failed": 0, "vusers.completed": 36, "vusers.created_by_name.10 Users - Details API": 6, "vusers.created": 29, "errors.Undefined function \"generateRandomMugshotId\"": 6, "http.requests": 29, "http.codes.400": 5, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 5, "vusers.created_by_name.10 Users - Mugshots API": 23, "errors.Undefined function \"generateRandomFilters\"": 23}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753539040002, "firstHistogramAt": 1753539040093, "lastCounterAt": 1753539049951, "lastHistogramAt": 1753539049616, "firstMetricAt": 1753539040002, "lastMetricAt": 1753539049951, "period": "1753539040000", "summaries": {"http.response_time": {"min": 61, "max": 1952, "count": 29, "mean": 686.9, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1436.8, "p95": 1465.9, "p99": 1495.5, "p999": 1495.5}, "http.response_time.2xx": {"min": 583, "max": 1952, "count": 24, "mean": 813.9, "p50": 620.3, "median": 620.3, "p75": 671.9, "p90": 1436.8, "p95": 1465.9, "p99": 1495.5, "p999": 1495.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 583, "max": 1952, "count": 24, "mean": 813.9, "p50": 620.3, "median": 620.3, "p75": 671.9, "p90": 1436.8, "p95": 1465.9, "p99": 1495.5, "p999": 1495.5}, "vusers.session_length": {"min": 2600, "max": 3959.3, "count": 36, "mean": 2875.4, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3328.3, "p95": 3464.1, "p99": 3534.1, "p999": 3534.1}, "http.response_time.4xx": {"min": 61, "max": 90, "count": 5, "mean": 77.4, "p50": 79.1, "median": 79.1, "p75": 79.1, "p90": 79.1, "p95": 79.1, "p99": 79.1, "p999": 79.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 90, "count": 5, "mean": 77.4, "p50": 79.1, "median": 79.1, "p75": 79.1, "p90": 79.1, "p95": 79.1, "p99": 79.1, "p999": 79.1}}, "histograms": {"http.response_time": {"min": 61, "max": 1952, "count": 29, "mean": 686.9, "p50": 620.3, "median": 620.3, "p75": 658.6, "p90": 1436.8, "p95": 1465.9, "p99": 1495.5, "p999": 1495.5}, "http.response_time.2xx": {"min": 583, "max": 1952, "count": 24, "mean": 813.9, "p50": 620.3, "median": 620.3, "p75": 671.9, "p90": 1436.8, "p95": 1465.9, "p99": 1495.5, "p999": 1495.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 583, "max": 1952, "count": 24, "mean": 813.9, "p50": 620.3, "median": 620.3, "p75": 671.9, "p90": 1436.8, "p95": 1465.9, "p99": 1495.5, "p999": 1495.5}, "vusers.session_length": {"min": 2600, "max": 3959.3, "count": 36, "mean": 2875.4, "p50": 2671, "median": 2671, "p75": 3072.4, "p90": 3328.3, "p95": 3464.1, "p99": 3534.1, "p999": 3534.1}, "http.response_time.4xx": {"min": 61, "max": 90, "count": 5, "mean": 77.4, "p50": 79.1, "median": 79.1, "p75": 79.1, "p90": 79.1, "p95": 79.1, "p99": 79.1, "p999": 79.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 90, "count": 5, "mean": 77.4, "p50": 79.1, "median": 79.1, "p75": 79.1, "p90": 79.1, "p95": 79.1, "p99": 79.1, "p999": 79.1}}}, {"counters": {"http.codes.400": 4, "http.responses": 20, "http.downloaded_bytes": 141714, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 4, "vusers.failed": 0, "vusers.completed": 21, "vusers.created_by_name.10 Users - Mugshots API": 17, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 17, "http.requests": 20, "http.codes.200": 16, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 16, "vusers.created_by_name.10 Users - Details API": 3, "errors.Undefined function \"generateRandomMugshotId\"": 3}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753539050003, "firstHistogramAt": 1753539050010, "lastCounterAt": 1753539059951, "lastHistogramAt": 1753539059611, "firstMetricAt": 1753539050003, "lastMetricAt": 1753539059951, "period": "1753539050000", "summaries": {"http.response_time": {"min": 59, "max": 641, "count": 20, "mean": 495.6, "p50": 596, "median": 596, "p75": 608, "p90": 608, "p95": 632.8, "p99": 632.8, "p999": 632.8}, "http.response_time.4xx": {"min": 59, "max": 82, "count": 4, "mean": 67.8, "p50": 63.4, "median": 63.4, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 59, "max": 82, "count": 4, "mean": 67.8, "p50": 63.4, "median": 63.4, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "vusers.session_length": {"min": 2587.4, "max": 3092.5, "count": 21, "mean": 2744.2, "p50": 2618.1, "median": 2618.1, "p75": 3072.4, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.2xx": {"min": 579, "max": 641, "count": 16, "mean": 602.6, "p50": 608, "median": 608, "p75": 608, "p90": 608, "p95": 632.8, "p99": 632.8, "p999": 632.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 579, "max": 641, "count": 16, "mean": 602.6, "p50": 608, "median": 608, "p75": 608, "p90": 608, "p95": 632.8, "p99": 632.8, "p999": 632.8}}, "histograms": {"http.response_time": {"min": 59, "max": 641, "count": 20, "mean": 495.6, "p50": 596, "median": 596, "p75": 608, "p90": 608, "p95": 632.8, "p99": 632.8, "p999": 632.8}, "http.response_time.4xx": {"min": 59, "max": 82, "count": 4, "mean": 67.8, "p50": 63.4, "median": 63.4, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 59, "max": 82, "count": 4, "mean": 67.8, "p50": 63.4, "median": 63.4, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "vusers.session_length": {"min": 2587.4, "max": 3092.5, "count": 21, "mean": 2744.2, "p50": 2618.1, "median": 2618.1, "p75": 3072.4, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.2xx": {"min": 579, "max": 641, "count": 16, "mean": 602.6, "p50": 608, "median": 608, "p75": 608, "p90": 608, "p95": 632.8, "p99": 632.8, "p999": 632.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 579, "max": 641, "count": 16, "mean": 602.6, "p50": 608, "median": 608, "p75": 608, "p90": 608, "p95": 632.8, "p99": 632.8, "p999": 632.8}}}, {"counters": {"http.codes.200": 18, "http.responses": 20, "http.downloaded_bytes": 159231, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 18, "vusers.failed": 0, "vusers.completed": 19, "vusers.created_by_name.10 Users - Mugshots API": 18, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 18, "http.requests": 20, "vusers.created_by_name.10 Users - Details API": 2, "errors.Undefined function \"generateRandomMugshotId\"": 2, "http.codes.400": 2, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 2}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753539060003, "firstHistogramAt": 1753539060538, "lastCounterAt": 1753539069952, "lastHistogramAt": 1753539069892, "firstMetricAt": 1753539060003, "lastMetricAt": 1753539069952, "period": "1753539060000", "summaries": {"http.response_time": {"min": 60, "max": 887, "count": 20, "mean": 572.5, "p50": 596, "median": 596, "p75": 608, "p90": 671.9, "p95": 788.5, "p99": 788.5, "p999": 788.5}, "http.response_time.2xx": {"min": 580, "max": 887, "count": 18, "mean": 628.4, "p50": 596, "median": 596, "p75": 608, "p90": 671.9, "p95": 788.5, "p99": 788.5, "p999": 788.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 580, "max": 887, "count": 18, "mean": 628.4, "p50": 596, "median": 596, "p75": 608, "p90": 671.9, "p95": 788.5, "p99": 788.5, "p999": 788.5}, "vusers.session_length": {"min": 2581.8, "max": 2678.2, "count": 19, "mean": 2613, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 2671, "p95": 2671, "p99": 2671, "p999": 2671}, "http.response_time.4xx": {"min": 60, "max": 77, "count": 2, "mean": 68.5, "p50": 59.7, "median": 59.7, "p75": 59.7, "p90": 59.7, "p95": 59.7, "p99": 59.7, "p999": 59.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 60, "max": 77, "count": 2, "mean": 68.5, "p50": 59.7, "median": 59.7, "p75": 59.7, "p90": 59.7, "p95": 59.7, "p99": 59.7, "p999": 59.7}}, "histograms": {"http.response_time": {"min": 60, "max": 887, "count": 20, "mean": 572.5, "p50": 596, "median": 596, "p75": 608, "p90": 671.9, "p95": 788.5, "p99": 788.5, "p999": 788.5}, "http.response_time.2xx": {"min": 580, "max": 887, "count": 18, "mean": 628.4, "p50": 596, "median": 596, "p75": 608, "p90": 671.9, "p95": 788.5, "p99": 788.5, "p999": 788.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 580, "max": 887, "count": 18, "mean": 628.4, "p50": 596, "median": 596, "p75": 608, "p90": 671.9, "p95": 788.5, "p99": 788.5, "p999": 788.5}, "vusers.session_length": {"min": 2581.8, "max": 2678.2, "count": 19, "mean": 2613, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 2671, "p95": 2671, "p99": 2671, "p999": 2671}, "http.response_time.4xx": {"min": 60, "max": 77, "count": 2, "mean": 68.5, "p50": 59.7, "median": 59.7, "p75": 59.7, "p90": 59.7, "p95": 59.7, "p99": 59.7, "p999": 59.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 60, "max": 77, "count": 2, "mean": 68.5, "p50": 59.7, "median": 59.7, "p75": 59.7, "p90": 59.7, "p95": 59.7, "p99": 59.7, "p999": 59.7}}}, {"counters": {"http.codes.200": 14, "http.responses": 20, "http.downloaded_bytes": 124194, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 14, "vusers.created_by_name.10 Users - Mugshots API": 14, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 14, "http.requests": 20, "vusers.failed": 0, "vusers.completed": 20, "vusers.created_by_name.10 Users - Details API": 6, "errors.Undefined function \"generateRandomMugshotId\"": 6, "http.codes.400": 6, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 6}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753539070003, "firstHistogramAt": 1753539070083, "lastCounterAt": 1753539079951, "lastHistogramAt": 1753539079623, "firstMetricAt": 1753539070003, "lastMetricAt": 1753539079951, "period": "1753539070000", "summaries": {"http.response_time": {"min": 61, "max": 798, "count": 20, "mean": 455.1, "p50": 596, "median": 596, "p75": 608, "p90": 620.3, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "http.response_time.2xx": {"min": 569, "max": 798, "count": 14, "mean": 620.4, "p50": 596, "median": 596, "p75": 608, "p90": 620.3, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 569, "max": 798, "count": 14, "mean": 620.4, "p50": 596, "median": 596, "p75": 608, "p90": 620.3, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "vusers.session_length": {"min": 2587.6, "max": 3080.8, "count": 20, "mean": 2764.6, "p50": 2618.1, "median": 2618.1, "p75": 2893.5, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.4xx": {"min": 61, "max": 74, "count": 6, "mean": 69.3, "p50": 70.1, "median": 70.1, "p75": 73, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 74, "count": 6, "mean": 69.3, "p50": 70.1, "median": 70.1, "p75": 73, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}}, "histograms": {"http.response_time": {"min": 61, "max": 798, "count": 20, "mean": 455.1, "p50": 596, "median": 596, "p75": 608, "p90": 620.3, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "http.response_time.2xx": {"min": 569, "max": 798, "count": 14, "mean": 620.4, "p50": 596, "median": 596, "p75": 608, "p90": 620.3, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 569, "max": 798, "count": 14, "mean": 620.4, "p50": 596, "median": 596, "p75": 608, "p90": 620.3, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "vusers.session_length": {"min": 2587.6, "max": 3080.8, "count": 20, "mean": 2764.6, "p50": 2618.1, "median": 2618.1, "p75": 2893.5, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.4xx": {"min": 61, "max": 74, "count": 6, "mean": 69.3, "p50": 70.1, "median": 70.1, "p75": 73, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 74, "count": 6, "mean": 69.3, "p50": 70.1, "median": 70.1, "p75": 73, "p90": 74.4, "p95": 74.4, "p99": 74.4, "p999": 74.4}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 21, "http.codes.200": 17, "http.responses": 20, "http.downloaded_bytes": 150485, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 17, "vusers.created_by_name.10 Users - Mugshots API": 16, "vusers.created": 20, "errors.Undefined function \"generateRandomFilters\"": 16, "http.requests": 20, "vusers.created_by_name.10 Users - Details API": 4, "errors.Undefined function \"generateRandomMugshotId\"": 4, "http.codes.400": 3, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 3}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753539080003, "firstHistogramAt": 1753539080030, "lastCounterAt": 1753539089951, "lastHistogramAt": 1753539089609, "firstMetricAt": 1753539080003, "lastMetricAt": 1753539089951, "period": "1753539080000", "summaries": {"vusers.session_length": {"min": 2578.9, "max": 3084.7, "count": 21, "mean": 2722, "p50": 2618.1, "median": 2618.1, "p75": 2671, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time": {"min": 66, "max": 679, "count": 20, "mean": 521.5, "p50": 584.2, "median": 584.2, "p75": 608, "p90": 608, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 575, "max": 679, "count": 17, "mean": 601.6, "p50": 596, "median": 596, "p75": 608, "p90": 608, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 575, "max": 679, "count": 17, "mean": 601.6, "p50": 596, "median": 596, "p75": 608, "p90": 608, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.4xx": {"min": 66, "max": 68, "count": 3, "mean": 67, "p50": 67.4, "median": 67.4, "p75": 67.4, "p90": 67.4, "p95": 67.4, "p99": 67.4, "p999": 67.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 66, "max": 68, "count": 3, "mean": 67, "p50": 67.4, "median": 67.4, "p75": 67.4, "p90": 67.4, "p95": 67.4, "p99": 67.4, "p999": 67.4}}, "histograms": {"vusers.session_length": {"min": 2578.9, "max": 3084.7, "count": 21, "mean": 2722, "p50": 2618.1, "median": 2618.1, "p75": 2671, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time": {"min": 66, "max": 679, "count": 20, "mean": 521.5, "p50": 584.2, "median": 584.2, "p75": 608, "p90": 608, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 575, "max": 679, "count": 17, "mean": 601.6, "p50": 596, "median": 596, "p75": 608, "p90": 608, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 575, "max": 679, "count": 17, "mean": 601.6, "p50": 596, "median": 596, "p75": 608, "p90": 608, "p95": 671.9, "p99": 671.9, "p999": 671.9}, "http.response_time.4xx": {"min": 66, "max": 68, "count": 3, "mean": 67, "p50": 67.4, "median": 67.4, "p75": 67.4, "p90": 67.4, "p95": 67.4, "p99": 67.4, "p999": 67.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 66, "max": 68, "count": 3, "mean": 67, "p50": 67.4, "median": 67.4, "p75": 67.4, "p90": 67.4, "p95": 67.4, "p99": 67.4, "p999": 67.4}}}, {"counters": {"http.codes.400": 6, "http.responses": 20, "http.downloaded_bytes": 124205, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 6, "vusers.failed": 0, "vusers.completed": 20, "vusers.created_by_name.10 Users - Details API": 6, "vusers.created": 20, "errors.Undefined function \"generateRandomMugshotId\"": 6, "http.requests": 20, "vusers.created_by_name.10 Users - Mugshots API": 14, "errors.Undefined function \"generateRandomFilters\"": 14, "http.codes.200": 14, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 14}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753539090003, "firstHistogramAt": 1753539090012, "lastCounterAt": 1753539099951, "lastHistogramAt": 1753539099625, "firstMetricAt": 1753539090003, "lastMetricAt": 1753539099951, "period": "1753539090000", "summaries": {"http.response_time": {"min": 61, "max": 616, "count": 20, "mean": 476.9, "p50": 584.2, "median": 584.2, "p75": 596, "p90": 608, "p95": 608, "p99": 608, "p999": 608}, "http.response_time.4xx": {"min": 61, "max": 492, "count": 6, "mean": 205.5, "p50": 68.7, "median": 68.7, "p75": 74.4, "p90": 478.3, "p95": 478.3, "p99": 478.3, "p999": 478.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 492, "count": 6, "mean": 205.5, "p50": 68.7, "median": 68.7, "p75": 74.4, "p90": 478.3, "p95": 478.3, "p99": 478.3, "p999": 478.3}, "vusers.session_length": {"min": 2583, "max": 3494.9, "count": 20, "mean": 2759.6, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 3072.4, "p95": 3464.1, "p99": 3464.1, "p999": 3464.1}, "http.response_time.2xx": {"min": 570, "max": 616, "count": 14, "mean": 593.1, "p50": 596, "median": 596, "p75": 596, "p90": 608, "p95": 608, "p99": 608, "p999": 608}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 570, "max": 616, "count": 14, "mean": 593.1, "p50": 596, "median": 596, "p75": 596, "p90": 608, "p95": 608, "p99": 608, "p999": 608}}, "histograms": {"http.response_time": {"min": 61, "max": 616, "count": 20, "mean": 476.9, "p50": 584.2, "median": 584.2, "p75": 596, "p90": 608, "p95": 608, "p99": 608, "p999": 608}, "http.response_time.4xx": {"min": 61, "max": 492, "count": 6, "mean": 205.5, "p50": 68.7, "median": 68.7, "p75": 74.4, "p90": 478.3, "p95": 478.3, "p99": 478.3, "p999": 478.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 61, "max": 492, "count": 6, "mean": 205.5, "p50": 68.7, "median": 68.7, "p75": 74.4, "p90": 478.3, "p95": 478.3, "p99": 478.3, "p999": 478.3}, "vusers.session_length": {"min": 2583, "max": 3494.9, "count": 20, "mean": 2759.6, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 3072.4, "p95": 3464.1, "p99": 3464.1, "p999": 3464.1}, "http.response_time.2xx": {"min": 570, "max": 616, "count": 14, "mean": 593.1, "p50": 596, "median": 596, "p75": 596, "p90": 608, "p95": 608, "p99": 608, "p999": 608}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 570, "max": 616, "count": 14, "mean": 593.1, "p50": 596, "median": 596, "p75": 596, "p90": 608, "p95": 608, "p99": 608, "p999": 608}}}, {"counters": {"http.codes.400": 1, "http.responses": 6, "http.downloaded_bytes": 44263, "plugins.metrics-by-endpoint.GET /api/mugshots/[id] - 10 users.codes.400": 1, "vusers.failed": 0, "vusers.completed": 10, "vusers.created_by_name.10 Users - Mugshots API": 5, "vusers.created": 5, "errors.Undefined function \"generateRandomFilters\"": 5, "http.requests": 5, "http.codes.200": 5, "plugins.metrics-by-endpoint.GET /api/mugshots - 10 users.codes.200": 5}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753539100003, "firstHistogramAt": 1753539100017, "lastCounterAt": 1753539104611, "lastHistogramAt": 1753539104611, "firstMetricAt": 1753539100003, "lastMetricAt": 1753539104611, "period": "1753539100000", "summaries": {"http.response_time": {"min": 66, "max": 601, "count": 6, "mean": 502.8, "p50": 584.2, "median": 584.2, "p75": 596, "p90": 596, "p95": 596, "p99": 596, "p999": 596}, "http.response_time.4xx": {"min": 66, "max": 66, "count": 1, "mean": 66, "p50": 66, "median": 66, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 66, "max": 66, "count": 1, "mean": 66, "p50": 66, "median": 66, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "vusers.session_length": {"min": 2582.5, "max": 3088.2, "count": 10, "mean": 2742, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.2xx": {"min": 577, "max": 601, "count": 5, "mean": 590.2, "p50": 596, "median": 596, "p75": 596, "p90": 596, "p95": 596, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 577, "max": 601, "count": 5, "mean": 590.2, "p50": 596, "median": 596, "p75": 596, "p90": 596, "p95": 596, "p99": 596, "p999": 596}}, "histograms": {"http.response_time": {"min": 66, "max": 601, "count": 6, "mean": 502.8, "p50": 584.2, "median": 584.2, "p75": 596, "p90": 596, "p95": 596, "p99": 596, "p999": 596}, "http.response_time.4xx": {"min": 66, "max": 66, "count": 1, "mean": 66, "p50": 66, "median": 66, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id] - 10 users": {"min": 66, "max": 66, "count": 1, "mean": 66, "p50": 66, "median": 66, "p75": 66, "p90": 66, "p95": 66, "p99": 66, "p999": 66}, "vusers.session_length": {"min": 2582.5, "max": 3088.2, "count": 10, "mean": 2742, "p50": 2618.1, "median": 2618.1, "p75": 2618.1, "p90": 3072.4, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}, "http.response_time.2xx": {"min": 577, "max": 601, "count": 5, "mean": 590.2, "p50": 596, "median": 596, "p75": 596, "p90": 596, "p95": 596, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - 10 users": {"min": 577, "max": 601, "count": 5, "mean": 590.2, "p50": 596, "median": 596, "p75": 596, "p90": 596, "p95": 596, "p99": 596, "p999": 596}}}]}