import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock Next.js functions
vi.mock('next/cache', () => ({
  revalidatePath: vi.fn()
}))

vi.mock('next/navigation', () => ({
  redirect: vi.fn()
}))

vi.mock('@/lib/supabase/server', () => ({
  createClient: vi.fn()
}))

vi.mock('@/lib/auth-utils', () => ({
  signUpUser: vi.fn(),
  signInUser: vi.fn(),
  validateAuthForm: vi.fn()
}))

describe('Authentication Server Actions', () => {
  let mockSupabase: any
  let mockFormData: FormData

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockSupabase = {
      auth: {
        signOut: vi.fn()
      }
    }

    // Create fresh FormData for each test
    mockFormData = new FormData()
  })

  describe('loginAction', () => {
    it('should handle successful login', async () => {
      const { loginAction } = await import('@/lib/auth-actions')
      const { createClient } = await import('@/lib/supabase/server')
      const { signInUser, validateAuthForm } = await import('@/lib/auth-utils')
             const { redirect } = await import('next/navigation')

      // Setup mocks
      vi.mocked(createClient).mockResolvedValue(mockSupabase)
      vi.mocked(validateAuthForm).mockReturnValue({
        isValid: true,
        errors: {}
      })
      vi.mocked(signInUser).mockResolvedValue({
        success: true,
        user: { id: 'user-123', email: '<EMAIL>' }
      })

      // Setup form data
      mockFormData.append('email', '<EMAIL>')
      mockFormData.append('password', 'Password123!')

      // Act & Assert - should redirect on success
      await expect(loginAction(mockFormData)).rejects.toThrow()
      
      expect(validateAuthForm).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'Password123!'
      }, 'signin')
      expect(signInUser).toHaveBeenCalledWith(mockSupabase, {
        email: '<EMAIL>',
        password: 'Password123!'
      })
    })

    it('should handle validation errors', async () => {
      const { loginAction } = await import('@/lib/auth-actions')
      const { createClient } = await import('@/lib/supabase/server')
      const { validateAuthForm } = await import('@/lib/auth-utils')

      // Setup mocks
      vi.mocked(createClient).mockResolvedValue(mockSupabase)
      vi.mocked(validateAuthForm).mockReturnValue({
        isValid: false,
        errors: { email: 'Invalid email' }
      })

      // Setup form data
      mockFormData.append('email', 'invalid-email')
      mockFormData.append('password', 'Password123!')

      // Act
      const result = await loginAction(mockFormData)

      // Assert
      expect(result).toEqual({
        success: false,
        errors: { email: 'Invalid email' }
      })
    })
  })

  describe('signupAction', () => {
    it('should handle successful signup', async () => {
      const { signupAction } = await import('@/lib/auth-actions')
      const { createClient } = await import('@/lib/supabase/server')
      const { signUpUser, validateAuthForm } = await import('@/lib/auth-utils')

      // Setup mocks
      vi.mocked(createClient).mockResolvedValue(mockSupabase)
      vi.mocked(validateAuthForm).mockReturnValue({
        isValid: true,
        errors: {}
      })
      vi.mocked(signUpUser).mockResolvedValue({
        success: true,
        user: { id: 'user-123', email: '<EMAIL>' }
      })

      // Setup form data
      mockFormData.append('email', '<EMAIL>')
      mockFormData.append('password', 'Password123!')
      mockFormData.append('confirmPassword', 'Password123!')
      mockFormData.append('fullName', 'John Doe')
      mockFormData.append('selectedState', 'CA')
      mockFormData.append('selectedCounty', 'Los Angeles')

      // Act
      const result = await signupAction(mockFormData)

      // Assert
      expect(result).toEqual({
        success: true,
        message: 'Please check your email to confirm your account'
      })
      expect(signUpUser).toHaveBeenCalledWith(mockSupabase, {
        email: '<EMAIL>',
        password: 'Password123!',
        fullName: 'John Doe',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      })
    })
  })

  describe('logoutAction', () => {
    it('should log out user and redirect', async () => {
      const { logoutAction } = await import('@/lib/auth-actions')
      const { createClient } = await import('@/lib/supabase/server')
      const { redirect } = await import('next/navigation')

      // Setup mocks
      vi.mocked(createClient).mockResolvedValue(mockSupabase)

      // Act & Assert - should redirect
      await expect(logoutAction()).rejects.toThrow()
      
      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
    })
  })
}) 