import { NextRequest, NextResponse } from 'next/server'
import { mugshottsFastService } from '@/lib/services/mugshots-fast-service'

/**
 * Ultra-Fast Mugshots API Endpoint
 * 
 * This endpoint bypasses all complex logic and provides the absolute
 * fastest possible response for performance testing.
 */

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('🚀 [FAST API] Starting ultra-fast mugshots request')
    
    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const perPage = Math.min(parseInt(searchParams.get('perPage') || '12'), 48) // Limit to 48 max
    const mode = searchParams.get('mode') || 'baseline' // baseline, fast, lightning, or count
    
    // Filters (for all modes)
    const filters = {
      searchTerm: searchParams.get('search') || undefined,
      state: searchParams.get('state') || undefined,
      county: searchParams.get('county') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      tags: searchParams.get('tags') || undefined,
      sortBy: (searchParams.get('sortBy') as 'newest' | 'top-rated' | 'most-viewed') || 'newest',
    }
    
    console.log(`📋 [FAST API] Mode: ${mode}, Page: ${page}, PerPage: ${perPage}`)
    
    let mugshots = []
    let totalCount = 0
    
    switch (mode) {
      case 'baseline':
        // Absolute fastest - no filters, no count
        mugshots = await mugshottsFastService.getBaseline({ page, perPage })
        totalCount = 1000 // Fake count to avoid slow count query
        break
        
      case 'fast':
        // Fast with minimal filters
        mugshots = await mugshottsFastService.getFastFiltered(filters, { page, perPage })
        totalCount = await mugshottsFastService.getCount(filters)
        break
        
      case 'lightning':
        // Ultra-fast - just IDs from top 1000 records
        const lightningData = await mugshottsFastService.getLightning({ page, perPage })
        mugshots = lightningData.map(item => ({
          id: item.id,
          created_at: new Date().toISOString(),
          firstName: 'Test',
          lastName: 'User',
          dateOfBooking: null,
          stateOfBooking: null,
          countyOfBooking: null,
          imagePath: null
        }))
        totalCount = 100 // Fake small count
        break

      case 'count':
        // Only get count (for testing count performance)
        totalCount = await mugshottsFastService.getCount(filters)
        break
        
      default:
        mugshots = await mugshottsFastService.getBaseline({ page, perPage })
        totalCount = 1000
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log(`✅ [FAST API] Request completed in ${duration}ms`)
    
    // Return response in same format as main API
    return NextResponse.json({
      success: true,
      data: {
        mugshots: mugshots,
        pagination: {
          page,
          perPage,
          totalCount,
          totalPages: Math.ceil(totalCount / perPage),
          hasNextPage: page < Math.ceil(totalCount / perPage),
          hasPreviousPage: page > 1
        },
        filters: mode === 'fast' ? filters : {},
        metadata: {
          queryDuration: duration,
          resultCount: mugshots.length,
          mode: mode,
          timestamp: new Date().toISOString()
        }
      }
    })
    
  } catch (error) {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.error('❌ [FAST API] Error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch mugshots',
      message: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        queryDuration: duration,
        timestamp: new Date().toISOString()
      }
    }, { status: 500 })
  }
}
