import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { toast } from 'sonner'
import RatingInterface from '../../components/RatingInterface'

// Mock dependencies
vi.mock('sonner')
vi.mock('../../lib/services/rating-service')
vi.mock('../../lib/stores/auth-store')

import { submitRating, getRatingStatistics, checkTimeGate } from '../../lib/services/rating-service'
import { useAuthStore } from '../../lib/stores/auth-store'

const mockSubmitRating = submitRating as any
const mockGetRatingStatistics = getRatingStatistics as any
const mockCheckTimeGate = checkTimeGate as any
const mockUseAuthStore = useAuthStore as any

describe('Rating Popup UI Enhancement (Story 3.3)', () => {
  const mockUser = { id: 'test-user', email: '<EMAIL>' }
  const defaultProps = {
    mugshotId: '123',
    className: '',
    compact: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default auth store state
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      isLoading: false,
    })
    
    // Default service responses
    mockCheckTimeGate.mockResolvedValue({
      allowed: true,
      message: 'Rating is available',
    })
    
    mockGetRatingStatistics.mockResolvedValue({
      averageRating: 7.5,
      totalRatings: 10,
      userRating: null
    })
    
    mockSubmitRating.mockResolvedValue({
      success: true,
      message: 'Rating saved successfully!',
    })
  })

  describe('Enhanced Rating Popover UI Display', () => {
    it('should display "Rate" button trigger', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
    })

    it('should open popover with 2x5 grid layout when rate button is clicked', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      // Should show numbers 1-10 in popover
      for (let i = 1; i <= 10; i++) {
        expect(screen.getByRole('button', { name: i.toString() })).toBeInTheDocument()
      }
      
      // Check grid layout structure (first row: 1-5, second row: 6-10)
      const gridContainer = screen.getByTestId('rating-grid')
      expect(gridContainer).toHaveClass('grid-cols-5')
      expect(gridContainer).toHaveClass('grid-rows-2')
    })

    it('should display tag selection buttons with emoji icons', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      // Check for tag buttons with emoji icons
      expect(screen.getByRole('button', { name: /😂.*funny/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /🤪.*wild/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /😱.*scary/i })).toBeInTheDocument()
    })

    it('should show gradient color fill on number hover', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const numberButton = screen.getByRole('button', { name: '5' })
      
      // Check initial state (outlined style)
      expect(numberButton).toHaveClass('border-2')
      expect(numberButton).toHaveClass('bg-transparent')
      
      // Hover over number
      fireEvent.mouseEnter(numberButton)
      
      // Check hover state (gradient fill)
      expect(numberButton).toHaveClass('bg-gradient-to-r')
      expect(numberButton).toHaveClass('from-blue-500')
      expect(numberButton).toHaveClass('to-orange-500')
    })
  })

  describe('Unauthenticated User Experience', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        user: null,
        isLoading: false,
      })
    })

    it('should display disabled rating UI for unauthenticated users', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      // Numbers should be disabled
      for (let i = 1; i <= 10; i++) {
        const numberButton = screen.getByRole('button', { name: i.toString() })
        expect(numberButton).toBeDisabled()
        expect(numberButton).toHaveClass('opacity-50')
      }
      
      // Tag buttons should be disabled
      expect(screen.getByRole('button', { name: /😂.*funny/i })).toBeDisabled()
      expect(screen.getByRole('button', { name: /🤪.*wild/i })).toBeDisabled()
      expect(screen.getByRole('button', { name: /😱.*scary/i })).toBeDisabled()
    })

    it('should show "Login required to rate" message', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      expect(screen.getByText(/login required to rate/i)).toBeInTheDocument()
    })

    it('should not allow rating submission for unauthenticated users', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const numberButton = screen.getByRole('button', { name: '8' })
      fireEvent.click(numberButton)
      
      // Should not call submitRating
      expect(mockSubmitRating).not.toHaveBeenCalled()
    })
  })

  describe('Authenticated User Interaction', () => {
    it('should allow functional rating UI for authenticated users', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      // Numbers should be enabled
      for (let i = 1; i <= 10; i++) {
        const numberButton = screen.getByRole('button', { name: i.toString() })
        expect(numberButton).toBeEnabled()
        expect(numberButton).not.toHaveClass('opacity-50')
      }
      
      // Tag buttons should be enabled
      expect(screen.getByRole('button', { name: /😂.*funny/i })).toBeEnabled()
      expect(screen.getByRole('button', { name: /🤪.*wild/i })).toBeEnabled()
      expect(screen.getByRole('button', { name: /😱.*scary/i })).toBeEnabled()
    })

    it('should submit rating when number is clicked', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const numberButton = screen.getByRole('button', { name: '7' })
      fireEvent.click(numberButton)
      
      expect(mockSubmitRating).toHaveBeenCalledWith('123', 7)
      
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith('Rating saved successfully!')
      })
    })

    it('should maintain existing rating submission flow', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const numberButton = screen.getByRole('button', { name: '9' })
      fireEvent.click(numberButton)
      
      // Should call existing rating service
      expect(mockSubmitRating).toHaveBeenCalledWith('123', 9)
      expect(mockSubmitRating).toHaveBeenCalledTimes(1)
    })
  })

  describe('Responsive Design and Positioning', () => {
    it('should maintain proper popover positioning on mobile', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', { value: 375 })
      Object.defineProperty(window, 'innerHeight', { value: 667 })
      
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const popoverContent = screen.getByTestId('rating-popover-content')
      expect(popoverContent).toHaveClass('max-w-sm') // Mobile-friendly max width
    })

    it('should maintain proper popover positioning on desktop', async () => {
      // Mock desktop viewport
      Object.defineProperty(window, 'innerWidth', { value: 1920 })
      Object.defineProperty(window, 'innerHeight', { value: 1080 })
      
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const popoverContent = screen.getByTestId('rating-popover-content')
      expect(popoverContent).toHaveClass('sm:max-w-md') // Desktop responsive max width
    })

    it('should ensure gradient effects work across devices', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      // Test gradient transitions on different numbers
      for (let i = 1; i <= 10; i++) {
        const numberButton = screen.getByRole('button', { name: i.toString() })
        fireEvent.mouseEnter(numberButton)
        
        // Should have smooth transition classes
        expect(numberButton).toHaveClass('transition-all')
        expect(numberButton).toHaveClass('duration-300')
      }
    })
  })

  describe('Existing System Compatibility', () => {
    it('should preserve existing authentication state handling', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      // Should use the same auth store
      expect(mockUseAuthStore).toHaveBeenCalled()
    })

    it('should not modify existing rating submission API', async () => {
      render(<RatingInterface {...defaultProps} />)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /rate/i })).toBeInTheDocument()
      })
      
      const rateButton = screen.getByRole('button', { name: /rate/i })
      fireEvent.click(rateButton)
      
      const numberButton = screen.getByRole('button', { name: '6' })
      fireEvent.click(numberButton)
      
      // Should call the same API function with same parameters
      expect(mockSubmitRating).toHaveBeenCalledWith('123', 6)
    })

    it('should maintain compact mode functionality', async () => {
      render(<RatingInterface {...defaultProps} compact={true} />)
      
      await waitFor(() => {
        const rateButton = screen.getByRole('button', { name: /rate/i })
        expect(rateButton).toHaveClass('h-8') // Compact button size
      })
    })
  })
}) 