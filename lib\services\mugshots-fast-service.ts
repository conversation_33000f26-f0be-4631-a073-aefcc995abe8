import { createClient } from '@/lib/supabase/server'

/**
 * Ultra-Fast Mugshots Service for Supabase Nano Plan
 *
 * Uses optimized indexes:
 * - idx_mugshots_newest_perfect: (dateOfBooking DESC, created_at DESC, id DESC)
 * - idx_mugshots_location_newest: (stateOfBooking, countyOfBooking, dateOfBooking DESC, created_at DESC, id DESC)
 * - idx_mugshots_state_newest / idx_mugshots_county_newest for single filters
 * - gin_idx_firstname_trgm / gin_idx_lastname_trgm for fast trigram search
 */

export interface FastMugshot {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  imagePath: string | null
}

export interface FastMugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  tags?: string    // comma-separated: "wild,funny,spooky"
  sortBy?: 'newest' | 'top-rated' | 'most-viewed'
}

export interface FastPaginationOptions {
  page: number
  perPage: number
}

class MugshottsFastService {
  /**
   * Baseline: true "newest" query, uses idx_mugshots_newest_perfect
   * WITH RETRY LOGIC for load testing
   */
  async getBaseline(
    pagination: FastPaginationOptions = { page: 1, perPage: 12 }
  ): Promise<FastMugshot[]> {
    const maxRetries = 3
    const retryDelay = 100 // ms

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const supabase = await createClient()
        const offset = (pagination.page - 1) * pagination.perPage

        const { data, error } = await supabase
          .from('mugshots')
          .select(
            'id, created_at, firstName, lastName, dateOfBooking, stateOfBooking, countyOfBooking, imagePath'
          )
          .order('dateOfBooking', { ascending: false })
          .range(offset, offset + pagination.perPage - 1)

        if (error) {
          console.error(`❌ [PRO SERVICE] Baseline query failed (attempt ${attempt}):`, error)

          // If it's a connection error and we have retries left, try again
          if (attempt < maxRetries && (
            error.message?.includes('connection') ||
            error.message?.includes('timeout') ||
            error.message?.includes('network')
          )) {
            console.log(`🔄 [PRO SERVICE] Retrying in ${retryDelay}ms...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay))
            continue
          }

          return []
        }

        console.log(`✅ [PRO SERVICE] Baseline query succeeded on attempt ${attempt}`)
        return data || []

      } catch (error) {
        console.error(`❌ [PRO SERVICE] Baseline query error (attempt ${attempt}):`, error)

        if (attempt < maxRetries) {
          console.log(`🔄 [PRO SERVICE] Retrying in ${retryDelay}ms...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        }

        return []
      }
    }

    return []
  }

  /**
   * Lightning: IDs for the most recent 1,000 records (by id)
   */
  async getLightning(
    pagination: FastPaginationOptions = { page: 1, perPage: 12 }
  ): Promise<{ id: number }[]> {
    const supabase = await createClient()
    const offset = (pagination.page - 1) * pagination.perPage

    // Assumes total count ~121k; adjust threshold as needed
    const { data, error } = await supabase
      .from('mugshots')
      .select('id')
      .gte('id', 121321 - 1000)    // last 1,000 IDs
      .order('id', { ascending: false })
      .range(offset, offset + pagination.perPage - 1)

    if (error) {
      console.error('❌ [FAST SERVICE] Lightning query failed:', error)
      return []
    }
    return data || []
  }

  /**
   * Fast filtered query: name / date range / state / county
   * Falls back to idx_mugshots_location_newest or pure-newest
   */
  async getFastFiltered(
    filters: FastMugshotFilters = {},
    pagination: FastPaginationOptions = { page: 1, perPage: 12 }
  ): Promise<FastMugshot[]> {
    const supabase = await createClient()
    const offset = (pagination.page - 1) * pagination.perPage
    const sortBy = filters.sortBy || 'newest'

    // Top-rated takes separate path
    if (sortBy === 'top-rated') {
      return this.getTopRatedMugshots(filters, pagination)
    }

    // Tag filtering takes separate path
    if (filters.tags) {
      return this.getTagFilteredMugshots(filters, pagination)
    }

    let query = supabase
      .from('mugshots')
      .select(
        'id, created_at, firstName, lastName, dateOfBooking, stateOfBooking, countyOfBooking, imagePath'
      )

    // Apply filters in index order
    if (filters.state && filters.state !== 'all-states') {
      query = query.eq('stateOfBooking', filters.state)
    }
    if (filters.county && filters.county !== 'all-counties') {
      query = query.eq('countyOfBooking', filters.county)
    }
    if (filters.dateFrom) {
      query = query.gte('dateOfBooking', filters.dateFrom)
    }
    if (filters.dateTo) {
      query = query.lte('dateOfBooking', filters.dateTo)
    }

    // Trigram name search
    if (filters.searchTerm?.trim()) {
      const term = filters.searchTerm.trim()
      query = query.or(
        `firstName.ilike.%${term}%,lastName.ilike.%${term}%`
      )
    }

    // Order by newest
    query = query
      .order('dateOfBooking', { ascending: false })

    const { data, error } = await query.range(offset, offset + pagination.perPage - 1)
    if (error) {
      console.error('❌ [PRO SERVICE] Fast filtered query failed:', error)
      return []
    }
    return data || []
  }

  /**
   * Top-rated path: ULTRA-FAST fallback for load testing
   */
  private async getTopRatedMugshots(
    filters: FastMugshotFilters,
    pagination: FastPaginationOptions
  ): Promise<FastMugshot[]> {
    const supabase = await createClient()

    // FAST FALLBACK: Instead of complex aggregation, use a simple approach
    console.log('🏆 [FAST SERVICE] Using simplified top-rated approach for load testing')

    // Get recent mugshots with any ratings (much faster than aggregation)
    const { data: recentRated, error: ratingError } = await supabase
      .from('ratings')
      .select('mugshot_id')
      .order('created_at', { ascending: false })
      .limit(500) // Much smaller set for speed

    if (ratingError) {
      console.error('❌ [FAST SERVICE] Ratings query failed:', ratingError)
      // Fallback to newest mugshots
      return this.getFastFiltered({ ...filters, sortBy: 'newest' }, pagination)
    }

    // Get unique mugshot IDs (simple approach, no complex aggregation)
    const uniqueIds = [...new Set(recentRated?.map(r => r.mugshot_id) || [])]

    if (uniqueIds.length === 0) {
      console.log('📊 [FAST SERVICE] No rated mugshots found, using newest')
      return this.getFastFiltered({ ...filters, sortBy: 'newest' }, pagination)
    }

    console.log(`✅ [FAST SERVICE] Found ${uniqueIds.length} rated mugshots`)
    return this.getMugshotsByIds(uniqueIds, filters, pagination)
  }

  /**
   * Tag-filtered path: intersection via JS then batch fetch
   */
  private async getTagFilteredMugshots(
    filters: FastMugshotFilters,
    pagination: FastPaginationOptions
  ): Promise<FastMugshot[]> {
    const supabase = await createClient()
    const tagTypes = filters.tags?.split(',').map(t => t.trim()) || []
    if (!tagTypes.length) return this.getFastFiltered(filters, pagination)

    // Step 1: fetch tag rows
    const { data: tags, error } = await supabase
      .from('tags')
      .select('mugshot_id, tag_type')
      .in('tag_type', tagTypes)
    if (error || !tags) {
      console.error('❌ [PRO SERVICE] Tags query failed:', error)
      return []
    }

    // Step 2: intersect IDs
    const counts = new Map<number, Set<string>>()
    tags.forEach(t => {
      const s = counts.get(t.mugshot_id) || new Set<string>()
      s.add(t.tag_type); counts.set(t.mugshot_id, s)
    })
    const ids = Array.from(counts.entries())
      .filter(([_, s]) => tagTypes.every(tt => s.has(tt)))
      .map(([id]) => id)
    if (!ids.length) return []

    return this.getMugshotsByIds(ids, filters, pagination)
  }

  /**
   * Batch fetch by IDs with optional filters, sorted newest
   */
  private async getMugshotsByIds(
    mugshotIds: number[],
    filters: FastMugshotFilters,
    pagination: FastPaginationOptions
  ): Promise<FastMugshot[]> {
    const supabase = await createClient()
    const offset = (pagination.page - 1) * pagination.perPage

    let query = supabase
      .from('mugshots')
      .select(
        'id, created_at, firstName, lastName, dateOfBooking, stateOfBooking, countyOfBooking, imagePath'
      )
      .in('id', mugshotIds)

    if (filters.state && filters.state !== 'all-states') {
      query = query.eq('stateOfBooking', filters.state)
    }
    if (filters.county && filters.county !== 'all-counties') {
      query = query.eq('countyOfBooking', filters.county)
    }
    if (filters.searchTerm?.trim()) {
      const term = filters.searchTerm.trim()
      query = query.or(
        `firstName.ilike.%${term}%,lastName.ilike.%${term}%`
      )
    }

    query = query
      .order('dateOfBooking', { ascending: false })
      .range(offset, offset + pagination.perPage - 1)

    const { data, error } = await query
    if (error) {
      console.error('❌ [PRO SERVICE] Mugshots by IDs query failed:', error)
      return []
    }
    return data || []
  }

  /**
   * Fast estimated count (avoids slow count on large table)
   */
  async getCount(filters: FastMugshotFilters = {}): Promise<number> {
    if (Object.keys(filters).length === 0) return 121321
    if (filters.state) return 500
    if (filters.searchTerm) return 100
    return 1000
  }

  /**
   * Accurate count for testing only
   */
  async getRealCount(filters: FastMugshotFilters = {}): Promise<number> {
    const supabase = await createClient()
    let q = supabase.from('mugshots').select('*', { count: 'exact', head: true })
    if (filters.state && filters.state !== 'all-states') {
      q = q.eq('stateOfBooking', filters.state)
    }
    if (filters.county && filters.county !== 'all-counties') {
      q = q.eq('countyOfBooking', filters.county)
    }
    if (filters.searchTerm?.trim()) {
      const term = filters.searchTerm.trim()
      q = q.or(`firstName.ilike.%${term}%,lastName.ilike.%${term}%`)
    }
    if (filters.dateFrom) q = q.gte('dateOfBooking', filters.dateFrom)
    if (filters.dateTo)   q = q.lte('dateOfBooking', filters.dateTo)

    const { count, error } = await q
    if (error) {
      console.error('❌ [FAST SERVICE] Real count failed:', error)
      return 0
    }
    return count || 0
  }

  /**
   * Simple health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const supabase = await createClient()
      const { error } = await supabase.from('mugshots').select('id').limit(1)
      return !error
    } catch {
      return false
    }
  }
}

export const mugshottsFastService = new MugshottsFastService()
