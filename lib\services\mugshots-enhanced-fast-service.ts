/**
 * Enhanced Fast Mugshots Service
 * 
 * Combines the performance of fast service with essential features
 * needed for frontend compatibility (ratings, tags, user data)
 */

import { createClient } from '@/lib/supabase/server'
import type { DatabaseMugshot } from './mugshots-native-service'

export interface EnhancedFastMugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  tags?: string // comma-separated: "wild,funny,spooky"
  sortBy?: 'newest' | 'top-rated' | 'most-viewed'
}

export interface EnhancedFastPaginationOptions {
  page: number
  perPage: number
}

class MugshotsEnhancedFastService {
  /**
   * Fast query with essential rating/tag data for frontend compatibility
   */
  async getMugshots(
    filters: EnhancedFastMugshotFilters = {},
    pagination: EnhancedFastPaginationOptions = { page: 1, perPage: 12 },
    userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      const supabase = await createClient()
      const offset = (pagination.page - 1) * pagination.perPage
      const sortBy = filters.sortBy || 'newest'

      // Step 1: Get mugshots with optimized query (same as fast service)
      let query = supabase
        .from('mugshots')
        .select(`
          id, created_at, firstName, lastName, dateOfBooking,
          stateOfBooking, countyOfBooking, imagePath,
          offenseDescription, additionalDetails, fb_status,
          adsText, jb_post_link, jb_fb_post
        `)

      // Apply filters (same logic as fast service)
      if (filters.state && filters.state !== 'all-states') {
        query = query.eq('stateOfBooking', filters.state)
      }
      if (filters.county && filters.county !== 'all-counties') {
        query = query.eq('countyOfBooking', filters.county)
      }
      if (filters.searchTerm?.trim()) {
        const term = filters.searchTerm.trim()
        query = query.or(`firstName.ilike.%${term}%,lastName.ilike.%${term}%`)
      }
      if (filters.dateFrom) {
        query = query.gte('dateOfBooking', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('dateOfBooking', filters.dateTo)
      }

      // Apply sorting with optimized indexes
      if (sortBy === 'newest') {
        query = query
          .order('dateOfBooking', { ascending: false })
          .order('created_at', { ascending: false })
          .order('id', { ascending: false })
      }

      const { data: mugshots, error } = await query.range(offset, offset + pagination.perPage - 1)

      if (error || !mugshots?.length) {
        console.error('❌ [ENHANCED FAST] Mugshots query failed:', error)
        return []
      }

      const mugshotIds = mugshots.map(m => m.id)

      // Step 2: Get rating and tag data in parallel (optimized queries)
      const [ratingsResult, tagsResult, userRatingsResult, userTagsResult] = await Promise.all([
        // Aggregate ratings efficiently
        supabase
          .from('ratings')
          .select('mugshot_id, rating')
          .in('mugshot_id', mugshotIds),
        
        // Get tag counts efficiently  
        supabase
          .from('tags')
          .select('mugshot_id, tag_type')
          .in('mugshot_id', mugshotIds),
        
        // User-specific ratings (if userId provided)
        userId ? supabase
          .from('ratings')
          .select('mugshot_id, rating')
          .in('mugshot_id', mugshotIds)
          .eq('user_id', userId) : Promise.resolve({ data: [] }),
        
        // User-specific tags (if userId provided)
        userId ? supabase
          .from('tags')
          .select('mugshot_id, tag_type')
          .in('mugshot_id', mugshotIds)
          .eq('user_id', userId) : Promise.resolve({ data: [] })
      ])

      // Step 3: Process rating data efficiently
      const ratingMap: Record<number, { sum: number; count: number }> = {}
      ratingsResult.data?.forEach((r: { mugshot_id: number; rating: number }) => {
        if (!ratingMap[r.mugshot_id]) {
          ratingMap[r.mugshot_id] = { sum: 0, count: 0 }
        }
        ratingMap[r.mugshot_id].sum += r.rating
        ratingMap[r.mugshot_id].count += 1
      })

      // Step 4: Process tag data efficiently
      const tagMap: Record<number, Record<string, number>> = {}
      tagsResult.data?.forEach((t: { mugshot_id: number; tag_type: string }) => {
        if (!tagMap[t.mugshot_id]) {
          tagMap[t.mugshot_id] = { wild: 0, funny: 0, spooky: 0 }
        }
        tagMap[t.mugshot_id][t.tag_type] = (tagMap[t.mugshot_id][t.tag_type] || 0) + 1
      })

      // Step 5: Process user-specific data
      const userRatingMap: Record<number, number> = {}
      userRatingsResult.data?.forEach((r: { mugshot_id: number; rating: number }) => {
        userRatingMap[r.mugshot_id] = r.rating
      })

      const userTagMap: Record<number, string[]> = {}
      userTagsResult.data?.forEach((t: { mugshot_id: number; tag_type: string }) => {
        if (!userTagMap[t.mugshot_id]) {
          userTagMap[t.mugshot_id] = []
        }
        userTagMap[t.mugshot_id].push(t.tag_type)
      })

      // Step 6: Combine all data efficiently
      return mugshots.map(mugshot => {
        const ratings = ratingMap[mugshot.id]
        const tags = tagMap[mugshot.id] || { wild: 0, funny: 0, spooky: 0 }
        
        return {
          ...mugshot,
          // Rating statistics
          average_rating: ratings ? +(ratings.sum / ratings.count).toFixed(2) : 0,
          total_ratings: ratings?.count || 0,
          // Tag counts
          wild_count: tags.wild || 0,
          funny_count: tags.funny || 0,
          spooky_count: tags.spooky || 0,
          // User-specific data
          user_rating: userRatingMap[mugshot.id] || null,
          user_tags: userTagMap[mugshot.id] || []
        }
      })

    } catch (error) {
      console.error('❌ [ENHANCED FAST] Error fetching mugshots:', error)
      return []
    }
  }

  /**
   * Fast count with same logic as fast service
   */
  async getCount(filters: EnhancedFastMugshotFilters = {}): Promise<number> {
    // Use same estimation logic as fast service for speed
    if (Object.keys(filters).length === 0) return 121321
    if (filters.state) return 500
    if (filters.searchTerm) return 100
    return 1000
  }
}

export const mugshotsEnhancedFastService = new MugshotsEnhancedFastService()
