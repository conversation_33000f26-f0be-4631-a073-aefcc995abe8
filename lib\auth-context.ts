import { createClient } from '@/lib/supabase/server'
import { getProfile, Profile } from '@/lib/profile-utils'

interface AuthUser {
  id: string
  email?: string
  user_metadata?: Record<string, unknown>
  app_metadata?: Record<string, unknown>
  created_at?: string
}

export interface UserSession {
  user: AuthUser | null
  profile: Profile | null
  isAuthenticated: boolean
  isAdmin: boolean
}

export interface AdminOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export async function getCurrentUser(): Promise<UserSession> {
  const supabase = await createClient()
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
      return {
        user: null,
        profile: null,
        isAuthenticated: false,
        isAdmin: false
      }
    }

    // Get user profile
    const profileResult = await getProfile(supabase, user.id)
    const profile = profileResult.success ? profileResult.profile : null

    return {
      user,
      profile: profile || null,
      isAuthenticated: true,
      isAdmin: profile?.role === 'admin'
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    return {
      user: null,
      profile: null,
      isAuthenticated: false,
      isAdmin: false
    }
  }
}

export async function requireAuth(): Promise<UserSession> {
  const session = await getCurrentUser()
  
  if (!session.isAuthenticated) {
    throw new Error('Authentication required')
  }
  
  return session
}

export async function requireAdmin(): Promise<UserSession> {
  const session = await getCurrentUser()
  
  if (!session.isAuthenticated) {
    throw new Error('Authentication required')
  }
  
  if (!session.isAdmin) {
    throw new Error('Admin access required')
  }
  
  return session
}

export function checkIsAdmin(profile: Profile | null): boolean {
  return profile?.role === 'admin'
} 