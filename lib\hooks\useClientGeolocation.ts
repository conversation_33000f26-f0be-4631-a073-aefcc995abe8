import { useState, useEffect } from 'react'

interface GeolocationResult {
  state: string | null
  county: string | null
  loading: boolean
  error: string | null
}

interface LocationCoordinates {
  latitude: number
  longitude: number
}

// You'll need to implement this function based on your available states/counties data
// This is a placeholder that would need to be connected to your actual location data
async function findNearestLocationFromCoords(coords: LocationCoordinates): Promise<{ state: string; county: string } | null> {
  try {
    // This would typically call a reverse geocoding service or lookup in your database
    // For now, this is a placeholder implementation
    const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${coords.latitude}&longitude=${coords.longitude}&localityLanguage=en`)
    const data = await response.json()
    
    if (data.principalSubdivision && data.locality) {
      return {
        state: data.principalSubdivision,
        county: data.locality
      }
    }
    
    return null
  } catch (error) {
    console.error('Error in reverse geocoding:', error)
    return null
  }
}

export function useClientGeolocation(): GeolocationResult {
  const [result, setResult] = useState<GeolocationResult>({
    state: null,
    county: null,
    loading: false,
    error: null
  })

  useEffect(() => {
    // Only attempt geolocation for guest users (you might want to check auth state here)
    if (!navigator.geolocation) {
      setResult(prev => ({
        ...prev,
        error: 'Geolocation is not supported by this browser'
      }))
      return
    }

    setResult(prev => ({ ...prev, loading: true }))

    const options = {
      enableHighAccuracy: false, // Faster, less accurate
      timeout: 10000, // 10 second timeout
      maximumAge: 600000 // Accept location cached within 10 minutes
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const coords = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          }

          const location = await findNearestLocationFromCoords(coords)
          
          setResult({
            state: location?.state || null,
            county: location?.county || null,
            loading: false,
            error: null
          })
        } catch {
          setResult({
            state: null,
            county: null,
            loading: false,
            error: 'Failed to determine location from coordinates'
          })
        }
      },
      (error) => {
        let errorMessage = 'Failed to get location'
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user'
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable'
            break
          case error.TIMEOUT:
            errorMessage = 'Location request timed out'
            break
        }

        setResult({
          state: null,
          county: null,
          loading: false,
          error: errorMessage
        })
      },
      options
    )
  }, [])

  return result
} 