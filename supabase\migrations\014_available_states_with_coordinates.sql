-- Create state coordinates reference table
CREATE TABLE public.state_coordinates (
  id SERIAL PRIMARY KEY,
  state_name TEXT UNIQUE NOT NULL,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert US state coordinates
INSERT INTO public.state_coordinates (state_name, latitude, longitude) VALUES
  ('Alabama', 32.3617, -86.2792),
  ('Alaska', 64.0685, -152.2782),
  ('Arizona', 34.2744, -111.2847),
  ('Arkansas', 34.7519, -92.1313),
  ('California', 36.7783, -119.4179),
  ('Colorado', 39.1612, -105.3515),
  ('Connecticut', 41.6219, -72.7273),
  ('Delaware', 38.9108, -75.5277),
  ('Florida', 27.7663, -82.6404),
  ('Georgia', 32.9866, -83.6487),
  ('Hawaii', 19.8968, -155.5828),
  ('Idaho', 44.2394, -114.5103),
  ('Illinois', 40.3363, -89.0022),
  ('Indiana', 39.8647, -86.2604),
  ('Iowa', 42.0046, -93.2140),
  ('Kansas', 38.5266, -96.7265),
  ('Kentucky', 37.6668, -84.6670),
  ('Louisiana', 31.1801, -91.8749),
  ('Maine', 44.6074, -69.3977),
  ('Maryland', 39.0639, -76.8021),
  ('Massachusetts', 42.2373, -71.5314),
  ('Michigan', 43.3266, -84.5361),
  ('Minnesota', 45.7326, -93.9196),
  ('Mississippi', 32.7673, -89.6812),
  ('Missouri', 38.4623, -92.3020),
  ('Montana', 47.0527, -110.2148),
  ('Nebraska', 41.1289, -98.2883),
  ('Nevada', 38.4199, -117.1219),
  ('New Hampshire', 43.4108, -71.5653),
  ('New Jersey', 40.3140, -74.5089),
  ('New Mexico', 34.8375, -106.2371),
  ('New York', 42.1657, -74.9481),
  ('North Carolina', 35.6301, -79.8064),
  ('North Dakota', 47.5362, -99.7930),
  ('Ohio', 40.3736, -82.7755),
  ('Oklahoma', 35.5376, -96.9247),
  ('Oregon', 44.5672, -122.1269),
  ('Pennsylvania', 40.5773, -77.2640),
  ('Rhode Island', 41.6762, -71.5562),
  ('South Carolina', 33.8191, -80.9066),
  ('South Dakota', 44.2853, -99.4632),
  ('Tennessee', 35.7449, -86.7489),
  ('Texas', 31.9686, -99.9018),
  ('Utah', 40.1135, -111.8535),
  ('Vermont', 44.0429, -72.7108),
  ('Virginia', 37.7693, -78.2057),
  ('Washington', 47.3917, -121.5708),
  ('West Virginia', 38.4680, -80.9696),
  ('Wisconsin', 44.2619, -89.6165),
  ('Wyoming', 42.7475, -107.2085);

-- Create index for faster lookups
CREATE INDEX idx_state_coordinates_name ON public.state_coordinates(state_name);


-- Create view for available states with coordinates
CREATE OR REPLACE VIEW public.available_states_with_coordinates AS
SELECT DISTINCT
  m."stateOfBooking" as state_name,
  sc.latitude,
  sc.longitude,
  COUNT(m.id) as total_mugshots
FROM public.mugshots m
INNER JOIN public.state_coordinates sc ON m."stateOfBooking" = sc.state_name
WHERE m."stateOfBooking" IS NOT NULL 
  AND m."stateOfBooking" != ''
GROUP BY m."stateOfBooking", sc.latitude, sc.longitude
ORDER BY m."stateOfBooking";
