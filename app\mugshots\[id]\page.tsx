import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { ArrowLeft, FileText, Info } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import UniversalNavLink from '@/components/UniversalNavLink'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { mugshotsServiceServer } from '@/lib/services/mugshots-service-server'
import { 
  transformDBMugshotToUI, 
  generateCanonicalUrl, 
  generateMugshotStructuredData,
  generateBreadcrumbStructuredData,
  generateOrganizationStructuredData 
} from '@/lib/utils/mugshot-transforms'
import MugshotDetailClient from './components/MugshotDetailClient'
import MugshotDetailLoading from './components/MugshotDetailLoading'
import MugshotRatingSection from './components/MugshotRatingSection'

// Types
interface PageProps {
  params: Promise<{ id: string }>
}

// Enhanced metadata generation for SEO and social sharing
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = await params
  const mugshotId = parseInt(id, 10)
  
  // Default fallback metadata
  const defaultMetadata: Metadata = {
    title: 'Mugshot Not Found - America\'s Top Mugshots',
    description: 'The requested mugshot could not be found. Browse our extensive collection of arrest records and mugshots.',
    robots: 'noindex, nofollow',
  }
  
  if (isNaN(mugshotId)) {
    return defaultMetadata
  }

  try {
    const dbMugshot = await mugshotsServiceServer.getMugshotById(mugshotId)
    
    if (!dbMugshot) {
      return defaultMetadata
    }

    const mugshot = transformDBMugshotToUI(dbMugshot)
    
    // Enhanced title and description
    const title = `${mugshot.name} - ${mugshot.location} Mugshot | America's Top Mugshots`
    const primaryOffense = (mugshot.offenses || [])[0] || 'various charges'
    const description = `View ${mugshot.name}'s arrest record from ${mugshot.location}. Arrested ${mugshot.arrestDate ? `on ${new Date(mugshot.arrestDate).toLocaleDateString()}` : ''} for ${primaryOffense}. Rate and comment on this mugshot.`
    
    // Canonical URL for SEO
    const canonicalUrl = generateCanonicalUrl(mugshot)
    
    // Enhanced image URL (ensure absolute path)
    const imageUrl = mugshot.image?.startsWith('http') 
      ? mugshot.image 
      : `${process.env.NEXT_PUBLIC_SITE_URL || 'https://americastopmugs.com'}${mugshot.image || '/images/mugshot-placeholder.png'}`
    
    // Rating information for enhanced descriptions
    const ratingInfo = mugshot.averageRating 
      ? ` Average rating: ${mugshot.averageRating.toFixed(1)}/10 (${mugshot.totalRatings || 0} votes)`
      : ''
    
    return {
      title,
      description: description + ratingInfo,
      keywords: `${mugshot.name}, mugshot, arrest record, ${mugshot.location}, ${mugshot.category}, booking photo`,
      
      // Canonical URL for SEO
      alternates: {
        canonical: canonicalUrl,
      },
      
      // Enhanced Open Graph metadata
      openGraph: {
        title: `${mugshot.name} - ${mugshot.location} Mugshot`,
        description: `Rate ${mugshot.name}'s mugshot from ${mugshot.location}. ${primaryOffense}${ratingInfo}`,
        type: 'article',
        url: canonicalUrl,
        siteName: 'America\'s Top Mugshots',
        locale: 'en_US',
        images: [
          {
            url: imageUrl,
            width: 400,
            height: 600,
            alt: `${mugshot.name} mugshot from ${mugshot.location}`,
            type: 'image/jpeg',
          },
          // Additional image sizes for different platforms
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${mugshot.name} arrest record`,
            type: 'image/jpeg',
          }
        ],
        authors: ['America\'s Top Mugshots'],
        publishedTime: dbMugshot.created_at,
        modifiedTime: dbMugshot.created_at,
        section: 'Mugshots',
        tags: [mugshot.category, mugshot.location, 'arrest record', 'booking photo'],
      },
      
      // Enhanced Twitter Card metadata
      twitter: {
        card: 'summary_large_image',
        site: '@AmericasTopMugs',
        creator: '@AmericasTopMugs',
        title: `${mugshot.name} - ${mugshot.location}`,
        description: `Rate this mugshot! ${primaryOffense} from ${mugshot.location}${ratingInfo}`,
        images: [imageUrl],
      },
      
      // Additional metadata for better SEO
      other: {
        'article:author': 'America\'s Top Mugshots',
        'article:section': 'Mugshots',
        'article:tag': [mugshot.category, mugshot.location, 'arrest record'].join(', '),
        'og:image:secure_url': imageUrl,
        'og:image:type': 'image/jpeg',
        'og:video': '', // Explicitly set to avoid issues
        'classification': 'arrest record',
        'coverage': 'Worldwide',
        'distribution': 'Global',
        'rating': 'General',
        'revisit-after': '7 days',
        'robots': 'index, follow, max-image-preview:large, max-snippet:-1',
      },
      
      // Schema.org structured data will be added separately
      verification: {
        google: process.env.GOOGLE_VERIFICATION_CODE,
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Mugshot Details - America\'s Top Mugshots',
      description: 'View detailed mugshot information and arrest records. Rate and comment on mugshots from across America.',
      robots: 'index, follow',
    }
  }
}

// Server Component for data fetching
async function MugshotDetailContent({ params }: PageProps) {
  const { id } = await params
  const mugshotId = parseInt(id, 10)
  
  // Validate ID parameter
  if (isNaN(mugshotId) || mugshotId <= 0) {
    notFound()
  }

  try {
    // Fetch mugshot data server-side
    const dbMugshot = await mugshotsServiceServer.getMugshotById(mugshotId)
    
    if (!dbMugshot) {
      notFound()
    }

    // Transform data for UI
    const mugshot = transformDBMugshotToUI(dbMugshot)

    // Generate structured data for SEO
    const mugshotStructuredData = generateMugshotStructuredData(dbMugshot)
    const breadcrumbStructuredData = generateBreadcrumbStructuredData(dbMugshot)
    const organizationStructuredData = generateOrganizationStructuredData()

    return (
      <div className="min-h-screen bg-black text-white">
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(mugshotStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationStructuredData)
          }}
        />
        
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          {/* Back Navigation - Top Left Only */}
          <div className="mb-8">
            <UniversalNavLink href="/mugshots">
              <Button variant="outline" size="sm" className="border-cyan-500/30 text-white hover:bg-gray-800">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Mugshots
              </Button>
            </UniversalNavLink>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            {/* Left Column - Mugshot Image and Rating (40%) */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex flex-col items-center">
                <div className="relative overflow-hidden rounded-lg border-2 border-cyan-500 bg-gray-800 shadow-lg w-full max-w-[360px]">
                  <Image
                    src={mugshot.image || '/images/mugshot-placeholder.png'}
                    alt={`${mugshot.name} mugshot`}
                    width={360}
                    height={480}
                    className="object-cover w-full h-auto"
                    priority
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-black/80 px-4 py-3">
                    <div className="text-lg font-bold text-white">{mugshot.name}</div>
                    <div className="text-cyan-400 text-sm">{mugshot.location}</div>
                  </div>
                </div>


              </div>

              {/* Mugshot Rating Section with Time Gate Logic */}
              <MugshotRatingSection 
                mugshotId={mugshotId.toString()}
                arrestDate={mugshot.arrestDate}
                location={mugshot.location}
              />
            </div>

            {/* Right Column - Information (60%) */}
            <div className="lg:col-span-3 space-y-6">
              {/* Basic Information */}
              <Card className="bg-gray-900/90 border-cyan-500/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <FileText className="h-5 w-5 text-cyan-400" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-gray-800/50 rounded-lg p-3 border border-cyan-500/20">
                    <h3 className="text-sm uppercase text-gray-400 mb-1">Full Name</h3>
                    <p className="text-white text-lg font-medium">{mugshot.name}</p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-gray-800/50 rounded-lg p-3 border border-cyan-500/20">
                      <h3 className="text-sm uppercase text-gray-400 mb-1">Date of Booking</h3>
                      <p className="text-white font-medium">{mugshot.arrestDate || 'Not available'}</p>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-3 border border-cyan-500/20">
                      <h3 className="text-sm uppercase text-gray-400 mb-1">Location</h3>
                      <p className="text-white font-medium">{mugshot.location}</p>
                    </div>

                    <div className="bg-gray-800/50 rounded-lg p-3 border border-cyan-500/20 md:col-span-2">
                      <h3 className="text-sm uppercase text-gray-400 mb-1">Birth Date</h3>
                      <p className="text-white font-medium">{mugshot.birthDate || 'N/A'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Details */}
              {mugshot.additionalDetails && (() => {
                // Filter out empty, null, or N/A values
                const validEntries = Object.entries(mugshot.additionalDetails).filter(([, value]) => 
                  value && 
                  value !== 'N/A' && 
                  value !== '' && 
                  String(value).trim() !== ''
                );

                return (
                  <Card className="bg-gray-900/90 border-cyan-500/30">
                    <CardHeader>
                      <CardTitle className="text-white">Additional Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {validEntries.length > 0 ? (
                          validEntries.map(([key, value]) => (
                            <div key={key} className="bg-gray-800/50 rounded-lg p-3 border border-cyan-500/20">
                              <h3 className="text-sm uppercase text-gray-400 mb-1">
                                {key.replace(/([A-Z])/g, ' $1').replace(/[_-]/g, ' ').trim()}
                              </h3>
                              <p className="text-white font-medium">{String(value)}</p>
                            </div>
                          ))
                        ) : (
                          <div className="md:col-span-2 bg-gray-800/50 rounded-lg p-3 border border-cyan-500/20">
                            <p className="text-gray-300 leading-relaxed">
                              No additional details available
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })()}

              {/* Charges and Offenses */}
              {(mugshot.offenses || []).length > 0 && (
                <Card className="bg-gray-900/90 border-cyan-500/30">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-white">
                      <Info className="h-5 w-5 text-cyan-400" />
                      Charges & Offenses
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {(mugshot.offenses || []).map((offense, index) => (
                        <div 
                          key={index}
                          className="p-3 bg-gray-800/50 rounded-lg border border-gray-700"
                        >
                          <p className="text-white text-sm leading-relaxed">{offense}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Share Button at Bottom */}
              <div className="pt-4">
                <MugshotDetailClient mugshot={mugshot} />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error loading mugshot details:', error)
    
    // Error boundary fallback
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-500 mb-4">Failed to Load Mugshot</h2>
          <p className="text-gray-400 mb-6">Sorry, we couldn&apos;t load this mugshot. Please try again later.</p>
          <Link href="/mugshots">
            <Button variant="outline" className="border-cyan-500/30 text-white hover:bg-gray-800">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Mugshots
            </Button>
          </Link>
        </div>
      </div>
    )
  }
}

// Main page component with Suspense
export default function MugshotDetailPage({ params }: PageProps) {
  return (
    <Suspense fallback={<MugshotDetailLoading />}>
      <MugshotDetailContent params={params} />
    </Suspense>
  )
} 