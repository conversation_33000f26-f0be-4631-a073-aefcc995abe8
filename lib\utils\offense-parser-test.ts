import { smartParseOffenses, offenseParserExamples } from './mugshot-transforms'

// Test function to validate offense parser
export function testOffenseParser() {
  console.log('🚓 Testing Smart Offense Parser\n')
  
  Object.entries(offenseParserExamples).forEach(([testName, { input, expected }]) => {
    const result = smartParseOffenses(input)
    const passed = JSON.stringify(result) === JSON.stringify(expected)
    
    console.log(`📋 Test: ${testName}`)
    console.log(`Input: "${input}"`)
    console.log(`Expected: ${JSON.stringify(expected, null, 2)}`)
    console.log(`Got: ${JSON.stringify(result, null, 2)}`)
    console.log(`${passed ? '✅ PASSED' : '❌ FAILED'}\n`)
  })
  
  // Additional real-world test cases
  const realWorldTests = [
    {
      name: "Mixed Case DUI",
      input: "carrying firearms while under the influence; dui: drive under the influence of alcohol; driving with license canceled, suspended or revoked; improper turning at intersection",
      expected: [
        "Carrying Firearms While Under The Influence",
        "Dui: Drive Under The Influence Of Alcohol", 
        "Driving With License Canceled, Suspended Or Revoked",
        "Improper Turning At Intersection"
      ]
    },
    {
      name: "Simple Drug Offense",
      input: "POSSESSION OF CONTROLLED SUBSTANCE",
      expected: ["Possession Of Controlled Substance"]
    }
  ]
  
  console.log('🌍 Real-World Test Cases\n')
  realWorldTests.forEach(({ name, input, expected }) => {
    const result = smartParseOffenses(input)
    const passed = JSON.stringify(result) === JSON.stringify(expected)
    
    console.log(`📋 Test: ${name}`)
    console.log(`Input: "${input}"`)
    console.log(`Expected: ${JSON.stringify(expected, null, 2)}`)
    console.log(`Got: ${JSON.stringify(result, null, 2)}`)
    console.log(`${passed ? '✅ PASSED' : '❌ FAILED'}\n`)
  })
}

// Export for easy testing in browser console
export { smartParseOffenses } 