"use client"

import { useState } from "react"
import MugshotCard from "@/components/MugshotCard"
import MugshotModal from "@/components/MugshotModal"
import CategoryOverlay from "@/components/CategoryOverlay"

import type { UIMugshot } from "@/lib/utils/mugshot-transforms"

type MugshotsGridProps = {
  mugshots: UIMugshot[]
  gridView: string
}

export default function MugshotsGrid({ mugshots, gridView }: MugshotsGridProps) {
  // Local state for modal and animations
  const [selectedMugshot, setSelectedMugshot] = useState<UIMugshot | null>(null)
  const [isMugshotModalOpen, setIsMugshotModalOpen] = useState(false)
  const [animationTrigger, setAnimationTrigger] = useState<string | null>(null)

  // Note: Rating statistics are now preloaded in mugshot data from server
  // No need for additional API calls!

  const getGridClasses = () => {
    switch (gridView) {
      case "large":
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      case "compact":
        return "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
      default:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    }
  }

  const handleCardClick = (mugshot: UIMugshot) => {
    setSelectedMugshot(mugshot)
    setIsMugshotModalOpen(true)
  }

  const handleAnimationComplete = () => {
    setAnimationTrigger(null)
  }

  return (
    <>
      <div className={`grid ${getGridClasses()} gap-6 mb-8`}>
        {mugshots.map((mugshot) => (
          <MugshotCard
            key={mugshot.id}
            mugshot={mugshot}
            onClick={handleCardClick}
            cardSize={gridView === "large" ? "large" : "medium"}
            ratingStats={mugshot.averageRating !== undefined ? {
              averageRating: mugshot.averageRating || 0,
              totalRatings: mugshot.totalRatings || 0
            } : undefined}
          />
        ))}
      </div>

      {/* Mugshot Modal */}
      {selectedMugshot && (
        <MugshotModal
          mugshot={{
            ...selectedMugshot,
            arrestDate: selectedMugshot.arrestDate || '',
            birthDate: selectedMugshot.birthDate || '',
            offenses: selectedMugshot.offenses || []
          }}
          isOpen={isMugshotModalOpen}
          onClose={() => setIsMugshotModalOpen(false)}
          preloadedRatingStats={selectedMugshot.averageRating !== undefined ? {
            averageRating: selectedMugshot.averageRating || 0,
            totalRatings: selectedMugshot.totalRatings || 0
          } : undefined}
        />
      )}

      {/* Category Animation Overlay */}
      <CategoryOverlay 
        trigger={animationTrigger as "Hot" | "Funny" | "Wild" | "Scary" | null} 
        onComplete={handleAnimationComplete}
      />
    </>
  )
} 