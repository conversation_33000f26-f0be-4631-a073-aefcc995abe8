/**
 * Artillery Data Generators for Mugshots API Load Testing
 * 
 * This file contains functions to generate realistic random data
 * for testing the mugshots API endpoints with various filter combinations.
 */

// Realistic US states (based on your constants.ts)
const US_STATES = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado",
  "Connecticut", "Delaware", "Florida", "Georgia", "Hawaii", "Idaho",
  "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana",
  "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota",
  "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada",
  "New Hampshire", "New Jersey", "New Mexico", "New York",
  "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon",
  "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota",
  "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington",
  "West Virginia", "Wisconsin", "Wyoming"
];

// Counties by state (sample data based on your constants.ts)
const COUNTIES_BY_STATE = {
  "California": ["Los Angeles", "San Diego", "Orange", "Riverside", "San Bernardino", "Santa Clara"],
  "Texas": ["Harris", "Dallas", "Tarrant", "Bexar", "Travis", "Collin"],
  "Florida": ["Miami-Dade", "Broward", "Palm Beach", "Hillsborough", "Orange", "Pinellas"],
  "New York": ["Kings", "Queens", "New York", "Suffolk", "Bronx", "Nassau"],
  "Illinois": ["Cook", "DuPage", "Lake", "Will", "Kane", "McHenry"],
  "Pennsylvania": ["Philadelphia", "Allegheny", "Montgomery", "Bucks", "Chester", "Delaware"],
  "Ohio": ["Cuyahoga", "Franklin", "Hamilton", "Summit", "Montgomery", "Lucas"],
  "Georgia": ["Fulton", "Gwinnett", "Cobb", "DeKalb", "Clayton", "Cherokee"],
  "North Carolina": ["Mecklenburg", "Wake", "Guilford", "Forsyth", "Cumberland", "Durham"],
  "Michigan": ["Wayne", "Oakland", "Macomb", "Kent", "Genesee", "Washtenaw"]
};

// Tag types (based on your database schema)
const TAG_TYPES = ["wild", "funny", "spooky"];

// Sort options (based on your API)
const SORT_OPTIONS = ["newest", "top-rated", "most-viewed"];

// Per page options (based on your API)
const PER_PAGE_OPTIONS = [12, 24, 48];

// Common search terms for realistic testing
const SEARCH_TERMS = [
  "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
  "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson",
  "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson",
  "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker"
];

// Utility function to get random element from array
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Utility function to get random integer between min and max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Utility function to get random date in the past year
function getRandomDateInPastYear() {
  const now = new Date();
  const pastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const randomTime = pastYear.getTime() + Math.random() * (now.getTime() - pastYear.getTime());
  return new Date(randomTime).toISOString().split('T')[0]; // Return YYYY-MM-DD format
}

// Utility function to decide if something should happen based on probability
function shouldInclude(probability = 0.5) {
  return Math.random() < probability;
}

/**
 * Generate random filters for the mugshots API
 * This simulates realistic user behavior with various filter combinations
 */
function generateRandomFilters(context, events, done) {
  const filters = {};
  
  // Page (always include, simulate pagination)
  filters.page = getRandomInt(1, 10); // Most users stay within first 10 pages
  
  // Per page (always include)
  filters.perPage = getRandomElement(PER_PAGE_OPTIONS);
  
  // Sort by (always include)
  filters.sortBy = getRandomElement(SORT_OPTIONS);
  
  // State filter (60% chance)
  if (shouldInclude(0.6)) {
    filters.state = getRandomElement(US_STATES);
    
    // County filter (30% chance if state is selected)
    if (shouldInclude(0.3) && COUNTIES_BY_STATE[filters.state]) {
      filters.county = getRandomElement(COUNTIES_BY_STATE[filters.state]);
    }
  }
  
  // Search term (25% chance)
  if (shouldInclude(0.25)) {
    filters.searchTerm = getRandomElement(SEARCH_TERMS);
  }
  
  // Tags filter (40% chance)
  if (shouldInclude(0.4)) {
    // Select 1-2 random tags
    const numTags = getRandomInt(1, 2);
    const selectedTags = [];
    for (let i = 0; i < numTags; i++) {
      const tag = getRandomElement(TAG_TYPES);
      if (!selectedTags.includes(tag)) {
        selectedTags.push(tag);
      }
    }
    filters.tags = selectedTags.join(',');
  }
  
  // Date range filters (20% chance)
  if (shouldInclude(0.2)) {
    const fromDate = getRandomDateInPastYear();
    const toDate = getRandomDateInPastYear();
    
    // Ensure fromDate is before toDate
    if (new Date(fromDate) <= new Date(toDate)) {
      filters.dateFrom = fromDate;
      filters.dateTo = toDate;
    } else {
      filters.dateFrom = toDate;
      filters.dateTo = fromDate;
    }
  }
  
  // Set all filter values in context for use in the request
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

/**
 * Generate random mugshot ID for detail page testing
 * Simulates realistic ID ranges based on typical database patterns
 */
function generateRandomMugshotId(context, events, done) {
  // Generate realistic mugshot IDs (assuming IDs range from 1 to 100000)
  // Weight towards lower IDs as they're more likely to exist
  let mugshotId;
  
  if (shouldInclude(0.7)) {
    // 70% chance: Lower ID range (more likely to exist)
    mugshotId = getRandomInt(1, 10000);
  } else if (shouldInclude(0.8)) {
    // 24% chance: Medium ID range
    mugshotId = getRandomInt(10001, 50000);
  } else {
    // 6% chance: Higher ID range
    mugshotId = getRandomInt(50001, 100000);
  }
  
  context.vars.mugshotId = mugshotId;
  return done();
}

/**
 * Generate realistic user behavior patterns
 * This creates more complex scenarios that mimic real user interactions
 */
function generateUserBehaviorPattern(context, events, done) {
  const patterns = [
    'casual_browser',    // Just browsing, minimal filters
    'specific_searcher', // Looking for specific person/criteria
    'location_focused',  // Interested in specific geographic area
    'category_explorer'  // Exploring different tag categories
  ];
  
  const pattern = getRandomElement(patterns);
  context.vars.userPattern = pattern;
  
  switch (pattern) {
    case 'casual_browser':
      context.vars.page = getRandomInt(1, 3);
      context.vars.perPage = 12;
      context.vars.sortBy = 'newest';
      break;
      
    case 'specific_searcher':
      context.vars.searchTerm = getRandomElement(SEARCH_TERMS);
      context.vars.page = getRandomInt(1, 5);
      context.vars.sortBy = getRandomElement(['newest', 'top-rated']);
      break;
      
    case 'location_focused':
      context.vars.state = getRandomElement(US_STATES);
      if (COUNTIES_BY_STATE[context.vars.state]) {
        context.vars.county = getRandomElement(COUNTIES_BY_STATE[context.vars.state]);
      }
      context.vars.sortBy = 'newest';
      break;
      
    case 'category_explorer':
      context.vars.tags = getRandomElement(TAG_TYPES);
      context.vars.sortBy = 'top-rated';
      context.vars.perPage = 24;
      break;
  }
  
  return done();
}

// Export functions for Artillery to use
module.exports = {
  generateRandomFilters,
  generateRandomMugshotId,
  generateUserBehaviorPattern
};
