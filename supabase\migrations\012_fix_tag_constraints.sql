-- Migration 012: Fix Tag Constraints
-- Add unique constraint to prevent users from tagging same mugshot with same tag type multiple times

-- Add unique constraint to enforce one tag per user per mugshot per tag type
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_user_mugshot_tag 
ON public.tags (user_id, mugshot_id, tag_type);

-- Update the tag function to handle constraint violations gracefully
CREATE OR REPLACE FUNCTION public.add_tag_to_mugshot(
    p_mugshot_id BIGINT,
    p_user_id UUID,
    p_tag_type TEXT
)
RETURNS JSON AS $$
DECLARE
    v_already_tagged BOOLEAN;
BEGIN
    -- Validate tag type
    IF p_tag_type NOT IN ('wild', 'funny', 'spooky') THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Invalid tag type. Must be wild, funny, or spooky'
        );
    END IF;
    
    -- Check if user already tagged this mugshot with this tag type
    SELECT EXISTS(
        SELECT 1 FROM public.tags 
        WHERE mugshot_id = p_mugshot_id 
          AND tag_type = p_tag_type 
          AND user_id = p_user_id
    ) INTO v_already_tagged;
    
    IF v_already_tagged THEN
        -- Remove existing tag (toggle behavior)
        DELETE FROM public.tags 
        WHERE mugshot_id = p_mugshot_id 
          AND tag_type = p_tag_type 
          AND user_id = p_user_id;
          
        RETURN json_build_object(
            'success', true,
            'action', 'removed',
            'tag_type', p_tag_type
        );
    ELSE
        -- Add new tag
        INSERT INTO public.tags (mugshot_id, user_id, tag_type)
        VALUES (p_mugshot_id, p_user_id, p_tag_type);
        
        RETURN json_build_object(
            'success', true,
            'action', 'added',
            'tag_type', p_tag_type
        );
    END IF;
    
EXCEPTION 
    WHEN unique_violation THEN
        -- Handle race condition where tag was added between check and insert
        -- Just return that tag already exists (treat as toggle to remove)
        DELETE FROM public.tags 
        WHERE mugshot_id = p_mugshot_id 
          AND tag_type = p_tag_type 
          AND user_id = p_user_id;
          
        RETURN json_build_object(
            'success', true,
            'action', 'removed',
            'tag_type', p_tag_type
        );
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 