<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

### Database Table Structure for Ratings, Voting, and Events

Based on your Next.js project with Supabase and existing `mugshots` table (assuming it has columns like `id`, `arrest_date`, `state`, `county`, etc.), I'll propose additional tables to handle ratings, tags, voting, and the tiered events (daily, weekly, monthly, quarterly, yearly). These tables are designed to enforce the rules you described, such as one rating per user per mugshot, time-based rating windows, and dynamic event configurations.

I'll use Supabase/PostgreSQL conventions for the schema. Each table includes primary keys, foreign keys, timestamps, and constraints where relevant. To make events dynamic, I'll include a configuration table that allows rule changes without schema modifications.

#### 1. Ratings Table

This stores attractiveness ratings (1-10) for each mugshot. Ratings are enabled starting midnight the day after the arrest date and remain open indefinitely, but only ratings from the immediate next day count for daily winner calculations. Enforce one rating per user per mugshot.


| Column Name | Data Type | Description | Constraints/Notes |
| :-- | :-- | :-- | :-- |
| id | uuid | Primary key | Auto-generated |
| mugshot_id | uuid | Foreign key to `mugshots.id` | Not null, references `mugshots` |
| user_id | uuid | Foreign key to users table (assuming you have one, e.g., Supabase auth) | Not null |
| rating | integer | Attractiveness score (1-10) | Check: rating BETWEEN 1 AND 10 |
| created_at | timestamp | Timestamp when rating was submitted | Default: now() |
| updated_at | timestamp | Timestamp of last update | Default: now(), on update now() |

- **Indexes**: Unique composite index on `(mugshot_id, user_id)` to prevent duplicate ratings.
- **Triggers/Functions**: Create a trigger to validate that the rating is submitted after the enabled date (midnight after `mugshots.arrest_date`).


#### 2. Tags Table

This handles tags like Funny, Wild, Spooky. A mugshot can have zero or more tags, assigned by users or admins. Tags could be used for filtering and displaying "max vote" lists on the Funny/Wild/Spooky page.


| Column Name | Data Type | Description | Constraints/Notes |
| :-- | :-- | :-- | :-- |
| id | uuid | Primary key | Auto-generated |
| mugshot_id | uuid | Foreign key to `mugshots.id` | Not null, references `mugshots` |
| user_id | uuid | Foreign key to users | Optional (if user-assigned) |
| tag_type | text | Tag name (e.g., 'funny', 'wild', 'spooky') | Check: tag_type IN ('funny', 'wild', 'spooky') |
| created_at | timestamp | Timestamp when tag was added | Default: now() |

- **Indexes**: Composite index on `(mugshot_id, tag_type)` for quick filtering.
- **Notes**: If tags are votable (e.g., "max vote of wild/spooky/funny"), add a `vote_count` column or use a separate votes table (see below). For now, this assumes simple assignment; extend with counts if needed for "Show All by max vote".


#### 3. Votes Table

This is for voting in weekly, monthly, quarterly, and yearly events (distinct from ratings). Votes are per event, with one vote per user per event. Daily tops use ratings, not votes.


| Column Name | Data Type | Description | Constraints/Notes |
| :-- | :-- | :-- | :-- |
| id | uuid | Primary key | Auto-generated |
| event_id | uuid | Foreign key to `events.id` (see Events table) | Not null |
| mugshot_id | uuid | Foreign key to `mugshots.id` | Not null |
| user_id | uuid | Foreign key to users | Not null |
| created_at | timestamp | Timestamp when vote was cast | Default: now() |

- **Indexes**: Unique composite index on `(event_id, user_id)` to prevent multiple votes per user per event.
- **Triggers**: Validate vote timestamp against event start/end times.


#### 4. Events Table

This central table manages all tiered events (daily, weekly, etc.) dynamically. Each row represents an event instance (e.g., "Daily Top for April 1"). Use this to store winners and configure rules flexibly.


| Column Name | Data Type | Description | Constraints/Notes |
| :-- | :-- | :-- | :-- |
| id | uuid | Primary key | Auto-generated |
| event_type | text | Type: 'daily', 'weekly', 'monthly', 'quarterly', 'yearly' | Not null |
| start_date | date | Start date of the event period (e.g., arrest date for daily) | Not null |
| end_date | date | End date of the event period | Optional |
| voting_start | timestamp | When voting opens (e.g., midnight on day 8 for weekly) | Optional (null for daily, as it uses ratings) |
| voting_end | timestamp | When voting closes (e.g., 24 hours later for weekly) | Optional |
| participants | uuid[] | Array of mugshot_ids eligible (e.g., daily winners for weekly) | JSON or array type |
| winners | uuid[] | Array of winning mugshot_ids (allows ties) | Updated post-event |
| config | jsonb | Dynamic rules (e.g., {"duration_hours": 24, "vote_limit": 1, "calculation": "max_average_rating"}) | For flexibility |
| created_at | timestamp | Timestamp event was created | Default: now() |
| status | text | 'pending', 'active', 'completed' | Default: 'pending' |

- **Indexes**: Index on `event_type` and `start_date` for quick queries.
- **Dynamic Notes**: The `config` JSONB column allows storing changeable rules (e.g., voting duration, calculation method). Use Supabase functions or cron jobs (via pg_cron) to auto-generate events daily/weekly and compute winners.


#### 5. Winners Table (Optional for Quick Access)

For easy querying of all winners across events, without joining everything.


| Column Name | Data Type | Description | Constraints/Notes |
| :-- | :-- | :-- | :-- |
| id | uuid | Primary key | Auto-generated |
| event_id | uuid | Foreign key to `events.id` | Not null |
| mugshot_id | uuid | Foreign key to `mugshots.id` | Not null |
| rank | integer | Optional rank (1 for winner, handles ties) | Default: 1 |
| score | numeric | Winning score (average rating or vote count) | Optional |
| awarded_at | timestamp | When winner was decided | Default: now() |

- **Use Case**: Populate this via triggers after event completion.


### Laid-Out Rules and Events

Here’s a clear breakdown of the rules based on your description. These can be implemented via Supabase edge functions, triggers, or scheduled jobs to enforce timing and calculations. For dynamism, store rules in the `events.config` JSONB and adjust via admin interfaces.

#### General Rules

- **Ratings**: Enabled at midnight the day after `arrest_date`. Open indefinitely, but only ratings from that first enabled day count for daily tops. Calculation: Average rating (sum of ratings / number of raters). Ties allowed; multiple winners possible.
- **Voting**: For weekly+ events. One vote per user per event. Winners by max votes received during voting window.
- **Users**: Assume authenticated via Supabase auth; enforce one action (rate/vote) per user.
- **Tags**: Optional per mugshot (funny, wild, spooky). Use for filtering on dedicated page; sort by "max vote" if votes are added to tags.
- **Accumulated Ratings**: Post-daily, ratings continue but aren't used in current rules. You could extend for future features (e.g., all-time tops) by querying all ratings.


#### Event-Specific Rules

- **Daily Top**:
    - Participants: All mugshots with `arrest_date` = today (e.g., April 1).
    - Rating Window: Opens midnight April 2, considers only April 2 ratings.
    - Winner Decision: April 3 midnight, based on max average rating. Store in `events` table.
- **Weekly Top**:
    - Participants: Winners from past 7 daily events.
    - Voting Window: Starts midnight on day 8, runs 24 hours.
    - Winner: Max votes; ties possible.
- **Monthly Top**:
    - Participants: 4-5 weekly winners from previous month.
    - Voting Window: Starts midnight of next month (adjust for Sunday/Monday), runs 48 hours.
    - Winner: Max votes.
- **Quarterly Top**:
    - Participants: 3 monthly winners from past quarter.
    - Voting Window: Similar to monthly (e.g., 48 hours, configurable).
    - Winner: Max votes.
- **Yearly Top**:
    - Participants: All monthly winners from the year.
    - Voting Window: Starts end of year, runs 7 days.
    - Winner: Max votes.


#### Implementing Dynamism

- Use the `events.config` JSONB for parameters like `duration_hours`, `calculation_method` (e.g., 'average_rating' vs. 'vote_count'), or `participant_source` (e.g., 'previous_daily_winners').
- Create admin pages in Next.js to update configs and generate events.
- Automate with Supabase functions: E.g., a daily cron job to create events, compute winners, and populate participants for higher tiers.


### Additional Features and Page Ideas

#### Page: Funny/Wild/Spooky

- **Title/Description**: Customizable, e.g., "Top Funny Mugshots".
- **Filters**: Dropdowns for State + County; show all mugshots sorted by max votes/tags.
- **Display**: Grid of mugshots with ratings (1-10 attractiveness) and tags.


#### Mugshot Interactions

- **Rating**: Slider or stars for 1-10 attractiveness.
- **Tags**: Checkboxes for funny/wild/spooky (one or more).
- **Social Share**: Buttons for Facebook share and copy link (use Next.js libraries like `react-share`).
- **Detail Popup**: Show mugshot image, offences (with "show more" link to full page), ratings, tags, and share options.


#### Implementation Tips in Next.js/Supabase

- **Frontend**: Use React components for filters, popups, and dynamic lists. Fetch data via Supabase JS client with RLS (row-level security) to enforce user limits.
- **Backend Logic**: Edge functions for rating/voting submission (validate windows). Realtime subscriptions for live updates on voting.
- **Scalability**: Add caching (e.g., Vercel Edge) for high-traffic pages. For winner calculations, use SQL queries or stored procedures.

This structure supports your current rules while allowing future changes through dynamic configs. If you provide more details on your existing `mugshots` schema or users table, I can refine it further.

