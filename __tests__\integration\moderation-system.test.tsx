import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import SimpleModerationQueue from '@/app/admin/components/SimpleModerationQueue'
import ReportContentDialog from '@/components/ReportContentDialog'
import AuditLogViewer from '@/app/admin/components/AuditLogViewer'
import UserModerationPanel from '@/app/admin/components/UserModerationPanel'
import AutoFlaggingManager from '@/app/admin/components/AutoFlaggingManager'

// Mock fetch globally
global.fetch = vi.fn()

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    refresh: vi.fn()
  })
}))

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  // Mock all icons as simple divs
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
  Eye: () => <div data-testid="eye-icon" />,
  Flag: () => <div data-testid="flag-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Image: () => <div data-testid="image-icon" />,
  Download: () => <div data-testid="download-icon" />,
  Search: () => <div data-testid="search-icon" />,
  Filter: () => <div data-testid="filter-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  User: () => <div data-testid="user-icon" />,
  Shield: () => <div data-testid="shield-icon" />,
  Activity: () => <div data-testid="activity-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Brain: () => <div data-testid="brain-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  BarChart3: () => <div data-testid="bar-chart-icon" />,
  Plus: () => <div data-testid="plus-icon" />,
  Edit: () => <div data-testid="edit-icon" />,
  Trash2: () => <div data-testid="trash-icon" />,
  Ban: () => <div data-testid="ban-icon" />,
  ClipboardList: () => <div data-testid="clipboard-icon" />,
  Calendar: () => <div data-testid="calendar-icon" />,
  Mail: () => <div data-testid="mail-icon" />,
  MoreHorizontal: () => <div data-testid="more-horizontal-icon" />
}))

describe('Moderation System Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset fetch mock
    global.fetch = vi.fn()
  })

  describe('SimpleModerationQueue Component', () => {
    it('should render moderation queue with reports', () => {
      render(<SimpleModerationQueue />)
      
      expect(screen.getByText('Moderation Queue')).toBeInTheDocument()
      expect(screen.getByText('Review and moderate flagged content')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Search reports...')).toBeInTheDocument()
    })

    it('should filter reports by status', async () => {
      const user = userEvent.setup()
      render(<SimpleModerationQueue />)
      
      // Find and interact with status filter
      const statusFilter = screen.getByDisplayValue('All Status')
      await user.click(statusFilter)
      
      // Check if filter options are available
      await waitFor(() => {
        expect(screen.getByText('Pending')).toBeInTheDocument()
      })
    })

    it('should search reports by text', async () => {
      const user = userEvent.setup()
      render(<SimpleModerationQueue />)
      
      const searchInput = screen.getByPlaceholderText('Search reports...')
      await user.type(searchInput, 'harassment')
      
      expect(searchInput).toHaveValue('harassment')
    })

    it('should handle bulk actions', async () => {
      const user = userEvent.setup()
      render(<SimpleModerationQueue />)
      
      // Select some reports (assuming checkboxes are rendered)
      const checkboxes = screen.getAllByRole('checkbox')
      if (checkboxes.length > 1) {
        await user.click(checkboxes[1]) // Click first report checkbox
        
        // Bulk actions button should appear
        await waitFor(() => {
          expect(screen.getByText(/Bulk Actions/)).toBeInTheDocument()
        })
      }
    })

    it('should approve individual reports', async () => {
      const user = userEvent.setup()
      render(<SimpleModerationQueue />)
      
      // Look for approve buttons (check circle icons)
      const approveButtons = screen.getAllByTestId('check-circle-icon')
      if (approveButtons.length > 0) {
        const firstApproveButton = approveButtons[0].closest('button')
        if (firstApproveButton) {
          await user.click(firstApproveButton)
          // Should trigger action (we can't test the actual API call in unit tests)
        }
      }
    })

    it('should export reports', async () => {
      const user = userEvent.setup()
      render(<SimpleModerationQueue />)
      
      const exportButton = screen.getByText('Export')
      await user.click(exportButton)
      
      // Should trigger export functionality
      expect(exportButton).toBeInTheDocument()
    })
  })

  describe('ReportContentDialog Component', () => {
    const defaultProps = {
      contentType: 'mugshot' as const,
      contentId: 'test_123',
      onReportSubmitted: vi.fn()
    }

    it('should render report dialog trigger', () => {
      render(<ReportContentDialog {...defaultProps} />)
      
      expect(screen.getByText('Report')).toBeInTheDocument()
    })

    it('should open dialog when trigger is clicked', async () => {
      const user = userEvent.setup()
      render(<ReportContentDialog {...defaultProps} />)
      
      const reportButton = screen.getByText('Report')
      await user.click(reportButton)
      
      await waitFor(() => {
        expect(screen.getByText('Report Mugshot')).toBeInTheDocument()
      })
    })

    it('should submit report with valid data', async () => {
      const user = userEvent.setup()
      const mockOnReportSubmitted = vi.fn()
      
      // Mock successful API response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })
      
      render(
        <ReportContentDialog 
          {...defaultProps} 
          onReportSubmitted={mockOnReportSubmitted}
        />
      )
      
      // Open dialog
      await user.click(screen.getByText('Report'))
      
      await waitFor(() => {
        expect(screen.getByText('Report Mugshot')).toBeInTheDocument()
      })
      
      // Select a reason (this would depend on how the Select component renders)
      // Fill description
      const descriptionField = screen.getByPlaceholderText(/Provide any additional context/)
      if (descriptionField) {
        await user.type(descriptionField, 'This content violates guidelines')
      }
      
      // Submit would require proper reason selection which depends on UI implementation
    })

    it('should handle API errors gracefully', async () => {
      const user = userEvent.setup()
      
      // Mock API error
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
      
      render(<ReportContentDialog {...defaultProps} />)
      
      await user.click(screen.getByText('Report'))
      
      // Should handle error without crashing
      await waitFor(() => {
        expect(screen.getByText('Report Mugshot')).toBeInTheDocument()
      })
    })
  })

  describe('AuditLogViewer Component', () => {
    beforeEach(() => {
      // Mock fetch for audit log data
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          audit_logs: [],
          statistics: {
            actions_today: 5,
            content_removed: 12,
            user_actions: 3
          }
        })
      })
    })

    it('should render audit log viewer', () => {
      render(<AuditLogViewer />)
      
      expect(screen.getByText('Audit Log')).toBeInTheDocument()
      expect(screen.getByText('Track all moderation actions and changes')).toBeInTheDocument()
    })

    it('should filter audit logs by action type', async () => {
      const user = userEvent.setup()
      render(<AuditLogViewer />)
      
      // Look for action type filter
      const actionFilter = screen.getByDisplayValue('All Actions')
      if (actionFilter) {
        await user.click(actionFilter)
      }
    })

    it('should search audit logs', async () => {
      const user = userEvent.setup()
      render(<AuditLogViewer />)
      
      const searchInput = screen.getByPlaceholderText('Search actions...')
      await user.type(searchInput, 'approve')
      
      expect(searchInput).toHaveValue('approve')
    })

    it('should export audit logs', async () => {
      const user = userEvent.setup()
      render(<AuditLogViewer />)
      
      const exportButton = screen.getByText('Export Log')
      await user.click(exportButton)
      
      expect(exportButton).toBeInTheDocument()
    })
  })

  describe('UserModerationPanel Component', () => {
    it('should render user management interface', () => {
      render(<UserModerationPanel />)
      
      expect(screen.getByText('User Management')).toBeInTheDocument()
      expect(screen.getByText('Manage users and enforce community guidelines')).toBeInTheDocument()
    })

    it('should display user statistics', () => {
      render(<UserModerationPanel />)
      
      expect(screen.getByText('Total Users')).toBeInTheDocument()
      expect(screen.getByText('Warned Users')).toBeInTheDocument()
      expect(screen.getByText('Suspended Users')).toBeInTheDocument()
      expect(screen.getByText('Banned Users')).toBeInTheDocument()
    })

    it('should filter users by status', async () => {
      const user = userEvent.setup()
      render(<UserModerationPanel />)
      
      const statusFilter = screen.getByDisplayValue('All Users')
      await user.click(statusFilter)
      
      // Should show filter options
    })

    it('should search users', async () => {
      const user = userEvent.setup()
      render(<UserModerationPanel />)
      
      const searchInput = screen.getByPlaceholderText('Search users...')
      await user.type(searchInput, 'john')
      
      expect(searchInput).toHaveValue('john')
    })
  })

  describe('AutoFlaggingManager Component', () => {
    beforeEach(() => {
      // Mock fetch for auto-flagging data
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          active_rules: [
            {
              id: 'rule_1',
              name: 'Test Rule',
              content_type: 'mugshot',
              rule_type: 'keyword',
              priority: 'high',
              is_active: true
            }
          ],
          statistics: {
            total_rules: 5,
            rules_by_type: { keyword: 3, image_analysis: 1, user_pattern: 1 },
            rules_by_priority: { urgent: 1, high: 2, medium: 1, low: 1 },
            mock_stats: {
              total_content_checked: 1000,
              content_flagged: 50,
              false_positives: 5,
              accuracy_rate: 90
            }
          }
        })
      })
    })

    it('should render auto-flagging manager', async () => {
      render(<AutoFlaggingManager />)
      
      expect(screen.getByText('Auto-Flagging System')).toBeInTheDocument()
      expect(screen.getByText('Configure automated content moderation rules')).toBeInTheDocument()
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Active Rules')).toBeInTheDocument()
      })
    })

    it('should display flagging statistics', async () => {
      render(<AutoFlaggingManager />)
      
      await waitFor(() => {
        expect(screen.getByText('Active Rules')).toBeInTheDocument()
        expect(screen.getByText('Content Checked')).toBeInTheDocument()
        expect(screen.getByText('Content Flagged')).toBeInTheDocument()
        expect(screen.getByText('Accuracy Rate')).toBeInTheDocument()
      })
    })

    it('should open new rule dialog', async () => {
      const user = userEvent.setup()
      render(<AutoFlaggingManager />)
      
      const addRuleButton = screen.getByText('Add Rule')
      await user.click(addRuleButton)
      
      await waitFor(() => {
        expect(screen.getByText('Create New Flagging Rule')).toBeInTheDocument()
      })
    })

    it('should open test content dialog', async () => {
      const user = userEvent.setup()
      render(<AutoFlaggingManager />)
      
      const testButton = screen.getByText('Test Content')
      await user.click(testButton)
      
      await waitFor(() => {
        expect(screen.getByText('Test Auto-Flagging')).toBeInTheDocument()
      })
    })

    it('should test content flagging', async () => {
      const user = userEvent.setup()
      
      // Mock auto-flagging API response
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            active_rules: [],
            statistics: {}
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            flagging_result: {
              should_flag: true,
              confidence_score: 0.85,
              severity: 'high',
              auto_action: 'quarantine',
              reasons: ['Matched keywords: inappropriate'],
              matched_rules: 1
            }
          })
        })
      
      render(<AutoFlaggingManager />)
      
      // Open test dialog
      await user.click(screen.getByText('Test Content'))
      
      await waitFor(() => {
        expect(screen.getByText('Test Auto-Flagging')).toBeInTheDocument()
      })
      
      // Enter test content
      const textArea = screen.getByPlaceholderText('Enter content to test auto-flagging...')
      await user.type(textArea, 'This is inappropriate content')
      
      // Submit test
      const testSubmitButton = screen.getByText('Test Content')
      await user.click(testSubmitButton)
      
      // Should show result
      await waitFor(() => {
        expect(screen.getByText('Content Flagged')).toBeInTheDocument()
      })
    })
  })

  describe('Component Integration', () => {
    it('should handle moderation queue actions and update audit log', async () => {
      const user = userEvent.setup()
      
      // Mock API responses
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Action completed'
          })
        })
      
      const mockOnActionComplete = vi.fn()
      render(<SimpleModerationQueue onActionComplete={mockOnActionComplete} />)
      
      // Perform an action (if available)
      const approveButtons = screen.getAllByTestId('check-circle-icon')
      if (approveButtons.length > 0) {
        const firstApproveButton = approveButtons[0].closest('button')
        if (firstApproveButton) {
          await user.click(firstApproveButton)
          
          await waitFor(() => {
            expect(mockOnActionComplete).toHaveBeenCalled()
          })
        }
      }
    })

    it('should handle report submission and show success state', async () => {
      const user = userEvent.setup()
      
      // Mock successful API response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })
      
      const mockOnReportSubmitted = vi.fn()
      
      render(
        <ReportContentDialog 
          contentType="mugshot"
          contentId="test_123"
          onReportSubmitted={mockOnReportSubmitted}
        />
      )
      
      // Open dialog and submit (simplified test)
      await user.click(screen.getByText('Report'))
      
      await waitFor(() => {
        expect(screen.getByText('Report Mugshot')).toBeInTheDocument()
      })
    })

    it('should handle auto-flagging integration with report creation', async () => {
      // This would test the integration between auto-flagging and report creation
      // Mock the scenario where auto-flagging creates a report automatically
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          flagging_result: {
            should_flag: true,
            confidence_score: 0.9,
            severity: 'urgent',
            auto_action: 'quarantine'
          }
        })
      })
      
      // This would test the full flow from content submission to auto-flagging to report creation
      expect(global.fetch).toBeDefined()
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle network errors gracefully', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
      
      render(<SimpleModerationQueue />)
      
      // Component should render without crashing even with network errors
      expect(screen.getByText('Moderation Queue')).toBeInTheDocument()
    })

    it('should handle empty data states', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          reports: [],
          pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
        })
      })
      
      render(<SimpleModerationQueue />)
      
      await waitFor(() => {
        expect(screen.getByText('No reports found')).toBeInTheDocument()
      })
    })

    it('should handle loading states', () => {
      // Mock slow API response
      global.fetch = vi.fn(() => new Promise(resolve => setTimeout(resolve, 1000)))
      
      render(<SimpleModerationQueue />)
      
      expect(screen.getByText('Loading reports...')).toBeInTheDocument()
    })
  })
}) 