#!/bin/bash

# Graduated Load Testing Script - 10 to 10,000 Users
# This script runs progressive load tests and provides comprehensive analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
BASE_URL="http://localhost:3000"
RESULTS_DIR="load-tests/results/graduated"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Test configurations
declare -a TESTS=(
    "010:load-tests/graduated-tests/test-010-users.yml:10 Users - Baseline"
    "025:load-tests/graduated-tests/test-025-users.yml:25 Users - Light Load"
    "050:load-tests/graduated-tests/test-050-users.yml:50 Users - Medium Load"
    "100:load-tests/graduated-tests/test-100-users.yml:100 Users - High Load"
    "250:load-tests/graduated-tests/test-250-users.yml:250 Users - Stress Test"
    "500:load-tests/graduated-tests/test-500-users.yml:500 Users - Breaking Point"
    "1000:load-tests/graduated-tests/test-1000-users.yml:1000 Users - Extreme Stress"
    "10000:load-tests/graduated-tests/test-10000-users.yml:10000 Users - Ultimate Test"
)

# Ensure results directory exists
mkdir -p "$RESULTS_DIR"

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================================================================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================================================================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test_header() {
    echo -e "${MAGENTA}🚀 $1${NC}"
    echo -e "${MAGENTA}$(echo "$1" | sed 's/./=/g')${NC}"
}

# Function to check if server is running
check_server() {
    print_status "Checking if server is running at $BASE_URL..."
    if curl -s -f "$BASE_URL/api/mugshots?page=1&perPage=1" > /dev/null; then
        print_success "Server is running and API is accessible"
        return 0
    else
        print_error "Server is not running or API is not accessible"
        print_error "Please start your Next.js development server with: npm run dev"
        return 1
    fi
}

# Function to run a specific test
run_test() {
    local test_level="$1"
    local config_file="$2"
    local test_name="$3"
    local output_file="$RESULTS_DIR/test-${test_level}-users-${TIMESTAMP}.json"
    
    print_test_header "TEST $test_level: $test_name"
    print_status "Config: $config_file"
    print_status "Output: $output_file"
    print_status "Expected Duration: $(get_test_duration $test_level) minutes"
    
    # Ask for confirmation for high-load tests
    if [[ "$test_level" -ge 250 ]]; then
        print_warning "This test will put significant load on your system!"
        print_warning "Your Supabase Nano plan may not handle this load well."
        read -p "Do you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Test skipped by user"
            return 0
        fi
    fi
    
    # Run the test
    print_status "Starting test... (Press Ctrl+C to stop if needed)"
    local start_time=$(date +%s)
    
    if npx artillery run "$config_file" --output "$output_file"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        print_success "Test completed in ${duration} seconds"
        
        # Analyze results immediately
        print_status "Analyzing results..."
        node load-tests/analyze-results.js "$output_file"
        
        # Generate summary
        generate_test_summary "$test_level" "$output_file" "$test_name"
        
        return 0
    else
        print_error "Test failed or was interrupted"
        return 1
    fi
}

# Function to get expected test duration
get_test_duration() {
    case "$1" in
        "010") echo "4" ;;
        "025") echo "5" ;;
        "050") echo "6" ;;
        "100") echo "7" ;;
        "250") echo "8" ;;
        "500") echo "9" ;;
        "1000") echo "10" ;;
        "10000") echo "12" ;;
        *) echo "?" ;;
    esac
}

# Function to generate test summary
generate_test_summary() {
    local test_level="$1"
    local result_file="$2"
    local test_name="$3"
    
    print_status "Generating summary for $test_name..."
    
    # Extract key metrics using node
    local summary=$(node -e "
    try {
        const fs = require('fs');
        const data = JSON.parse(fs.readFileSync('$result_file', 'utf8'));
        const agg = data.aggregate;
        const counters = agg.counters || {};
        const summaries = agg.summaries || {};
        const rt = summaries['http.response_time'] || {};
        
        const totalReqs = counters['http.requests'] || 0;
        const successReqs = counters['http.codes.200'] || 0;
        const successRate = totalReqs > 0 ? ((successReqs / totalReqs) * 100).toFixed(1) : '0.0';
        const median = (rt.median || 0).toFixed(0);
        const p95 = (rt.p95 || 0).toFixed(0);
        const errors = counters['errors.total'] || 0;
        
        console.log(\`\${totalReqs}|\${successReqs}|\${successRate}|\${median}|\${p95}|\${errors}\`);
    } catch(e) {
        console.log('ERROR|ERROR|ERROR|ERROR|ERROR|ERROR');
    }
    ")
    
    IFS='|' read -r total_reqs success_reqs success_rate median p95 errors <<< "$summary"
    
    # Determine test result
    local result_status="❌ FAILED"
    local recommendations=""
    
    if [[ "$test_level" -le 50 ]]; then
        if (( $(echo "$success_rate > 95" | bc -l) )); then
            result_status="✅ PASSED"
        elif (( $(echo "$success_rate > 80" | bc -l) )); then
            result_status="⚠️ MARGINAL"
            recommendations="Consider optimizing database queries and adding indexes"
        fi
    elif [[ "$test_level" -le 100 ]]; then
        if (( $(echo "$success_rate > 80" | bc -l) )); then
            result_status="✅ PASSED"
        elif (( $(echo "$success_rate > 60" | bc -l) )); then
            result_status="⚠️ MARGINAL"
            recommendations="Database optimization required. Consider upgrading Supabase plan"
        fi
    else
        if (( $(echo "$success_rate > 50" | bc -l) )); then
            result_status="✅ BETTER THAN EXPECTED"
        elif (( $(echo "$success_rate > 20" | bc -l) )); then
            result_status="⚠️ EXPECTED FAILURE"
            recommendations="Upgrade to higher Supabase plan required for this load"
        fi
    fi
    
    # Display summary
    echo ""
    echo -e "${CYAN}📊 TEST SUMMARY: $test_name${NC}"
    echo "----------------------------------------"
    echo "Total Requests: $total_reqs"
    echo "Successful Requests: $success_reqs"
    echo "Success Rate: $success_rate%"
    echo "Median Response Time: ${median}ms"
    echo "95th Percentile: ${p95}ms"
    echo "Errors: $errors"
    echo "Result: $result_status"
    if [[ -n "$recommendations" ]]; then
        echo "Recommendations: $recommendations"
    fi
    echo "----------------------------------------"
    echo ""
}

# Function to show help
show_help() {
    echo "Graduated Load Testing Script - 10 to 10,000 Users"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  all           Run all tests from 10 to 10,000 users"
    echo "  realistic     Run realistic tests (10, 25, 50 users)"
    echo "  stress        Run stress tests (100, 250, 500 users)"
    echo "  extreme       Run extreme tests (1000, 10000 users)"
    echo "  010           Run 10 users test only"
    echo "  025           Run 25 users test only"
    echo "  050           Run 50 users test only"
    echo "  100           Run 100 users test only"
    echo "  250           Run 250 users test only"
    echo "  500           Run 500 users test only"
    echo "  1000          Run 1000 users test only"
    echo "  10000         Run 10000 users test only"
    echo "  help          Show this help message"
    echo ""
    echo "Recommendations for your Supabase Nano plan:"
    echo "  • Start with 'realistic' tests (10-50 users)"
    echo "  • Only run higher tests after optimizations"
    echo "  • Tests above 100 users will likely fail"
    echo ""
}

# Main execution logic
main() {
    local test_type="${1:-help}"
    
    print_header "GRADUATED LOAD TESTING SUITE"
    print_status "Target: $BASE_URL"
    print_status "Results Directory: $RESULTS_DIR"
    print_status "Timestamp: $TIMESTAMP"
    
    case "$test_type" in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "realistic")
            if ! check_server; then exit 1; fi
            print_header "REALISTIC LOAD TESTS (Recommended for Supabase Nano)"
            run_test "010" "load-tests/graduated-tests/test-010-users.yml" "10 Users - Baseline"
            run_test "025" "load-tests/graduated-tests/test-025-users.yml" "25 Users - Light Load"
            run_test "050" "load-tests/graduated-tests/test-050-users.yml" "50 Users - Medium Load"
            ;;
        "stress")
            if ! check_server; then exit 1; fi
            print_header "STRESS TESTS (May fail on Supabase Nano)"
            run_test "100" "load-tests/graduated-tests/test-100-users.yml" "100 Users - High Load"
            run_test "250" "load-tests/graduated-tests/test-250-users.yml" "250 Users - Stress Test"
            run_test "500" "load-tests/graduated-tests/test-500-users.yml" "500 Users - Breaking Point"
            ;;
        "extreme")
            if ! check_server; then exit 1; fi
            print_header "EXTREME TESTS (Will likely fail on Supabase Nano)"
            run_test "1000" "load-tests/graduated-tests/test-1000-users.yml" "1000 Users - Extreme Stress"
            run_test "10000" "load-tests/graduated-tests/test-10000-users.yml" "10000 Users - Ultimate Test"
            ;;
        "all")
            if ! check_server; then exit 1; fi
            print_header "COMPLETE GRADUATED TEST SUITE"
            print_warning "This will run ALL tests from 10 to 10,000 users"
            print_warning "Total estimated time: 60+ minutes"
            read -p "Are you sure you want to continue? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_status "Test suite cancelled by user"
                exit 0
            fi
            
            for test_config in "${TESTS[@]}"; do
                IFS=':' read -r level config_file name <<< "$test_config"
                run_test "$level" "$config_file" "$name"
                
                # Brief pause between tests
                print_status "Waiting 30 seconds before next test..."
                sleep 30
            done
            ;;
        "010"|"025"|"050"|"100"|"250"|"500"|"1000"|"10000")
            if ! check_server; then exit 1; fi
            
            for test_config in "${TESTS[@]}"; do
                IFS=':' read -r level config_file name <<< "$test_config"
                if [[ "$level" == "$test_type" ]]; then
                    run_test "$level" "$config_file" "$name"
                    break
                fi
            done
            ;;
        *)
            print_error "Unknown option: $test_type"
            show_help
            exit 1
            ;;
    esac
    
    print_success "Testing completed!"
    print_status "Results are saved in: $RESULTS_DIR"
    print_status "Use 'node load-tests/analyze-results.js list' to see all results"
}

# Execute main function with all arguments
main "$@"
