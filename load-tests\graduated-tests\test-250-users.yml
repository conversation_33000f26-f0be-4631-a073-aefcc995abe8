# Test 5: 250 Concurrent Users - Stress Test (Will Like<PERSON>ail)
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 25
      name: "Warm-up: 25 users/sec"
    - duration: 360
      arrivalRate: 125
      name: "Stress: 125 users/sec (250 concurrent)"
    - duration: 60
      arrivalRate: 25
      name: "Cool-down: 25 users/sec"
  
  ensure:
    - http.response_time.p95: 5000   # 95% under 5 seconds
    - http.response_time.median: 1500 # Median under 1.5 seconds
    - http.codes.200: 60             # 60% success rate (expect many failures)
    - http.codes.5xx: 20             # Less than 20% server errors

  http:
    timeout: 45
    pool: 300
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "250 Users - Mugshots API"
    weight: 60
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
            sortBy: "{{ sortBy }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
          name: "GET /api/mugshots - 250 users"
      - think: 0.5

  - name: "250 Users - Details API"
    weight: 40
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 250 users"
      - think: 1

processor: "../scenarios/data-generators.js"
