import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import type { UserRatingData } from '@/lib/types/database'

/**
 * User rating query hook for authenticated user's rating data
 * Fetches the current user's rating for a specific mugshot
 * 
 * @param mugshotId - The ID of the mugshot to fetch user rating for
 * @param enabled - Whether the query should run (default: true)
 * @returns TanStack Query result with user's rating data
 */
export function useUserRatingQuery(mugshotId: string, enabled = true) {
  const { isAuthenticated, user } = useAuthStore()
  
  return useQuery({
    queryKey: ['user', 'mugshot', mugshotId, 'rating'],
    queryFn: async (): Promise<UserRatingData> => {
      // Don't make request if user is not authenticated
      if (!isAuthenticated || !user) {
        return { userRating: null }
      }

      try {
        const response = await fetch(`/api/user/mugshot/${mugshotId}/rating`)
        
        // Check if response is JSON before parsing
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          const text = await response.text()
          console.error('Non-JSON response received for user rating:', text.substring(0, 200))
          throw new Error('Server returned invalid response format')
        }
        
        const result = await response.json()
        
        // Handle authentication errors with login redirect
        if (!response.ok) {
          if (response.status === 401 || result.error === 'UNAUTHENTICATED') {
            // Return empty data instead of redirecting on public pages
            console.log('User not authenticated, returning null rating')
            return { userRating: null }
          } else if (response.status === 404) {
            // User hasn't rated this mugshot yet - this is normal
            return { userRating: null }
          } else if (response.status >= 500) {
            throw new Error('Server error occurred')
          } else if (response.status === 429) {
            throw new Error('Too many requests - please wait a moment')
          } else {
            throw new Error(result.message || 'Failed to fetch user rating')
          }
        }
        
        if (!result.success) {
          throw new Error(result.message || 'Failed to fetch user rating')
        }
        
        // Validate the data structure
        const data = result.data
        if (data && typeof data !== 'object') {
          throw new Error('Invalid user rating data format')
        }
        
        // Return the rating data with proper validation
        return {
          userRating: data?.userRating ?? null
        }
      } catch (error) {
        // If user is not authenticated, return empty data instead of throwing error
        if (!isAuthenticated || !user) {
          return { userRating: null }
        }
        
        console.error('Error fetching user rating:', error)
        throw error
      }
    },
    // Only enable query if user is authenticated and enabled flag is true
    enabled: enabled && isAuthenticated && !!user,
    // Return empty data immediately for unauthenticated users
    placeholderData: { userRating: null },
    // Don't retry on auth errors for unauthenticated users
    retry: (failureCount, _error) => {
      if (!isAuthenticated || !user) return false
      return failureCount < 3
    },
    // Stale time - cache for 30 seconds
    staleTime: 30 * 1000,
    // Garbage collect after 5 minutes of inactivity
    gcTime: 5 * 60 * 1000
  })
}

/**
 * Prefetch utility for user rating data
 * Can be used to prefetch user-specific rating data when authentication is confirmed
 */
export function prefetchUserRating(
  queryClient: { prefetchQuery: (config: object) => Promise<void> },
  mugshotId: string,
  isAuthenticated: boolean
) {
  if (!isAuthenticated) {
    return Promise.resolve()
  }
  
  return queryClient.prefetchQuery({
    queryKey: ['user', 'mugshot', mugshotId, 'rating'],
    queryFn: async () => {
      const response = await fetch(`/api/user/mugshot/${mugshotId}/rating`)
      const result = await response.json()
      
      if (!response.ok) {
        if (result.error === 'UNAUTHENTICATED') {
          return { userRating: null }
        }
        throw new Error(result.message || 'Failed to fetch user rating')
      }
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch user rating')
      }
      
      return {
        userRating: result.data?.userRating ?? null
      }
    },
    staleTime: 1 * 60 * 1000,
  })
}

/**
 * Hook to get cached user rating without triggering a fetch
 * Useful for components that only need cached data
 */
export function useCachedUserRating(mugshotId: string): number | null | undefined {
  const { data } = useQuery<{ userRating: number | null }>({
    queryKey: ['user', 'mugshot', mugshotId, 'rating'],
    queryFn: async () => ({ userRating: null }),
    enabled: false, // Don't fetch, only return cached data
  })
  
  return data?.userRating
} 