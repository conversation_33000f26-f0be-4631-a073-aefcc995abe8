# Two-Cache Update Strategy Implementation

## Overview

Successfully implemented the **Two-Cache Update Strategy** for TanStack Query to solve the mugshot reactivity issue. When users rate or tag a mugshot, both the detail cache AND all grid caches are updated optimistically, ensuring instant UI reactivity across all components.

## The Problem We Solved

**Before (Broken):**
- Rating/tagging a mugshot only updated the detail cache
- Mugshot cards in the grid showed stale data
- Users had to refresh the page to see updated ratings/tags
- Poor UX with inconsistent data across components

**After (Fixed):**
- One mutation updates both detail cache AND all related grid caches
- Mugshot cards update instantly when rated/tagged from detail view
- Perfect cross-component reactivity
- Optimistic updates with error rollback for both cache types

## Implementation Details

### Rating Mutation (use-rating-mutation.ts)

```typescript
onMutate: async (newRating: number) => {
  // ✅ STEP 1: Cancel outgoing refetches to prevent race conditions
  await queryClient.cancelQueries({ queryKey: ['mugshot', mugshotId, 'rating-statistics'] })
  await queryClient.cancelQueries({ queryKey: ['user', 'mugshot', mugshotId, 'rating'] })
  await queryClient.cancelQueries({ queryKey: ['mugshots'] }) // Cancel all grid queries
  
  // ✅ STEP 2: Snapshot previous data for rollback
  const previousStatistics = queryClient.getQueryData<RatingStatistics>(['mugshot', mugshotId, 'rating-statistics'])
  const previousUserRating = queryClient.getQueryData<UserRatingData>(['user', 'mugshot', mugshotId, 'rating'])
  
  // ✅ STEP 3: Update detail cache (already working)
  const newUserRatingData: UserRatingData = { userRating: newRating }
  queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], newUserRatingData)
  
  // Calculate new statistics locally
  let newStatistics: RatingStatistics | undefined
  if (previousStatistics) {
    // ... local calculation logic ...
    queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], newStatistics)
  }
  
  // ✅ STEP 4: Update ALL grid caches - THE MAGIC HAPPENS HERE!
  const mugshotCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
  const previousGridCaches: Array<{ queryKey: readonly unknown[], data: unknown }> = []
  
  mugshotCaches.forEach(([queryKey, data]) => {
    if (!data || typeof data !== 'object') return
    
    // Save original data for rollback
    previousGridCaches.push({ queryKey, data })
    
    const gridData = data as { mugshots?: Array<{ id: number, [key: string]: unknown }> }
    if (!gridData.mugshots || !Array.isArray(gridData.mugshots)) return
    
    // Find and update the specific mugshot in this grid cache
    const updatedMugshots = gridData.mugshots.map(mugshot => {
      if (mugshot.id === parseInt(mugshotId, 10)) {
        // Update this mugshot's rating data in the grid
        return {
          ...mugshot,
          average_rating: newStatistics?.averageRating ?? mugshot.average_rating,
          total_ratings: newStatistics?.totalRatings ?? mugshot.total_ratings,
        }
      }
      return mugshot // Leave other mugshots unchanged
    })
    
    // Update the cache with the modified grid data
    queryClient.setQueryData(queryKey, {
      ...gridData,
      mugshots: updatedMugshots
    })
  })
  
  return { previousStatistics, previousUserRating, previousGridCaches }
}
```

### Tag Mutation (use-tag-mutation.ts)

```typescript
onMutate: async (tagType: string) => {
  // ✅ STEP 1: Cancel outgoing refetches
  await queryClient.cancelQueries({ queryKey: ['mugshot', mugshotId] })
  await queryClient.cancelQueries({ queryKey: ['user', 'mugshot', mugshotId] })
  await queryClient.cancelQueries({ queryKey: ['mugshots'] }) // Cancel all grid queries
  
  // ✅ STEP 2: Snapshot previous data for rollback
  const previousMugshot = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
  const previousUser = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
  
  // ✅ STEP 3: Update detail cache (toggle user tag and update tag counts)
  // ... existing detail cache update logic ...
  
  // ✅ STEP 4: Update ALL grid caches
  const mugshotCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
  const previousGridCaches: Array<{ queryKey: readonly unknown[], data: unknown }> = []
  
  mugshotCaches.forEach(([queryKey, data]) => {
    // Find and update the specific mugshot's tag_counts in grid cache
    const updatedMugshots = gridData.mugshots.map(mugshot => {
      if (mugshot.id === parseInt(mugshotId, 10)) {
        return {
          ...mugshot,
          tag_counts: {
            ...currentTagCounts,
            [tagType]: newCount
          }
        }
      }
      return mugshot
    })
    
    queryClient.setQueryData(queryKey, { ...gridData, mugshots: updatedMugshots })
  })
  
  return { previousMugshot, previousUser, previousGridCaches }
}
```

### Error Rollback (Both Mutations)

```typescript
onError: (error, _, context) => {
  // ✅ STEP 5: Rollback ALL optimistic updates
  if (context?.previousStatistics) {
    queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], context.previousStatistics)
  }
  if (context?.previousUserRating) {
    queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], context.previousUserRating)
  }
  
  // Rollback all grid cache updates
  if (context?.previousGridCaches) {
    context.previousGridCaches.forEach(({ queryKey, data }) => {
      queryClient.setQueryData(queryKey, data)
    })
  }
}
```

## The Magic: How It Works

1. **One Mutation, Multiple Caches**: When a user rates/tags, the mutation updates:
   - Detail cache (`['mugshot', id, 'detail']`)
   - User-specific cache (`['user', 'mugshot', id, 'data']`)
   - **ALL grid caches** (`['mugshots', ...]` with any filter combinations)

2. **Smart Cache Discovery**: Uses `queryClient.getQueriesData({ queryKey: ['mugshots'] })` to find ALL existing grid caches regardless of filters/pagination

3. **Surgical Updates**: Only updates the specific mugshot in each grid cache, leaving all other mugshots unchanged

4. **Automatic Reactivity**: When TanStack Query cache data changes, every component using that cache automatically re-renders with fresh data

5. **Comprehensive Rollback**: If the mutation fails, all caches (detail + grid) are restored to their previous state

## Benefits Achieved

✅ **Instant Reactivity**: Mugshot cards update immediately when rated/tagged  
✅ **No Page Re-renders**: Only affected components update, popover stays open  
✅ **Optimistic Updates**: UI changes before server responds  
✅ **Error Handling**: Complete rollback if mutation fails  
✅ **Cross-Filter Support**: Works across all filter combinations (state, county, tags, search, etc.)  
✅ **Minimal Code Changes**: Doesn't break existing functionality  
✅ **Performance**: No unnecessary refetches or invalidations  

## Test Coverage

Created comprehensive test suite (`two-cache-update-strategy.test.tsx`) covering:

- **Rating mutations** updating both detail and grid caches
- **Tag mutations** updating both detail and grid caches  
- **Error rollback** for both cache types
- **Cross-cache reactivity** with multiple filter combinations
- **Edge cases** (empty grids, malformed data)
- **Tag addition and removal** scenarios

## Usage Example

```typescript
// In a mugshot detail component
const ratingMutation = useRatingMutation(mugshotId)

// When user rates (e.g., gives 9/10)
ratingMutation.mutate(9)

// 🎯 INSTANT RESULTS:
// ✅ Detail view shows new rating immediately
// ✅ Grid cards show updated rating immediately  
// ✅ All filtered grids (by state, tags, etc.) update
// ✅ User can continue interacting without page refresh
// ✅ If API call fails, everything rolls back perfectly
```

## Key Files Modified

- `lib/hooks/mutations/use-rating-mutation.ts` - Added grid cache updates
- `lib/hooks/mutations/use-tag-mutation.ts` - Added grid cache updates  
- `__tests__/hooks/two-cache-update-strategy.test.tsx` - Comprehensive test suite

## The Fix: Property Name Mapping Issue

**Root Cause Discovered:**
The TanStack Query cache had updated data, but UI components weren't showing changes due to property name mismatches:

1. **Grid Cache**: Contains `tag_counts: { wild: 6 }` and `average_rating: 7.5` (snake_case from database)
2. **Transform Layer**: `transformDBMugshotToUI()` maps to `wildCount` and `averageRating` (camelCase for UI)  
3. **UI Components**: Read from camelCase properties but cache updates only modified snake_case

**Solution Applied:**
Updated mutations to set **both** property name formats in grid caches:

```typescript
// Tag Mutation - Update ALL property formats
return {
  ...mugshot,
  // Raw database format (JSONB from SQL)
  tag_counts: updatedTagCounts,
  // Database snake_case properties  
  wild_count: updatedTagCounts.wild || 0,
  funny_count: updatedTagCounts.funny || 0, 
  spooky_count: updatedTagCounts.spooky || 0,
  // UI camelCase properties
  wildCount: updatedTagCounts.wild || 0,
  funnyCount: updatedTagCounts.funny || 0,
  spookyCount: updatedTagCounts.spooky || 0,
}

// Rating Mutation - Update ALL property formats  
return {
  ...mugshot,
  // Database snake_case properties
  average_rating: newAvgRating,
  total_ratings: newTotalRatings,
  // UI camelCase properties  
  averageRating: newAvgRating,
  totalRatings: newTotalRatings,
  // Legacy properties some components use
  rating: newAvgRating,
  votes: newTotalRatings,
}
```

## Debug Tools

Created `debug-two-cache-update.ts` utility for testing:

```typescript
// Test in browser console:
debugCacheUpdates('123')           // View current cache state
simulateTagUpdate('123', 'wild')   // Test tag update
simulateRatingUpdate('123', 9)     // Test rating update
```

## Technical Notes

- Uses `queryClient.getQueriesData()` to find all related caches
- Maintains type safety with proper TypeScript interfaces
- Handles edge cases gracefully (empty arrays, malformed data)
- **Property Compatibility**: Updates both snake_case and camelCase properties for full UI compatibility
- Performance optimized - only updates necessary data
- Compatible with all existing filter/sort/pagination combinations

This implementation provides the seamless, reactive user experience expected from modern web applications, where user actions result in immediate visual feedback across all related UI components. 