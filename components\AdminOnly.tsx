'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'

export interface AdminOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

interface ProfileRole {
  role: string
}

// Simple admin check function - moved here to avoid server import issues
function checkIsAdmin(profile: ProfileRole | null): boolean {
  return profile?.role === 'admin'
}

export default function AdminOnly({ children, fallback = null }: AdminOnlyProps) {
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(true)

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const supabase = createClient()
        
        // First check if there's a session using getSession() which doesn't throw errors
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        // If there's no session or session error, user is not authenticated
        if (sessionError || !session?.user) {
          setIsAdmin(false)
          setLoading(false)
          return
        }

        // Only call getUser if we have a valid session
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        
        if (userError || !user) {
          setIsAdmin(false)
          setLoading(false)
          return
        }

        // Get user profile to check role
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('user_id', user.id)
          .single()

        if (profileError) {
          // Profile not found or other error - not admin
          setIsAdmin(false)
        } else {
          setIsAdmin(checkIsAdmin(profile))
        }
      } catch (error) {
        // Catch any other errors and treat as not admin
        console.error('Error checking admin status:', error)
        setIsAdmin(false)
      } finally {
        setLoading(false)
      }
    }

    checkAdminStatus()
  }, [])

  if (loading) {
    return fallback
  }

  return isAdmin ? <>{children}</> : <>{fallback}</>
} 