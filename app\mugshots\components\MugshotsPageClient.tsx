"use client"

import { useRef, useEffect, useState, useMemo } from "react"
import { useSearchParams, useRout<PERSON> } from "next/navigation"
import { useAuthStore } from "@/lib/stores/auth-store"
import { useLocationForUnauthenticatedUsers } from "@/lib/hooks/queries/use-detected-location"
import MugshotsFiltersClient from "./MugshotsFiltersClient"
import MugshotsContentClient from "./MugshotsContentClient"
import FilteredResultsCount from "@/components/FilteredResultsCount"
import MugshotsLoading from "./MugshotsLoading"
import { useMugshotsQuery } from "@/lib/hooks/queries/use-mugshots-query"
import type { TagType } from "@/lib/constants"
import { convertUITagArrayToDB } from "@/lib/utils/tag-utils"

interface MugshotsPageClientProps {
  // Initial URL parameters for faster first render
  initialSearchTerm?: string
  initialSelectedState?: string
  initialSelectedCounty?: string
  initialDateFrom?: string
  initialDateTo?: string
  initialCategories?: string[]
  initialTags?: string[] // UI display names, will be converted to TagType[] for API
  initialSortBy?: string
  initialPerPage?: number
  initialGridView?: 'large' | 'compact'
  initialCurrentPage?: number
}

export default function MugshotsPageClient({
  initialSearchTerm = '',
  initialSelectedState = 'all-states',
  initialSelectedCounty = 'all-counties',
  initialDateFrom = '',
  initialDateTo = '',
  initialCategories = [],
  initialTags: _initialTags = [],
  initialSortBy = 'newest',
  initialPerPage = 12,
  initialGridView = 'large',
  initialCurrentPage = 1
}: MugshotsPageClientProps) {
  const router = useRouter()
  
  // Auth store for user location
  const { hasHomeLocation, getHomeLocation, isAuthenticated, isLoading: authLoading } = useAuthStore()

  // Get detected location for unauthenticated users
  const {
    data: detectedLocation,
    isLoading: locationDetecting,
    error: _locationError
  } = useLocationForUnauthenticatedUsers({
    fallbackState: 'California',
    staleTime: 30 * 60 * 1000 // 30 minutes
  })
  
  // URL parameters state
  const searchParams = useSearchParams()
  const rawCurrentSearchTerm = searchParams.get('search') || initialSearchTerm
  const rawCurrentSelectedState = searchParams.get('state') || initialSelectedState
  const rawCurrentSelectedCounty = searchParams.get('county') || initialSelectedCounty
  const rawCurrentDateFrom = searchParams.get('dateFrom') || initialDateFrom
  const rawCurrentDateTo = searchParams.get('dateTo') || initialDateTo
  
  // Memoize arrays to prevent recreation on every render
  const rawCurrentCategories = useMemo(() => 
    searchParams.get('categories')?.split(',').filter(Boolean) || initialCategories,
    [searchParams, initialCategories]
  )
  const rawCurrentTags = useMemo(() => 
    searchParams.get('tags')?.split(',').filter(Boolean) || _initialTags,
    [searchParams, _initialTags]
  )
  
  const rawCurrentSortBy = searchParams.get('sortBy') || initialSortBy
  const rawCurrentPerPage = parseInt(searchParams.get('perPage') || initialPerPage.toString(), 10)
  const rawCurrentGridView = (searchParams.get('gridView') || initialGridView) as 'large' | 'compact'
  const rawCurrentPage = parseInt(searchParams.get('page') || initialCurrentPage.toString(), 10)
  
  // Track user location application
  const [hasAppliedUserLocation, setHasAppliedUserLocation] = useState(false)
  const [userHasOverriddenLocation, setUserHasOverriddenLocation] = useState(false)
  
  // Apply user location preselection logic (for both authenticated and unauthenticated users)
  const shouldApplyUserLocation = () => {
    // Check for URL location filters first
    const hasUrlLocationFilters = searchParams.get('state') || searchParams.get('county')

    // Don't apply if user has already overridden or we've already applied
    if (hasAppliedUserLocation || userHasOverriddenLocation || hasUrlLocationFilters) {
      return false
    }

    // For authenticated users: check if they have home location
    if (isAuthenticated && !authLoading) {
      return hasHomeLocation()
    }

    // For unauthenticated users: check if we have detected location
    if (!isAuthenticated && !authLoading && !locationDetecting) {
      return !!detectedLocation?.state
    }

    return false
  }
  
  // Get effective location values (with user location applied if appropriate)
  const getEffectiveLocationValues = () => {
    if (shouldApplyUserLocation()) {
      // For authenticated users: use home location
      if (isAuthenticated && hasHomeLocation()) {
        const { state: homeState, county: homeCounty } = getHomeLocation()
        console.log('🏠 Applying authenticated user home location:', { homeState, homeCounty })
        return {
          effectiveState: homeState || rawCurrentSelectedState,
          effectiveCounty: homeCounty || rawCurrentSelectedCounty,
          userLocationApplied: true
        }
      }

      // For unauthenticated users: use detected location
      if (!isAuthenticated && detectedLocation?.state) {
        console.log('🎯 Applying detected location for unauthenticated user:', detectedLocation.state)
        return {
          effectiveState: detectedLocation.state,
          effectiveCounty: rawCurrentSelectedCounty, // Keep existing county or default
          userLocationApplied: true
        }
      }
    }

    return {
      effectiveState: rawCurrentSelectedState,
      effectiveCounty: rawCurrentSelectedCounty,
      userLocationApplied: false
    }
  }
  
  const { effectiveState, effectiveCounty, userLocationApplied } = getEffectiveLocationValues()
  
  // Final current values to use throughout the component
  const currentSearchTerm = rawCurrentSearchTerm
  const currentSelectedState = effectiveState
  const currentSelectedCounty = effectiveCounty
  const currentDateFrom = rawCurrentDateFrom
  const currentDateTo = rawCurrentDateTo
  const currentCategories = rawCurrentCategories
  const currentTags = rawCurrentTags
  const currentSortBy = rawCurrentSortBy
  const currentPerPage = rawCurrentPerPage
  const currentGridView = rawCurrentGridView
  const currentPage = rawCurrentPage

  // Convert UI tags to database format for the query
  const dbTags: TagType[] = useMemo(() =>
    currentTags.length > 0 ? convertUITagArrayToDB(currentTags) : [],
    [currentTags]
  )

  // FIXED: Stabilize query parameters to prevent unnecessary re-renders
  const queryFilters = useMemo(() => ({
    search: currentSearchTerm || undefined,
    state: currentSelectedState !== 'all-states' ? currentSelectedState : undefined,
    county: currentSelectedCounty !== 'all-counties' ? currentSelectedCounty : undefined,
    dateFrom: currentDateFrom || undefined,
    dateTo: currentDateTo || undefined,
    tags: dbTags.length > 0 ? dbTags : undefined
  }), [currentSearchTerm, currentSelectedState, currentSelectedCounty, currentDateFrom, currentDateTo, dbTags])

  const querySortOptions = useMemo(() => ({
    sortBy: currentSortBy as 'newest' | 'top-rated' | 'most-viewed'
  }), [currentSortBy])

  const queryPagination = useMemo(() => ({
    page: currentPage,
    perPage: currentPerPage,
    includeTotal: true
  }), [currentPage, currentPerPage])

  // Use TanStack Query with stabilized parameters
  const {
    data,
    isLoading: queryLoading,
    error,
    isStale,
    isFetching,
    isPending
  } = useMugshotsQuery(queryFilters, querySortOptions, queryPagination)

  // FIXED: Unified loading state management
  // Use TanStack Query's loading states as the source of truth
  const isLoading = queryLoading || isFetching || isPending

  // Extract data from query result
  const mugshots = data?.mugshots || []
  const totalCount = data?.pagination?.totalCount || 0
  const totalPages = Math.ceil(totalCount / currentPerPage)
  const isEmpty = mugshots.length === 0 && !isLoading

  // FIXED: Simplified data staleness detection using TanStack Query's built-in states
  // TanStack Query already handles staleness detection properly
  const isDataStale = isStale && !isLoading

  // Ref for auto-scrolling to grid on mobile
  const gridContainerRef = useRef<HTMLDivElement | null>(null)

  // REMOVED: Filter store synchronization to prevent excessive API calls
  // TanStack Query is now the single source of truth for loading states

  // Track when user makes explicit location choices in URL
  useEffect(() => {
    const hasExplicitLocationChoice = searchParams.get('state') || searchParams.get('county')
    if (hasExplicitLocationChoice && !userHasOverriddenLocation) {
      setUserHasOverriddenLocation(true)
    }
  }, [searchParams, userHasOverriddenLocation])

  // Update URL when user location is applied (one-time)
  useEffect(() => {
    if (userLocationApplied && !hasAppliedUserLocation) {
      setHasAppliedUserLocation(true)
      
      // Update URL to reflect user's preselected location
      const newSearchParams = new URLSearchParams(searchParams.toString())
      
      if (currentSelectedState && currentSelectedState !== 'all-states') {
        newSearchParams.set('state', currentSelectedState)
      }
      if (currentSelectedCounty && currentSelectedCounty !== 'all-counties') {
        newSearchParams.set('county', currentSelectedCounty)
      }
      
      const newUrl = `${window.location.pathname}?${newSearchParams.toString()}`
      
      // Update URL without triggering a page reload
      router.replace(newUrl, { scroll: false })
    }
  }, [userLocationApplied, hasAppliedUserLocation, currentSelectedState, currentSelectedCounty, searchParams, router])

  // Auto-scroll to grid on page load/refresh
  useEffect(() => {
    const timer = setTimeout(() => {
      if (gridContainerRef.current) {
        gridContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100) // Small delay to ensure layout is ready
    
    return () => clearTimeout(timer)
  }, []) // Only run on mount

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold uppercase tracking-tight text-white mb-4">
            <span className="text-pink-500">SEARCH</span> MUGSHOTS
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Find and rate the hottest mugshots from across America
          </p>
        </div>

        {/* Show loading spinner only when actually loading */}
        {isLoading && <MugshotsLoading />}
        
        {/* Show error message if fetch failed */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-500 text-lg mb-4">{error.message}</p>
          </div>
        )}

        {/* Show content when not loading */}
        {!isLoading && !error && (
          <>
            {/* Client Component for Filters */}
            <MugshotsFiltersClient
              searchTerm={currentSearchTerm}
              selectedState={currentSelectedState}
              selectedCounty={currentSelectedCounty}
              dateFrom={currentDateFrom}
              dateTo={currentDateTo}
              categories={currentCategories}
              tags={currentTags}
              sortBy={currentSortBy}
              perPage={currentPerPage}
              gridView={currentGridView}
              currentPage={currentPage}
              gridContainerRef={gridContainerRef}
            />

            {/* Grid Container - Target for auto-scroll */}
            <div ref={gridContainerRef}>
              {/* Comprehensive Results Count */}
              <FilteredResultsCount
                currentCount={mugshots.length}
                totalCount={totalCount}
                searchTerm={currentSearchTerm}
                selectedState={currentSelectedState}
                selectedCounty={currentSelectedCounty}
                dateFrom={currentDateFrom}
                dateTo={currentDateTo}
                categories={currentCategories}
              />

              {/* Content Client with TanStack Query data */}
              <MugshotsContentClient
                mugshots={mugshots}
                totalCount={totalCount}
                currentPage={currentPage}
                totalPages={totalPages}
                perPage={currentPerPage}
                gridView={currentGridView}
                isEmpty={isEmpty}
                isDataStale={isDataStale}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
} 