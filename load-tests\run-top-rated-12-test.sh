#!/bin/bash

# Top-Rated First 12 Load Test Runner
# This script runs the focused test for top-rated mugshots (first 12 only)

set -e

echo "🏆 Starting Top-Rated First 12 Load Test..."
echo "📊 Test Focus: Ratings table query optimization"
echo "🎯 Target: First 12 top-rated mugshots only"
echo ""

# Check if server is running
echo "🔍 Checking if server is running on localhost:3000..."
if ! curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "❌ Server is not running on localhost:3000"
    echo "💡 Please start your Next.js server first:"
    echo "   npm run dev"
    exit 1
fi

echo "✅ Server is running"
echo ""

# Create results directory if it doesn't exist
mkdir -p load-tests/results

# Run the test
echo "🚀 Running Top-Rated First 12 Load Test..."
echo "📈 Expected performance:"
echo "   - P95 response time: < 1200ms"
echo "   - Median response time: < 350ms"
echo "   - Success rate: > 96%"
echo ""

# Get current timestamp for result file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULT_FILE="load-tests/results/top-rated-first-12_${TIMESTAMP}.json"

# Run Artillery test
npx artillery run load-tests/focused-tests/top-rated-first-12.yml \
    --output "$RESULT_FILE"

echo ""
echo "✅ Test completed!"
echo "📊 Results saved to: $RESULT_FILE"
echo ""

# Generate HTML report if artillery-plugin-html-report is available
if npm list artillery-plugin-html-report > /dev/null 2>&1; then
    HTML_REPORT="load-tests/results/top-rated-first-12_${TIMESTAMP}.html"
    echo "📈 Generating HTML report..."
    npx artillery report "$RESULT_FILE" --output "$HTML_REPORT"
    echo "📊 HTML report saved to: $HTML_REPORT"
else
    echo "💡 Install artillery-plugin-html-report for HTML reports:"
    echo "   npm install --save-dev artillery-plugin-html-report"
fi

echo ""
echo "🎯 Test Summary:"
echo "   Focus: Top-rated mugshots (first 12 only)"
echo "   Query: Ratings table aggregation"
echo "   Sorting: avg_rating DESC, total_ratings DESC"
echo "   Expected: Only mugshots with ratings returned"
echo ""
echo "📊 Next steps:"
echo "   1. Review the results in $RESULT_FILE"
echo "   2. Check if performance targets were met"
echo "   3. Compare with other top-rated test results"
echo "   4. If needed, run: npm run analyze-results $RESULT_FILE"
