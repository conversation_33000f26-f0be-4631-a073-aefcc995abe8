'use client'

import { useState, useEffect, useMemo } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useRatingStatisticsQuery } from '@/lib/hooks/queries/use-rating-statistics-query'
import { useUserRatingQuery } from '@/lib/hooks/queries/use-user-rating-query'
import { useUserMugshotDataQuery } from '@/lib/hooks/queries/use-user-mugshot-data-query'
import { useTagStatisticsQuery } from '@/lib/hooks/queries/use-tag-statistics-query'
import { useCanRate } from '@/lib/hooks/mutations/use-rating-mutation'
import RatingSectionSkeleton from '@/components/RatingSectionSkeleton'
import RatingTagPopover from '@/components/RatingTagPopover'
import { Button } from '@/components/ui/button'
import { 
  checkTimeGate,
  type TimeGateResult
} from '@/lib/services/rating-service'
import { Loader2 } from 'lucide-react'
import type { RatingStatistics } from '@/lib/types/database'

interface SimpleMugshotRatingProps {
  mugshotId: string
  className?: string
  preloadedStats?: RatingStatistics
}

export default function SimpleMugshotRating({ 
  mugshotId, 
  className = '', 
  preloadedStats 
}: SimpleMugshotRatingProps) {
  const { user } = useAuthStore()
  
  // TanStack Query hooks for rating data
  const { 
    data: ratingStats, 
    isLoading: isLoadingStats,
    error: statsError
  } = useRatingStatisticsQuery(mugshotId, true, preloadedStats)
  
  const { 
    data: userRatingData, 
    isLoading: isLoadingUserRating,
    error: userRatingError
  } = useUserRatingQuery(mugshotId)
  
  const { 
    data: userMugshotData,
    isLoading: isLoadingUserData,
    error: userDataError
  } = useUserMugshotDataQuery(mugshotId)
  
  const { 
    data: tagStats,
    isLoading: isLoadingTagStats,
    error: tagStatsError
  } = useTagStatisticsQuery(mugshotId)
  
  const _canRate = useCanRate(mugshotId)

  // Local state
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const [timeGate, setTimeGate] = useState<TimeGateResult | null>(null)
  const [hasError, setHasError] = useState<string | null>(null)

  // Check for any errors and set a user-friendly error message
  useEffect(() => {
    const errors = [statsError, userRatingError, userDataError, tagStatsError].filter(Boolean)
    if (errors.length > 0) {
      // Set a generic user-friendly error message instead of showing raw error objects
      setHasError('Unable to load rating data. Please try refreshing the page.')
      console.error('Rating component errors:', errors)
    } else {
      setHasError(null)
    }
  }, [statsError, userRatingError, userDataError, tagStatsError])

  // Timeout fallback to prevent infinite skeleton
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isInitialLoading) {
        console.warn('Rating section: Timeout reached, hiding skeleton')
        setIsInitialLoading(false)
      }
    }, 3000) // 3 second timeout

    return () => clearTimeout(timeout)
  }, [isInitialLoading])

  // Get current data from TanStack Query with proper error handling
  const stats = useMemo(() => {
    try {
      return ratingStats || { averageRating: 0, totalRatings: 0 }
    } catch (error) {
      console.error('Error processing rating stats:', error)
      return { averageRating: 0, totalRatings: 0 }
    }
  }, [ratingStats])
  
  const userRating = useMemo(() => {
    try {
      return userRatingData?.userRating ?? null
    } catch (error) {
      console.error('Error processing user rating:', error)
      return null
    }
  }, [userRatingData])
  
  const currentUserTags = useMemo(() => {
    try {
      return new Set(userMugshotData?.userTags || [])
    } catch (error) {
      console.error('Error processing user tags:', error)
      return new Set()
    }
  }, [userMugshotData])
  
  const currentTagStats = useMemo(() => {
    try {
      return tagStats || { wild: 0, funny: 0, spooky: 0, totalTags: 0 }
    } catch (error) {
      console.error('Error processing tag stats:', error)
      return { wild: 0, funny: 0, spooky: 0, totalTags: 0 }
    }
  }, [tagStats])
  
  const isLoadingTags = isLoadingUserData || isLoadingTagStats
  const isAnyLoading = isLoadingStats || isLoadingUserRating || isLoadingUserData || isLoadingTagStats

  // Check data availability and manage loading state
  useEffect(() => {
    const checkDataAvailability = () => {
      // Check if we have basic stats (from TanStack Query or preloaded)
      const hasStats = ratingStats || preloadedStats
      
      // If we have an error, don't show loading
      if (hasError) {
        setIsInitialLoading(false)
        return
      }
      
      // If we have rating stats or they're loading, we can show/prepare the UI
      if (hasStats || !isLoadingStats) {
        setIsInitialLoading(false)
        
        // Load time gate info only once per mugshot
        if (user && mugshotId) {
          checkTimeGate(mugshotId).then(timeGateResult => {
            setTimeGate(timeGateResult)
          }).catch(error => {
            console.error('Error loading time gate:', error)
          })
        }
      } else if (isLoadingStats) {
        // Still loading rating stats
        setIsInitialLoading(true)
      }
    }

    checkDataAvailability()
  }, [mugshotId, user, ratingStats, preloadedStats, isLoadingStats, hasError]) // Dependencies with proper references

  // Show skeleton while loading
  if (isInitialLoading) {
    return <RatingSectionSkeleton className={className} showTags={true} />
  }

  // Show error state with user-friendly message
  if (hasError) {
    return (
      <div className={`bg-gray-800/50 rounded-lg p-4 ${className}`}>
        <div className="text-center">
          <div className="text-red-400 text-sm mb-2">⚠️ Unable to load ratings</div>
          <div className="text-gray-400 text-xs">Please try refreshing the page</div>
        </div>
      </div>
    )
  }

  // Create tag display data
  const tagDisplayData = [
    { type: 'funny' as const, emoji: '😂', label: 'Funny', count: currentTagStats.funny },
    { type: 'wild' as const, emoji: '🤪', label: 'Wild', count: currentTagStats.wild },
    { type: 'spooky' as const, emoji: '😱', label: 'Spooky', count: currentTagStats.spooky }
  ].filter(tag => tag.count > 0 || currentUserTags.has(tag.type))

  return (
    <div className={`bg-gray-800/50 rounded-lg p-4 ${className}`}>
      {/* Rating Display */}
      <div className="text-center mb-4">
        <div className="flex items-center justify-center gap-1">
          <span className="text-5xl font-bold text-cyan-400">{stats.averageRating.toFixed(1)}</span>
          <span className="text-sm text-gray-400">out of 10</span>
          <span className="text-sm text-gray-400 ml-1">
            ({stats.totalRatings} rating{stats.totalRatings !== 1 ? 's' : ''})
          </span>
        </div>
      </div>

      {/* Tags Section */}
      {(tagDisplayData.length > 0 || isLoadingTags) && (
        <div className="mb-4">
          {isLoadingTags ? (
            <div className="flex justify-center gap-2">
              <div className="h-6 w-16 bg-gray-700/30 rounded-full animate-pulse" />
              <div className="h-6 w-14 bg-gray-700/30 rounded-full animate-pulse" />
              <div className="h-6 w-18 bg-gray-700/30 rounded-full animate-pulse" />
            </div>
          ) : (
            <div className="flex flex-wrap justify-center gap-2">
              {tagDisplayData.map((tag) => {
                const isUserTag = currentUserTags.has(tag.type)
                const tagColor = tag.type === 'funny' ? 'yellow' : tag.type === 'wild' ? 'purple' : 'red'
                
                return (
                  <span 
                    key={tag.type}
                    className={`
                      flex items-center gap-1 px-3 py-1 rounded-lg text-sm font-medium relative
                      ${isUserTag 
                        ? `bg-${tagColor}-500/30 border-${tagColor}-500/70 text-${tagColor}-300 border` 
                        : 'bg-gray-700/50 text-gray-300 border border-gray-600/50'
                      }
                    `}
                  >
                    <span className="text-base">{tag.emoji}</span>
                    <span>{tag.label}</span>
                    <span className="text-xs opacity-75">({tag.count})</span>
                  </span>
                )
              })}
            </div>
          )}
        </div>
      )}

      {/* Click to Rate Button with Reusable Popover */}
      <div className="text-center">
        <RatingTagPopover
          mugshotId={mugshotId}
          isOpen={isPopoverOpen}
          onOpenChange={setIsPopoverOpen}
          trigger={
            <Button 
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-2 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isAnyLoading}
            >
              {isLoadingUserRating && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {userRating ? `Rated ${userRating}/10` : 'Click to Rate'}
            </Button>
          }
        />
      </div>

      {/* Time Gate Info */}
      {timeGate && !timeGate.canRate && (
        <div className="text-center mt-2">
          <p className="text-xs text-gray-400">{timeGate.message}</p>
        </div>
      )}
    </div>
  )
} 