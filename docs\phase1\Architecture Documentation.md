<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Architecture Documentation

**Project:** Americas Top Mugshot
**Version:** 1.0
**Date:** July 25, 2025
**Next.js Version:** 15.2.4
**React Version:** 19.0.0
**Status:** Production Ready

## Table of Contents

1. [Overview](#overview)
2. [Architecture Principles](#architecture-principles)
3. [System Architecture](#system-architecture)
4. [Technology Stack](#technology-stack)
5. [Project Structure](#project-structure)
6. [Data Flow](#data-flow)
7. [Authentication \& Authorization](#authentication--authorization)
8. [State Management](#state-management)
9. [API Design](#api-design)
10. [Database Layer](#database-layer)
11. [Frontend Layer](#frontend-layer)
12. [UI Components](#ui-components)
13. [Performance Optimizations](#performance-optimizations)
14. [Testing Strategy](#testing-strategy)
15. [Security Considerations](#security-considerations)
16. [Development Guidelines](#development-guidelines)
17. [Deployment Strategy](#deployment-strategy)
18. [Monitoring \& Observability](#monitoring--observability)

## Overview

This architecture document outlines a modern, scalable Next.js 15 application with Supabase backend that maintains strict separation between frontend and backend concerns. The architecture leverages the latest React 19 features, server components, and modern state management patterns while providing optimal performance and developer experience.

### Key Objectives

- **Modern Stack**: Next.js 15 + React 19 + TanStack Query v5 + Zustand v5
- **Clean API Separation**: Frontend communicates only through well-defined APIs
- **Type Safety**: End-to-end TypeScript with Zod validation
- **Performance First**: Server-side rendering with intelligent caching
- **Developer Experience**: Modern tooling with Vitest, Playwright, and comprehensive testing
- **Scalability**: Modular architecture supporting team growth


## Architecture Principles

### 1. **Modern React Patterns**

- Server Components for data fetching
- Client Components for interactivity
- Streaming and Suspense boundaries
- React 19 concurrent features


### 2. **Separation of Concerns**

- API routes handle business logic
- Components handle presentation
- Hooks manage state and side effects
- Services handle external integrations


### 3. **Type Safety First**

- Comprehensive TypeScript coverage
- Zod schemas for runtime validation
- Generated types from Supabase
- Compile-time error prevention


### 4. **Performance by Default**

- Server-side rendering and streaming
- Intelligent caching with TanStack Query v5
- Code splitting and lazy loading
- Optimized bundle sizes


### 5. **Security by Design**

- Row-level security in Supabase
- HTTP-only authentication cookies
- Input validation and sanitization
- Rate limiting and CSRF protection


## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    CLIENT LAYER                            │
├─────────────────────────────────────────────────────────────┤
│  Browser/Mobile App                                         │
│  ├── React 19 Components (Server/Client)                   │
│  ├── TanStack Query v5 (Server State)                      │
│  ├── Zustand v5 (Client State)                             │
│  ├── Radix UI Components                                   │
│  └── API Client (HTTP Layer)                               │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/HTTPS
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 APPLICATION LAYER                          │
├─────────────────────────────────────────────────────────────┤
│  Next.js 15 Server                                         │
│  ├── App Router (RSC/SSR/SSG)                              │
│  ├── API Routes (Backend Logic)                            │
│  ├── Middleware (Auth/Security)                            │
│  ├── Server Actions                                        │
│  └── Static Assets                                         │
└─────────────────────────────────────────────────────────────┘
                              │ Database API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   DATA LAYER                               │
├─────────────────────────────────────────────────────────────┤
│  Supabase                                                   │
│  ├── PostgreSQL Database                                   │
│  ├── Real-time Subscriptions                               │
│  ├── Authentication Service                                │
│  ├── Storage Service                                       │
│  └── Edge Functions                                        │
└─────────────────────────────────────────────────────────────┘
```


## Technology Stack

### **Frontend**

- **Framework**: Next.js 15.2.4 (App Router)
- **Runtime**: React 19.0.0
- **Language**: TypeScript 5+
- **Styling**: Tailwind CSS v4.1.11
- **UI Components**: Radix UI v2+
- **Icons**: Lucide React v0.523.0
- **Animations**: Framer Motion v12.19.2
- **State Management**:
    - TanStack Query v5.83.0 (Server State)
    - Zustand v5.0.6 (Client State)
- **Forms**: React Hook Form v7.60.0 + Zod v4.0.5
- **Date Handling**: Date-fns v4.1.0
- **Progress**: NProgress v0.2.0
- **Notifications**: Sonner v2.0.5
- **Themes**: Next Themes v0.4.6


### **Backend \& Database**

- **Runtime**: Node.js (Next.js API Routes)
- **Database**: Supabase (PostgreSQL)
- **Auth**: @supabase/auth-helpers-nextjs v0.10.0 + @supabase/ssr v0.6.1
- **Client**: @supabase/supabase-js v2.52.0
- **Validation**: Zod v4.0.5


### **Testing \& Development**

- **Unit Testing**: Vitest v3.2.4
- **Component Testing**: React Testing Library v16.3.0
- **E2E Testing**: Playwright v1.54.1
- **Mocking**: MSW v2.10.4 + ts-mockito v2.6.1
- **Linting**: ESLint v9 + eslint-config-next


### **Development Tools**

- **Build Tool**: Turbopack (Next.js built-in)
- **CSS Framework**: Tailwind CSS v4 with animate-css plugin
- **Package Manager**: NPM
- **Environment**: Node.js 20+


## Project Structure

```
├── README.md
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── package.json
├── .env.local
├── .env.example
├── middleware.ts
├── vitest.config.ts
├── playwright.config.ts
│
├── public/
│   ├── images/
│   ├── icons/
│   └── favicon.ico
│
├── src/
│   ├── app/                        # Next.js 15 App Router
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── loading.tsx
│   │   ├── error.tsx
│   │   ├── not-found.tsx
│   │   │
│   │   ├── (auth)/                 # Route groups
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   └── register/
│   │   │       └── page.tsx
│   │   │
│   │   ├── dashboard/
│   │   │   ├── page.tsx
│   │   │   ├── loading.tsx
│   │   │   └── layout.tsx
│   │   │
│   │   └── api/                    # API Routes
│   │       ├── auth/
│   │       │   ├── login/route.ts
│   │       │   ├── logout/route.ts
│   │       │   └── me/route.ts
│   │       ├── users/
│   │       │   ├── route.ts
│   │       │   └── [id]/route.ts
│   │       └── health/route.ts
│   │
│   ├── components/
│   │   ├── ui/                     # Radix UI components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── dropdown-menu.tsx
│   │   │   ├── toast.tsx
│   │   │   └── index.ts
│   │   ├── forms/                  # Form components
│   │   │   ├── login-form.tsx
│   │   │   └── user-form.tsx
│   │   ├── layout/                 # Layout components
│   │   │   ├── header.tsx
│   │   │   ├── sidebar.tsx
│   │   │   └── footer.tsx
│   │   └── features/               # Feature-specific components
│   │       ├── auth/
│   │       ├── dashboard/
│   │       └── users/
│   │
│   ├── hooks/
│   │   ├── queries/                # TanStack Query hooks
│   │   │   ├── auth.ts
│   │   │   ├── users.ts
│   │   │   └── index.ts
│   │   ├── mutations/              # Mutation hooks
│   │   └── utils/                  # Utility hooks
│   │
│   ├── lib/
│   │   ├── supabase/
│   │   │   ├── client.ts           # Supabase client
│   │   │   ├── server.ts           # Server-side client
│   │   │   ├── middleware.ts       # Auth middleware
│   │   │   └── types.ts            # Database types
│   │   ├── api-client.ts           # Frontend API client
│   │   ├── auth.ts                 # Auth utilities
│   │   ├── validations.ts          # Zod schemas
│   │   ├── utils.ts                # General utilities
│   │   ├── constants.ts            # App constants
│   │   └── query-client.ts         # TanStack Query config
│   │
│   ├── store/
│   │   ├── auth.ts                 # Auth Zustand store
│   │   ├── ui.ts                   # UI state store
│   │   └── index.ts                # Store exports
│   │
│   ├── styles/
│   │   └── globals.css
│   │
│   └── types/
│       ├── api.ts                  # API response types
│       ├── auth.ts                 # Auth types
│       ├── database.ts             # Database types
│       └── global.d.ts             # Global type declarations
│
├── tests/                          # Test files
│   ├── __mocks__/
│   ├── components/
│   ├── pages/
│   └── utils/
│
├── e2e/                           # Playwright tests
│   ├── auth.spec.ts
│   └── dashboard.spec.ts
│
└── docs/                          # Documentation
    ├── api.md
    └── deployment.md
```


## Data Flow

### **Request Flow with App Router**

1. **Client Request**

```
User Action → Server Component → API Route → Supabase → Response
```

2. **Client Interaction**

```
User Action → Client Component → Hook → API Client → HTTP Request
```

3. **Response Handling**

```
HTTP Response → TanStack Query → Cache Update → Component Re-render
```


### **State Management Flow**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Action   │───▶│  Query Hook     │───▶│  API Client     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Component      │◀───│  TanStack       │◀───│  HTTP Response  │
│  Re-render      │    │  Query Cache    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Zustand Store  │
                       │ (Global State)  │
                       └─────────────────┘
```


## Authentication \& Authorization

### **Supabase Auth Configuration**

```typescript
// lib/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from './types'

export function createClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

```typescript
// lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from './types'

export async function createServerClient() {
  const cookieStore = await cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}
```


### **Authentication Middleware**

```typescript
// middleware.ts
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value)
            supabaseResponse.cookies.set(name, value, options)
          })
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl

  // Protected routes
  const protectedRoutes = ['/dashboard', '/profile', '/admin']
  const authRoutes = ['/login', '/register']

  if (protectedRoutes.some(route => pathname.startsWith(route)) && !user) {
    const url = request.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  if (authRoutes.some(route => pathname.startsWith(route)) && user) {
    const url = request.nextUrl.clone()
    url.pathname = '/dashboard'
    return NextResponse.redirect(url)
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object instead of the supabaseResponse object

  return supabaseResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```


### **Auth Hooks**

```typescript
// hooks/queries/auth.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createClient } from '@/lib/supabase/client'
import { useAuthStore } from '@/store/auth'
import { useRouter } from 'next/navigation'

const supabase = createClient()

export const useAuth = () => {
  const { user, setUser, logout: storeLogout } = useAuthStore()
  const queryClient = useQueryClient()
  const router = useRouter()

  const loginMutation = useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      
      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      setUser(data.user)
      queryClient.invalidateQueries({ queryKey: ['auth'] })
      router.push('/dashboard')
    },
  })

  const logoutMutation = useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    },
    onSuccess: () => {
      storeLogout()
      queryClient.clear()
      router.push('/login')
    },
  })

  const registerMutation = useMutation({
    mutationFn: async ({ email, password, name }: { 
      email: string; 
      password: string; 
      name: string;
    }) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      })
      
      if (error) throw error
      return data
    },
    onSuccess: () => {
      router.push('/login?message=Check your email to confirm your account')
    },
  })

  return {
    user,
    isAuthenticated: !!user,
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    register: registerMutation.mutate,
    isLoading: loginMutation.isPending || logoutMutation.isPending || registerMutation.isPending,
  }
}

export const useUser = () => {
  return useQuery({
    queryKey: ['auth', 'user'],
    queryFn: async () => {
      const { data: { session } } = await supabase.auth.getSession()
      return session?.user || null
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
```


## State Management

### **TanStack Query v5 Configuration**

```typescript
// lib/query-client.ts
import { QueryClient } from '@tanstack/react-query'
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister'
import { persistQueryClient } from '@tanstack/react-query-persist-client'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000,   // 10 minutes (renamed from cacheTime)
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false
        }
        return failureCount < 2
      },
    },
    mutations: {
      retry: 1,
      onError: (error) => {
        console.error('Mutation error:', error)
      },
    },
  },
})

// Persist queries to localStorage
if (typeof window !== 'undefined') {
  const persister = createSyncStoragePersister({
    storage: window.localStorage,
  })

  persistQueryClient({
    queryClient,
    persister,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  })
}
```


### **Query Hooks with v5 Patterns**

```typescript
// hooks/queries/users.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createClient } from '@/lib/supabase/client'
import type { User, CreateUserRequest } from '@/types'

const supabase = createClient()

// Query Keys Factory
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...userKeys.lists(), { filters }] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
}

// Queries
export const useUsers = (filters: Record<string, any> = {}) => {
  return useQuery({
    queryKey: userKeys.list(filters),
    queryFn: async () => {
      let query = supabase.from('users').select('*')
      
      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          query = query.eq(key, value)
        }
      })
      
      const { data, error } = await query
      if (error) throw error
      return data
    },
    placeholderData: [], // v5 replacement for initialData
  })
}

export const useUser = (id: string) => {
  return useQuery({
    queryKey: userKeys.detail(id),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single()
      
      if (error) throw error
      return data
    },
    enabled: !!id,
  })
}

// Optimistic Updates with v5
export const useUpdateUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, ...updates }: { id: string } & Partial<User>) => {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
      
      if (error) throw error
      return data
    },
    onMutate: async ({ id, ...newUser }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: userKeys.detail(id) })

      // Snapshot previous value
      const previousUser = queryClient.getQueryData(userKeys.detail(id))

      // Optimistically update
      queryClient.setQueryData(userKeys.detail(id), (old: User | undefined) => 
        old ? { ...old, ...newUser } : undefined
      )

      return { previousUser }
    },
    onError: (err, { id }, context) => {
      // Rollback on error
      if (context?.previousUser) {
        queryClient.setQueryData(userKeys.detail(id), context.previousUser)
      }
    },
    onSettled: (data, error, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: userKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
    },
  })
}

export const useCreateUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (user: CreateUserRequest) => {
      const { data, error } = await supabase
        .from('users')
        .insert(user)
        .select()
        .single()
      
      if (error) throw error
      return data
    },
    onSuccess: (newUser) => {
      // Update the users list
      queryClient.invalidateQueries({ queryKey: userKeys.lists() })
      
      // Add to cache
      queryClient.setQueryData(userKeys.detail(newUser.id), newUser)
    },
  })
}
```


### **Zustand v5 Stores**

```typescript
// store/auth.ts
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { User } from '@supabase/supabase-js'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  logout: () => void
  
  // Computed
  isAdmin: () => boolean
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user,
        isLoading: false 
      }),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      logout: () => set({ 
        user: null, 
        isAuthenticated: false, 
        isLoading: false 
      }),

      isAdmin: () => {
        const { user } = get()
        return user?.user_metadata?.role === 'admin'
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)
```

```typescript
// store/ui.ts
import { create } from 'zustand'

interface UIState {
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
  loading: boolean
  
  // Actions
  setSidebarOpen: (open: boolean) => void
  toggleSidebar: () => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setLoading: (loading: boolean) => void
}

export const useUIStore = create<UIState>((set, get) => ({
  sidebarOpen: false,
  theme: 'system',
  loading: false,

  setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),
  
  toggleSidebar: () => set((state) => ({ 
    sidebarOpen: !state.sidebarOpen 
  })),
  
  setTheme: (theme) => set({ theme }),
  
  setLoading: (loading) => set({ loading }),
}))
```


## API Design

### **Next.js 15 App Router API Routes**

```typescript
// app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
  role: z.enum(['user', 'admin']).default('user'),
})

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = Number(searchParams.get('page')) || 1
    const limit = Number(searchParams.get('limit')) || 20
    const search = searchParams.get('search') || ''

    let query = supabase
      .from('users')
      .select('*', { count: 'exact' })
      .range((page - 1) * limit, page * limit - 1)

    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    const { data: users, error, count } = await query

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    // Check authentication and admin role
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: userProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    const { data: newUser, error } = await supabase
      .from('users')
      .insert(validatedData)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(newUser, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation Error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}
```

```typescript
// app/api/users/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { z } from 'zod'

const updateUserSchema = z.object({
  name: z.string().min(1).optional(),
  email: z.string().email().optional(),
  role: z.enum(['user', 'admin']).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: targetUser, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json(targetUser)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions (user can update their own profile, admin can update anyone)
    const { data: userProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    const isAdmin = userProfile?.role === 'admin'
    const isOwner = user.id === params.id

    if (!isAdmin && !isOwner) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)

    // Non-admin users cannot change roles
    if (!isAdmin && validatedData.role) {
      delete validatedData.role
    }

    const { data: updatedUser, error } = await supabase
      .from('users')
      .update(validatedData)
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(updatedUser)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation Error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}
```


## Database Layer

### **Supabase Types Generation**

```typescript
// lib/supabase/types.ts
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'user' | 'admin'
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role?: 'user' | 'admin'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'user' | 'admin'
          avatar_url?: string | null
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          user_id: string
          bio: string | null
          website: string | null
          location: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          bio?: string | null
          website?: string | null
          location?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          bio?: string | null
          website?: string | null
          location?: string | null
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'user' | 'admin'
    }
  }
}

export type User = Database['public']['Tables']['users']['Row']
export type CreateUser = Database['public']['Tables']['users']['Insert']
export type UpdateUser = Database['public']['Tables']['users']['Update']
export type Profile = Database['public']['Tables']['profiles']['Row']
```


### **Database Migrations**

```sql
-- Create users table
create table if not exists public.users (
  id uuid references auth.users on delete cascade not null primary key,
  email text unique not null,
  name text not null,
  role user_role default 'user'::user_role not null,
  avatar_url text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create profiles table
create table if not exists public.profiles (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references public.users(id) on delete cascade not null,
  bio text,
  website text,
  location text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS
alter table public.users enable row level security;
alter table public.profiles enable row level security;

-- Create policies
create policy "Users can view their own profile" on public.users
  for select using (auth.uid() = id);

create policy "Users can update their own profile" on public.users
  for update using (auth.uid() = id);

create policy "Admins can view all users" on public.users
  for select using (
    exists (
      select 1 from public.users
      where id = auth.uid() and role = 'admin'
    )
  );

create policy "Users can view their own profile details" on public.profiles
  for select using (user_id = auth.uid());

create policy "Users can update their own profile details" on public.profiles
  for update using (user_id = auth.uid());

-- Create functions for updated_at
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$ language plpgsql;

-- Create triggers
create trigger handle_updated_at before update on public.users
  for each row execute procedure public.handle_updated_at();

create trigger handle_updated_at before update on public.profiles
  for each row execute procedure public.handle_updated_at();
```


## Frontend Layer

### **App Router Layout**

```typescript
// app/layout.tsx
import { Inter } from 'next/font/google'
import { ThemeProvider } from 'next-themes'
import { Toaster } from '@/components/ui/sonner'
import { QueryProvider } from '@/components/providers/query-provider'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Americas Top Mugshot',
  description: 'Modern web application with Next.js 15 and Supabase',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            {children}
            <Toaster />
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
```

```typescript
// components/providers/query-provider.tsx
'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState } from 'react'

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
            refetchOnWindowFocus: false,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}
```


### **Server Components**

```typescript
// app/dashboard/page.tsx
import { createServerClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { DashboardContent } from '@/components/features/dashboard/dashboard-content'

export default async function DashboardPage() {
  const supabase = await createServerClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    redirect('/login')
  }

  // Pre-fetch data on the server
  const { data: users } = await supabase
    .from('users')
    .select('*')
    .limit(10)

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>
      <DashboardContent initialUsers={users || []} />
    </div>
  )
}
```


### **Client Components**

```typescript
// components/features/dashboard/dashboard-content.tsx
'use client'

import { useState } from 'react'
import { useUsers } from '@/hooks/queries/users'
import { UserList } from '@/components/features/users/user-list'
import { UserFilters } from '@/components/features/users/user-filters'
import type { User } from '@/lib/supabase/types'

interface DashboardContentProps {
  initialUsers: User[]
}

export function DashboardContent({ initialUsers }: DashboardContentProps) {
  const [filters, setFilters] = useState({})
  
  const { data: users, isLoading, error } = useUsers(filters, {
    initialData: initialUsers,
  })

  return (
    <div className="space-y-6">
      <UserFilters onFiltersChange={setFilters} />
      
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {error.message}</div>}
      {users && <UserList users={users} />}
    </div>
  )
}
```


## UI Components

### **Radix UI Component Patterns**

```typescript
// components/ui/button.tsx
import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
        outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-10 rounded-md px-8',
        icon: 'h-9 w-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants }
```

```typescript
// components/ui/dialog.tsx
'use client'

import * as React from 'react'
import * as DialogPrimitive from '@radix-ui/react-dialog'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

const Dialog = DialogPrimitive.Root
const DialogTrigger = DialogPrimitive.Trigger
const DialogPortal = DialogPrimitive.Portal
const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogContent,
  DialogClose,
}
```


## Performance Optimizations

### **Next.js 15 Configuration**

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    optimizePackageImports: [
      '@tanstack/react-query',
      'zustand',
      'date-fns',
      'lucide-react',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
    ],
  },

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // Performance optimizations
  poweredByHeader: false,
  
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ]
  },

  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Bundle analyzer in production builds
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
          })
        )
      }
    }
    
    return config
  },
}

module.exports = nextConfig
```


### **Streaming and Suspense**

```typescript
// app/dashboard/loading.tsx
export default function DashboardLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    </div>
  )
}
```

```typescript
// components/features/users/user-list-async.tsx
import { Suspense } from 'react'
import { UserListServer } from './user-list-server'
import { UserListSkeleton } from './user-list-skeleton'

export function UserListAsync() {
  return (
    <Suspense fallback={<UserListSkeleton />}>
      <UserListServer />
    </Suspense>
  )
}
```


## Testing Strategy

### **Vitest Configuration**

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    globals: true,
    css: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

```typescript
// tests/setup.ts
import '@testing-library/jest-dom'
import { beforeAll, afterEach, afterAll } from 'vitest'
import { server } from './mocks/server'

// Establish API mocking before all tests
beforeAll(() => server.listen())

// Reset any request handlers that we may add during the tests
afterEach(() => server.resetHandlers())

// Clean up after the tests are finished
afterAll(() => server.close())
```


### **Component Testing**

```typescript
// tests/components/ui/button.test.tsx
import { render, screen } from '@testing-library/react'
import { Button } from '@/components/ui/button'
import { describe, it, expect } from 'vitest'

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
  })

  it('applies variant classes', () => {
    render(<Button variant="destructive">Delete</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-destructive')
  })

  it('handles click events', async () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    await userEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledOnce()
  })
})
```


### **API Testing**

```typescript
// tests/api/users.test.ts
import { createMockRequest, createMockResponse } from './utils'
import { GET, POST } from '@/app/api/users/route'
import { describe, it, expect, beforeEach } from 'vitest'

describe('/api/users', () => {
  beforeEach(() => {
    // Reset mocks
  })

  describe('GET', () => {
    it('returns users for authenticated request', async () => {
      const request = createMockRequest('GET', '/api/users', {
        headers: { authorization: 'Bearer valid-token' }
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.users).toBeDefined()
      expect(Array.isArray(data.users)).toBe(true)
    })

    it('returns 401 for unauthenticated request', async () => {
      const request = createMockRequest('GET', '/api/users')
      
      const response = await GET(request)
      
      expect(response.status).toBe(401)
    })
  })

  describe('POST', () => {
    it('creates user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user'
      }

      const request = createMockRequest('POST', '/api/users', {
        body: userData,
        headers: { authorization: 'Bearer admin-token' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.email).toBe(userData.email)
    })
  })
})
```


### **E2E Testing with Playwright**

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
})
```

```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Authentication', () => {
  test('should login successfully', async ({ page }) => {
    await page.goto('/login')

    await page.fill('[data-testid=email]', '<EMAIL>')
    await page.fill('[data-testid=password]', 'password123')
    await page.click('[data-testid=login-button]')

    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('[data-testid=user-menu]')).toBeVisible()
  })

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/login')

    await page.fill('[data-testid=email]', '<EMAIL>')
    await page.fill('[data-testid=password]', 'wrongpassword')
    await page.click('[data-testid=login-button]')

    await expect(page.locator('[data-testid=error-message]')).toBeVisible()
    await expect(page.locator('[data-testid=error-message]')).toContainText('Invalid credentials')
  })
})
```


## Security Considerations

### **Environment Variables**

```bash
# .env.example
# App Configuration
NEXT_PUBLIC_APP_NAME="Americas Top Mugshot"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Security
JWT_SECRET="your-super-secret-jwt-key-min-32-chars"
NEXTAUTH_SECRET="your-nextauth-secret"

# Rate Limiting
UPSTASH_REDIS_REST_URL="your-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-redis-token"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
NEXT_PUBLIC_GA_ID="your-google-analytics-id"

# Development
NODE_ENV="development"
ANALYZE="false"
```


### **Input Validation**

```typescript
// lib/validations.ts
import { z } from 'zod'

export const userSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  role: z.enum(['user', 'admin']).default('user'),
})

export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

export const updateUserSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
  role: z.enum(['user', 'admin']).optional(),
  bio: z.string().max(500).optional(),
  website: z.string().url().optional(),
  location: z.string().max(100).optional(),
})

export const paginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

// Sanitization utilities
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '')
}

export function validateAndSanitize<T>(
  schema: z.ZodSchema<T>, 
  data: unknown
): T {
  const result = schema.parse(data)
  
  // Apply sanitization if needed
  if (typeof result === 'object' && result !== null) {
    Object.keys(result).forEach(key => {
      const value = (result as any)[key]
      if (typeof value === 'string') {
        (result as any)[key] = sanitizeString(value)
      }
    })
  }
  
  return result
}
```


### **Rate Limiting**

```typescript
// lib/rate-limit.ts
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'
import { NextRequest } from 'next/server'

const redis = Redis.fromEnv()

export const ratelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(100, '1 m'), // 100 requests per minute
  analytics: true,
})

export async function checkRateLimit(identifier: string) {
  const { success, limit, reset, remaining } = await ratelimit.limit(identifier)
  
  return {
    success,
    limit,
    reset,
    remaining,
  }
}

export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP.trim()
  }
  
  return request.ip || 'anonymous'
}

// Rate limiting middleware for API routes
export async function withRateLimit(
  request: NextRequest,
  handler: () => Promise<Response>
): Promise<Response> {
  const identifier = getClientIP(request)
  const { success, limit, reset, remaining } = await checkRateLimit(identifier)
  
  if (!success) {
    return new Response(
      JSON.stringify({
        error: 'Too many requests',
        retryAfter: Math.round((reset - Date.now()) / 1000),
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': reset.toString(),
          'Retry-After': Math.round((reset - Date.now()) / 1000).toString(),
        },
      }
    )
  }
  
  const response = await handler()
  
  // Add rate limit headers to successful responses
  response.headers.set('X-RateLimit-Limit', limit.toString())
  response.headers.set('X-RateLimit-Remaining', remaining.toString())
  response.headers.set('X-RateLimit-Reset', reset.toString())
  
  return response
}
```


## Development Guidelines

### **Code Quality Standards**

```json
// .eslintrc.json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-var": "error",
    "no-console": "warn",
    "react-hooks/exhaustive-deps": "error",
    "react/jsx-curly-brace-presence": ["error", "never"]
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module"
  }
}
```


### **Git Workflow**

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Run linter
        run: npm run lint
        
      - name: Type check
        run: npx tsc --noEmit
        
      - name: Run unit tests
        run: npm run test:coverage
        
      - name: Build application
        run: npm run build
        
      - name: Run E2E tests
        run: npm run e2e

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```


### **Commit Convention**

```
feat: add user authentication
fix: resolve login redirect issue
docs: update API documentation
style: format code with prettier
refactor: extract auth logic to hooks
test: add user creation tests
chore: update dependencies
```


## Deployment Strategy

### **Environment Configuration**

```typescript
// lib/config.ts
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
] as const

const optionalEnvVars = [
  'SENTRY_DSN',
  'UPSTASH_REDIS_REST_URL',
  'NEXT_PUBLIC_GA_ID',
] as const

type RequiredEnvVar = typeof requiredEnvVars[number]
type OptionalEnvVar = typeof optionalEnvVars[number]

function getEnvVar(name: RequiredEnvVar): string
function getEnvVar(name: OptionalEnvVar): string | undefined
function getEnvVar(name: string): string | undefined {
  return process.env[name]
}

export const config = {
  app: {
    name: getEnvVar('NEXT_PUBLIC_APP_NAME') || 'Americas Top Mugshot',
    url: getEnvVar('NEXT_PUBLIC_APP_URL') || 'http://localhost:3000',
    env: process.env.NODE_ENV || 'development',
  },
  
  supabase: {
    url: getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
    anonKey: getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
    serviceRoleKey: getEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
  },
  
  redis: {
    url: getEnvVar('UPSTASH_REDIS_REST_URL'),
    token: getEnvVar('UPSTASH_REDIS_REST_TOKEN'),
  },
  
  monitoring: {
    sentryDsn: getEnvVar('SENTRY_DSN'),
    gaId: getEnvVar('NEXT_PUBLIC_GA_ID'),
  },
} as const

// Validate required environment variables
export function validateConfig() {
  const missing = requiredEnvVars.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

// Only validate in production builds
if (process.env.NODE_ENV === 'production') {
  validateConfig()
}
```


### **Production Checklist**

- [ ] **Environment Setup**
    - [ ] All required environment variables configured
    - [ ] Supabase project configured with RLS policies
    - [ ] Database migrations applied
    - [ ] API keys rotated and secured
- [ ] **Security**
    - [ ] HTTPS enforced
    - [ ] Security headers configured
    - [ ] Rate limiting enabled
    - [ ] Input validation implemented
    - [ ] CORS policies configured
- [ ] **Performance**
    - [ ] Images optimized
    - [ ] Bundle size analyzed
    - [ ] CDN configured
    - [ ] Caching strategies implemented
    - [ ] Database indexes created
- [ ] **Monitoring**
    - [ ] Error tracking setup (Sentry)
    - [ ] Analytics configured (GA4)
    - [ ] Performance monitoring enabled
    - [ ] Health checks implemented
    - [ ] Logging configured
- [ ] **Backup \& Recovery**
    - [ ] Database backup strategy
    - [ ] File storage backup
    - [ ] Disaster recovery plan
    - [ ] Data retention policies


## Monitoring \& Observability

### **Error Tracking**

```typescript
// lib/monitoring.ts
import * as Sentry from '@sentry/nextjs'

export function initMonitoring() {
  if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 0.1,
      
      beforeSend(event) {
        // Filter out sensitive data
        if (event.request?.cookies) {
          delete event.request.cookies
        }
        
        if (event.request?.headers?.authorization) {
          delete event.request.headers.authorization
        }
        
        return event
      },
      
      beforeSendTransaction(event) {
        // Filter sensitive transaction data
        return event
      },
    })
  }
}

export function trackEvent(event: string, data?: Record<string, any>) {
  if (process.env.NODE_ENV === 'production') {
    Sentry.addBreadcrumb({
      message: event,
      data,
      level: 'info',
      timestamp: Date.now() / 1000,
    })
  } else {
    console.log(`Event: ${event}`, data)
  }
}

export function trackError(error: Error, context?: Record<string, any>) {
  console.error(error)
  
  if (process.env.NODE_ENV === 'production') {
    Sentry.withScope(scope => {
      if (context) {
        Object.keys(context).forEach(key => {
          scope.setContext(key, context[key])
        })
      }
      Sentry.captureException(error)
    })
  }
}

export function trackUserAction(userId: string, action: string, metadata?: Record<string, any>) {
  Sentry.setUser({ id: userId })
  trackEvent(`user.${action}`, metadata)
}
```


### **Performance Monitoring**

```typescript
// lib/analytics.ts
import { config } from './config'

declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: any) => void
  }
}

export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && config.monitoring.gaId) {
    window.gtag('config', config.monitoring.gaId, {
      page_path: url,
    })
  }
}

export function trackUserAction(action: string, parameters?: Record<string, any>) {
  if (typeof window !== 'undefined' && config.monitoring.gaId) {
    window.gtag('event', action, {
      event_category: 'User Action',
      ...parameters,
    })
  }
}

// Web Vitals tracking
export function reportWebVitals(metric: any) {
  if (config.monitoring.gaId) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      value: Math.round(metric.value),
      event_label: metric.id,
      non_interaction: true,
    })
  }
  
  // Log to console in development
  if (config.app.env === 'development') {
    console.log(metric)
  }
}
```


### **Health Checks**

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET() {
  const healthChecks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    checks: {
      database: 'unknown',
      redis: 'unknown',
    },
  }

  try {
    // Database health check
    const supabase = await createServerClient()
    const { error: dbError } = await supabase.from('users').select('count').limit(1)
    healthChecks.checks.database = dbError ? 'unhealthy' : 'healthy'
  } catch (error) {
    healthChecks.checks.database = 'unhealthy'
  }

  // Determine overall status
  const isHealthy = Object.values(healthChecks.checks).every(
    status => status === 'healthy'
  )
  
  healthChecks.status = isHealthy ? 'healthy' : 'unhealthy'

  return NextResponse.json(healthChecks, {
    status: isHealthy ? 200 : 503,
  })
}
```


## Conclusion

This architecture provides a robust, scalable foundation for building modern web applications with Next.js 15 and Supabase. The clean separation of concerns, comprehensive state management, and performance optimizations ensure both excellent developer experience and optimal user experience.

Key benefits of this architecture:

- **Modern Stack**: Leverages the latest Next.js 15, React 19, and TanStack Query v5 features
- **Type Safety**: End-to-end TypeScript with Zod validation
- **Performance**: Server-side rendering, streaming, and intelligent caching
- **Security**: Row-level security, input validation, and secure authentication
- **Developer Experience**: Comprehensive testing, linting, and development tools
- **Scalability**: Modular architecture that grows with your team

The architecture is designed to scale with your application requirements while maintaining code quality, security standards, and optimal performance.

