-- Add OAuth-specific fields to profiles table
ALTER TABLE profiles 
ADD COLUMN email TEXT,
ADD COLUMN avatar_url TEXT;

-- Make state and county nullable for OAuth users who haven't set location yet
ALTER TABLE profiles 
ALTER COLUMN state DROP NOT NULL,
ALTER COLUMN county DROP NOT NULL;

-- Create index for email lookups
CREATE INDEX IF NOT EXISTS profiles_email_idx ON profiles(email);

-- Update RLS policies to allow profile creation without state/county for OAuth users
CREATE POLICY "OAuth users can create profile without location" ON profiles
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND 
    (state IS NOT NULL OR email IS NOT NULL)
  ); 