config:
  target: 'http://localhost:3000'
  phases:
    # Quick 2-minute smoke test
    - duration: 120
      arrivalRate: 5
      name: "Quick smoke test"
  
  # Relaxed thresholds for quick testing
  ensure:
    - http.response_time.p95: 3000   # 95% under 3 seconds
    - http.response_time.median: 1000 # Median under 1 second
    - http.codes.200: 90  # At least 90% success rate
    - http.codes.5xx: 5   # Less than 5% server errors

  http:
    timeout: 10
    pool: 10
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  # Simple test of mugshots API
  - name: "Quick Mugshots API Test"
    weight: 100
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - Quick Test"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
      - think: 1

processor: "./load-tests/scenarios/data-generators.js"
