import { useQuery } from '@tanstack/react-query'
import type { 
  MugshotFilters,
  MugshotsPaginationOptions,
  MugshotsSortOptions,
  MugshotsResponse
} from '@/lib/services/api-client'

/**
 * Core mugshots query hook that fetches paginated mugshots with filters
 * Replaces direct API calls in MugshotsPageClient with intelligent caching
 * 
 * @param filters - Filter parameters for mugshots (state, county, search, etc.)
 * @param sortOptions - Sort options (sortBy field)
 * @param pagination - Pagination options (page, perPage, includeTotal)
 * @returns TanStack Query result with mugshots data
 */
export function useMugshotsQuery(
  filters: MugshotFilters = {},
  sortOptions: MugshotsSortOptions = {},
  pagination: MugshotsPaginationOptions = { page: 1, perPage: 12 }
) {
  return useQuery({
    queryKey: ['mugshots', filters, sortOptions, pagination],
    queryFn: async (): Promise<MugshotsResponse['data']> => {
      // Transform filter object into URLSearchParams matching existing /api/mugshots format
      const params = new URLSearchParams()
      
      // Add pagination parameters
      if (pagination.page) params.set('page', pagination.page.toString())
      if (pagination.perPage) params.set('perPage', pagination.perPage.toString())
      if (pagination.includeTotal) params.set('includeTotal', 'true')
      
      // Add filter parameters - only include defined values to prevent unnecessary cache keys
      if (filters.search) params.set('search', filters.search)
      if (filters.state) params.set('state', filters.state)
      if (filters.county) params.set('county', filters.county)
      if (filters.dateFrom) params.set('dateFrom', filters.dateFrom)
      if (filters.dateTo) params.set('dateTo', filters.dateTo)
      if (filters.categories?.length) params.set('categories', filters.categories.join(','))
      if (filters.tags?.length) params.set('tags', filters.tags.join(','))
      
      // Add sort parameters
      if (sortOptions.sortBy) params.set('sortBy', sortOptions.sortBy)
      
      const response = await fetch(`/api/mugshots?${params.toString()}`)
      const result = await response.json()
      
      // Handle API errors by throwing to trigger Query error boundaries
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to fetch mugshots')
      }
      
      // Return the data portion that components expect
      return result.data
    },
    // FIXED: Aggressive caching to prevent excessive API calls
    placeholderData: (previousData) => previousData, // Smooth pagination without loading states
    staleTime: 5 * 60 * 1000, // INCREASED: 5 minutes to reduce API calls
    gcTime: 30 * 60 * 1000, // INCREASED: 30 minutes for better caching
    retry: 1, // REDUCED: Only 1 retry to prevent call multiplication
    retryDelay: 1000, // FIXED: Simple 1 second delay
    refetchOnWindowFocus: false, // Prevent unnecessary refetching
    // FIXED: Use global setting to prevent conflicts
    refetchOnMount: false, // Let global config handle this
    // ADDED: Network mode for better offline handling
    networkMode: 'online',
    // ADDED: Prevent background refetching
    refetchInterval: false,
    refetchIntervalInBackground: false,
  })
}

/**
 * Prefetch utility for hover-based preloading
 * Can be used by components to prefetch next page or related data
 */
export function prefetchMugshots(
  queryClient: { prefetchQuery: (config: object) => Promise<void> },
  filters: MugshotFilters = {},
  sortOptions: MugshotsSortOptions = {},
  pagination: MugshotsPaginationOptions = { page: 1, perPage: 12 }
) {
  return queryClient.prefetchQuery({
    queryKey: ['mugshots', filters, sortOptions, pagination],
    queryFn: async () => {
      const params = new URLSearchParams()
      
      if (pagination.page) params.set('page', pagination.page.toString())
      if (pagination.perPage) params.set('perPage', pagination.perPage.toString())
      if (pagination.includeTotal) params.set('includeTotal', 'true')
      
      if (filters.search) params.set('search', filters.search)
      if (filters.state) params.set('state', filters.state)
      if (filters.county) params.set('county', filters.county)
      if (filters.dateFrom) params.set('dateFrom', filters.dateFrom)
      if (filters.dateTo) params.set('dateTo', filters.dateTo)
      if (filters.categories?.length) params.set('categories', filters.categories.join(','))
      if (filters.tags?.length) params.set('tags', filters.tags.join(','))
      
      if (sortOptions.sortBy) params.set('sortBy', sortOptions.sortBy)
      
      const response = await fetch(`/api/mugshots?${params.toString()}`)
      const result = await response.json()
      
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to fetch mugshots')
      }
      
      return result.data
    },
    staleTime: 5 * 60 * 1000,
  })
} 