import { renderWithQuery } from '@/lib/testing/query-test-utils'
import { useQueryClient } from '@tanstack/react-query'
import { screen } from '@testing-library/react'

function TestComponent() {
  const queryClient = useQueryClient()
  return <div data-testid="test">Query client available: {queryClient ? 'true' : 'false'}</div>
}

function TestDefaultOptions() {
  const queryClient = useQueryClient()
  const defaultOptions = queryClient.getDefaultOptions()
  
  return (
    <div>
      <div data-testid="retry">{String(defaultOptions.queries?.retry)}</div>
      <div data-testid="gctime">{String(defaultOptions.queries?.gcTime)}</div>
      <div data-testid="staletime">{String(defaultOptions.queries?.staleTime)}</div>
    </div>
  )
}

describe('Query Setup', () => {
  test('QueryClient is available in component tree', () => {
    renderWithQuery(<TestComponent />)
    expect(screen.getByTestId('test')).toHaveTextContent('Query client available: true')
  })

  test('Query client has correct default options for testing', () => {
    renderWithQuery(<TestDefaultOptions />)
    
    expect(screen.getByTestId('retry')).toHaveTextContent('false') // Test environment
    expect(screen.getByTestId('gctime')).toHaveTextContent('0') // Test environment
    expect(screen.getByTestId('staletime')).toHaveTextContent('0') // Test environment
  })

  test('Query client can be customized per test', () => {
    const customQueryClient = renderWithQuery(<TestComponent />).queryClient
    
    // Should be a unique instance per test
    expect(customQueryClient).toBeDefined()
    expect(customQueryClient.getDefaultOptions().queries?.retry).toBe(false)
  })

  test('Multiple renders create separate query clients', () => {
    const { queryClient: client1 } = renderWithQuery(<TestComponent />)
    const { queryClient: client2 } = renderWithQuery(<TestComponent />)
    
    // Each render should get a fresh query client instance
    expect(client1).not.toBe(client2)
  })
})

describe('Query Test Utils', () => {
  test('Mock API responses are properly structured', () => {
    const { mockApiResponses } = require('../lib/testing/query-test-utils')
    
    // Verify mugshots response structure
    expect(mockApiResponses.mugshots).toHaveProperty('success', true)
    expect(mockApiResponses.mugshots.data).toHaveProperty('mugshots')
    expect(mockApiResponses.mugshots.data).toHaveProperty('pagination')
    expect(mockApiResponses.mugshots.data.mugshots[0]).toHaveProperty('id')
    expect(mockApiResponses.mugshots.data.mugshots[0]).toHaveProperty('firstName')
    
    // Verify mugshot detail response structure
    expect(mockApiResponses.mugshotDetail).toHaveProperty('success', true)
    expect(mockApiResponses.mugshotDetail.data).toHaveProperty('mugshot')
    expect(mockApiResponses.mugshotDetail.data).toHaveProperty('ratings')
    expect(mockApiResponses.mugshotDetail.data).toHaveProperty('tags')
    
    // Verify user data response structure
    expect(mockApiResponses.userMugshotData).toHaveProperty('success', true)
    expect(mockApiResponses.userMugshotData.data).toHaveProperty('userRating')
    expect(mockApiResponses.userMugshotData.data).toHaveProperty('userTags')
  })

  test('Mock error responses are properly structured', () => {
    const { mockApiErrors } = require('../lib/testing/query-test-utils')
    
    expect(mockApiErrors.networkError).toHaveProperty('success', false)
    expect(mockApiErrors.networkError).toHaveProperty('message')
    expect(mockApiErrors.networkError).toHaveProperty('error')
    
    expect(mockApiErrors.authError).toHaveProperty('error', 'UNAUTHENTICATED')
    expect(mockApiErrors.notFound).toHaveProperty('error', 'NOT_FOUND')
  })
}) 