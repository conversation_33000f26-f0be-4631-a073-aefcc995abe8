# Design Document

## Overview

This design implements a modern state management architecture that enhances the existing Zustand-based system with TanStack Query for server state and Supabase realtime capabilities. The approach is purely additive - we'll layer the new patterns on top of existing code without modifying any working functionality, ensuring zero risk to current operations while dramatically improving performance and user experience.

## Architecture

### Current API Endpoints Analysis

**Core Data Endpoints:**
- `GET /api/mugshots` - Paginated mugshots with filters, includes ratings/tags data
- `GET /api/mugshots/[id]` - Single mugshot with complete ratings/tags statistics
- `GET /api/user/mugshot/[id]/data` - User-specific rating/tag data (requires auth)

**Interaction Endpoints:**
- `POST /api/ratings/submit` - Submit user rating
- `POST /api/tags/toggle` - Toggle user tag
- `GET /api/tags/statistics` - Tag statistics

**Auth & User Endpoints:**
- `POST /api/auth/signin` - User authentication
- `POST /api/auth/signup` - User registration
- `GET /api/debug-profile` - User profile data

**Current Data Flow Patterns:**
1. **Initial Page Load:** `/api/mugshots` with all data included
2. **Mugshot Detail Popup:** `/api/mugshots/[id]` for fresh ratings/tags
3. **User-Specific Data:** `/api/user/mugshot/[id]/data` when authenticated
4. **Optimistic Updates:** Immediate UI updates, then API calls
5. **Auth Redirects:** Login redirects with URL parameters for return navigation

**Existing Zustand Stores:**
- `auth-store.ts` - User authentication and profile data (✅ Keep as-is)
- `filter-store.ts` - Search filters with URL sync (🔄 Enhance with Query)
- `rating-store.ts` - Rating data with optimistic updates (🔄 Replace with Query)
- `tag-store.ts` - Tag data with optimistic updates (🔄 Replace with Query)

**Migration Strategy:**
1. **Phase 1:** Add TanStack Query alongside existing patterns
2. **Phase 2:** Create Query-based alternatives for server state
3. **Phase 3:** Gradually migrate components to use Query hooks
4. **Phase 4:** Remove old server state management (keep client state)

### New Architecture Components

```mermaid
graph TB
    subgraph "Client State (Zustand)"
        A[Auth Store] --> A1[User Session]
        A[Auth Store] --> A2[Profile Data]
        F[Filter Store] --> F1[UI Preferences]
        F[Filter Store] --> F2[URL Sync]
    end
    
    subgraph "Server State (TanStack Query)"
        Q[Query Client] --> Q1[Mugshots Data]
        Q[Query Client] --> Q2[Rating Data]
        Q[Query Client] --> Q3[Tag Data]
        Q[Query Client] --> Q4[User Data]
    end
    
    subgraph "Realtime Layer"
        R[Supabase Realtime] --> R1[Rating Updates]
        R[Supabase Realtime] --> R2[Tag Updates]
        R[Supabase Realtime] --> R3[New Mugshots]
    end
    
    subgraph "API Layer (Unchanged)"
        API[Existing APIs] --> API1[/api/mugshots]
        API[Existing APIs] --> API2[/api/ratings]
        API[Existing APIs] --> API3[/api/tags]
    end
    
    Q --> API
    R --> Q
    Components --> Q
    Components --> A
    Components --> F
```

## Components and Interfaces

### 1. Query Client Setup

**File:** `lib/query/query-client.ts`
```typescript
// New file - provides centralized Query configuration
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
})
```

### 2. Query Hooks (Server State)

**File:** `lib/hooks/queries/use-mugshots-query.ts`
```typescript
// New file - replaces direct API calls in MugshotsPageClient
export function useMugshotsQuery(filters: MugshotFilters, pagination: PaginationOptions) {
  return useQuery({
    queryKey: ['mugshots', filters, pagination],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        perPage: pagination.perPage.toString(),
        includeTotal: 'true',
        ...Object.fromEntries(Object.entries(filters).filter(([, v]) => v))
      })
      
      const response = await fetch(`/api/mugshots?${params}`)
      const data = await response.json()
      
      if (!data.success) throw new Error(data.message)
      return data.data
    },
    keepPreviousData: true, // Smooth pagination
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
```

**File:** `lib/hooks/queries/use-mugshot-detail-query.ts`
```typescript
// New file - for mugshot detail popup with fresh data
export function useMugshotDetailQuery(mugshotId: string, enabled = true) {
  return useQuery({
    queryKey: ['mugshot', mugshotId, 'detail'],
    queryFn: async () => {
      const response = await fetch(`/api/mugshots/${mugshotId}`)
      const data = await response.json()
      
      if (!data.success) throw new Error(data.message)
      return data.data
    },
    enabled,
    staleTime: 1 * 60 * 1000, // 1 minute (fresher for popups)
  })
}
```

**File:** `lib/hooks/queries/use-user-mugshot-data-query.ts`
```typescript
// New file - for authenticated user's rating/tag data
export function useUserMugshotDataQuery(mugshotId: string, enabled = true) {
  const { isAuthenticated } = useAuthStore()
  
  return useQuery({
    queryKey: ['user', 'mugshot', mugshotId, 'data'],
    queryFn: async () => {
      const response = await fetch(`/api/user/mugshot/${mugshotId}/data`)
      const data = await response.json()
      
      if (!data.success) {
        if (data.error === 'UNAUTHENTICATED') {
          // Handle auth redirect with current URL
          const currentUrl = window.location.href
          window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
          return null
        }
        throw new Error(data.message)
      }
      return data.data
    },
    enabled: enabled && isAuthenticated,
    staleTime: 30 * 1000, // 30 seconds (user data changes frequently)
  })
}
```

**File:** `lib/hooks/mutations/use-rating-mutation.ts`
```typescript
// New file - replaces rating-store.ts optimistic updates
export function useRatingMutation(mugshotId: string) {
  const queryClient = useQueryClient()
  const { isAuthenticated } = useAuthStore()
  
  return useMutation({
    mutationFn: async (rating: number) => {
      if (!isAuthenticated) {
        // Redirect to login with return URL
        const currentUrl = window.location.href
        window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
        throw new Error('Authentication required')
      }
      
      const response = await fetch('/api/ratings/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mugshotId, rating })
      })
      
      const data = await response.json()
      if (!data.success) throw new Error(data.message)
      return data.data
    },
    onMutate: async (newRating) => {
      // Optimistic updates for instant feedback
      await queryClient.cancelQueries(['mugshot', mugshotId])
      await queryClient.cancelQueries(['user', 'mugshot', mugshotId])
      
      const previousMugshot = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
      const previousUser = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
      
      // Update user rating immediately
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], old => ({
        ...old,
        userRating: newRating
      }))
      
      // Update mugshot statistics optimistically
      queryClient.setQueryData(['mugshot', mugshotId, 'detail'], old => {
        if (!old?.ratings) return old
        const currentTotal = old.ratings.totalRatings
        const currentAvg = old.ratings.averageRating
        const newTotal = currentTotal + (previousUser?.userRating ? 0 : 1)
        const newSum = (currentAvg * currentTotal) - (previousUser?.userRating || 0) + newRating
        const newAvg = newSum / newTotal
        
        return {
          ...old,
          ratings: {
            ...old.ratings,
            averageRating: Math.round(newAvg * 100) / 100,
            totalRatings: newTotal
          }
        }
      })
      
      return { previousMugshot, previousUser }
    },
    onError: (err, newRating, context) => {
      // Rollback optimistic updates
      if (context?.previousMugshot) {
        queryClient.setQueryData(['mugshot', mugshotId, 'detail'], context.previousMugshot)
      }
      if (context?.previousUser) {
        queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], context.previousUser)
      }
    },
    onSettled: () => {
      // Refetch for server truth
      queryClient.invalidateQueries(['mugshot', mugshotId])
      queryClient.invalidateQueries(['user', 'mugshot', mugshotId])
    }
  })
}
```

**File:** `lib/hooks/mutations/use-tag-mutation.ts`
```typescript
// New file - replaces tag-store.ts optimistic updates
export function useTagMutation(mugshotId: string) {
  const queryClient = useQueryClient()
  const { isAuthenticated } = useAuthStore()
  
  return useMutation({
    mutationFn: async (tagType: string) => {
      if (!isAuthenticated) {
        const currentUrl = window.location.href
        window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
        throw new Error('Authentication required')
      }
      
      const response = await fetch('/api/tags/toggle', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mugshotId, tagType })
      })
      
      const data = await response.json()
      if (!data.success) throw new Error(data.message)
      return data.data
    },
    onMutate: async (tagType) => {
      // Optimistic tag toggle
      await queryClient.cancelQueries(['mugshot', mugshotId])
      await queryClient.cancelQueries(['user', 'mugshot', mugshotId])
      
      const previousMugshot = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
      const previousUser = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
      
      // Toggle user tag
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], old => {
        const currentTags = old?.userTags || []
        const hasTag = currentTags.includes(tagType)
        return {
          ...old,
          userTags: hasTag 
            ? currentTags.filter(t => t !== tagType)
            : [...currentTags, tagType]
        }
      })
      
      // Update tag statistics
      queryClient.setQueryData(['mugshot', mugshotId, 'detail'], old => {
        if (!old?.tags) return old
        const hasTag = previousUser?.userTags?.includes(tagType)
        const currentCount = old.tags.tagCounts[tagType] || 0
        
        return {
          ...old,
          tags: {
            ...old.tags,
            tagCounts: {
              ...old.tags.tagCounts,
              [tagType]: hasTag ? currentCount - 1 : currentCount + 1
            }
          }
        }
      })
      
      return { previousMugshot, previousUser }
    },
    onError: (err, tagType, context) => {
      // Rollback optimistic updates
      if (context?.previousMugshot) {
        queryClient.setQueryData(['mugshot', mugshotId, 'detail'], context.previousMugshot)
      }
      if (context?.previousUser) {
        queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], context.previousUser)
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries(['mugshot', mugshotId])
      queryClient.invalidateQueries(['user', 'mugshot', mugshotId])
    }
  })
}
```

### 3. Enhanced Zustand Stores (Client State Only)

**File:** `lib/stores/client-preferences-store.ts`
```typescript
// New file - pure client state only
interface ClientPreferencesState {
  // UI preferences only
  gridView: 'large' | 'medium'
  theme: 'dark' | 'light'
  autoRefresh: boolean
  
  // Actions
  setGridView: (view: 'large' | 'medium') => void
  setTheme: (theme: 'dark' | 'light') => void
  setAutoRefresh: (enabled: boolean) => void
}
```

**Enhanced:** `lib/stores/auth-store.ts`
```typescript
// Keep existing auth store but remove server data fetching
// Auth store becomes pure client state for session management
// Server-side profile data moves to TanStack Query
```

### 4. Realtime Integration

**File:** `lib/realtime/supabase-realtime.ts`
```typescript
// New file - handles all realtime subscriptions
export function useRealtimeRatings(mugshotId: string) {
  const queryClient = useQueryClient()
  
  useEffect(() => {
    const channel = supabase
      .channel(`ratings-${mugshotId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'ratings',
        filter: `mugshot_id=eq.${mugshotId}`
      }, (payload) => {
        // Update Query cache with realtime data
        queryClient.invalidateQueries(['mugshot', mugshotId, 'ratings'])
      })
      .subscribe()
      
    return () => supabase.removeChannel(channel)
  }, [mugshotId, queryClient])
}
```

### 5. Migration Compatibility Layer

**File:** `lib/compatibility/store-bridge.ts`
```typescript
// New file - provides backward compatibility during migration
export function createStoreBridge() {
  // Allows old Zustand patterns to work alongside new Query patterns
  // Components can gradually migrate without breaking
}
```

## Data Models

### Query Keys Structure
```typescript
// Standardized query key patterns
const queryKeys = {
  mugshots: (filters: MugshotFilters, pagination: PaginationOptions) => 
    ['mugshots', filters, pagination],
  mugshot: (id: string) => ['mugshot', id],
  ratings: (mugshotId: string) => ['mugshot', mugshotId, 'ratings'],
  tags: (mugshotId: string) => ['mugshot', mugshotId, 'tags'],
  userProfile: (userId: string) => ['user', userId, 'profile'],
}
```

### State Separation
```typescript
// Clear separation between client and server state
interface ClientState {
  // UI preferences, form state, temporary data
  preferences: UserPreferences
  ui: UIState
  session: SessionData
}

interface ServerState {
  // All data from APIs - managed by TanStack Query
  mugshots: MugshotData[]
  ratings: RatingData
  tags: TagData
  userProfiles: UserProfile[]
}
```

## Error Handling

### Query Error Boundaries
```typescript
// Centralized error handling for all server state
export function QueryErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={<ErrorFallback />}
      onError={(error) => {
        // Log errors, show user-friendly messages
        console.error('Query error:', error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}
```

### Graceful Degradation
```typescript
// Fallback patterns when realtime fails
export function useRealtimeWithFallback(mugshotId: string) {
  const [isRealtimeConnected, setIsRealtimeConnected] = useState(false)
  
  // If realtime fails, fall back to polling
  useEffect(() => {
    if (!isRealtimeConnected) {
      const interval = setInterval(() => {
        queryClient.invalidateQueries(['mugshot', mugshotId])
      }, 30000) // Poll every 30 seconds
      
      return () => clearInterval(interval)
    }
  }, [isRealtimeConnected, mugshotId])
}
```

## Testing Strategy

### Migration Testing
```typescript
// Comprehensive tests to ensure no functionality breaks
describe('State Management Migration', () => {
  test('existing components work identically', () => {
    // Test that MugshotsPageClient behaves exactly the same
  })
  
  test('all user flows remain unchanged', () => {
    // Test rating, tagging, filtering, pagination
  })
  
  test('performance improvements are measurable', () => {
    // Benchmark before/after performance
  })
})
```

### Compatibility Testing
```typescript
// Tests to ensure old and new patterns coexist
describe('Backward Compatibility', () => {
  test('old Zustand stores continue working', () => {
    // Verify existing stores still function
  })
  
  test('mixed usage patterns work', () => {
    // Test components using both old and new patterns
  })
})
```

## Performance Optimizations

### Smart Caching Strategy
```typescript
// Intelligent cache management
const cacheConfig = {
  mugshots: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  },
  ratings: {
    staleTime: 1 * 60 * 1000, // 1 minute (more dynamic)
    cacheTime: 10 * 60 * 1000, // 10 minutes
  }
}
```

### Prefetching Strategy
```typescript
// Proactive data loading
export function usePrefetchStrategy() {
  const queryClient = useQueryClient()
  
  // Prefetch next page on hover
  const prefetchNextPage = useCallback((currentPage: number, filters: MugshotFilters) => {
    queryClient.prefetchQuery({
      queryKey: ['mugshots', filters, { page: currentPage + 1 }],
      queryFn: () => getMugshots(filters, { page: currentPage + 1 }),
    })
  }, [queryClient])
  
  return { prefetchNextPage }
}
```

### Optimistic Updates
```typescript
// Instant UI feedback
export function useOptimisticRating(mugshotId: string) {
  return useMutation({
    mutationFn: submitRating,
    onMutate: async (newRating) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries(['mugshot', mugshotId])
      
      // Snapshot previous value
      const previousRating = queryClient.getQueryData(['mugshot', mugshotId])
      
      // Optimistically update
      queryClient.setQueryData(['mugshot', mugshotId], old => ({
        ...old,
        userRating: newRating,
        averageRating: calculateNewAverage(old.averageRating, old.totalRatings, newRating)
      }))
      
      return { previousRating }
    },
    onError: (err, newRating, context) => {
      // Rollback on error
      queryClient.setQueryData(['mugshot', mugshotId], context.previousRating)
    }
  })
}
```

## SEO and SSR Compatibility

### Enhanced SEO with Better URLs
```typescript
// Generate SEO-friendly slugs for mugshots
export function generateMugshotSlug(mugshot: Mugshot): string {
  const name = `${mugshot.firstName}-${mugshot.lastName}`.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
  
  const location = `${mugshot.countyOfBooking}-${mugshot.stateOfBooking}`.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
  
  return `${name}-${location}-${mugshot.id}`
}

// URL structure: /mugshots/john-doe-miami-dade-fl-12345
```

### Server-Side Query Hydration
```typescript
// Ensure SEO-friendly server rendering with Next.js App Router
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const mugshotId = extractIdFromSlug(params.slug)
  
  // Fetch mugshot data on server for meta tags
  const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/mugshots/${mugshotId}`)
  const data = await response.json()
  
  if (!data.success) {
    return {
      title: 'Mugshot Not Found - America\'s Top Mugshots',
    }
  }
  
  const mugshot = data.data.mugshot
  const ratings = data.data.ratings
  
  return {
    title: `${mugshot.name} - ${mugshot.location} | America's Top Mugshots`,
    description: `Rate ${mugshot.firstName} ${mugshot.lastName}'s mugshot from ${mugshot.location}. Average rating: ${ratings.averageRating}/10 (${ratings.totalRatings} votes)`,
    openGraph: {
      title: `${mugshot.name} - America's Top Mugshot`,
      description: `${mugshot.firstName} from ${mugshot.location} - Rate this mugshot!`,
      images: [
        {
          url: mugshot.imagePath,
          width: 400,
          height: 600,
          alt: `${mugshot.name} mugshot from ${mugshot.location}`,
        }
      ],
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${mugshot.name} - America's Top Mugshot`,
      description: `Rate ${mugshot.firstName}'s mugshot from ${mugshot.location}`,
      images: [mugshot.imagePath],
    },
    alternates: {
      canonical: `/mugshots/${generateMugshotSlug(mugshot)}`,
    }
  }
}
```

### Rich Snippets and Structured Data
```typescript
// JSON-LD structured data for search engines and LLMs
export function generateMugshotStructuredData(mugshot: Mugshot, ratings: RatingData) {
  return {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": mugshot.name,
    "image": mugshot.imagePath,
    "description": `Mugshot of ${mugshot.name} from ${mugshot.location}`,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": mugshot.countyOfBooking,
      "addressRegion": mugshot.stateOfBooking,
      "addressCountry": "US"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": ratings.averageRating,
      "ratingCount": ratings.totalRatings,
      "bestRating": 10,
      "worstRating": 1
    },
    "dateCreated": mugshot.dateOfBooking,
    "url": `${process.env.NEXT_PUBLIC_SITE_URL}/mugshots/${generateMugshotSlug(mugshot)}`
  }
}
```

### Server Component Data Fetching
```typescript
// Server Components for initial data loading
export default async function MugshotPage({ params }: { params: { slug: string } }) {
  const mugshotId = extractIdFromSlug(params.slug)
  
  // Fetch initial data on server
  const mugshotData = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/mugshots/${mugshotId}`)
    .then(res => res.json())
  
  if (!mugshotData.success) {
    notFound()
  }
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateMugshotStructuredData(
            mugshotData.data.mugshot,
            mugshotData.data.ratings
          ))
        }}
      />
      <MugshotDetailClient 
        initialData={mugshotData.data}
        mugshotId={mugshotId}
      />
    </>
  )
}
```

## Migration Plan

### Phase 1: Foundation (Week 1)
1. Install TanStack Query
2. Set up Query Client
3. Create basic query hooks
4. Add compatibility layer

### Phase 2: Server State Migration (Week 2)
1. Migrate mugshots data to Query
2. Migrate ratings to Query mutations
3. Migrate tags to Query mutations
4. Add realtime subscriptions

### Phase 3: Component Migration (Week 3)
1. Update MugshotsPageClient to use Query hooks
2. Update rating components to use mutations
3. Update tag components to use mutations
4. Test all user flows

### Phase 4: Cleanup (Week 4)
1. Remove old server state from Zustand stores
2. Clean up unused code
3. Performance testing and optimization
4. Documentation updates

## Risk Mitigation

### Rollback Strategy
- Feature flags for new vs old patterns
- Gradual rollout with monitoring
- Immediate rollback capability if issues arise

### Testing Coverage
- 100% test coverage for existing functionality
- Performance benchmarks before/after
- User acceptance testing for all flows

### Monitoring
- Performance metrics tracking
- Error rate monitoring
- User experience analytics#
# Visual Feedback and Loading States

### Enhanced Loading States
```typescript
// Comprehensive loading state management
export function useLoadingStates() {
  return {
    // Button loading states with spinners
    ButtonWithLoading: ({ isLoading, children, ...props }) => (
      <button {...props} disabled={isLoading}>
        {isLoading ? (
          <div className="flex items-center gap-2">
            <Spinner size="sm" />
            <span>Loading...</span>
          </div>
        ) : children}
      </button>
    ),
    
    // Skeleton components for different sections
    MugshotCardSkeleton: () => (
      <div className="animate-pulse">
        <div className="bg-gray-300 aspect-[3/4] rounded-lg mb-4"></div>
        <div className="bg-gray-300 h-4 rounded mb-2"></div>
        <div className="bg-gray-300 h-3 rounded w-3/4"></div>
      </div>
    ),
    
    MugshotDetailSkeleton: () => (
      <div className="animate-pulse">
        <div className="bg-gray-300 aspect-[3/4] rounded-lg mb-6"></div>
        <div className="bg-gray-300 h-6 rounded mb-4"></div>
        <div className="bg-gray-300 h-4 rounded mb-2"></div>
        <div className="bg-gray-300 h-4 rounded w-2/3"></div>
      </div>
    ),
    
    // Page-level skeleton
    MugshotsPageSkeleton: () => (
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {Array.from({ length: 12 }).map((_, i) => (
          <MugshotCardSkeleton key={i} />
        ))}
      </div>
    )
  }
}
```

### Optimistic UI Feedback
```typescript
// Immediate visual feedback for user interactions
export function useOptimisticFeedback() {
  return {
    // Rating button with immediate feedback
    RatingButton: ({ rating, onRate, currentUserRating, isSubmitting }) => {
      const [optimisticRating, setOptimisticRating] = useState(null)
      
      const handleClick = () => {
        setOptimisticRating(rating)
        onRate(rating)
      }
      
      const isActive = optimisticRating === rating || currentUserRating === rating
      
      return (
        <button
          onClick={handleClick}
          disabled={isSubmitting}
          className={`
            transition-all duration-200 
            ${isActive ? 'bg-pink-500 text-white' : 'bg-gray-200 hover:bg-gray-300'}
            ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          {isSubmitting && optimisticRating === rating ? (
            <Spinner size="xs" />
          ) : (
            rating
          )}
        </button>
      )
    },
    
    // Tag button with immediate feedback
    TagButton: ({ tagType, onToggle, isActive, count, isSubmitting }) => {
      const [optimisticActive, setOptimisticActive] = useState(null)
      const [optimisticCount, setOptimisticCount] = useState(count)
      
      const handleClick = () => {
        const newActive = !isActive
        setOptimisticActive(newActive)
        setOptimisticCount(prev => newActive ? prev + 1 : prev - 1)
        onToggle(tagType)
      }
      
      const displayActive = optimisticActive !== null ? optimisticActive : isActive
      const displayCount = optimisticCount
      
      return (
        <button
          onClick={handleClick}
          disabled={isSubmitting}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-200
            ${displayActive ? 'bg-blue-500 text-white' : 'bg-gray-200 hover:bg-gray-300'}
            ${isSubmitting ? 'opacity-50' : ''}
          `}
        >
          {isSubmitting ? <Spinner size="xs" /> : <TagIcon type={tagType} />}
          <span>{tagType}</span>
          <span className="bg-white/20 px-2 py-1 rounded-full text-xs">
            {displayCount}
          </span>
        </button>
      )
    }
  }
}
```

### Navigation Loading States
```typescript
// Handle Next.js navigation delays with visual feedback
export function useNavigationFeedback() {
  const [isNavigating, setIsNavigating] = useState(false)
  
  useEffect(() => {
    const handleStart = () => setIsNavigating(true)
    const handleComplete = () => setIsNavigating(false)
    
    Router.events.on('routeChangeStart', handleStart)
    Router.events.on('routeChangeComplete', handleComplete)
    Router.events.on('routeChangeError', handleComplete)
    
    return () => {
      Router.events.off('routeChangeStart', handleStart)
      Router.events.off('routeChangeComplete', handleComplete)
      Router.events.off('routeChangeError', handleComplete)
    }
  }, [])
  
  return {
    isNavigating,
    NavigationLoader: () => isNavigating ? (
      <div className="fixed top-0 left-0 right-0 z-50">
        <div className="h-1 bg-pink-500 animate-pulse"></div>
      </div>
    ) : null
  }
}
```

## Existing Functionality Preservation

### Component Interface Compatibility
```typescript
// Ensure existing components receive identical props
interface MugshotsPageClientProps {
  // Keep all existing props exactly the same
  initialSearchTerm?: string
  initialSelectedState?: string
  initialSelectedCounty?: string
  // ... all other existing props
}

// New implementation maintains identical interface
export default function MugshotsPageClient(props: MugshotsPageClientProps) {
  // Internal implementation uses TanStack Query
  // But external interface remains identical
  const { data, isLoading, error } = useMugshotsQuery(filters, pagination)
  
  // Transform data to match existing format exactly
  const mugshots = data?.mugshots || []
  const totalCount = data?.pagination?.totalCount || 0
  
  // Rest of component logic remains identical
  // Only the data fetching mechanism changes
}
```

### Backward Compatibility Bridge
```typescript
// Bridge to allow gradual migration
export function createCompatibilityBridge() {
  // Allow old Zustand patterns to work during transition
  const legacyRatingStore = useRatingStore()
  const newRatingQuery = useRatingQuery()
  
  return {
    // Provide data from new source but with old interface
    getRating: (mugshotId: string) => {
      // Try new system first, fallback to old
      return newRatingQuery.data?.userRating ?? legacyRatingStore.userRatings[mugshotId]
    },
    
    // Maintain old method signatures
    setRating: (mugshotId: string, rating: number) => {
      // Use new mutation but maintain old interface
      ratingMutation.mutate({ mugshotId, rating })
    }
  }
}
```