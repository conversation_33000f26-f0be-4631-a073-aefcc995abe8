'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { createClient } from '@/lib/supabase/client'

interface AuthProviderProps {
  children: React.ReactNode
}

export default function AuthProvider({ children }: AuthProviderProps) {
  const { setUser, setProfile, clearAuth, setError } = useAuthStore()

  useEffect(() => {
    const supabase = createClient()
    let mounted = true

    // SIMPLE: Just listen for Supabase auth events, don't try to be clever
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return
        
        if (process.env.NODE_ENV === 'development') {
          console.log('🔐 Auth event:', event, 'Session:', !!session?.user)
        }
        
        if (event === 'SIGNED_IN' && session?.user) {
          // User signed in - set basic auth state
          setUser(session.user)
          
          // Let the profile load happen via TanStack Query later
          // Don't try to load profile here to avoid complexity
          
        } else if (event === 'SIGNED_OUT') {
          // User signed out - clear everything
          clearAuth()
          
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          // Token refreshed - just update user
          setUser(session.user)
        }
      }
    )

    // Get initial session - simple check
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!mounted) return
      
      if (session?.user) {
        setUser(session.user)
      } else {
        clearAuth()
      }
    })

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [setUser, setProfile, clearAuth, setError])

  return <>{children}</>
} 