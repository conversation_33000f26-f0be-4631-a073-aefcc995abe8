'use client'

import { QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import { QueryDevtools } from '@/lib/query/devtools'
import { queryClient } from '@/lib/query/query-client'

interface QueryProviderProps {
  children: ReactNode
}

export function QueryProvider({ children }: QueryProviderProps) {
  // Use centralized query client configuration

  return (
    <QueryClientProvider client={queryClient}>
      <QueryDevtools>
        {children}
      </QueryDevtools>
    </QueryClientProvider>
  )
} 