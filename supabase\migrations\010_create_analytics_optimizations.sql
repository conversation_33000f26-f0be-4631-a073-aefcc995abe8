-- =====================================================
-- MIGRATION 010: Analytics Optimizations
-- =====================================================
-- Purpose: Add indices and materialized views for API analytics endpoints
-- Created for: Story 10.1 - Complete API Conversion from Server Actions
-- Date: 2024-01-XX

-- =====================================================
-- FIX: Add physical column to support IMMUTABLE-safe date indexing
-- =====================================================

ALTER TABLE public.ratings
ADD COLUMN IF NOT EXISTS created_at_date DATE;

UPDATE public.ratings
SET created_at_date = created_at::date
WHERE created_at_date IS NULL;

-- =====================================================
-- TRIGGER TO KEEP created_at_date IN SYNC
-- =====================================================

CREATE OR REPLACE FUNCTION public.sync_created_at_date()
RETURNS TRIGGER AS $$
BEGIN
  NEW.created_at_date := NEW.created_at::date;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_sync_created_at_date ON public.ratings;

CREATE TRIGGER trg_sync_created_at_date
BEFORE INSERT OR UPDATE ON public.ratings
FOR EACH ROW
EXECUTE FUNCTION public.sync_created_at_date();

-- =====================================================
-- PERFORMANCE INDICES
-- =====================================================

-- For daily rating analytics (/api/ratings/daily/[mugshotId])
CREATE INDEX IF NOT EXISTS idx_ratings_mugshot_date 
ON public.ratings(mugshot_id, created_at_date);

-- For tag count sorting in filters (mugshot filtering by tag counts)
CREATE INDEX IF NOT EXISTS idx_tags_mugshot_type_created 
ON public.tags(mugshot_id, tag_type, created_at);

-- For popular tags by location (/api/tags/popular with location filter)
CREATE INDEX IF NOT EXISTS idx_mugshots_location 
ON public.mugshots("stateOfBooking", "countyOfBooking");

-- For trending analysis (time-based tag queries)
CREATE INDEX IF NOT EXISTS idx_tags_type_created 
ON public.tags(tag_type, created_at);

-- For user activity queries (/api/ratings/user/[userId], /api/tags/user/[userId])
CREATE INDEX IF NOT EXISTS idx_ratings_user_created 
ON public.ratings(user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_tags_user_created 
ON public.tags(user_id, created_at DESC);

-- For overall platform statistics (/api/ratings/overall)
CREATE INDEX IF NOT EXISTS idx_ratings_rating_created 
ON public.ratings(rating, created_at DESC);

-- For efficient tag counting in bulk operations
CREATE INDEX IF NOT EXISTS idx_tags_mugshot_type_count 
ON public.tags(mugshot_id, tag_type);

-- For efficient rating statistics in bulk operations  
CREATE INDEX IF NOT EXISTS idx_ratings_mugshot_rating 
ON public.ratings(mugshot_id, rating);

-- =====================================================
-- MATERIALIZED VIEWS FOR ANALYTICS
-- =====================================================

-- Daily rating aggregates
CREATE MATERIALIZED VIEW IF NOT EXISTS public.daily_rating_stats AS
SELECT 
    mugshot_id,
    created_at_date AS rating_date,
    ROUND(AVG(rating)::numeric, 2) AS average_rating,
    COUNT(*)::integer AS total_ratings,
    MIN(rating)::integer AS min_rating,
    MAX(rating)::integer AS max_rating
FROM public.ratings 
GROUP BY mugshot_id, created_at_date
ORDER BY mugshot_id, rating_date;

CREATE UNIQUE INDEX IF NOT EXISTS idx_daily_rating_stats_unique
ON public.daily_rating_stats(mugshot_id, rating_date);

-- Popular tags with location data
CREATE MATERIALIZED VIEW IF NOT EXISTS public.popular_tags_by_location AS
SELECT 
    t.tag_type,
    m."stateOfBooking" AS state,
    m."countyOfBooking" AS county,
    COUNT(*)::integer AS usage_count,
    COUNT(DISTINCT t.user_id)::integer AS unique_users,
    MIN(t.created_at) AS first_used,
    MAX(t.created_at) AS last_used
FROM public.tags t
JOIN public.mugshots m ON t.mugshot_id = m.id
GROUP BY t.tag_type, m."stateOfBooking", m."countyOfBooking"
ORDER BY usage_count DESC;

CREATE INDEX IF NOT EXISTS idx_popular_tags_location_type
ON public.popular_tags_by_location(state, county, tag_type);

CREATE INDEX IF NOT EXISTS idx_popular_tags_usage_count
ON public.popular_tags_by_location(usage_count DESC);

-- Global popular tags
CREATE MATERIALIZED VIEW IF NOT EXISTS public.popular_tags_global AS
SELECT 
    tag_type,
    COUNT(*)::integer AS total_usage,
    COUNT(DISTINCT user_id)::integer AS unique_users,
    COUNT(DISTINCT mugshot_id)::integer AS unique_mugshots,
    MIN(created_at) AS first_used,
    MAX(created_at) AS last_used,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END)::integer AS recent_week_usage,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' AND created_at < NOW() - INTERVAL '7 days' THEN 1 END)::integer AS previous_week_usage
FROM public.tags
GROUP BY tag_type
ORDER BY total_usage DESC;

-- Trending tags
-- Trending tags
CREATE MATERIALIZED VIEW IF NOT EXISTS public.trending_tags AS
SELECT 
    tag_type,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END)::integer AS current_week_usage,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' 
               AND created_at < NOW() - INTERVAL '7 days' THEN 1 END)::integer AS previous_week_usage,
    CASE 
        WHEN COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' 
                        AND created_at < NOW() - INTERVAL '7 days' THEN 1 END) = 0 THEN 
            CASE WHEN COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) > 0 
                 THEN 100.0 ELSE 0.0 END
        ELSE
            ROUND((
                (
                    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END)::float - 
                    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' 
                               AND created_at < NOW() - INTERVAL '7 days' THEN 1 END)::float
                ) / 
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' 
                           AND created_at < NOW() - INTERVAL '7 days' THEN 1 END)::float
            )::numeric, 2)
    END AS growth_rate,
    CASE 
        WHEN COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) > 
             COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' 
                        AND created_at < NOW() - INTERVAL '7 days' THEN 1 END) THEN 'up'
        WHEN COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) < 
             COUNT(CASE WHEN created_at >= NOW() - INTERVAL '14 days' 
                        AND created_at < NOW() - INTERVAL '7 days' THEN 1 END) THEN 'down'
        ELSE 'stable'
    END AS trend_direction
FROM public.tags
WHERE created_at >= NOW() - INTERVAL '14 days'
GROUP BY tag_type
ORDER BY growth_rate DESC;


-- Platform statistics summary
CREATE MATERIALIZED VIEW IF NOT EXISTS public.platform_rating_stats AS
SELECT 
    COUNT(*)::integer AS total_ratings,
    ROUND(AVG(rating)::numeric, 2) AS average_rating,
    COUNT(DISTINCT mugshot_id)::integer AS rated_mugshots,
    COUNT(DISTINCT user_id)::integer AS rating_users,
    MIN(rating)::integer AS min_rating,
    MAX(rating)::integer AS max_rating,
    ROUND(STDDEV(rating)::numeric, 2) AS rating_stddev,
    COUNT(CASE WHEN rating >= 9 THEN 1 END)::integer AS excellent_ratings,
    COUNT(CASE WHEN rating >= 7 AND rating < 9 THEN 1 END)::integer AS good_ratings,
    COUNT(CASE WHEN rating >= 5 AND rating < 7 THEN 1 END)::integer AS average_ratings,
    COUNT(CASE WHEN rating < 5 THEN 1 END)::integer AS poor_ratings,
    MIN(created_at) AS first_rating_date,
    MAX(created_at) AS last_rating_date
FROM public.ratings;

-- =====================================================
-- FUNCTIONS FOR MATERIALIZED VIEW REFRESH
-- =====================================================

CREATE OR REPLACE FUNCTION public.refresh_analytics_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.daily_rating_stats;
    REFRESH MATERIALIZED VIEW public.popular_tags_by_location;
    REFRESH MATERIALIZED VIEW public.popular_tags_global;
    REFRESH MATERIALIZED VIEW public.trending_tags;
    REFRESH MATERIALIZED VIEW public.platform_rating_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.refresh_mugshot_analytics(p_mugshot_id BIGINT)
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.daily_rating_stats;
    REFRESH MATERIALIZED VIEW public.popular_tags_by_location;
    REFRESH MATERIALIZED VIEW public.popular_tags_global;
    REFRESH MATERIALIZED VIEW public.trending_tags;
    REFRESH MATERIALIZED VIEW public.platform_rating_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

GRANT SELECT ON public.daily_rating_stats TO authenticated;
GRANT SELECT ON public.popular_tags_by_location TO authenticated;
GRANT SELECT ON public.popular_tags_global TO authenticated;
GRANT SELECT ON public.trending_tags TO authenticated;
GRANT SELECT ON public.platform_rating_stats TO authenticated;

GRANT SELECT ON public.daily_rating_stats TO anon;
GRANT SELECT ON public.popular_tags_by_location TO anon;
GRANT SELECT ON public.popular_tags_global TO anon;
GRANT SELECT ON public.trending_tags TO anon;
GRANT SELECT ON public.platform_rating_stats TO anon;

GRANT EXECUTE ON FUNCTION public.refresh_analytics_views() TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_mugshot_analytics(BIGINT) TO authenticated;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON MATERIALIZED VIEW public.daily_rating_stats IS 
'Daily rating aggregates by mugshot - only includes days with actual ratings';

COMMENT ON MATERIALIZED VIEW public.popular_tags_by_location IS 
'Tag popularity statistics broken down by location (state/county)';

COMMENT ON MATERIALIZED VIEW public.popular_tags_global IS 
'Global tag popularity statistics with trend indicators';

COMMENT ON MATERIALIZED VIEW public.trending_tags IS 
'Weekly trending analysis for tags showing growth/decline patterns';

COMMENT ON MATERIALIZED VIEW public.platform_rating_stats IS 
'Overall platform rating statistics and distribution metrics';

COMMENT ON FUNCTION public.refresh_analytics_views() IS 
'Refreshes all analytics materialized views - should be run periodically';

COMMENT ON FUNCTION public.refresh_mugshot_analytics(BIGINT) IS 
'Refreshes analytics views after mugshot-related changes';
