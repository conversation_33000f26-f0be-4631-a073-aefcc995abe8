#!/bin/bash

# Artillery Load Testing Script for Mugshots API
# This script provides easy execution of various load test scenarios

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:3000"
RESULTS_DIR="load-tests/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Ensure results directory exists
mkdir -p "$RESULTS_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    print_status "Checking if server is running at $BASE_URL..."
    if curl -s -f "$BASE_URL/api/mugshots?page=1&perPage=1" > /dev/null; then
        print_success "Server is running and API is accessible"
        return 0
    else
        print_error "Server is not running or API is not accessible"
        print_error "Please start your Next.js development server with: npm run dev"
        return 1
    fi
}

# Function to run a specific test scenario
run_test() {
    local test_name="$1"
    local config_file="$2"
    local output_file="$RESULTS_DIR/${test_name}_${TIMESTAMP}.json"
    local report_file="$RESULTS_DIR/${test_name}_${TIMESTAMP}.html"
    
    print_status "Running $test_name..."
    print_status "Config: $config_file"
    print_status "Output: $output_file"
    
    # Run the test
    if artillery run "$config_file" --output "$output_file"; then
        print_success "$test_name completed successfully"
        
        # Generate HTML report
        print_status "Generating HTML report..."
        if artillery report "$output_file" --output "$report_file"; then
            print_success "HTML report generated: $report_file"
        else
            print_warning "Failed to generate HTML report"
        fi
        
        # Display quick summary
        print_status "Quick Summary:"
        echo "----------------------------------------"
        node -e "
        const fs = require('fs');
        const data = JSON.parse(fs.readFileSync('$output_file', 'utf8'));
        const summary = data.aggregate;
        console.log('Total Requests:', summary.counters['http.requests'] || 0);
        console.log('Success Rate:', ((summary.counters['http.codes.200'] || 0) / (summary.counters['http.requests'] || 1) * 100).toFixed(2) + '%');
        console.log('Average Response Time:', (summary.summaries['http.response_time']?.mean || 0).toFixed(2) + 'ms');
        console.log('95th Percentile:', (summary.summaries['http.response_time']?.p95 || 0).toFixed(2) + 'ms');
        console.log('99th Percentile:', (summary.summaries['http.response_time']?.p99 || 0).toFixed(2) + 'ms');
        console.log('Errors:', (summary.counters['errors.total'] || 0));
        "
        echo "----------------------------------------"
        
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Function to display help
show_help() {
    echo "Artillery Load Testing Script for Mugshots API"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  full          Run the complete load test suite (default)"
    echo "  mugshots      Run only the mugshots API load test"
    echo "  details       Run only the mugshot details API load test"
    echo "  quick         Run a quick smoke test"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 full       # Run all tests"
    echo "  $0 mugshots   # Test only the main mugshots API"
    echo "  $0 quick      # Quick 2-minute test"
    echo ""
}

# Main execution logic
main() {
    local test_type="${1:-full}"
    
    case "$test_type" in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "quick")
            print_status "Starting quick smoke test..."
            if ! check_server; then
                exit 1
            fi
            
            print_status "Running 2-minute quick test with 50 concurrent users..."
            artillery quick --count 100 --num 50 "$BASE_URL/api/mugshots?page=1&perPage=12"
            ;;
        "mugshots")
            print_status "Starting mugshots API load test..."
            if ! check_server; then
                exit 1
            fi
            
            run_test "mugshots_api" "load-tests/scenarios/mugshots-api-load-test.yml"
            ;;
        "details")
            print_status "Starting mugshot details API load test..."
            if ! check_server; then
                exit 1
            fi
            
            run_test "mugshot_details" "load-tests/scenarios/mugshot-details-load-test.yml"
            ;;
        "full")
            print_status "Starting complete load test suite..."
            if ! check_server; then
                exit 1
            fi
            
            print_status "This will run comprehensive load tests simulating 10,000 concurrent users"
            print_warning "The full test suite will take approximately 15-20 minutes to complete"
            read -p "Do you want to continue? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_status "Test cancelled by user"
                exit 0
            fi
            
            # Run the main comprehensive test
            run_test "comprehensive" "load-tests/artillery.yml"
            ;;
        *)
            print_error "Unknown option: $test_type"
            show_help
            exit 1
            ;;
    esac
    
    print_success "Load testing completed!"
    print_status "Results are saved in: $RESULTS_DIR"
}

# Execute main function with all arguments
main "$@"
