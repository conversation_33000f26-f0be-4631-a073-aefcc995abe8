import { describe, it, expect, vi, beforeEach, MockedFunction } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { useMugshotsData, useMugshotsPage } from '@/lib/hooks/useMugshotsData'
import { mugshotsService } from '@/lib/services/mugshots-service'

// Mock the service
vi.mock('@/lib/services/mugshots-service', () => ({
  mugshotsService: {
    getMugshots: vi.fn(),
    getMugshotCount: vi.fn(),
    healthCheck: vi.fn()
  }
}))

// Mock the transform utilities
vi.mock('@/lib/utils/mugshot-transforms', () => ({
  transformDBMugshotsToUI: vi.fn((data) => data.map((item: any) => ({
    ...item,
    name: `${item.firstName} ${item.lastName}`.trim(),
    location: `${item.countyOfBooking}, ${item.stateOfBooking}`,
    arrestDate: item.dateOfBooking,
    offenses: item.offenseDescription ? [item.offenseDescription] : [],
    rating: 0,
    votes: 0,
    views: 0,
    category: 'Hot',
    image: item.imagePath || '/images/mugshot-placeholder.png',
    state: item.stateOfBooking || '',
    county: item.countyOfBooking || '',
    birthDate: ''
  })))
}))

const mockDBMugshots = [
  {
    id: 1,
    created_at: '2024-01-01T00:00:00Z',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBooking: '2024-01-01',
    stateOfBooking: 'California',
    countyOfBooking: 'Los Angeles',
    offenseDescription: 'Public intoxication',
    additionalDetails: null,
    imagePath: '/images/mugshot-1.jpg'
  },
  {
    id: 2,
    created_at: '2024-01-02T00:00:00Z',
    firstName: 'Jane',
    lastName: 'Smith',
    dateOfBooking: '2024-01-02',
    stateOfBooking: 'Texas',
    countyOfBooking: 'Harris',
    offenseDescription: 'DUI',
    additionalDetails: null,
    imagePath: '/images/mugshot-2.jpg'
  }
]

describe('useMugshotsData', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(mugshotsService.healthCheck as MockedFunction<any>).mockResolvedValue(true)
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)
    ;(mugshotsService.getMugshotCount as MockedFunction<any>).mockResolvedValue(100)
  })

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useMugshotsData())

    expect(result.current.loading).toBe(true)
    expect(result.current.mugshots).toEqual([])
    expect(result.current.totalCount).toBe(0)
    expect(result.current.error).toBeNull()
    expect(result.current.isHealthy).toBe(false)
  })

  it('should fetch mugshots on mount', async () => {
    const { result } = renderHook(() => useMugshotsData())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(mugshotsService.healthCheck).toHaveBeenCalled()
    expect(mugshotsService.getMugshots).toHaveBeenCalled()
    expect(mugshotsService.getMugshotCount).toHaveBeenCalled()
    expect(result.current.mugshots).toHaveLength(2)
    expect(result.current.totalCount).toBe(100)
    expect(result.current.isHealthy).toBe(true)
    expect(result.current.error).toBeNull()
  })

  it('should handle health check failure', async () => {
    ;(mugshotsService.healthCheck as MockedFunction<any>).mockResolvedValue(false)

    const { result } = renderHook(() => useMugshotsData())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe('Database connection failed. Please check your configuration.')
    expect(result.current.isHealthy).toBe(false)
    expect(result.current.mugshots).toEqual([])
  })

  it('should handle service errors gracefully', async () => {
    const errorMessage = 'Service unavailable'
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockRejectedValue(new Error(errorMessage))

    const { result } = renderHook(() => useMugshotsData())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe(errorMessage)
    expect(result.current.mugshots).toEqual([])
    expect(result.current.totalCount).toBe(0)
    expect(result.current.isHealthy).toBe(false)
  })

  it('should allow manual data fetching with parameters', async () => {
    const { result } = renderHook(() => useMugshotsData())

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const filters = { searchTerm: 'john' }
    const sort = { sortBy: 'top-rated' as const }
    const pagination = { page: 2, perPage: 24 }

    await act(async () => {
      await result.current.fetchMugshots(filters, sort, pagination)
    })

    expect(mugshotsService.getMugshots).toHaveBeenCalledWith(filters, sort, pagination)
    expect(mugshotsService.getMugshotCount).toHaveBeenCalledWith(filters)
  })

  it('should provide refresh functionality', async () => {
    const { result } = renderHook(() => useMugshotsData())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    vi.clearAllMocks()

    await act(async () => {
      await result.current.refresh()
    })

    expect(mugshotsService.getMugshots).toHaveBeenCalled()
    expect(mugshotsService.getMugshotCount).toHaveBeenCalled()
  })

  it('should clear errors', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockRejectedValue(new Error('Test error'))

    const { result } = renderHook(() => useMugshotsData())

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBeNull()
  })
})

describe('useMugshotsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(mugshotsService.healthCheck as MockedFunction<any>).mockResolvedValue(true)
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)
    ;(mugshotsService.getMugshotCount as MockedFunction<any>).mockResolvedValue(100)
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useMugshotsPage())

    expect(result.current.filters).toEqual({})
    expect(result.current.sort).toEqual({ sortBy: 'newest' })
    expect(result.current.pagination).toEqual({ page: 1, perPage: 12 })
  })

  it('should calculate pagination properties correctly', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.totalPages).toBe(9) // Math.ceil(100/12)
    expect(result.current.hasNextPage).toBe(true)
    expect(result.current.hasPreviousPage).toBe(false)
    expect(result.current.isEmpty).toBe(false)
  })

  it('should update filters and reset to first page', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Go to page 2 first
    act(() => {
      result.current.goToPage(2)
    })

    expect(result.current.pagination.page).toBe(2)

    // Update filters should reset to page 1
    act(() => {
      result.current.updateFilters({ searchTerm: 'john' })
    })

    expect(result.current.filters.searchTerm).toBe('john')
    expect(result.current.pagination.page).toBe(1)
  })

  it('should update sort and reset to first page', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Go to page 2 first
    act(() => {
      result.current.goToPage(2)
    })

    expect(result.current.pagination.page).toBe(2)

    // Update sort should reset to page 1
    act(() => {
      result.current.updateSort({ sortBy: 'top-rated' })
    })

    expect(result.current.sort.sortBy).toBe('top-rated')
    expect(result.current.pagination.page).toBe(1)
  })

  it('should update pagination without affecting other settings', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    act(() => {
      result.current.updatePagination({ perPage: 24 })
    })

    expect(result.current.pagination.perPage).toBe(24)
    expect(result.current.pagination.page).toBe(1) // Should stay the same
  })

  it('should clear all filters and settings', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Set some values
    act(() => {
      result.current.updateFilters({ searchTerm: 'john', state: 'California' })
      result.current.updateSort({ sortBy: 'top-rated' })
      result.current.updatePagination({ page: 3, perPage: 24 })
    })

    // Clear everything
    act(() => {
      result.current.clearFilters()
    })

    expect(result.current.filters).toEqual({})
    expect(result.current.sort).toEqual({ sortBy: 'newest' })
    expect(result.current.pagination).toEqual({ page: 1, perPage: 12 })
  })

  it('should navigate to specific pages', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    act(() => {
      result.current.goToPage(5)
    })

    expect(result.current.pagination.page).toBe(5)
  })

  it('should detect empty state correctly', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue([])
    ;(mugshotsService.getMugshotCount as MockedFunction<any>).mockResolvedValue(0)

    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.isEmpty).toBe(true)
    expect(result.current.mugshots).toHaveLength(0)
    expect(result.current.totalCount).toBe(0)
  })

  it('should refetch data when filters, sort, or pagination change', async () => {
    const { result } = renderHook(() => useMugshotsPage())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Clear previous calls
    vi.clearAllMocks()

    // Update filters
    act(() => {
      result.current.updateFilters({ searchTerm: 'test' })
    })

    await waitFor(() => {
      expect(mugshotsService.getMugshots).toHaveBeenCalledWith(
        expect.objectContaining({ searchTerm: 'test' }),
        expect.any(Object),
        expect.any(Object)
      )
    })
  })
}) 