"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import UniversalNavLink from "@/components/UniversalNavLink"
import SimpleMugshotRating from "./SimpleMugshotRating"
import MugshotRatingTimeGate from "./MugshotRatingTimeGate"
import { type RatingStatistics } from "@/lib/services/rating-service"
import { loadMugshotWithStoreUpdate } from "@/lib/services/mugshot-store-service"
import { shouldShowTimeGate } from "@/lib/utils/rating-timegate-utils"

type Mugshot = {
  id: number
  name: string
  location: string
  rating: number
  image: string
  arrestDate: string
  birthDate: string
  offenses: string[]
  additionalDetails?: Record<string, string>
}

type MugshotModalProps = {
  mugshot: Mugshot
  isOpen: boolean
  onClose: () => void
  preloadedRatingStats?: RatingStatistics
}

// Helper function to get zodiac sign
function getZodiacSign(date: string): string {
  const birthDate = new Date(date)
  const month = birthDate.getMonth() + 1
  const day = birthDate.getDate()

  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return "Aquarius"
  if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) return "Pisces"
  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return "Aries"
  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return "Taurus"
  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return "Gemini"
  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return "Cancer"
  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return "Leo"
  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return "Virgo"
  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return "Libra"
  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return "Scorpio"
  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return "Sagittarius"
  return "Capricorn"
}

// Helper function to get zodiac sign emoji
function getZodiacEmoji(sign: string): string {
  const zodiacEmojis: Record<string, string> = {
    Aquarius: "♒",
    Pisces: "♓",
    Aries: "♈",
    Taurus: "♉",
    Gemini: "♊",
    Cancer: "♋",
    Leo: "♌",
    Virgo: "♍",
    Libra: "♎",
    Scorpio: "♏",
    Sagittarius: "♐",
    Capricorn: "♑",
  }
  return zodiacEmojis[sign] || ""
}

export default function MugshotModal({ mugshot, isOpen, onClose, preloadedRatingStats }: MugshotModalProps) {
  const [tagsLoaded, setTagsLoaded] = useState(false)
  const [showTimeGate, setShowTimeGate] = useState(false)

  // Load complete mugshot data when modal opens
  useEffect(() => {
    if (isOpen && !tagsLoaded) {
      const loadCompleteData = async () => {
        try {
          // Load complete mugshot data and update stores
          await loadMugshotWithStoreUpdate(mugshot.id)
          
          setTagsLoaded(true)
        } catch (error) {
          console.error('Error loading mugshot data:', error)
          setTagsLoaded(true)
        }
      }
      loadCompleteData()
    }
  }, [isOpen, mugshot.id, tagsLoaded])

  // Reset tags when modal closes
  useEffect(() => {
    if (!isOpen) {
      setTagsLoaded(false)
    }
  }, [isOpen])

  // Parse location
  const locationParts = mugshot.location.split(', ')
  const state = locationParts[locationParts.length - 1] || ''
  const county = locationParts[locationParts.length - 2] || ''

  // Determine which component to show based on time gate logic
  useEffect(() => {
    const mugshotData = {
      id: mugshot.id,
      dateOfBooking: mugshot.arrestDate,
      stateOfBooking: state,
      countyOfBooking: county
    }
    setShowTimeGate(shouldShowTimeGate(mugshotData))
  }, [mugshot.id, mugshot.arrestDate, state, county])

  // Callback when rating becomes enabled
  const handleRatingEnabled = () => {
    setShowTimeGate(false)
  }

  // Zodiac sign calculation
  const zodiacSign = getZodiacSign(mugshot.birthDate)
  const zodiacEmoji = getZodiacEmoji(zodiacSign)

  // Date validation and formatting
  const isValidArrestDate = mugshot.arrestDate && !isNaN(new Date(mugshot.arrestDate).getTime())
  const isValidBirthDate = mugshot.birthDate && !isNaN(new Date(mugshot.birthDate).getTime())

  const formattedArrestDate = isValidArrestDate
    ? new Date(mugshot.arrestDate).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "N/A"

  const formattedBirthDate = isValidBirthDate
    ? new Date(mugshot.birthDate).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "N/A"

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] sm:max-w-[600px] max-h-[95vh] bg-gray-900 border border-pink-500/30 text-white p-0 overflow-hidden">
        <DialogHeader className="bg-gradient-to-r from-purple-900 to-pink-900 p-4">
          <DialogTitle className="text-xl sm:text-2xl font-bold tracking-tight">Mugshot Details</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 p-4 sm:p-6 max-h-[calc(95vh-80px)] overflow-y-auto">
          {/* Left column - Image */}
          <div className="flex flex-col items-center">
            <div className="relative overflow-hidden rounded-lg border-2 border-pink-500 bg-gray-800 shadow-glow w-full max-w-[280px]">
              <Image
                src={mugshot.image || "/images/mugshot-placeholder.svg"}
                alt={`${mugshot.name} mugshot`}
                width={240}
                height={320}
                className="object-cover w-full h-auto"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-black/80 px-3 py-2">
                <div className="text-base font-bold text-white">{mugshot.name}</div>
              </div>
            </div>

            {/* Rating section - conditional rendering based on time gate */}
            <div className="mt-4 sm:mt-6 w-full">
              {showTimeGate ? (
                <MugshotRatingTimeGate 
                  mugshotId={mugshot.id.toString()}
                  dateOfBooking={mugshot.arrestDate}
                  state={state}
                  county={county}
                  onRatingEnabled={handleRatingEnabled}
                />
              ) : (
                <SimpleMugshotRating 
                  mugshotId={mugshot.id.toString()} 
                  preloadedStats={preloadedRatingStats}
                />
              )}
            </div>


          </div>

          {/* Right column - Details */}
          <div className="space-y-4 sm:space-y-5">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-white">{mugshot.name}</h2>
              <p className="text-cyan-400">
                {county}, {state}
              </p>
            </div>

            <div className="space-y-3">
              <div className="bg-gray-800/50 rounded-lg p-3 border border-pink-500/20">
                <h3 className="text-sm uppercase text-gray-400 mb-1">Last Arrest</h3>
                <p className="text-white font-medium">{formattedArrestDate}</p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-3 border border-pink-500/20">
                <h3 className="text-sm uppercase text-gray-400 mb-1">Birth Date</h3>
                <p className="text-white font-medium">
                  {formattedBirthDate}
                  {isValidBirthDate && zodiacEmoji && (
                    <span className="ml-2 text-pink-400">
                      {zodiacEmoji} {zodiacSign}
                    </span>
                  )}
                </p>
              </div>

              <div className="bg-gray-800/50 rounded-lg p-3 border border-pink-500/20">
                <h3 className="text-sm uppercase text-gray-400 mb-1">Offenses</h3>
                <div className="max-h-24 overflow-y-auto pr-2 -mr-2">
                  <ul className="list-disc list-inside space-y-1">
                    {mugshot.offenses.map((offense, index) => (
                      <li key={index} className="text-white text-sm leading-relaxed">
                        {offense}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {mugshot.additionalDetails && (
                <div className="bg-gray-800/50 rounded-lg p-3 border border-pink-500/20">
                  <h3 className="text-sm uppercase text-gray-400 mb-2">Additional Information</h3>
                  {Object.entries(mugshot.additionalDetails).filter(([, value]) => 
                    value && value.trim() !== '' && value !== 'N/A'
                  ).map(([label, value], index) => (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <span className="text-gray-300 capitalize">{label}:</span>
                      <span className="text-white font-medium">{value}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="pt-4">
              <UniversalNavLink href={`/mugshots/${mugshot.id}`}>
                <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                  View Full Record
                </Button>
              </UniversalNavLink>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
