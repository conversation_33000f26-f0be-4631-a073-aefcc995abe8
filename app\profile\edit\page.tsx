"use client"

import { useState, useEffect, Suspense } from "react"
import { useRout<PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import LocationDropdown from "@/components/LocationDropdown"
import { ArrowLeft, Save, User, MapPin } from "lucide-react"

function EditProfileContent() {
  const router = useRouter()
  
  const [user, setUser] = useState<{
    id: string
    email?: string
    user_metadata?: Record<string, unknown>
    app_metadata?: Record<string, unknown>
    created_at?: string
  } | null>(null)
  // Profile state removed - using individual form fields instead for better performance
  // const [profile, setProfile] = useState<ProfileData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  
  // Form state
  const [fullName, setFullName] = useState("")
  const [selectedState, setSelectedState] = useState("")
  const [selectedCounty, setSelectedCounty] = useState("")

  useEffect(() => {
    // Check if user is authenticated and get profile
    const loadUserProfile = async () => {
      const supabase = createClient()
      
      // First check if there's a session using getSession() which doesn't throw errors
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session?.user) {
        router.push('/login?returnUrl=/profile/edit')
        return
      }
      
      // Only call getUser if we have a valid session
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        router.push('/login?returnUrl=/profile/edit')
        return
      }
      
      setUser(user)

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (profileError) {
        console.error('Profile fetch error:', profileError)
        setError('Failed to load profile data')
      } else {
        // Set form fields directly from profile data
        setFullName(profileData.full_name || '')
        setSelectedState(profileData.state || '')
        setSelectedCounty(profileData.county || '')
      }
      
      setIsLoading(false)
    }
    
    loadUserProfile()
  }, [router])

  const handleSave = async () => {
    if (!fullName.trim()) {
      setError("Full name is required")
      return
    }

    if (!user) {
      setError("User not authenticated")
      return
    }

    setIsSaving(true)
    setError("")
    setSuccess("")

    try {
      const supabase = createClient()
      
             const { error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: fullName.trim(),
          state: selectedState || null,
          county: selectedCounty || null,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .select()
        .single()

      if (updateError) {
        setError(updateError.message)
        return
      }

              // Profile updated successfully - form fields already contain latest values
      setSuccess('Profile updated successfully!')
      
      // Redirect back to profile page after a brief delay
      setTimeout(() => {
        router.push('/profile')
      }, 1500)

    } catch (error) {
      console.error('Profile update error:', error)
      setError('An unexpected error occurred')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    router.push('/profile')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading profile...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleCancel}
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Profile
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-white">Edit Profile</h1>
              <p className="text-gray-400">Update your account information</p>
            </div>
          </div>

          {/* Edit Form */}
          <Card className="bg-gray-800 border-pink-500/30 text-white">
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="w-5 h-5 mr-2 text-pink-400" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Status Messages */}
              {error && (
                <div className="bg-red-500/20 border border-red-500 rounded-lg p-4">
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              )}
              
              {success && (
                <div className="bg-green-500/20 border border-green-500 rounded-lg p-4">
                  <p className="text-green-300 text-sm">{success}</p>
                </div>
              )}

              {/* Full Name */}
              <div className="space-y-2">
                <Label htmlFor="fullName" className="text-gray-300">
                  Full Name *
                </Label>
                <Input
                  id="fullName"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Enter your full name"
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                />
              </div>

              {/* Email (read-only) */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-300">
                  Email
                </Label>
                <Input
                  id="email"
                  value={user?.email || ''}
                  disabled
                  className="bg-gray-700/50 border-gray-600 text-gray-400"
                />
                <p className="text-xs text-gray-500">Email cannot be changed</p>
              </div>

              {/* Location */}
              <div className="space-y-4">
                <Label className="text-gray-300 flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-pink-400" />
                  Location (Optional)
                </Label>
                <LocationDropdown
                  selectedState={selectedState}
                  setSelectedState={setSelectedState}
                  selectedCounty={selectedCounty}
                  setSelectedCounty={setSelectedCounty}
                  stateLabel="State"
                  countyLabel="County"
                  statePlaceholder="Select your state"
                  countyPlaceholder="Select your county"
                />
                <p className="text-xs text-gray-500">
                  Your location helps us show relevant content for your area
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 pt-4">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                
                <Button
                  onClick={handleSave}
                  disabled={isSaving || !fullName.trim()}
                  className="flex-1 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-semibold"
                >
                  {isSaving ? (
                    <>Saving...</>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function EditProfilePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    }>
      <EditProfileContent />
    </Suspense>
  )
} 