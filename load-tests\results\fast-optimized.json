{"aggregate": {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 5760, "vusers.created": 5760, "errors.Undefined function \"generateFastQueryFilters\"": 5760, "http.requests": 5760, "http.codes.200": 704, "http.responses": 704, "http.downloaded_bytes": 3936087, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 704, "vusers.failed": 5056, "vusers.completed": 704, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 5056, "errors.ETIMEDOUT": 5056}, "rates": {"http.request_rate": 10}, "firstCounterAt": 1753655840862, "firstHistogramAt": 1753655841766, "lastCounterAt": 1753656215272, "lastHistogramAt": 1753655928103, "firstMetricAt": 1753655840862, "lastMetricAt": 1753656215272, "period": 1753656210000, "summaries": {"http.response_time": {"min": 321, "max": 14984, "count": 704, "mean": 3017.1, "p50": 620.3, "median": 620.3, "p75": 3984.7, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}, "http.response_time.2xx": {"min": 321, "max": 14984, "count": 704, "mean": 3017.1, "p50": 620.3, "median": 620.3, "p75": 3984.7, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 321, "max": 14984, "count": 704, "mean": 3017.1, "p50": 620.3, "median": 620.3, "p75": 3984.7, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}, "vusers.session_length": {"min": 324.2, "max": 14988.9, "count": 704, "mean": 3028.1, "p50": 632.8, "median": 632.8, "p75": 4065.2, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}}, "histograms": {"http.response_time": {"min": 321, "max": 14984, "count": 704, "mean": 3017.1, "p50": 620.3, "median": 620.3, "p75": 3984.7, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}, "http.response_time.2xx": {"min": 321, "max": 14984, "count": 704, "mean": 3017.1, "p50": 620.3, "median": 620.3, "p75": 3984.7, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 321, "max": 14984, "count": 704, "mean": 3017.1, "p50": 620.3, "median": 620.3, "p75": 3984.7, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}, "vusers.session_length": {"min": 324.2, "max": 14988.9, "count": 704, "mean": 3028.1, "p50": 632.8, "median": 632.8, "p75": 4065.2, "p90": 10201.2, "p95": 13230.3, "p99": 14621.8, "p999": 14917.2}}}, "intermediate": [{"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 75, "vusers.created": 75, "errors.Undefined function \"generateFastQueryFilters\"": 75, "http.requests": 75, "http.codes.200": 72, "http.responses": 72, "http.downloaded_bytes": 402552, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 72, "vusers.failed": 0, "vusers.completed": 72}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753655840862, "firstHistogramAt": 1753655841766, "lastCounterAt": 1753655849974, "lastHistogramAt": 1753655849672, "firstMetricAt": 1753655840862, "lastMetricAt": 1753655849974, "period": "1753655840000", "summaries": {"http.response_time": {"min": 333, "max": 944, "count": 72, "mean": 451, "p50": 368.8, "median": 368.8, "p75": 459.5, "p90": 671.9, "p95": 871.5, "p99": 889.1, "p999": 889.1}, "http.response_time.2xx": {"min": 333, "max": 944, "count": 72, "mean": 451, "p50": 368.8, "median": 368.8, "p75": 459.5, "p90": 671.9, "p95": 871.5, "p99": 889.1, "p999": 889.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 333, "max": 944, "count": 72, "mean": 451, "p50": 368.8, "median": 368.8, "p75": 459.5, "p90": 671.9, "p95": 871.5, "p99": 889.1, "p999": 889.1}, "vusers.session_length": {"min": 337.3, "max": 989.3, "count": 72, "mean": 465.1, "p50": 376.2, "median": 376.2, "p75": 478.3, "p90": 685.5, "p95": 925.4, "p99": 944, "p999": 944}}, "histograms": {"http.response_time": {"min": 333, "max": 944, "count": 72, "mean": 451, "p50": 368.8, "median": 368.8, "p75": 459.5, "p90": 671.9, "p95": 871.5, "p99": 889.1, "p999": 889.1}, "http.response_time.2xx": {"min": 333, "max": 944, "count": 72, "mean": 451, "p50": 368.8, "median": 368.8, "p75": 459.5, "p90": 671.9, "p95": 871.5, "p99": 889.1, "p999": 889.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 333, "max": 944, "count": 72, "mean": 451, "p50": 368.8, "median": 368.8, "p75": 459.5, "p90": 671.9, "p95": 871.5, "p99": 889.1, "p999": 889.1}, "vusers.session_length": {"min": 337.3, "max": 989.3, "count": 72, "mean": 465.1, "p50": 376.2, "median": 376.2, "p75": 478.3, "p90": 685.5, "p95": 925.4, "p99": 944, "p999": 944}}}, {"counters": {"http.codes.200": 80, "http.responses": 80, "http.downloaded_bytes": 447280, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 80, "vusers.failed": 0, "vusers.completed": 80, "vusers.created_by_name.Fast Queries - Simple Filters": 77, "vusers.created": 77, "errors.Undefined function \"generateFastQueryFilters\"": 77, "http.requests": 77}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753655850024, "firstHistogramAt": 1753655850258, "lastCounterAt": 1753655859965, "lastHistogramAt": 1753655859965, "firstMetricAt": 1753655850024, "lastMetricAt": 1753655859965, "period": "1753655850000", "summaries": {"http.response_time": {"min": 321, "max": 1416, "count": 80, "mean": 601.6, "p50": 572.6, "median": 572.6, "p75": 632.8, "p90": 1085.9, "p95": 1176.4, "p99": 1353.1, "p999": 1353.1}, "http.response_time.2xx": {"min": 321, "max": 1416, "count": 80, "mean": 601.6, "p50": 572.6, "median": 572.6, "p75": 632.8, "p90": 1085.9, "p95": 1176.4, "p99": 1353.1, "p999": 1353.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 321, "max": 1416, "count": 80, "mean": 601.6, "p50": 572.6, "median": 572.6, "p75": 632.8, "p90": 1085.9, "p95": 1176.4, "p99": 1353.1, "p999": 1353.1}, "vusers.session_length": {"min": 324.2, "max": 1425.4, "count": 80, "mean": 612.4, "p50": 572.6, "median": 572.6, "p75": 645.6, "p90": 1085.9, "p95": 1200.1, "p99": 1380.5, "p999": 1380.5}}, "histograms": {"http.response_time": {"min": 321, "max": 1416, "count": 80, "mean": 601.6, "p50": 572.6, "median": 572.6, "p75": 632.8, "p90": 1085.9, "p95": 1176.4, "p99": 1353.1, "p999": 1353.1}, "http.response_time.2xx": {"min": 321, "max": 1416, "count": 80, "mean": 601.6, "p50": 572.6, "median": 572.6, "p75": 632.8, "p90": 1085.9, "p95": 1176.4, "p99": 1353.1, "p999": 1353.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 321, "max": 1416, "count": 80, "mean": 601.6, "p50": 572.6, "median": 572.6, "p75": 632.8, "p90": 1085.9, "p95": 1176.4, "p99": 1353.1, "p999": 1353.1}, "vusers.session_length": {"min": 324.2, "max": 1425.4, "count": 80, "mean": 612.4, "p50": 572.6, "median": 572.6, "p75": 645.6, "p90": 1085.9, "p95": 1200.1, "p99": 1380.5, "p999": 1380.5}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "http.codes.200": 80, "http.responses": 80, "http.downloaded_bytes": 447280, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 80, "vusers.failed": 0, "vusers.completed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753655860214, "firstHistogramAt": 1753655860755, "lastCounterAt": 1753655869826, "lastHistogramAt": 1753655869826, "firstMetricAt": 1753655860214, "lastMetricAt": 1753655869826, "period": "1753655860000", "summaries": {"http.response_time": {"min": 504, "max": 793, "count": 80, "mean": 600.9, "p50": 584.2, "median": 584.2, "p75": 620.3, "p90": 685.5, "p95": 772.9, "p99": 788.5, "p999": 788.5}, "http.response_time.2xx": {"min": 504, "max": 793, "count": 80, "mean": 600.9, "p50": 584.2, "median": 584.2, "p75": 620.3, "p90": 685.5, "p95": 772.9, "p99": 788.5, "p999": 788.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 504, "max": 793, "count": 80, "mean": 600.9, "p50": 584.2, "median": 584.2, "p75": 620.3, "p90": 685.5, "p95": 772.9, "p99": 788.5, "p999": 788.5}, "vusers.session_length": {"min": 513.2, "max": 807.4, "count": 80, "mean": 610.5, "p50": 596, "median": 596, "p75": 632.8, "p90": 699.4, "p95": 772.9, "p99": 804.5, "p999": 804.5}}, "histograms": {"http.response_time": {"min": 504, "max": 793, "count": 80, "mean": 600.9, "p50": 584.2, "median": 584.2, "p75": 620.3, "p90": 685.5, "p95": 772.9, "p99": 788.5, "p999": 788.5}, "http.response_time.2xx": {"min": 504, "max": 793, "count": 80, "mean": 600.9, "p50": 584.2, "median": 584.2, "p75": 620.3, "p90": 685.5, "p95": 772.9, "p99": 788.5, "p999": 788.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 504, "max": 793, "count": 80, "mean": 600.9, "p50": 584.2, "median": 584.2, "p75": 620.3, "p90": 685.5, "p95": 772.9, "p99": 788.5, "p999": 788.5}, "vusers.session_length": {"min": 513.2, "max": 807.4, "count": 80, "mean": 610.5, "p50": 596, "median": 596, "p75": 632.8, "p90": 699.4, "p95": 772.9, "p99": 804.5, "p999": 804.5}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 83, "vusers.created": 83, "errors.Undefined function \"generateFastQueryFilters\"": 83, "http.requests": 83, "http.codes.200": 80, "http.responses": 80, "http.downloaded_bytes": 447294, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 80, "vusers.failed": 0, "vusers.completed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753655870056, "firstHistogramAt": 1753655874604, "lastCounterAt": 1753655879975, "lastHistogramAt": 1753655879689, "firstMetricAt": 1753655870056, "lastMetricAt": 1753655879975, "period": "1753655870000", "summaries": {"http.response_time": {"min": 375, "max": 6573, "count": 80, "mean": 2678.6, "p50": 2143.5, "median": 2143.5, "p75": 3984.7, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}, "http.response_time.2xx": {"min": 375, "max": 6573, "count": 80, "mean": 2678.6, "p50": 2143.5, "median": 2143.5, "p75": 3984.7, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 375, "max": 6573, "count": 80, "mean": 2678.6, "p50": 2143.5, "median": 2143.5, "p75": 3984.7, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}, "vusers.session_length": {"min": 382.8, "max": 6576.2, "count": 80, "mean": 2689.2, "p50": 2186.8, "median": 2186.8, "p75": 4065.2, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}}, "histograms": {"http.response_time": {"min": 375, "max": 6573, "count": 80, "mean": 2678.6, "p50": 2143.5, "median": 2143.5, "p75": 3984.7, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}, "http.response_time.2xx": {"min": 375, "max": 6573, "count": 80, "mean": 2678.6, "p50": 2143.5, "median": 2143.5, "p75": 3984.7, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 375, "max": 6573, "count": 80, "mean": 2678.6, "p50": 2143.5, "median": 2143.5, "p75": 3984.7, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}, "vusers.session_length": {"min": 382.8, "max": 6576.2, "count": 80, "mean": 2689.2, "p50": 2186.8, "median": 2186.8, "p75": 4065.2, "p90": 4965.3, "p95": 5826.9, "p99": 6312.2, "p999": 6312.2}}}, {"counters": {"http.codes.200": 80, "http.responses": 80, "http.downloaded_bytes": 447280, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 80, "vusers.failed": 0, "vusers.completed": 80, "vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753655880024, "firstHistogramAt": 1753655880302, "lastCounterAt": 1753655889975, "lastHistogramAt": 1753655889705, "firstMetricAt": 1753655880024, "lastMetricAt": 1753655889975, "period": "1753655880000", "summaries": {"http.response_time": {"min": 356, "max": 772, "count": 80, "mean": 480.3, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 584.2, "p95": 632.8, "p99": 727.9, "p999": 727.9}, "http.response_time.2xx": {"min": 356, "max": 772, "count": 80, "mean": 480.3, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 584.2, "p95": 632.8, "p99": 727.9, "p999": 727.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 356, "max": 772, "count": 80, "mean": 480.3, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 584.2, "p95": 632.8, "p99": 727.9, "p999": 727.9}, "vusers.session_length": {"min": 371.3, "max": 786.2, "count": 80, "mean": 489.9, "p50": 468.8, "median": 468.8, "p75": 539.2, "p90": 596, "p95": 645.6, "p99": 727.9, "p999": 727.9}}, "histograms": {"http.response_time": {"min": 356, "max": 772, "count": 80, "mean": 480.3, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 584.2, "p95": 632.8, "p99": 727.9, "p999": 727.9}, "http.response_time.2xx": {"min": 356, "max": 772, "count": 80, "mean": 480.3, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 584.2, "p95": 632.8, "p99": 727.9, "p999": 727.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 356, "max": 772, "count": 80, "mean": 480.3, "p50": 459.5, "median": 459.5, "p75": 528.6, "p90": 584.2, "p95": 632.8, "p99": 727.9, "p999": 727.9}, "vusers.session_length": {"min": 371.3, "max": 786.2, "count": 80, "mean": 489.9, "p50": 468.8, "median": 468.8, "p75": 539.2, "p90": 596, "p95": 645.6, "p99": 727.9, "p999": 727.9}}}, {"counters": {"http.codes.200": 80, "http.responses": 80, "http.downloaded_bytes": 447280, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 80, "vusers.failed": 0, "vusers.completed": 80, "vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753655890024, "firstHistogramAt": 1753655890240, "lastCounterAt": 1753655899975, "lastHistogramAt": 1753655899943, "firstMetricAt": 1753655890024, "lastMetricAt": 1753655899975, "period": "1753655890000", "summaries": {"http.response_time": {"min": 348, "max": 727, "count": 80, "mean": 437.8, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 561.2, "p95": 620.3, "p99": 699.4, "p999": 699.4}, "http.response_time.2xx": {"min": 348, "max": 727, "count": 80, "mean": 437.8, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 561.2, "p95": 620.3, "p99": 699.4, "p999": 699.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 348, "max": 727, "count": 80, "mean": 437.8, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 561.2, "p95": 620.3, "p99": 699.4, "p999": 699.4}, "vusers.session_length": {"min": 354.1, "max": 732.2, "count": 80, "mean": 446.2, "p50": 424.2, "median": 424.2, "p75": 468.8, "p90": 572.6, "p95": 632.8, "p99": 713.5, "p999": 713.5}}, "histograms": {"http.response_time": {"min": 348, "max": 727, "count": 80, "mean": 437.8, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 561.2, "p95": 620.3, "p99": 699.4, "p999": 699.4}, "http.response_time.2xx": {"min": 348, "max": 727, "count": 80, "mean": 437.8, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 561.2, "p95": 620.3, "p99": 699.4, "p999": 699.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 348, "max": 727, "count": 80, "mean": 437.8, "p50": 407.5, "median": 407.5, "p75": 459.5, "p90": 561.2, "p95": 620.3, "p99": 699.4, "p999": 699.4}, "vusers.session_length": {"min": 354.1, "max": 732.2, "count": 80, "mean": 446.2, "p50": 424.2, "median": 424.2, "p75": 468.8, "p90": 572.6, "p95": 632.8, "p99": 713.5, "p999": 713.5}}}, {"counters": {"http.codes.200": 91, "http.responses": 91, "http.downloaded_bytes": 508781, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 91, "vusers.failed": 0, "vusers.completed": 91, "vusers.created_by_name.Fast Queries - Simple Filters": 197, "vusers.created": 197, "errors.Undefined function \"generateFastQueryFilters\"": 197, "http.requests": 197}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655900024, "firstHistogramAt": 1753655900229, "lastCounterAt": 1753655909975, "lastHistogramAt": 1753655909896, "firstMetricAt": 1753655900024, "lastMetricAt": 1753655909975, "period": "1753655900000", "summaries": {"http.response_time": {"min": 368, "max": 5367, "count": 91, "mean": 2497.2, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5168, "p999": 5168}, "http.response_time.2xx": {"min": 368, "max": 5367, "count": 91, "mean": 2497.2, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5168, "p999": 5168}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 368, "max": 5367, "count": 91, "mean": 2497.2, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5168, "p999": 5168}, "vusers.session_length": {"min": 385.3, "max": 5371.6, "count": 91, "mean": 2511.4, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5272.4, "p999": 5272.4}}, "histograms": {"http.response_time": {"min": 368, "max": 5367, "count": 91, "mean": 2497.2, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5168, "p999": 5168}, "http.response_time.2xx": {"min": 368, "max": 5367, "count": 91, "mean": 2497.2, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5168, "p999": 5168}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 368, "max": 5367, "count": 91, "mean": 2497.2, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5168, "p999": 5168}, "vusers.session_length": {"min": 385.3, "max": 5371.6, "count": 91, "mean": 2511.4, "p50": 2725, "median": 2725, "p75": 3534.1, "p90": 4676.2, "p95": 4867, "p99": 5272.4, "p999": 5272.4}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "http.codes.200": 87, "http.responses": 87, "http.downloaded_bytes": 486423, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 87, "vusers.failed": 0, "vusers.completed": 86}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655910018, "firstHistogramAt": 1753655910018, "lastCounterAt": 1753655919999, "lastHistogramAt": 1753655919999, "firstMetricAt": 1753655910018, "lastMetricAt": 1753655919999, "period": "1753655910000", "summaries": {"http.response_time": {"min": 5377, "max": 11141, "count": 87, "mean": 8582, "p50": 8868.4, "median": 8868.4, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}, "http.response_time.2xx": {"min": 5377, "max": 11141, "count": 87, "mean": 8582, "p50": 8868.4, "median": 8868.4, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 5377, "max": 11141, "count": 87, "mean": 8582, "p50": 8868.4, "median": 8868.4, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}, "vusers.session_length": {"min": 5386.1, "max": 11153.4, "count": 86, "mean": 8564, "p50": 8692.8, "median": 8692.8, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}}, "histograms": {"http.response_time": {"min": 5377, "max": 11141, "count": 87, "mean": 8582, "p50": 8868.4, "median": 8868.4, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}, "http.response_time.2xx": {"min": 5377, "max": 11141, "count": 87, "mean": 8582, "p50": 8868.4, "median": 8868.4, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 5377, "max": 11141, "count": 87, "mean": 8582, "p50": 8868.4, "median": 8868.4, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}, "vusers.session_length": {"min": 5386.1, "max": 11153.4, "count": 86, "mean": 8564, "p50": 8692.8, "median": 8692.8, "p75": 10201.2, "p90": 10617.5, "p95": 10832, "p99": 11050.8, "p999": 11050.8}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "http.codes.200": 54, "http.responses": 54, "http.downloaded_bytes": 301917, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).codes.200": 54, "vusers.failed": 68, "vusers.completed": 55, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 68, "errors.ETIMEDOUT": 68}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655920008, "firstHistogramAt": 1753655920008, "lastCounterAt": 1753655929977, "lastHistogramAt": 1753655928103, "firstMetricAt": 1753655920008, "lastMetricAt": 1753655929977, "period": "1753655920000", "summaries": {"http.response_time": {"min": 11055, "max": 14984, "count": 54, "mean": 13588.5, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}, "http.response_time.2xx": {"min": 11055, "max": 14984, "count": 54, "mean": 13588.5, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 11055, "max": 14984, "count": 54, "mean": 13588.5, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}, "vusers.session_length": {"min": 11074.4, "max": 14988.9, "count": 55, "mean": 13552.4, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}}, "histograms": {"http.response_time": {"min": 11055, "max": 14984, "count": 54, "mean": 13588.5, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}, "http.response_time.2xx": {"min": 11055, "max": 14984, "count": 54, "mean": 13588.5, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Optimized)": {"min": 11055, "max": 14984, "count": 54, "mean": 13588.5, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}, "vusers.session_length": {"min": 11074.4, "max": 14988.9, "count": 55, "mean": 13552.4, "p50": 13770.3, "median": 13770.3, "p75": 14621.8, "p90": 14917.2, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655930024, "lastCounterAt": 1753655939977, "firstMetricAt": 1753655930024, "lastMetricAt": 1753655939977, "period": "1753655930000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655940024, "lastCounterAt": 1753655949978, "firstMetricAt": 1753655940024, "lastMetricAt": 1753655949978, "period": "1753655940000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655950024, "lastCounterAt": 1753655959976, "firstMetricAt": 1753655950024, "lastMetricAt": 1753655959976, "period": "1753655950000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655960025, "lastCounterAt": 1753655969976, "firstMetricAt": 1753655960025, "lastMetricAt": 1753655969976, "period": "1753655960000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200, "vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655970024, "lastCounterAt": 1753655979986, "firstMetricAt": 1753655970024, "lastMetricAt": 1753655979986, "period": "1753655970000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655980024, "lastCounterAt": 1753655989975, "firstMetricAt": 1753655980024, "lastMetricAt": 1753655989975, "period": "1753655980000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753655990024, "lastCounterAt": 1753655999975, "firstMetricAt": 1753655990024, "lastMetricAt": 1753655999975, "period": "1753655990000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656000024, "lastCounterAt": 1753656009976, "firstMetricAt": 1753656000024, "lastMetricAt": 1753656009976, "period": "1753656000000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656010024, "lastCounterAt": 1753656019975, "firstMetricAt": 1753656010024, "lastMetricAt": 1753656019975, "period": "1753656010000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656020024, "lastCounterAt": 1753656029979, "firstMetricAt": 1753656020024, "lastMetricAt": 1753656029979, "period": "1753656020000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200, "vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656030024, "lastCounterAt": 1753656039975, "firstMetricAt": 1753656030024, "lastMetricAt": 1753656039975, "period": "1753656030000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656040025, "lastCounterAt": 1753656049979, "firstMetricAt": 1753656040025, "lastMetricAt": 1753656049979, "period": "1753656040000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656050025, "lastCounterAt": 1753656059976, "firstMetricAt": 1753656050025, "lastMetricAt": 1753656059976, "period": "1753656050000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200, "vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656060025, "lastCounterAt": 1753656069985, "firstMetricAt": 1753656060025, "lastMetricAt": 1753656069985, "period": "1753656060000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656070025, "lastCounterAt": 1753656079979, "firstMetricAt": 1753656070025, "lastMetricAt": 1753656079979, "period": "1753656070000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656080025, "lastCounterAt": 1753656089977, "firstMetricAt": 1753656080025, "lastMetricAt": 1753656089977, "period": "1753656080000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656090027, "lastCounterAt": 1753656099975, "firstMetricAt": 1753656090027, "lastMetricAt": 1753656099975, "period": "1753656090000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656100024, "lastCounterAt": 1753656109977, "firstMetricAt": 1753656100024, "lastMetricAt": 1753656109977, "period": "1753656100000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656110024, "lastCounterAt": 1753656119975, "firstMetricAt": 1753656110024, "lastMetricAt": 1753656119975, "period": "1753656110000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656120024, "lastCounterAt": 1753656129976, "firstMetricAt": 1753656120024, "lastMetricAt": 1753656129976, "period": "1753656120000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 200, "vusers.created": 200, "errors.Undefined function \"generateFastQueryFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753656130030, "lastCounterAt": 1753656139978, "firstMetricAt": 1753656130030, "lastMetricAt": 1753656139978, "period": "1753656130000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200, "vusers.created_by_name.Fast Queries - Simple Filters": 83, "vusers.created": 83, "errors.Undefined function \"generateFastQueryFilters\"": 83, "http.requests": 83}, "rates": {"http.request_rate": 11}, "http.request_rate": null, "firstCounterAt": 1753656140024, "lastCounterAt": 1753656149986, "firstMetricAt": 1753656140024, "lastMetricAt": 1753656149986, "period": "1753656140000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 143, "errors.ETIMEDOUT": 143, "vusers.failed": 143, "vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753656150040, "lastCounterAt": 1753656159977, "firstMetricAt": 1753656150040, "lastMetricAt": 1753656159977, "period": "1753656150000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753656160025, "lastCounterAt": 1753656169981, "firstMetricAt": 1753656160025, "lastMetricAt": 1753656169981, "period": "1753656160000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753656170025, "lastCounterAt": 1753656179979, "firstMetricAt": 1753656170025, "lastMetricAt": 1753656179979, "period": "1753656170000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753656180025, "lastCounterAt": 1753656189979, "firstMetricAt": 1753656180025, "lastMetricAt": 1753656189979, "period": "1753656180000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Fast Queries - Simple Filters": 80, "vusers.created": 80, "errors.Undefined function \"generateFastQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753656190025, "lastCounterAt": 1753656199978, "firstMetricAt": 1753656190025, "lastMetricAt": 1753656199978, "period": "1753656190000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80, "vusers.created_by_name.Fast Queries - Simple Filters": 5, "vusers.created": 5, "errors.Undefined function \"generateFastQueryFilters\"": 5, "http.requests": 5}, "rates": {"http.request_rate": 5}, "firstCounterAt": 1753656200025, "lastCounterAt": 1753656209987, "firstMetricAt": 1753656200025, "lastMetricAt": 1753656209987, "period": "1753656200000", "http.request_rate": null, "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Optimized).errors.ETIMEDOUT": 45, "errors.ETIMEDOUT": 45, "vusers.failed": 45}, "rates": {}, "firstCounterAt": 1753656210029, "lastCounterAt": 1753656215272, "firstMetricAt": 1753656210029, "lastMetricAt": 1753656215272, "period": "1753656210000", "summaries": {}, "histograms": {}}]}