import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    console.log('Create profile API endpoint hit')
    
    const supabase = await createClient()
    
    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      console.log('User not authenticated:', authError?.message)
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      )
    }

    console.log('Creating/checking profile for authenticated user:', user.id, user.email)

    // Use account linking-aware profile lookup for OAuth users
    if (user.app_metadata?.provider === 'google') {
      console.log('Google OAuth user detected, using account linking logic...')
      
      const { getOrCreateProfileWithLinking } = await import('@/lib/auth-account-linking')
      const { profile, wasLinked, message } = await getOrCreateProfileWithLinking(supabase, user)
      
      if (profile) {
        return NextResponse.json({
          success: true,
          message: wasLinked ? `Account linked: ${message}` : 'Profile found',
          profile,
          wasLinked
        })
      }
    } else {
      // Regular email/password user - check normally
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

      // If profile exists, return success
      if (existingProfile && !fetchError) {
        console.log('Profile already exists for user:', existingProfile.id)
        return NextResponse.json({
          success: true,
          message: 'Profile already exists',
          profile: existingProfile
        })
      }

      // If error is not "not found", return the error
      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error checking existing profile:', fetchError)
        return NextResponse.json(
          { success: false, error: `Profile check failed: ${fetchError.message}` },
          { status: 500 }
        )
      }
    }

    console.log('Profile not found, creating new profile for user:', user.id)

    // Get user metadata for profile creation
    const metadata = user.user_metadata || {}
    const appMetadata = user.app_metadata || {}
    
    // Extract profile data from metadata (stored during signup)
    const fullName = metadata.full_name || user.email?.split('@')[0] || 'User'
    const state = metadata.state || null
    const county = metadata.county || null
    const avatarUrl = metadata.avatar_url || null

    console.log('Creating profile with data:', {
      userId: user.id,
      email: user.email,
      fullName,
      state,
      county,
      avatarUrl,
      provider: appMetadata.provider
    })

    // Create new profile for authenticated user
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        user_id: user.id,
        email: user.email || null,
        full_name: fullName,
        avatar_url: avatarUrl,
        state: state,
        county: county,
        role: 'user'
      })
      .select()
      .single()

    if (createError) {
      console.error('Profile creation error:', createError)
      return NextResponse.json(
        { success: false, error: `Profile creation failed: ${createError.message}` },
        { status: 500 }
      )
    }

    console.log('Profile created successfully:', newProfile.id)
    
    return NextResponse.json({
      success: true,
      message: 'Profile created successfully',
      profile: newProfile
    })

  } catch (error) {
    console.error('Create profile API error:', error)
    return NextResponse.json(
      { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    )
  }
} 