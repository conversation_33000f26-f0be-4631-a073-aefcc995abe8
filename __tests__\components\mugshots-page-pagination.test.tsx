import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useSearchParams, useRouter, usePathname } from 'next/navigation'
import MugshotsPage from '@/app/mugshots/page'

// Mock the Next.js navigation hooks
vi.mock('next/navigation', () => ({
  useSearchParams: vi.fn(),
  useRouter: vi.fn(),
  usePathname: vi.fn(),
}))

// Mock the Supabase client
vi.mock('@/lib/supabase/client', () => ({
  createClient: vi.fn(() => ({
    auth: {
      getUser: vi.fn(() => Promise.resolve({ data: { user: null } })),
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: null })),
        })),
      })),
    })),
  })),
}))

// Mock the mugshots service
vi.mock('@/lib/services/mugshots-service', () => ({
  mugshotsService: {
    getMugshots: vi.fn(() => Promise.resolve([])),
    getMugshotCount: vi.fn(() => Promise.resolve(0)),
  },
}))

// Mock other components and services
vi.mock('@/components/MugshotCard', () => ({
  default: ({ mugshot, onClick }: any) => (
    <div data-testid={`mugshot-${mugshot.id}`} onClick={() => onClick(mugshot)}>
      Mugshot Card
    </div>
  ),
}))

vi.mock('@/components/MugshotModal', () => ({
  default: () => <div data-testid="mugshot-modal">Modal</div>,
}))

vi.mock('@/components/LocationDropdown', () => ({
  default: () => <div data-testid="location-dropdown">Location Dropdown</div>,
}))

vi.mock('@/components/CategoryOverlay', () => ({
  default: () => <div data-testid="category-overlay">Category Overlay</div>,
}))

describe('MugshotsPage Pagination', () => {
  const mockPush = vi.fn()
  const mockSearchParams = new URLSearchParams()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup navigation mocks
    ;(useRouter as any).mockReturnValue({
      push: mockPush,
    })
    
    ;(usePathname as any).mockReturnValue('/mugshots')
    
    ;(useSearchParams as any).mockReturnValue({
      get: vi.fn((key: string) => mockSearchParams.get(key)),
      toString: vi.fn(() => mockSearchParams.toString()),
    })

    // Mock geolocation
    Object.defineProperty(global.navigator, 'geolocation', {
      value: {
        getCurrentPosition: vi.fn(),
      },
      writable: true,
    })
  })

  it('should show page 1 as active by default', async () => {
    render(<MugshotsPage />)
    
    await waitFor(() => {
      // Look for the pagination section
      const pagination = screen.getByText('Page 1 of 1')
      expect(pagination).toBeInTheDocument()
    })
  })

  it('should navigate to page 2 when page 2 button is clicked', async () => {
    // Mock having multiple pages of data
    const { mugshotsService } = await import('@/lib/services/mugshots-service')
    ;(mugshotsService.getMugshotCount as any).mockResolvedValue(25) // More than 12 items to create pagination
    ;(mugshotsService.getMugshots as any).mockResolvedValue([
      { id: 1, name: 'Test Mugshot' },
    ])

    render(<MugshotsPage />)
    
    await waitFor(() => {
      // Should show multiple pages
      expect(screen.getByText(/Page \d+ of \d+/)).toBeInTheDocument()
    })

    // Find and click page 2 button
    const page2Button = screen.getByRole('button', { name: '2' })
    fireEvent.click(page2Button)

    // Verify router.push was called with correct URL
    expect(mockPush).toHaveBeenCalledWith('/mugshots?page=2')
  })

  it('should sync page state when URL changes', async () => {
    // Initially on page 1
    mockSearchParams.set('page', '2')
    ;(useSearchParams as any).mockReturnValue({
      get: vi.fn((key: string) => key === 'page' ? '2' : null),
      toString: vi.fn(() => 'page=2'),
    })

    render(<MugshotsPage />)
    
    await waitFor(() => {
      // Should reflect the URL page parameter
      const pageInfo = screen.getByText(/Page 2/)
      expect(pageInfo).toBeInTheDocument()
    })
  })

  it('should remove page parameter from URL when navigating to page 1', async () => {
    // Start on page 2
    mockSearchParams.set('page', '2')
    ;(useSearchParams as any).mockReturnValue({
      get: vi.fn((key: string) => key === 'page' ? '2' : null),
      toString: vi.fn(() => 'page=2'),
    })

    render(<MugshotsPage />)
    
    await waitFor(() => {
      const page1Button = screen.getByRole('button', { name: '1' })
      fireEvent.click(page1Button)
    })

    // Verify router.push was called with clean URL (no page param for page 1)
    expect(mockPush).toHaveBeenCalledWith('/mugshots')
  })
}) 