import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createClient } from '@/lib/supabase/server'
import { mugshotsServiceServer } from '@/lib/services/mugshots-service-server'

describe('Tag Filtering Service', () => {
  let testMugshotIds: number[] = []
  let testUserIds: string[] = []

  beforeEach(async () => {
    await cleanup()
  })

  afterEach(async () => {
    await cleanup()
  })

  async function cleanup() {
    const supabase = await createClient()
    
    // Delete test tags
    if (testMugshotIds.length > 0) {
      await supabase.from('tags').delete().in('mugshot_id', testMugshotIds)
    }
    
    // Delete test ratings
    if (testMugshotIds.length > 0) {
      await supabase.from('ratings').delete().in('mugshot_id', testMugshotIds)
    }
    
    // Delete test mugshots
    if (testMugshotIds.length > 0) {
      await supabase.from('mugshots').delete().in('id', testMugshotIds)
    }
    
    testMugshotIds = []
    testUserIds = []
  }

  async function createTestMugshot(data: {
    firstName: string
    lastName: string
    stateOfBooking?: string
    countyOfBooking?: string
    dateOfBooking?: string
  }) {
    const supabase = await createClient()
    const { data: mugshot, error } = await supabase
      .from('mugshots')
      .insert({
        firstName: data.firstName,
        lastName: data.lastName,
        stateOfBooking: data.stateOfBooking || 'CA',
        countyOfBooking: data.countyOfBooking || 'Los Angeles',
        dateOfBooking: data.dateOfBooking || '2024-01-01',
        offenseDescription: 'Test offense',
        imagePath: '/test/image.jpg'
      })
      .select('id')
      .single()

    if (error) throw error
    testMugshotIds.push(mugshot.id)
    return mugshot.id
  }

  async function addTag(mugshotId: number, userId: string, tagType: 'wild' | 'funny' | 'spooky') {
    const supabase = await createClient()
    const { error } = await supabase.from('tags').insert({
      mugshot_id: mugshotId,
      user_id: userId,
      tag_type: tagType
    })
    if (error) throw error
  }

  async function addRating(mugshotId: number, userId: string, rating: number) {
    const supabase = await createClient()
    const { error } = await supabase.from('ratings').insert({
      mugshot_id: mugshotId,
      user_id: userId,
      rating
    })
    if (error) throw error
  }

  function generateUserId(): string {
    const userId = crypto.randomUUID()
    testUserIds.push(userId)
    return userId
  }

  it('should filter by single tag type', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })

    // Add different tags
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'funny')
    await addTag(mugshot3, generateUserId(), 'spooky')

    // Filter by 'wild' tag
    const results = await mugshotsServiceServer.getMugshots(
      { tags: ['wild'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(1)
    expect(results[0].id).toBe(mugshot1)
    expect(results[0].firstName).toBe('John')
  })

  it('should filter by multiple tag types (OR logic)', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })
    const mugshot4 = await createTestMugshot({ firstName: 'Alice', lastName: 'Brown' })

    // Add different tags
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'funny')
    await addTag(mugshot3, generateUserId(), 'spooky')
    // mugshot4 has no tags

    // Filter by 'wild' OR 'funny' tags
    const results = await mugshotsServiceServer.getMugshots(
      { tags: ['wild', 'funny'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(2)
    const resultIds = results.map(r => r.id)
    expect(resultIds).toContain(mugshot1)
    expect(resultIds).toContain(mugshot2)
    expect(resultIds).not.toContain(mugshot3)
    expect(resultIds).not.toContain(mugshot4)
  })

  it('should work with tag filtering in top-rated sorting', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })

    // Add tags
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'wild')
    await addTag(mugshot3, generateUserId(), 'funny') // Different tag

    // Add ratings (mugshot1 should be higher rated)
    await addRating(mugshot1, generateUserId(), 9)
    await addRating(mugshot2, generateUserId(), 7)
    await addRating(mugshot3, generateUserId(), 10) // Highest but wrong tag

    // Filter by 'wild' tag with top-rated sorting
    const results = await mugshotsServiceServer.getMugshots(
      { tags: ['wild'] },
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(2)
    expect(results[0].id).toBe(mugshot1) // Higher rated 'wild' mugshot
    expect(results[1].id).toBe(mugshot2) // Lower rated 'wild' mugshot
    expect(results[0].average_rating).toBe(9)
    expect(results[1].average_rating).toBe(7)
    
    // mugshot3 should not be included despite higher rating
    const resultIds = results.map(r => r.id)
    expect(resultIds).not.toContain(mugshot3)
  })

  it('should combine tag filtering with other filters', async () => {
    // Create test mugshots in different states
    const mugshot1 = await createTestMugshot({ 
      firstName: 'John', 
      lastName: 'Doe',
      stateOfBooking: 'CA'
    })
    const mugshot2 = await createTestMugshot({ 
      firstName: 'Jane', 
      lastName: 'Smith',
      stateOfBooking: 'NY'
    })
    const mugshot3 = await createTestMugshot({ 
      firstName: 'Bob', 
      lastName: 'Johnson',
      stateOfBooking: 'CA'
    })

    // Add wild tags to all
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'wild')
    await addTag(mugshot3, generateUserId(), 'funny') // Different tag

    // Filter by CA state AND wild tag
    const results = await mugshotsServiceServer.getMugshots(
      { state: 'CA', tags: ['wild'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(1)
    expect(results[0].id).toBe(mugshot1)
    expect(results[0].stateOfBooking).toBe('CA')
  })

  it('should return empty results when no mugshots match tag filter', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })

    // Add only funny tags
    await addTag(mugshot1, generateUserId(), 'funny')
    await addTag(mugshot2, generateUserId(), 'funny')

    // Filter by 'wild' tag (none exist)
    const results = await mugshotsServiceServer.getMugshots(
      { tags: ['wild'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(0)
  })

  it('should count correctly with tag filtering', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })

    // Add tags
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'wild')
    await addTag(mugshot3, generateUserId(), 'funny')

    // Count with tag filter
    const count = await mugshotsServiceServer.getMugshotCount({ tags: ['wild'] })

    expect(count).toBe(2)
  })

  it('should count correctly with tag filtering for top-rated', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })

    // Add tags
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'wild')
    await addTag(mugshot3, generateUserId(), 'funny')

    // Add ratings to make them eligible for top-rated
    await addRating(mugshot1, generateUserId(), 8)
    await addRating(mugshot2, generateUserId(), 7)
    await addRating(mugshot3, generateUserId(), 9)

    // Count with tag filter for top-rated
    const count = await mugshotsServiceServer.getMugshotCount({ tags: ['wild'] }, 'top-rated')

    expect(count).toBe(2) // Only 2 wild mugshots have ratings
  })

  it('should handle multiple tags on same mugshot', async () => {
    // Create test mugshot
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })

    const user1 = generateUserId()
    const user2 = generateUserId()

    // Add multiple tags to same mugshot
    await addTag(mugshot1, user1, 'wild')
    await addTag(mugshot1, user2, 'funny')
    
    // Add only one tag to second mugshot
    await addTag(mugshot2, user1, 'spooky')

    // Search for wild OR funny - should find mugshot1
    const results1 = await mugshotsServiceServer.getMugshots(
      { tags: ['wild', 'funny'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results1).toHaveLength(1)
    expect(results1[0].id).toBe(mugshot1)

    // Search for spooky - should find mugshot2
    const results2 = await mugshotsServiceServer.getMugshots(
      { tags: ['spooky'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results2).toHaveLength(1)
    expect(results2[0].id).toBe(mugshot2)
  })

  it('should return all mugshots when no tag filter is applied', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })

    // Add tags to some but not all
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'funny')
    // mugshot3 has no tags

    // Search without tag filter
    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(3)
    const resultIds = results.map(r => r.id)
    expect(resultIds).toContain(mugshot1)
    expect(resultIds).toContain(mugshot2)
    expect(resultIds).toContain(mugshot3)
  })

  it('should work with search term and tag filtering combined', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Johnny', lastName: 'Wilson' })

    // Add tags
    await addTag(mugshot1, generateUserId(), 'wild')
    await addTag(mugshot2, generateUserId(), 'wild')
    await addTag(mugshot3, generateUserId(), 'wild')

    // Search for "John" with wild tag - should find mugshot1 and mugshot3
    const results = await mugshotsServiceServer.getMugshots(
      { searchTerm: 'John', tags: ['wild'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(2)
    const resultIds = results.map(r => r.id)
    expect(resultIds).toContain(mugshot1) // John
    expect(resultIds).toContain(mugshot3) // Johnny
    expect(resultIds).not.toContain(mugshot2) // Jane doesn't match "John"
  })
}) 