{"aggregate": {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 3000, "vusers.created": 3000, "errors.Undefined function \"generateBaselineFilters\"": 3000, "http.requests": 3000, "http.codes.200": 1561, "http.responses": 1561, "http.downloaded_bytes": 6546916, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 1561, "vusers.failed": 1439, "vusers.completed": 1561, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 1439, "errors.ETIMEDOUT": 1439}, "rates": {"http.request_rate": 6}, "firstCounterAt": 1753660795692, "firstHistogramAt": 1753660796546, "lastCounterAt": 1753661054943, "lastHistogramAt": 1753660962953, "firstMetricAt": 1753660795692, "lastMetricAt": 1753661054943, "period": 1753661050000, "summaries": {"http.response_time": {"min": 323, "max": 19998, "count": 1561, "mean": 4710.6, "p50": 1790.4, "median": 1790.4, "p75": 7557.1, "p90": 14917.2, "p95": 17505.6, "p99": 19737.6, "p999": 19737.6}, "http.response_time.2xx": {"min": 323, "max": 19998, "count": 1561, "mean": 4710.6, "p50": 1790.4, "median": 1790.4, "p75": 7557.1, "p90": 14917.2, "p95": 17505.6, "p99": 19737.6, "p999": 19737.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 323, "max": 19998, "count": 1561, "mean": 4710.6, "p50": 1790.4, "median": 1790.4, "p75": 7557.1, "p90": 14917.2, "p95": 17505.6, "p99": 19737.6, "p999": 19737.6}, "vusers.session_length": {"min": 1328.1, "max": 21000.8, "count": 1561, "mean": 5723.2, "p50": 2780, "median": 2780, "p75": 8520.7, "p90": 15839.7, "p95": 18588.1, "p99": 20543.1, "p999": 20958.1}}, "histograms": {"http.response_time": {"min": 323, "max": 19998, "count": 1561, "mean": 4710.6, "p50": 1790.4, "median": 1790.4, "p75": 7557.1, "p90": 14917.2, "p95": 17505.6, "p99": 19737.6, "p999": 19737.6}, "http.response_time.2xx": {"min": 323, "max": 19998, "count": 1561, "mean": 4710.6, "p50": 1790.4, "median": 1790.4, "p75": 7557.1, "p90": 14917.2, "p95": 17505.6, "p99": 19737.6, "p999": 19737.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 323, "max": 19998, "count": 1561, "mean": 4710.6, "p50": 1790.4, "median": 1790.4, "p75": 7557.1, "p90": 14917.2, "p95": 17505.6, "p99": 19737.6, "p999": 19737.6}, "vusers.session_length": {"min": 1328.1, "max": 21000.8, "count": 1561, "mean": 5723.2, "p50": 2780, "median": 2780, "p75": 8520.7, "p90": 15839.7, "p95": 18588.1, "p99": 20543.1, "p999": 20958.1}}}, "intermediate": [{"counters": {"vusers.created_by_name.Baseline - Scaling Test": 25, "vusers.created": 25, "errors.Undefined function \"generateBaselineFilters\"": 25, "http.requests": 25, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83882, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 20, "vusers.failed": 0, "vusers.completed": 15}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660795692, "firstHistogramAt": 1753660796546, "lastCounterAt": 1753660799934, "lastHistogramAt": 1753660799308, "firstMetricAt": 1753660795692, "lastMetricAt": 1753660799934, "period": "1753660790000", "summaries": {"http.response_time": {"min": 337, "max": 1181, "count": 20, "mean": 623.5, "p50": 383.8, "median": 383.8, "p75": 837.3, "p90": 1064.4, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 337, "max": 1181, "count": 20, "mean": 623.5, "p50": 383.8, "median": 383.8, "p75": 837.3, "p90": 1064.4, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 337, "max": 1181, "count": 20, "mean": 623.5, "p50": 383.8, "median": 383.8, "p75": 837.3, "p90": 1064.4, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 1349.9, "max": 2226.6, "count": 15, "mean": 1732.7, "p50": 1790.4, "median": 1790.4, "p75": 1863.5, "p90": 2101.1, "p95": 2186.8, "p99": 2186.8, "p999": 2186.8}}, "histograms": {"http.response_time": {"min": 337, "max": 1181, "count": 20, "mean": 623.5, "p50": 383.8, "median": 383.8, "p75": 837.3, "p90": 1064.4, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "http.response_time.2xx": {"min": 337, "max": 1181, "count": 20, "mean": 623.5, "p50": 383.8, "median": 383.8, "p75": 837.3, "p90": 1064.4, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 337, "max": 1181, "count": 20, "mean": 623.5, "p50": 383.8, "median": 383.8, "p75": 837.3, "p90": 1064.4, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "vusers.session_length": {"min": 1349.9, "max": 2226.6, "count": 15, "mean": 1732.7, "p50": 1790.4, "median": 1790.4, "p75": 1863.5, "p90": 2101.1, "p95": 2186.8, "p99": 2186.8, "p999": 2186.8}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 209700, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 50, "vusers.failed": 0, "vusers.completed": 50, "vusers.created_by_name.Baseline - Scaling Test": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660800030, "firstHistogramAt": 1753660800030, "lastCounterAt": 1753660809935, "lastHistogramAt": 1753660809485, "firstMetricAt": 1753660800030, "lastMetricAt": 1753660809935, "period": "1753660800000", "summaries": {"http.response_time": {"min": 334, "max": 602, "count": 50, "mean": 404.4, "p50": 376.2, "median": 376.2, "p75": 424.2, "p90": 497.8, "p95": 550.1, "p99": 596, "p999": 596}, "http.response_time.2xx": {"min": 334, "max": 602, "count": 50, "mean": 404.4, "p50": 376.2, "median": 376.2, "p75": 424.2, "p90": 497.8, "p95": 550.1, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 334, "max": 602, "count": 50, "mean": 404.4, "p50": 376.2, "median": 376.2, "p75": 424.2, "p90": 497.8, "p95": 550.1, "p99": 596, "p999": 596}, "vusers.session_length": {"min": 1343.5, "max": 1608.7, "count": 50, "mean": 1405, "p50": 1380.5, "median": 1380.5, "p75": 1408.4, "p90": 1495.5, "p95": 1495.5, "p99": 1620, "p999": 1620}}, "histograms": {"http.response_time": {"min": 334, "max": 602, "count": 50, "mean": 404.4, "p50": 376.2, "median": 376.2, "p75": 424.2, "p90": 497.8, "p95": 550.1, "p99": 596, "p999": 596}, "http.response_time.2xx": {"min": 334, "max": 602, "count": 50, "mean": 404.4, "p50": 376.2, "median": 376.2, "p75": 424.2, "p90": 497.8, "p95": 550.1, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 334, "max": 602, "count": 50, "mean": 404.4, "p50": 376.2, "median": 376.2, "p75": 424.2, "p90": 497.8, "p95": 550.1, "p99": 596, "p999": 596}, "vusers.session_length": {"min": 1343.5, "max": 1608.7, "count": 50, "mean": 1405, "p50": 1380.5, "median": 1380.5, "p75": 1408.4, "p90": 1495.5, "p95": 1495.5, "p99": 1620, "p999": 1620}}}, {"counters": {"http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 209701, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 50, "vusers.failed": 0, "vusers.completed": 50, "vusers.created_by_name.Baseline - Scaling Test": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660810039, "firstHistogramAt": 1753660810039, "lastCounterAt": 1753660819934, "lastHistogramAt": 1753660819321, "firstMetricAt": 1753660810039, "lastMetricAt": 1753660819934, "period": "1753660810000", "summaries": {"http.response_time": {"min": 329, "max": 1338, "count": 50, "mean": 514.1, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 1002.4, "p95": 1153.1, "p99": 1176.4, "p999": 1176.4}, "http.response_time.2xx": {"min": 329, "max": 1338, "count": 50, "mean": 514.1, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 1002.4, "p95": 1153.1, "p99": 1176.4, "p999": 1176.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 329, "max": 1338, "count": 50, "mean": 514.1, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 1002.4, "p95": 1153.1, "p99": 1176.4, "p999": 1176.4}, "vusers.session_length": {"min": 1344.8, "max": 2348.9, "count": 50, "mean": 1535.1, "p50": 1408.4, "median": 1408.4, "p75": 1408.4, "p90": 2018.7, "p95": 2143.5, "p99": 2186.8, "p999": 2186.8}}, "histograms": {"http.response_time": {"min": 329, "max": 1338, "count": 50, "mean": 514.1, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 1002.4, "p95": 1153.1, "p99": 1176.4, "p999": 1176.4}, "http.response_time.2xx": {"min": 329, "max": 1338, "count": 50, "mean": 514.1, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 1002.4, "p95": 1153.1, "p99": 1176.4, "p999": 1176.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 329, "max": 1338, "count": 50, "mean": 514.1, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 1002.4, "p95": 1153.1, "p99": 1176.4, "p999": 1176.4}, "vusers.session_length": {"min": 1344.8, "max": 2348.9, "count": 50, "mean": 1535.1, "p50": 1408.4, "median": 1408.4, "p75": 1408.4, "p90": 2018.7, "p95": 2143.5, "p99": 2186.8, "p999": 2186.8}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 65, "http.codes.200": 70, "http.responses": 70, "http.downloaded_bytes": 293580, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 70, "vusers.created_by_name.Baseline - Scaling Test": 71, "vusers.created": 71, "errors.Undefined function \"generateBaselineFilters\"": 71, "http.requests": 71}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753660820022, "firstHistogramAt": 1753660820022, "lastCounterAt": 1753660829996, "lastHistogramAt": 1753660829640, "firstMetricAt": 1753660820022, "lastMetricAt": 1753660829996, "period": "1753660820000", "summaries": {"vusers.session_length": {"min": 1332.1, "max": 1445.5, "count": 65, "mean": 1377.8, "p50": 1380.5, "median": 1380.5, "p75": 1380.5, "p90": 1408.4, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "http.response_time": {"min": 323, "max": 446, "count": 70, "mean": 368, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 391.6, "p95": 407.5, "p99": 407.5, "p999": 407.5}, "http.response_time.2xx": {"min": 323, "max": 446, "count": 70, "mean": 368, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 391.6, "p95": 407.5, "p99": 407.5, "p999": 407.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 323, "max": 446, "count": 70, "mean": 368, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 391.6, "p95": 407.5, "p99": 407.5, "p999": 407.5}}, "histograms": {"vusers.session_length": {"min": 1332.1, "max": 1445.5, "count": 65, "mean": 1377.8, "p50": 1380.5, "median": 1380.5, "p75": 1380.5, "p90": 1408.4, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "http.response_time": {"min": 323, "max": 446, "count": 70, "mean": 368, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 391.6, "p95": 407.5, "p99": 407.5, "p999": 407.5}, "http.response_time.2xx": {"min": 323, "max": 446, "count": 70, "mean": 368, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 391.6, "p95": 407.5, "p99": 407.5, "p999": 407.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 323, "max": 446, "count": 70, "mean": 368, "p50": 361.5, "median": 361.5, "p75": 376.2, "p90": 391.6, "p95": 407.5, "p99": 407.5, "p999": 407.5}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 100, "http.codes.200": 99, "http.responses": 99, "http.downloaded_bytes": 415207, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 99, "vusers.created_by_name.Baseline - Scaling Test": 100, "vusers.created": 100, "errors.Undefined function \"generateBaselineFilters\"": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753660830019, "firstHistogramAt": 1753660830019, "lastCounterAt": 1753660839996, "lastHistogramAt": 1753660839778, "firstMetricAt": 1753660830019, "lastMetricAt": 1753660839996, "period": "1753660830000", "summaries": {"vusers.session_length": {"min": 1328.1, "max": 2432.7, "count": 100, "mean": 1532, "p50": 1408.4, "median": 1408.4, "p75": 1495.5, "p90": 2018.7, "p95": 2186.8, "p99": 2369, "p999": 2369}, "http.response_time": {"min": 323, "max": 1420, "count": 99, "mean": 542.5, "p50": 399.5, "median": 399.5, "p75": 620.3, "p90": 1002.4, "p95": 1153.1, "p99": 1353.1, "p999": 1353.1}, "http.response_time.2xx": {"min": 323, "max": 1420, "count": 99, "mean": 542.5, "p50": 399.5, "median": 399.5, "p75": 620.3, "p90": 1002.4, "p95": 1153.1, "p99": 1353.1, "p999": 1353.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 323, "max": 1420, "count": 99, "mean": 542.5, "p50": 399.5, "median": 399.5, "p75": 620.3, "p90": 1002.4, "p95": 1153.1, "p99": 1353.1, "p999": 1353.1}}, "histograms": {"vusers.session_length": {"min": 1328.1, "max": 2432.7, "count": 100, "mean": 1532, "p50": 1408.4, "median": 1408.4, "p75": 1495.5, "p90": 2018.7, "p95": 2186.8, "p99": 2369, "p999": 2369}, "http.response_time": {"min": 323, "max": 1420, "count": 99, "mean": 542.5, "p50": 399.5, "median": 399.5, "p75": 620.3, "p90": 1002.4, "p95": 1153.1, "p99": 1353.1, "p999": 1353.1}, "http.response_time.2xx": {"min": 323, "max": 1420, "count": 99, "mean": 542.5, "p50": 399.5, "median": 399.5, "p75": 620.3, "p90": 1002.4, "p95": 1153.1, "p99": 1353.1, "p999": 1353.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 323, "max": 1420, "count": 99, "mean": 542.5, "p50": 399.5, "median": 399.5, "p75": 620.3, "p90": 1002.4, "p95": 1153.1, "p99": 1353.1, "p999": 1353.1}}}, {"counters": {"http.codes.200": 101, "http.responses": 101, "http.downloaded_bytes": 423594, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 101, "vusers.failed": 0, "vusers.completed": 96, "vusers.created_by_name.Baseline - Scaling Test": 100, "vusers.created": 100, "errors.Undefined function \"generateBaselineFilters\"": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753660840057, "firstHistogramAt": 1753660840068, "lastCounterAt": 1753660849996, "lastHistogramAt": 1753660849974, "firstMetricAt": 1753660840057, "lastMetricAt": 1753660849996, "period": "1753660840000", "summaries": {"http.response_time": {"min": 350, "max": 2666, "count": 101, "mean": 808.2, "p50": 468.8, "median": 468.8, "p75": 1022.7, "p90": 1901.1, "p95": 2143.5, "p99": 2618.1, "p999": 2618.1}, "http.response_time.2xx": {"min": 350, "max": 2666, "count": 101, "mean": 808.2, "p50": 468.8, "median": 468.8, "p75": 1022.7, "p90": 1901.1, "p95": 2143.5, "p99": 2618.1, "p999": 2618.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 350, "max": 2666, "count": 101, "mean": 808.2, "p50": 468.8, "median": 468.8, "p75": 1022.7, "p90": 1901.1, "p95": 2143.5, "p99": 2618.1, "p999": 2618.1}, "vusers.session_length": {"min": 1360.8, "max": 3677, "count": 96, "mean": 1821.7, "p50": 1465.9, "median": 1465.9, "p75": 2059.5, "p90": 2893.5, "p95": 3134.5, "p99": 3605.5, "p999": 3605.5}}, "histograms": {"http.response_time": {"min": 350, "max": 2666, "count": 101, "mean": 808.2, "p50": 468.8, "median": 468.8, "p75": 1022.7, "p90": 1901.1, "p95": 2143.5, "p99": 2618.1, "p999": 2618.1}, "http.response_time.2xx": {"min": 350, "max": 2666, "count": 101, "mean": 808.2, "p50": 468.8, "median": 468.8, "p75": 1022.7, "p90": 1901.1, "p95": 2143.5, "p99": 2618.1, "p999": 2618.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 350, "max": 2666, "count": 101, "mean": 808.2, "p50": 468.8, "median": 468.8, "p75": 1022.7, "p90": 1901.1, "p95": 2143.5, "p99": 2618.1, "p999": 2618.1}, "vusers.session_length": {"min": 1360.8, "max": 3677, "count": 96, "mean": 1821.7, "p50": 1465.9, "median": 1465.9, "p75": 2059.5, "p90": 2893.5, "p95": 3134.5, "p99": 3605.5, "p999": 3605.5}}}, {"counters": {"http.codes.200": 100, "http.responses": 100, "http.downloaded_bytes": 419400, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 100, "vusers.failed": 0, "vusers.completed": 104, "vusers.created_by_name.Baseline - Scaling Test": 100, "vusers.created": 100, "errors.Undefined function \"generateBaselineFilters\"": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753660850032, "firstHistogramAt": 1753660850032, "lastCounterAt": 1753660859996, "lastHistogramAt": 1753660859732, "firstMetricAt": 1753660850032, "lastMetricAt": 1753660859996, "period": "1753660850000", "summaries": {"http.response_time": {"min": 354, "max": 1056, "count": 100, "mean": 491.2, "p50": 478.3, "median": 478.3, "p75": 550.1, "p90": 608, "p95": 645.6, "p99": 1043.3, "p999": 1043.3}, "http.response_time.2xx": {"min": 354, "max": 1056, "count": 100, "mean": 491.2, "p50": 478.3, "median": 478.3, "p75": 550.1, "p90": 608, "p95": 645.6, "p99": 1043.3, "p999": 1043.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 354, "max": 1056, "count": 100, "mean": 491.2, "p50": 478.3, "median": 478.3, "p75": 550.1, "p90": 608, "p95": 645.6, "p99": 1043.3, "p999": 1043.3}, "vusers.session_length": {"min": 1359.3, "max": 2065.2, "count": 104, "mean": 1523.7, "p50": 1495.5, "median": 1495.5, "p75": 1587.9, "p90": 1652.8, "p95": 1901.1, "p99": 1978.7, "p999": 2059.5}}, "histograms": {"http.response_time": {"min": 354, "max": 1056, "count": 100, "mean": 491.2, "p50": 478.3, "median": 478.3, "p75": 550.1, "p90": 608, "p95": 645.6, "p99": 1043.3, "p999": 1043.3}, "http.response_time.2xx": {"min": 354, "max": 1056, "count": 100, "mean": 491.2, "p50": 478.3, "median": 478.3, "p75": 550.1, "p90": 608, "p95": 645.6, "p99": 1043.3, "p999": 1043.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 354, "max": 1056, "count": 100, "mean": 491.2, "p50": 478.3, "median": 478.3, "p75": 550.1, "p90": 608, "p95": 645.6, "p99": 1043.3, "p999": 1043.3}, "vusers.session_length": {"min": 1359.3, "max": 2065.2, "count": 104, "mean": 1523.7, "p50": 1495.5, "median": 1495.5, "p75": 1587.9, "p90": 1652.8, "p95": 1901.1, "p99": 1978.7, "p999": 2059.5}}}, {"counters": {"http.codes.200": 100, "http.responses": 100, "http.downloaded_bytes": 419404, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 100, "vusers.failed": 0, "vusers.completed": 96, "vusers.created_by_name.Baseline - Scaling Test": 100, "vusers.created": 100, "errors.Undefined function \"generateBaselineFilters\"": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753660860053, "firstHistogramAt": 1753660860053, "lastCounterAt": 1753660869996, "lastHistogramAt": 1753660869941, "firstMetricAt": 1753660860053, "lastMetricAt": 1753660869996, "period": "1753660860000", "summaries": {"http.response_time": {"min": 360, "max": 2016, "count": 100, "mean": 856.6, "p50": 632.8, "median": 632.8, "p75": 1153.1, "p90": 1755, "p95": 1826.6, "p99": 1939.5, "p999": 1939.5}, "http.response_time.2xx": {"min": 360, "max": 2016, "count": 100, "mean": 856.6, "p50": 632.8, "median": 632.8, "p75": 1153.1, "p90": 1755, "p95": 1826.6, "p99": 1939.5, "p999": 1939.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 360, "max": 2016, "count": 100, "mean": 856.6, "p50": 632.8, "median": 632.8, "p75": 1153.1, "p90": 1755, "p95": 1826.6, "p99": 1939.5, "p999": 1939.5}, "vusers.session_length": {"min": 1371.8, "max": 3024.9, "count": 96, "mean": 1869.2, "p50": 1652.8, "median": 1652.8, "p75": 2186.8, "p90": 2780, "p95": 2836.2, "p99": 2951.9, "p999": 2951.9}}, "histograms": {"http.response_time": {"min": 360, "max": 2016, "count": 100, "mean": 856.6, "p50": 632.8, "median": 632.8, "p75": 1153.1, "p90": 1755, "p95": 1826.6, "p99": 1939.5, "p999": 1939.5}, "http.response_time.2xx": {"min": 360, "max": 2016, "count": 100, "mean": 856.6, "p50": 632.8, "median": 632.8, "p75": 1153.1, "p90": 1755, "p95": 1826.6, "p99": 1939.5, "p999": 1939.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 360, "max": 2016, "count": 100, "mean": 856.6, "p50": 632.8, "median": 632.8, "p75": 1153.1, "p90": 1755, "p95": 1826.6, "p99": 1939.5, "p999": 1939.5}, "vusers.session_length": {"min": 1371.8, "max": 3024.9, "count": 96, "mean": 1869.2, "p50": 1652.8, "median": 1652.8, "p75": 2186.8, "p90": 2780, "p95": 2836.2, "p99": 2951.9, "p999": 2951.9}}}, {"counters": {"http.codes.200": 89, "http.responses": 89, "http.downloaded_bytes": 373266, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 89, "vusers.failed": 0, "vusers.completed": 103, "vusers.created_by_name.Baseline - Scaling Test": 100, "vusers.created": 100, "errors.Undefined function \"generateBaselineFilters\"": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753660870017, "firstHistogramAt": 1753660870017, "lastCounterAt": 1753660879996, "lastHistogramAt": 1753660879655, "firstMetricAt": 1753660870017, "lastMetricAt": 1753660879996, "period": "1753660870000", "summaries": {"http.response_time": {"min": 333, "max": 1165, "count": 89, "mean": 523.4, "p50": 432.7, "median": 432.7, "p75": 620.3, "p90": 788.5, "p95": 871.5, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 333, "max": 1165, "count": 89, "mean": 523.4, "p50": 432.7, "median": 432.7, "p75": 620.3, "p90": 788.5, "p95": 871.5, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 333, "max": 1165, "count": 89, "mean": 523.4, "p50": 432.7, "median": 432.7, "p75": 620.3, "p90": 788.5, "p95": 871.5, "p99": 1107.9, "p999": 1107.9}, "vusers.session_length": {"min": 1339.7, "max": 2176.9, "count": 103, "mean": 1543.4, "p50": 1465.9, "median": 1465.9, "p75": 1620, "p90": 1826.6, "p95": 1901.1, "p99": 2059.5, "p999": 2143.5}}, "histograms": {"http.response_time": {"min": 333, "max": 1165, "count": 89, "mean": 523.4, "p50": 432.7, "median": 432.7, "p75": 620.3, "p90": 788.5, "p95": 871.5, "p99": 1107.9, "p999": 1107.9}, "http.response_time.2xx": {"min": 333, "max": 1165, "count": 89, "mean": 523.4, "p50": 432.7, "median": 432.7, "p75": 620.3, "p90": 788.5, "p95": 871.5, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 333, "max": 1165, "count": 89, "mean": 523.4, "p50": 432.7, "median": 432.7, "p75": 620.3, "p90": 788.5, "p95": 871.5, "p99": 1107.9, "p999": 1107.9}, "vusers.session_length": {"min": 1339.7, "max": 2176.9, "count": 103, "mean": 1543.4, "p50": 1465.9, "median": 1465.9, "p75": 1620, "p90": 1826.6, "p95": 1901.1, "p99": 2059.5, "p999": 2143.5}}}, {"counters": {"http.codes.200": 123, "http.responses": 123, "http.downloaded_bytes": 515863, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 123, "vusers.created_by_name.Baseline - Scaling Test": 124, "vusers.created": 124, "errors.Undefined function \"generateBaselineFilters\"": 124, "http.requests": 124, "vusers.failed": 0, "vusers.completed": 117}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753660880031, "firstHistogramAt": 1753660880031, "lastCounterAt": 1753660889996, "lastHistogramAt": 1753660889959, "firstMetricAt": 1753660880031, "lastMetricAt": 1753660889996, "period": "1753660880000", "summaries": {"http.response_time": {"min": 354, "max": 2245, "count": 123, "mean": 753.9, "p50": 608, "median": 608, "p75": 772.9, "p90": 1495.5, "p95": 1790.4, "p99": 2186.8, "p999": 2231}, "http.response_time.2xx": {"min": 354, "max": 2245, "count": 123, "mean": 753.9, "p50": 608, "median": 608, "p75": 772.9, "p90": 1495.5, "p95": 1790.4, "p99": 2186.8, "p999": 2231}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 354, "max": 2245, "count": 123, "mean": 753.9, "p50": 608, "median": 608, "p75": 772.9, "p90": 1495.5, "p95": 1790.4, "p99": 2186.8, "p999": 2231}, "vusers.session_length": {"min": 1366.8, "max": 3250.8, "count": 117, "mean": 1774.7, "p50": 1620, "median": 1620, "p75": 1826.6, "p90": 2566.3, "p95": 2836.2, "p99": 3197.8, "p999": 3197.8}}, "histograms": {"http.response_time": {"min": 354, "max": 2245, "count": 123, "mean": 753.9, "p50": 608, "median": 608, "p75": 772.9, "p90": 1495.5, "p95": 1790.4, "p99": 2186.8, "p999": 2231}, "http.response_time.2xx": {"min": 354, "max": 2245, "count": 123, "mean": 753.9, "p50": 608, "median": 608, "p75": 772.9, "p90": 1495.5, "p95": 1790.4, "p99": 2186.8, "p999": 2231}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 354, "max": 2245, "count": 123, "mean": 753.9, "p50": 608, "median": 608, "p75": 772.9, "p90": 1495.5, "p95": 1790.4, "p99": 2186.8, "p999": 2231}, "vusers.session_length": {"min": 1366.8, "max": 3250.8, "count": 117, "mean": 1774.7, "p50": 1620, "median": 1620, "p75": 1826.6, "p90": 2566.3, "p95": 2836.2, "p99": 3197.8, "p999": 3197.8}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 123, "vusers.created_by_name.Baseline - Scaling Test": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 130, "http.responses": 130, "http.downloaded_bytes": 545224, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 130}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753660890024, "firstHistogramAt": 1753660890024, "lastCounterAt": 1753660899998, "lastHistogramAt": 1753660899998, "firstMetricAt": 1753660890024, "lastMetricAt": 1753660899998, "period": "1753660890000", "summaries": {"vusers.session_length": {"min": 1431.7, "max": 4230, "count": 123, "mean": 3164.2, "p50": 3197.8, "median": 3197.8, "p75": 3464.1, "p90": 3752.7, "p95": 3828.5, "p99": 3984.7, "p999": 3984.7}, "http.response_time": {"min": 1298, "max": 3226, "count": 130, "mean": 2273.6, "p50": 2231, "median": 2231, "p75": 2566.3, "p90": 2725, "p95": 2836.2, "p99": 3011.6, "p999": 3011.6}, "http.response_time.2xx": {"min": 1298, "max": 3226, "count": 130, "mean": 2273.6, "p50": 2231, "median": 2231, "p75": 2566.3, "p90": 2725, "p95": 2836.2, "p99": 3011.6, "p999": 3011.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 1298, "max": 3226, "count": 130, "mean": 2273.6, "p50": 2231, "median": 2231, "p75": 2566.3, "p90": 2725, "p95": 2836.2, "p99": 3011.6, "p999": 3011.6}}, "histograms": {"vusers.session_length": {"min": 1431.7, "max": 4230, "count": 123, "mean": 3164.2, "p50": 3197.8, "median": 3197.8, "p75": 3464.1, "p90": 3752.7, "p95": 3828.5, "p99": 3984.7, "p999": 3984.7}, "http.response_time": {"min": 1298, "max": 3226, "count": 130, "mean": 2273.6, "p50": 2231, "median": 2231, "p75": 2566.3, "p90": 2725, "p95": 2836.2, "p99": 3011.6, "p999": 3011.6}, "http.response_time.2xx": {"min": 1298, "max": 3226, "count": 130, "mean": 2273.6, "p50": 2231, "median": 2231, "p75": 2566.3, "p90": 2725, "p95": 2836.2, "p99": 3011.6, "p999": 3011.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 1298, "max": 3226, "count": 130, "mean": 2273.6, "p50": 2231, "median": 2231, "p75": 2566.3, "p90": 2725, "p95": 2836.2, "p99": 3011.6, "p999": 3011.6}}}, {"counters": {"http.codes.200": 102, "http.responses": 102, "http.downloaded_bytes": 427802, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 102, "vusers.created_by_name.Baseline - Scaling Test": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "vusers.failed": 0, "vusers.completed": 98}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753660900015, "firstHistogramAt": 1753660900015, "lastCounterAt": 1753660909997, "lastHistogramAt": 1753660909956, "firstMetricAt": 1753660900015, "lastMetricAt": 1753660909997, "period": "1753660900000", "summaries": {"http.response_time": {"min": 2304, "max": 6836, "count": 102, "mean": 4231.5, "p50": 4316.6, "median": 4316.6, "p75": 5598.4, "p90": 6064.7, "p95": 6187.2, "p99": 6569.8, "p999": 6569.8}, "http.response_time.2xx": {"min": 2304, "max": 6836, "count": 102, "mean": 4231.5, "p50": 4316.6, "median": 4316.6, "p75": 5598.4, "p90": 6064.7, "p95": 6187.2, "p99": 6569.8, "p999": 6569.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 2304, "max": 6836, "count": 102, "mean": 4231.5, "p50": 4316.6, "median": 4316.6, "p75": 5598.4, "p90": 6064.7, "p95": 6187.2, "p99": 6569.8, "p999": 6569.8}, "vusers.session_length": {"min": 3310.1, "max": 7100, "count": 98, "mean": 4730.9, "p50": 3752.7, "median": 3752.7, "p75": 5711.5, "p90": 6838, "p95": 6976.1, "p99": 7117, "p999": 7117}}, "histograms": {"http.response_time": {"min": 2304, "max": 6836, "count": 102, "mean": 4231.5, "p50": 4316.6, "median": 4316.6, "p75": 5598.4, "p90": 6064.7, "p95": 6187.2, "p99": 6569.8, "p999": 6569.8}, "http.response_time.2xx": {"min": 2304, "max": 6836, "count": 102, "mean": 4231.5, "p50": 4316.6, "median": 4316.6, "p75": 5598.4, "p90": 6064.7, "p95": 6187.2, "p99": 6569.8, "p999": 6569.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 2304, "max": 6836, "count": 102, "mean": 4231.5, "p50": 4316.6, "median": 4316.6, "p75": 5598.4, "p90": 6064.7, "p95": 6187.2, "p99": 6569.8, "p999": 6569.8}, "vusers.session_length": {"min": 3310.1, "max": 7100, "count": 98, "mean": 4730.9, "p50": 3752.7, "median": 3752.7, "p75": 5711.5, "p90": 6838, "p95": 6976.1, "p99": 7117, "p999": 7117}}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 124, "http.responses": 124, "http.downloaded_bytes": 520066, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 124, "vusers.failed": 0, "vusers.completed": 128}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753660910058, "firstHistogramAt": 1753660910061, "lastCounterAt": 1753660919996, "lastHistogramAt": 1753660919921, "firstMetricAt": 1753660910058, "lastMetricAt": 1753660919996, "period": "1753660910000", "summaries": {"http.response_time": {"min": 5291, "max": 7808, "count": 124, "mean": 6535.1, "p50": 6569.8, "median": 6569.8, "p75": 6838, "p90": 7260.8, "p95": 7407.5, "p99": 7709.8, "p999": 7709.8}, "http.response_time.2xx": {"min": 5291, "max": 7808, "count": 124, "mean": 6535.1, "p50": 6569.8, "median": 6569.8, "p75": 6838, "p90": 7260.8, "p95": 7407.5, "p99": 7709.8, "p999": 7709.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 5291, "max": 7808, "count": 124, "mean": 6535.1, "p50": 6569.8, "median": 6569.8, "p75": 6838, "p90": 7260.8, "p95": 7407.5, "p99": 7709.8, "p999": 7709.8}, "vusers.session_length": {"min": 6081.6, "max": 8744.8, "count": 128, "mean": 7394.6, "p50": 7407.5, "median": 7407.5, "p75": 7709.8, "p90": 8024.5, "p95": 8352, "p99": 8352, "p999": 8520.7}}, "histograms": {"http.response_time": {"min": 5291, "max": 7808, "count": 124, "mean": 6535.1, "p50": 6569.8, "median": 6569.8, "p75": 6838, "p90": 7260.8, "p95": 7407.5, "p99": 7709.8, "p999": 7709.8}, "http.response_time.2xx": {"min": 5291, "max": 7808, "count": 124, "mean": 6535.1, "p50": 6569.8, "median": 6569.8, "p75": 6838, "p90": 7260.8, "p95": 7407.5, "p99": 7709.8, "p999": 7709.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 5291, "max": 7808, "count": 124, "mean": 6535.1, "p50": 6569.8, "median": 6569.8, "p75": 6838, "p90": 7260.8, "p95": 7407.5, "p99": 7709.8, "p999": 7709.8}, "vusers.session_length": {"min": 6081.6, "max": 8744.8, "count": 128, "mean": 7394.6, "p50": 7407.5, "median": 7407.5, "p75": 7709.8, "p90": 8024.5, "p95": 8352, "p99": 8352, "p999": 8520.7}}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150, "http.codes.200": 118, "http.responses": 118, "http.downloaded_bytes": 494906, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 118, "vusers.failed": 0, "vusers.completed": 120}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753660920058, "firstHistogramAt": 1753660920087, "lastCounterAt": 1753660929996, "lastHistogramAt": 1753660929861, "firstMetricAt": 1753660920058, "lastMetricAt": 1753660929996, "period": "1753660920000", "summaries": {"http.response_time": {"min": 7134, "max": 9892, "count": 118, "mean": 8598.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 7134, "max": 9892, "count": 118, "mean": 8598.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 7134, "max": 9892, "count": 118, "mean": 8598.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 7638.8, "max": 10770.6, "count": 120, "mean": 9386.1, "p50": 9416.8, "median": 9416.8, "p75": 9999.2, "p90": 10407.3, "p95": 10407.3, "p99": 10617.5, "p999": 10617.5}}, "histograms": {"http.response_time": {"min": 7134, "max": 9892, "count": 118, "mean": 8598.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 7134, "max": 9892, "count": 118, "mean": 8598.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 7134, "max": 9892, "count": 118, "mean": 8598.7, "p50": 8692.8, "median": 8692.8, "p75": 9230.4, "p90": 9416.8, "p95": 9607.1, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 7638.8, "max": 10770.6, "count": 120, "mean": 9386.1, "p50": 9416.8, "median": 9416.8, "p75": 9999.2, "p90": 10407.3, "p95": 10407.3, "p99": 10617.5, "p999": 10617.5}}}, {"counters": {"http.codes.200": 94, "http.responses": 94, "http.downloaded_bytes": 394245, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 94, "vusers.failed": 0, "vusers.completed": 92, "vusers.created_by_name.Baseline - Scaling Test": 150, "vusers.created": 150, "errors.Undefined function \"generateBaselineFilters\"": 150, "http.requests": 150}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1753660930021, "firstHistogramAt": 1753660930021, "lastCounterAt": 1753660939997, "lastHistogramAt": 1753660939973, "firstMetricAt": 1753660930021, "lastMetricAt": 1753660939997, "period": "1753660930000", "summaries": {"http.response_time": {"min": 9270, "max": 14145, "count": 94, "mean": 11263.6, "p50": 11274.1, "median": 11274.1, "p75": 11971.2, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}, "http.response_time.2xx": {"min": 9270, "max": 14145, "count": 94, "mean": 11263.6, "p50": 11274.1, "median": 11274.1, "p75": 11971.2, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 9270, "max": 14145, "count": 94, "mean": 11263.6, "p50": 11274.1, "median": 11274.1, "p75": 11971.2, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}, "vusers.session_length": {"min": 10194.7, "max": 13909.1, "count": 92, "mean": 11757.8, "p50": 11971.2, "median": 11971.2, "p75": 12459.8, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}}, "histograms": {"http.response_time": {"min": 9270, "max": 14145, "count": 94, "mean": 11263.6, "p50": 11274.1, "median": 11274.1, "p75": 11971.2, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}, "http.response_time.2xx": {"min": 9270, "max": 14145, "count": 94, "mean": 11263.6, "p50": 11274.1, "median": 11274.1, "p75": 11971.2, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 9270, "max": 14145, "count": 94, "mean": 11263.6, "p50": 11274.1, "median": 11274.1, "p75": 11971.2, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}, "vusers.session_length": {"min": 10194.7, "max": 13909.1, "count": 92, "mean": 11757.8, "p50": 11971.2, "median": 11971.2, "p75": 12459.8, "p90": 12968.3, "p95": 13230.3, "p99": 13770.3, "p999": 13770.3}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 90, "vusers.created_by_name.Baseline - Scaling Test": 175, "vusers.created": 175, "errors.Undefined function \"generateBaselineFilters\"": 175, "http.requests": 174, "http.codes.200": 96, "http.responses": 96, "http.downloaded_bytes": 402638, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 96}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753660940058, "firstHistogramAt": 1753660940098, "lastCounterAt": 1753660949997, "lastHistogramAt": 1753660949975, "firstMetricAt": 1753660940058, "lastMetricAt": 1753660949997, "period": "1753660940000", "summaries": {"vusers.session_length": {"min": 13931.3, "max": 18539.5, "count": 90, "mean": 15890.8, "p50": 15839.7, "median": 15839.7, "p75": 16819.2, "p90": 17158.9, "p95": 17158.9, "p99": 18588.1, "p999": 18588.1}, "http.response_time": {"min": 13316, "max": 18392, "count": 96, "mean": 15540.2, "p50": 15526, "median": 15526, "p75": 16159.7, "p90": 17505.6, "p95": 17505.6, "p99": 18220, "p999": 18220}, "http.response_time.2xx": {"min": 13316, "max": 18392, "count": 96, "mean": 15540.2, "p50": 15526, "median": 15526, "p75": 16159.7, "p90": 17505.6, "p95": 17505.6, "p99": 18220, "p999": 18220}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 13316, "max": 18392, "count": 96, "mean": 15540.2, "p50": 15526, "median": 15526, "p75": 16159.7, "p90": 17505.6, "p95": 17505.6, "p99": 18220, "p999": 18220}}, "histograms": {"vusers.session_length": {"min": 13931.3, "max": 18539.5, "count": 90, "mean": 15890.8, "p50": 15839.7, "median": 15839.7, "p75": 16819.2, "p90": 17158.9, "p95": 17158.9, "p99": 18588.1, "p999": 18588.1}, "http.response_time": {"min": 13316, "max": 18392, "count": 96, "mean": 15540.2, "p50": 15526, "median": 15526, "p75": 16159.7, "p90": 17505.6, "p95": 17505.6, "p99": 18220, "p999": 18220}, "http.response_time.2xx": {"min": 13316, "max": 18392, "count": 96, "mean": 15540.2, "p50": 15526, "median": 15526, "p75": 16159.7, "p90": 17505.6, "p95": 17505.6, "p99": 18220, "p999": 18220}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 13316, "max": 18392, "count": 96, "mean": 15540.2, "p50": 15526, "median": 15526, "p75": 16159.7, "p90": 17505.6, "p95": 17505.6, "p99": 18220, "p999": 18220}}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 200, "vusers.created": 200, "errors.Undefined function \"generateBaselineFilters\"": 200, "http.requests": 201, "http.codes.200": 86, "http.responses": 86, "http.downloaded_bytes": 360688, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 86, "vusers.failed": 21, "vusers.completed": 101, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 21, "errors.ETIMEDOUT": 21}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753660950000, "firstHistogramAt": 1753660950092, "lastCounterAt": 1753660959998, "lastHistogramAt": 1753660959943, "firstMetricAt": 1753660950000, "lastMetricAt": 1753660959998, "period": "1753660950000", "summaries": {"http.response_time": {"min": 16681, "max": 19757, "count": 86, "mean": 18359.3, "p50": 18588.1, "median": 18588.1, "p75": 19346.7, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "http.response_time.2xx": {"min": 16681, "max": 19757, "count": 86, "mean": 18359.3, "p50": 18588.1, "median": 18588.1, "p75": 19346.7, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 16681, "max": 19757, "count": 86, "mean": 18359.3, "p50": 18588.1, "median": 18588.1, "p75": 19346.7, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "vusers.session_length": {"min": 17222.8, "max": 20999.2, "count": 101, "mean": 19133.8, "p50": 18963.6, "median": 18963.6, "p75": 20136.3, "p90": 20543.1, "p95": 20543.1, "p99": 20958.1, "p999": 20958.1}}, "histograms": {"http.response_time": {"min": 16681, "max": 19757, "count": 86, "mean": 18359.3, "p50": 18588.1, "median": 18588.1, "p75": 19346.7, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "http.response_time.2xx": {"min": 16681, "max": 19757, "count": 86, "mean": 18359.3, "p50": 18588.1, "median": 18588.1, "p75": 19346.7, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 16681, "max": 19757, "count": 86, "mean": 18359.3, "p50": 18588.1, "median": 18588.1, "p75": 19346.7, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "vusers.session_length": {"min": 17222.8, "max": 20999.2, "count": 101, "mean": 19133.8, "p50": 18963.6, "median": 18963.6, "p75": 20136.3, "p90": 20543.1, "p95": 20543.1, "p99": 20958.1, "p999": 20958.1}}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 200, "vusers.created": 200, "errors.Undefined function \"generateBaselineFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 162, "errors.ETIMEDOUT": 162, "vusers.failed": 162, "http.codes.200": 9, "http.responses": 9, "http.downloaded_bytes": 37750, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.codes.200": 9, "vusers.completed": 13}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753660960048, "firstHistogramAt": 1753660960048, "lastCounterAt": 1753660969997, "lastHistogramAt": 1753660962953, "firstMetricAt": 1753660960048, "lastMetricAt": 1753660969997, "period": "1753660960000", "summaries": {"http.response_time": {"min": 19565, "max": 19998, "count": 9, "mean": 19768.9, "p50": 19737.6, "median": 19737.6, "p75": 19737.6, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "http.response_time.2xx": {"min": 19565, "max": 19998, "count": 9, "mean": 19768.9, "p50": 19737.6, "median": 19737.6, "p75": 19737.6, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 19565, "max": 19998, "count": 9, "mean": 19768.9, "p50": 19737.6, "median": 19737.6, "p75": 19737.6, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "vusers.session_length": {"min": 20229.7, "max": 21000.8, "count": 13, "mean": 20672.1, "p50": 20543.1, "median": 20543.1, "p75": 20958.1, "p90": 20958.1, "p95": 20958.1, "p99": 20958.1, "p999": 20958.1}}, "histograms": {"http.response_time": {"min": 19565, "max": 19998, "count": 9, "mean": 19768.9, "p50": 19737.6, "median": 19737.6, "p75": 19737.6, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "http.response_time.2xx": {"min": 19565, "max": 19998, "count": 9, "mean": 19768.9, "p50": 19737.6, "median": 19737.6, "p75": 19737.6, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Scaling": {"min": 19565, "max": 19998, "count": 9, "mean": 19768.9, "p50": 19737.6, "median": 19737.6, "p75": 19737.6, "p90": 19737.6, "p95": 19737.6, "p99": 19737.6, "p999": 19737.6}, "vusers.session_length": {"min": 20229.7, "max": 21000.8, "count": 13, "mean": 20672.1, "p50": 20543.1, "median": 20543.1, "p75": 20958.1, "p90": 20958.1, "p95": 20958.1, "p99": 20958.1, "p999": 20958.1}}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 200, "vusers.created": 200, "errors.Undefined function \"generateBaselineFilters\"": 200, "http.requests": 199, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753660970001, "lastCounterAt": 1753660979998, "firstMetricAt": 1753660970001, "lastMetricAt": 1753660979998, "period": "1753660970000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 200, "vusers.created": 200, "errors.Undefined function \"generateBaselineFilters\"": 200, "http.requests": 201, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753660980000, "lastCounterAt": 1753660989999, "firstMetricAt": 1753660980000, "lastMetricAt": 1753660989999, "period": "1753660980000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 200, "vusers.created": 200, "errors.Undefined function \"generateBaselineFilters\"": 200, "http.requests": 200, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753660990000, "lastCounterAt": 1753660999997, "firstMetricAt": 1753660990000, "lastMetricAt": 1753660999997, "period": "1753660990000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 130, "vusers.created": 130, "errors.Undefined function \"generateBaselineFilters\"": 130, "http.requests": 130, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753661000013, "lastCounterAt": 1753661009938, "firstMetricAt": 1753661000013, "lastMetricAt": 1753661009938, "period": "1753661000000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 200, "vusers.failed": 200, "vusers.created_by_name.Baseline - Scaling Test": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753661010010, "lastCounterAt": 1753661019937, "firstMetricAt": 1753661010010, "lastMetricAt": 1753661019937, "period": "1753661010000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 131, "errors.ETIMEDOUT": 131, "vusers.failed": 131, "vusers.created_by_name.Baseline - Scaling Test": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753661020012, "lastCounterAt": 1753661029937, "firstMetricAt": 1753661020012, "lastMetricAt": 1753661029937, "period": "1753661020000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Baseline - Scaling Test": 25, "vusers.created": 25, "errors.Undefined function \"generateBaselineFilters\"": 25, "http.requests": 25, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753661030692, "lastCounterAt": 1753661039938, "firstMetricAt": 1753661030692, "lastMetricAt": 1753661039938, "period": "1753661030000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 50, "vusers.failed": 50}, "rates": {}, "firstCounterAt": 1753661040702, "lastCounterAt": 1753661049951, "firstMetricAt": 1753661040702, "lastMetricAt": 1753661049951, "period": "1753661040000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots-fast - Scaling.errors.ETIMEDOUT": 25, "errors.ETIMEDOUT": 25, "vusers.failed": 25}, "rates": {}, "firstCounterAt": 1753661050696, "lastCounterAt": 1753661054943, "firstMetricAt": 1753661050696, "lastMetricAt": 1753661054943, "period": "1753661050000", "summaries": {}, "histograms": {}}]}