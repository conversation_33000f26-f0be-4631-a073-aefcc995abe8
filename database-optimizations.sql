-- COMPREHENSIVE DATABASE OPTIMIZATIONS FOR MUGSHOTS API
-- Based on actual schema and query patterns from native service
-- FOR SUPABASE PRO PLAN - Targeting 100-300ms response times

-- =====================================================
-- CRITICAL INDEXES FOR CORE QUERY PATTERNS
-- =====================================================

-- 1. PERFECT INDEX for newest sort (your most common query)
-- Matches: ORDER BY "dateOfBooking" DESC, created_at DESC, id DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_newest_perfect
ON public.mugshots ("dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC);

-- 2. OPTIMIZED INDEX for state + newest sort combination
-- Matches: WHERE "stateOfBooking" = ? ORDER BY "dateOfBooking" DESC, created_at DESC, id DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_state_newest
ON public.mugshots ("stateOfBooking", "dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC)
WHERE "stateOfBooking" IS NOT NULL;

-- 3. OPTIMIZED INDEX for county + newest sort combination
-- Matches: WHERE "countyOfBooking" = ? ORDER BY "dateOfBooking" DESC, created_at DESC, id DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_county_newest
ON public.mugshots ("countyOfBooking", "dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC)
WHERE "countyOfBooking" IS NOT NULL;

-- 4. OPTIMIZED INDEX for state + county + newest sort
-- Matches: WHERE "stateOfBooking" = ? AND "countyOfBooking" = ? ORDER BY "dateOfBooking" DESC, created_at DESC, id DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_location_newest
ON public.mugshots ("stateOfBooking", "countyOfBooking", "dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC)
WHERE "stateOfBooking" IS NOT NULL AND "countyOfBooking" IS NOT NULL;

-- =====================================================
-- INDEXES FOR DATE RANGE FILTERING
-- =====================================================

-- 5. DATE RANGE + newest sort optimization
-- Matches: WHERE "dateOfBooking" BETWEEN ? AND ? ORDER BY "dateOfBooking" DESC, created_at DESC, id DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_daterange_newest
ON public.mugshots ("dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC)
WHERE "dateOfBooking" IS NOT NULL;

-- 6. STATE + DATE RANGE + newest sort
-- Matches: WHERE "stateOfBooking" = ? AND "dateOfBooking" BETWEEN ? AND ? ORDER BY...
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_state_daterange_newest
ON public.mugshots ("stateOfBooking", "dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC)
WHERE "stateOfBooking" IS NOT NULL AND "dateOfBooking" IS NOT NULL;

-- =====================================================
-- INDEXES FOR NAME SEARCHING
-- =====================================================

-- 7. ENHANCED NAME SEARCH with combined trigram index
-- Your existing gin_idx_firstname_trgm and gin_idx_lastname_trgm are good
-- Adding combined search optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_fullname_search
ON public.mugshots USING gin (("firstName" || ' ' || "lastName") gin_trgm_ops);

-- 8. NAME SEARCH + newest sort combination
-- For when users search names and want newest results
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_names_newest
ON public.mugshots ("firstName", "lastName", "dateOfBooking" DESC NULLS LAST, created_at DESC, id DESC);

-- =====================================================
-- INDEXES FOR TOP-RATED QUERIES (COMPLEX)
-- =====================================================

-- 9. RATINGS AGGREGATION optimization
-- For calculating average ratings efficiently (your existing idx_ratings_mugshot_performance is good)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ratings_mugshot_avg_calc
-- ON public.ratings (mugshot_id, rating);

-- 10. TOP-RATED mugshots identification (your existing idx_mugshots_rating_sort is good)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mugshots_for_toprated
-- ON public.mugshots (id, dateOfBooking, created_at, firstName, lastName);

-- =====================================================
-- INDEXES FOR TAG FILTERING (MOST COMPLEX)
-- =====================================================

-- 11. TAG FILTERING optimization
-- For finding mugshots with specific tags (your existing indexes are good)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tags_type_mugshot_fast
-- ON public.tags (tag_type, mugshot_id);

-- 12. MULTIPLE TAG INTERSECTION optimization (your existing idx_tags_mugshot_type is good)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tags_mugshot_types
-- ON public.tags (mugshot_id, tag_type);

-- 13. TAG COUNTS per mugshot optimization (your existing idx_tags_mugshot_type_count is good)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tags_count_optimization
-- ON public.tags (mugshot_id, tag_type, user_id);

-- =====================================================
-- MAINTENANCE AND STATISTICS
-- =====================================================

-- 14. Update table statistics (CRITICAL for query planner)
ANALYZE public.mugshots;
ANALYZE public.ratings;
ANALYZE public.tags;

-- 15. Check autovacuum settings
SELECT
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE tablename IN ('mugshots', 'ratings', 'tags');

-- =====================================================
-- PERFORMANCE MONITORING QUERIES
-- =====================================================

-- Check which indexes are being used
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes
-- WHERE tablename IN ('mugshots', 'ratings', 'tags')
-- ORDER BY idx_scan DESC;

-- Check slow queries
-- SELECT query, mean_time, calls, total_time
-- FROM pg_stat_statements
-- WHERE query LIKE '%mugshots%' OR query LIKE '%ratings%' OR query LIKE '%tags%'
-- ORDER BY mean_time DESC LIMIT 10;

-- Check query execution plans
-- EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
-- SELECT id, firstName, lastName, imagePath, created_at, dateOfBooking, stateOfBooking, countyOfBooking
-- FROM mugshots
-- ORDER BY dateOfBooking DESC NULLS LAST, created_at DESC, id DESC
-- LIMIT 12 OFFSET 0;
