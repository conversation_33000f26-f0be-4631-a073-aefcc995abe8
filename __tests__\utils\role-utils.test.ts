import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getCurrentUser, requireAuth, requireAdmin, checkIsAdmin } from '@/lib/auth-context'
import { createClient } from '@/lib/supabase/server'
import { getProfile } from '@/lib/profile-utils'

// Mock the dependencies
vi.mock('@/lib/supabase/server')
vi.mock('@/lib/profile-utils')

const mockCreateClient = vi.mocked(createClient)
const mockGetProfile = vi.mocked(getProfile)

describe('Role Utilities', () => {
  let mockSupabase: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockSupabase = {
      auth: {
        getUser: vi.fn()
      }
    }
    
    mockCreateClient.mockResolvedValue(mockSupabase)
  })

  describe('getCurrentUser', () => {
    it('should return authenticated admin user session', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' }
      const mockProfile = { 
        id: 'profile-123',
        user_id: 'user-123', 
        role: 'admin', 
        full_name: 'Admin User' 
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockGetProfile.mockResolvedValue({
        success: true,
        profile: mockProfile
      })

      const result = await getCurrentUser()

      expect(result).toEqual({
        user: mockUser,
        profile: mockProfile,
        isAuthenticated: true,
        isAdmin: true
      })
    })

    it('should return authenticated regular user session', async () => {
      const mockUser = { id: 'user-456', email: '<EMAIL>' }
      const mockProfile = { 
        id: 'profile-456',
        user_id: 'user-456', 
        role: 'user', 
        full_name: 'Regular User' 
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockGetProfile.mockResolvedValue({
        success: true,
        profile: mockProfile
      })

      const result = await getCurrentUser()

      expect(result).toEqual({
        user: mockUser,
        profile: mockProfile,
        isAuthenticated: true,
        isAdmin: false
      })
    })

    it('should return unauthenticated session when no user', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const result = await getCurrentUser()

      expect(result).toEqual({
        user: null,
        profile: null,
        isAuthenticated: false,
        isAdmin: false
      })
    })

    it('should return unauthenticated session on auth error', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: new Error('Auth error')
      })

      const result = await getCurrentUser()

      expect(result).toEqual({
        user: null,
        profile: null,
        isAuthenticated: false,
        isAdmin: false
      })
    })

    it('should handle profile fetch failure gracefully', async () => {
      const mockUser = { id: 'user-789', email: '<EMAIL>' }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockGetProfile.mockResolvedValue({
        success: false,
        profile: undefined
      })

      const result = await getCurrentUser()

      expect(result).toEqual({
        user: mockUser,
        profile: null,
        isAuthenticated: true,
        isAdmin: false
      })
    })
  })

  describe('requireAuth', () => {
    it('should return user session when authenticated', async () => {
      const mockUser = { id: 'user-123', email: '<EMAIL>' }
      const mockProfile = { 
        id: 'profile-123',
        user_id: 'user-123', 
        role: 'user',
        full_name: 'Test User'
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockGetProfile.mockResolvedValue({
        success: true,
        profile: mockProfile
      })

      const result = await requireAuth()

      expect(result).toEqual({
        user: mockUser,
        profile: mockProfile,
        isAuthenticated: true,
        isAdmin: false
      })
    })

    it('should throw error when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      await expect(requireAuth()).rejects.toThrow('Authentication required')
    })
  })

  describe('requireAdmin', () => {
    it('should return admin session when user is admin', async () => {
      const mockUser = { id: 'admin-123', email: '<EMAIL>' }
      const mockProfile = { 
        id: 'profile-admin-123',
        user_id: 'admin-123', 
        role: 'admin',
        full_name: 'Admin User'
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockGetProfile.mockResolvedValue({
        success: true,
        profile: mockProfile
      })

      const result = await requireAdmin()

      expect(result).toEqual({
        user: mockUser,
        profile: mockProfile,
        isAuthenticated: true,
        isAdmin: true
      })
    })

    it('should throw error when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      await expect(requireAdmin()).rejects.toThrow('Authentication required')
    })

    it('should throw error when authenticated but not admin', async () => {
      const mockUser = { id: 'user-456', email: '<EMAIL>' }
      const mockProfile = { 
        id: 'profile-user-456',
        user_id: 'user-456', 
        role: 'user',
        full_name: 'Regular User'
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockGetProfile.mockResolvedValue({
        success: true,
        profile: mockProfile
      })

      await expect(requireAdmin()).rejects.toThrow('Admin access required')
    })
  })

  describe('checkIsAdmin', () => {
    it('should return true for admin profile', () => {
      const adminProfile = { role: 'admin', user_id: 'admin-123' }
      expect(checkIsAdmin(adminProfile)).toBe(true)
    })

    it('should return false for user profile', () => {
      const userProfile = { role: 'user', user_id: 'user-456' }
      expect(checkIsAdmin(userProfile)).toBe(false)
    })

    it('should return false for null profile', () => {
      expect(checkIsAdmin(null)).toBe(false)
    })

    it('should return false for undefined profile', () => {
      expect(checkIsAdmin(undefined)).toBe(false)
    })

    it('should return false for profile without role', () => {
      const profileWithoutRole = { user_id: 'user-789' }
      expect(checkIsAdmin(profileWithoutRole)).toBe(false)
    })
  })
}) 