import { useState, useEffect, useCallback } from 'react'
import { 
  isRatingEnabled, 
  getCountdownToRatingEnabled, 
  formatCountdown,
  type CountdownResult 
} from '@/lib/utils/timezone-utils'

interface UseRatingTimeGateProps {
  dateOfBooking: string | null
  state?: string | null
  county?: string | null
}

interface UseRatingTimeGateReturn {
  isEnabled: boolean
  countdown: CountdownResult
  formattedCountdown: string
  isLoading: boolean
  error: string | null
}

/**
 * Custom hook to manage rating time gate functionality
 * Provides real-time countdown and automatically updates when rating becomes enabled
 */
export function useRatingTimeGate({
  dateOfBooking,
  state,
  county
}: UseRatingTimeGateProps): UseRatingTimeGateReturn {
  const [isEnabled, setIsEnabled] = useState<boolean>(false)
  const [countdown, setCountdown] = useState<CountdownResult>({
    hours: 0,
    minutes: 0,
    seconds: 0,
    totalMilliseconds: 0,
    isExpired: false
  })
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Calculate current status
  const updateStatus = useCallback(() => {
    try {
      setError(null)
      
      if (!dateOfBooking) {
        setIsEnabled(false)
        setCountdown({
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalMilliseconds: 0,
          isExpired: true
        })
        setIsLoading(false)
        return
      }

      const enabled = isRatingEnabled(dateOfBooking, state, county)
      setIsEnabled(enabled)

      if (!enabled) {
        const countdownResult = getCountdownToRatingEnabled(dateOfBooking, state, county)
        setCountdown(countdownResult)
        
        // If countdown expired but rating still not enabled, there might be an issue
        if (countdownResult.isExpired) {
          setIsEnabled(true)
        }
      } else {
        setCountdown({
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalMilliseconds: 0,
          isExpired: true
        })
      }
      
      setIsLoading(false)
    } catch (err) {
      console.error('Error updating rating time gate status:', err)
      setError('Unable to determine rating availability')
      setIsLoading(false)
    }
  }, [dateOfBooking, state, county])

  // Initial calculation
  useEffect(() => {
    updateStatus()
  }, [updateStatus])

  // Set up interval for real-time updates
  useEffect(() => {
    if (isEnabled || !dateOfBooking) {
      return // No need for interval if rating is already enabled or no date
    }

    const interval = setInterval(() => {
      updateStatus()
    }, 1000) // Update every second

    return () => clearInterval(interval)
  }, [isEnabled, dateOfBooking, updateStatus])

  const formattedCountdown = formatCountdown(countdown)

  return {
    isEnabled,
    countdown,
    formattedCountdown,
    isLoading,
    error
  }
} 