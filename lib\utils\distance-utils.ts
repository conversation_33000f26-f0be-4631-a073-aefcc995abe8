/**
 * Distance Calculation Utilities
 * 
 * Provides geographic distance calculations using the Haversine formula
 * for finding the nearest available state based on coordinates.
 * 
 * The Haversine formula calculates the shortest distance between two points
 * on a sphere (Earth) given their latitude and longitude coordinates.
 */

// Coordinate interface
export interface Coordinates {
  latitude: number
  longitude: number
}

// Available state with coordinates (matches database view structure)
export interface AvailableStateWithCoordinates {
  state_name: string
  latitude: number
  longitude: number
  total_mugshots: number
}

// Distance calculation result
export interface StateDistance {
  state_name: string
  distance_miles: number
  distance_km: number
  coordinates: Coordinates
  total_mugshots: number
}

/**
 * Calculate distance between two points using Haversine formula
 * 
 * @param coord1 First coordinate point
 * @param coord2 Second coordinate point
 * @returns Distance in miles and kilometers
 */
export function calculateHaversineDistance(
  coord1: Coordinates, 
  coord2: Coordinates
): { miles: number; kilometers: number } {
  // Earth's radius in miles and kilometers
  const EARTH_RADIUS_MILES = 3959
  const EARTH_RADIUS_KM = 6371

  // Convert latitude and longitude from degrees to radians
  const lat1Rad = toRadians(coord1.latitude)
  const lon1Rad = toRadians(coord1.longitude)
  const lat2Rad = toRadians(coord2.latitude)
  const lon2Rad = toRadians(coord2.longitude)

  // Calculate differences
  const deltaLat = lat2Rad - lat1Rad
  const deltaLon = lon2Rad - lon1Rad

  // Haversine formula
  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
            Math.cos(lat1Rad) * Math.cos(lat2Rad) *
            Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  // Calculate distances
  const miles = EARTH_RADIUS_MILES * c
  const kilometers = EARTH_RADIUS_KM * c

  return {
    miles: Math.round(miles * 100) / 100, // Round to 2 decimal places
    kilometers: Math.round(kilometers * 100) / 100
  }
}

/**
 * Find the nearest available state from user coordinates
 * 
 * @param userCoordinates User's detected coordinates
 * @param availableStates Array of available states with coordinates
 * @returns Nearest state with distance information
 */
export function findNearestAvailableState(
  userCoordinates: Coordinates,
  availableStates: AvailableStateWithCoordinates[]
): StateDistance | null {
  if (!availableStates || availableStates.length === 0) {
    return null
  }

  let nearestState: StateDistance | null = null
  let minDistance = Infinity

  for (const state of availableStates) {
    // Validate state coordinates
    if (typeof state.latitude !== 'number' || typeof state.longitude !== 'number') {
      console.warn(`Invalid coordinates for state: ${state.state_name}`)
      continue
    }

    const stateCoordinates: Coordinates = {
      latitude: state.latitude,
      longitude: state.longitude
    }

    const distance = calculateHaversineDistance(userCoordinates, stateCoordinates)

    if (distance.miles < minDistance) {
      minDistance = distance.miles
      nearestState = {
        state_name: state.state_name,
        distance_miles: distance.miles,
        distance_km: distance.kilometers,
        coordinates: stateCoordinates,
        total_mugshots: state.total_mugshots
      }
    }
  }

  return nearestState
}

/**
 * Calculate distances to all available states and return sorted by distance
 * 
 * @param userCoordinates User's detected coordinates
 * @param availableStates Array of available states with coordinates
 * @returns Array of states sorted by distance (nearest first)
 */
export function calculateDistancesToAllStates(
  userCoordinates: Coordinates,
  availableStates: AvailableStateWithCoordinates[]
): StateDistance[] {
  if (!availableStates || availableStates.length === 0) {
    return []
  }

  const statesWithDistances: StateDistance[] = []

  for (const state of availableStates) {
    // Validate state coordinates
    if (typeof state.latitude !== 'number' || typeof state.longitude !== 'number') {
      console.warn(`Invalid coordinates for state: ${state.state_name}`)
      continue
    }

    const stateCoordinates: Coordinates = {
      latitude: state.latitude,
      longitude: state.longitude
    }

    const distance = calculateHaversineDistance(userCoordinates, stateCoordinates)

    statesWithDistances.push({
      state_name: state.state_name,
      distance_miles: distance.miles,
      distance_km: distance.kilometers,
      coordinates: stateCoordinates,
      total_mugshots: state.total_mugshots
    })
  }

  // Sort by distance (nearest first)
  return statesWithDistances.sort((a, b) => a.distance_miles - b.distance_miles)
}

/**
 * Validate coordinates
 * 
 * @param coordinates Coordinates to validate
 * @returns True if coordinates are valid
 */
export function validateCoordinates(coordinates: Coordinates): boolean {
  const { latitude, longitude } = coordinates

  // Check if values are numbers
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    return false
  }

  // Check if values are finite
  if (!isFinite(latitude) || !isFinite(longitude)) {
    return false
  }

  // Check latitude bounds (-90 to 90)
  if (latitude < -90 || latitude > 90) {
    return false
  }

  // Check longitude bounds (-180 to 180)
  if (longitude < -180 || longitude > 180) {
    return false
  }

  return true
}

/**
 * Convert degrees to radians
 * 
 * @param degrees Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * Convert radians to degrees
 * 
 * @param radians Angle in radians
 * @returns Angle in degrees
 */
export function toDegrees(radians: number): number {
  return radians * (180 / Math.PI)
}

/**
 * Get approximate coordinates for US geographic center (fallback)
 */
export function getUSGeographicCenter(): Coordinates {
  return {
    latitude: 39.8283, // Geographic center of contiguous US
    longitude: -98.5795
  }
}

/**
 * Check if coordinates are within US bounds (approximate)
 */
export function isWithinUSBounds(coordinates: Coordinates): boolean {
  const { latitude, longitude } = coordinates

  // Approximate US bounds (including Alaska and Hawaii)
  const US_BOUNDS = {
    north: 71.5, // Northern Alaska
    south: 18.9, // Southern Hawaii
    east: -66.9, // Eastern Maine
    west: -179.1 // Western Alaska (crosses date line)
  }

  // Handle Alaska crossing the date line
  const withinLatitude = latitude >= US_BOUNDS.south && latitude <= US_BOUNDS.north
  const withinLongitude = longitude >= US_BOUNDS.west || longitude <= US_BOUNDS.east

  return withinLatitude && withinLongitude
}
