import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { userId, state, county } = await request.json()

    if (!userId || !state || !county) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const supabase = await createClient()
    
    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify user can only update their own profile
    if (user.id !== userId) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    console.log('Updating/creating profile for user:', userId, 'with location:', { state, county })

    // First, try to check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (existingProfile && !fetchError) {
      // Profile exists - update it
      console.log('Profile exists, updating location for user:', userId)
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          state,
          county,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) {
        console.error('Location update error:', updateError)
        return NextResponse.json(
          { success: false, error: updateError.message },
          { status: 500 }
        )
      }

      console.log('Location updated successfully for user:', userId)
    } else {
      // Profile doesn't exist or fetch failed - create new profile
      console.log('Profile not found or fetch failed, creating new profile for user:', userId)
      
      // Get user metadata to extract any available info
      const metadata = user.user_metadata || {}
      const fullName = metadata.full_name || user.email?.split('@')[0] || 'User'
      
      console.log('Creating profile with data:', {
        userId,
        fullName,
        email: user.email,
        state,
        county,
        metadata
      })

      const { error: createError } = await supabase
        .from('profiles')
        .insert({
          user_id: userId,
          full_name: fullName,
          email: user.email || null,
          state,
          county,
          role: 'user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (createError) {
        console.error('Profile creation error:', createError)
        return NextResponse.json(
          { success: false, error: createError.message },
          { status: 500 }
        )
      }

      console.log('Profile created successfully for user:', userId)
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Location update/create exception:', error)
    return NextResponse.json(
      { success: false, error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
} 