// Client-side rating service that wraps API calls
import { 
  submitRating as apiSubmitRating, 
  getBulkRatingStatistics as apiGetBulkRatingStatistics,
  type RatingStatistics,
  type RatingSubmissionResult 
} from './api-client'

// Export types for backward compatibility
export type { RatingStatistics, RatingSubmissionResult }

export interface TimeGateResult {
  canRate: boolean
  message?: string
  timeUntilEnabled?: number
}

// Additional interfaces for missing functions
export interface UserRating {
  id: string
  mugshotId: number
  rating: number
  createdAt: string
}

export interface CategoryAverages {
  [category: string]: number
}

export interface OverallRating {
  totalRatings: number
  averageRating: number
  ratedMugshots: number
}

export interface MugshotWithRatings {
  id: number
  name: string
  averageRating: number
  totalRatings: number
}

// Wrapper functions that maintain the same interface for backward compatibility
export async function submitRating(mugshotId: number, rating: number): Promise<RatingSubmissionResult> {
  return apiSubmitRating({ mugshotId, rating })
}

// REMOVED: getRatingStatistics function - use /api/mugshots/[id] instead
// Rating statistics now come from the comprehensive mugshot API

export async function getBulkRatingStatistics(mugshotIds: string[]): Promise<Record<string, RatingStatistics>> {
  // Convert string IDs to numbers for the API call
  const numericIds = mugshotIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id))
  const result = await apiGetBulkRatingStatistics(numericIds)
  
  // Convert the numeric keys back to string keys for backward compatibility
  const stringKeyResult: Record<string, RatingStatistics> = {}
  Object.entries(result).forEach(([key, value]) => {
    stringKeyResult[key] = value
  })
  
  return stringKeyResult
}

// Time gate checking function (simplified version)
export async function checkTimeGate(mugshotId: number | string): Promise<TimeGateResult> {
  // Validate mugshotId
  if (!mugshotId || mugshotId.toString().trim() === '') {
    return {
      canRate: false,
      message: 'Invalid mugshot ID provided'
    }
  }
  
  // For now, assume rating is always enabled
  // This could be expanded to call an API endpoint if time-gating is needed
  return {
    canRate: true,
    message: 'Rating is enabled'
  }
}

// Stub functions for test compatibility - these will be implemented as APIs later
export async function getUserRatings(userId: string): Promise<UserRating[]> {
  // Validate userId
  if (!userId || userId.trim() === '') {
    console.warn('getUserRatings: Invalid user ID provided')
    return []
  }
  
  // TODO: Implement when /api/ratings/user/[userId] is created
  console.warn(`getUserRatings: API endpoint not yet implemented for user ${userId}`)
  return []
}

export async function getCategoryAverages(): Promise<CategoryAverages> {
  // Note: This function was removed as ratings are not category-based
  console.warn('getCategoryAverages: Function deprecated - ratings are not category-based')
  return {}
}

export async function getOverallRating(): Promise<OverallRating> {
  // TODO: Implement when /api/ratings/overall is created
  console.warn('getOverallRating: API endpoint not yet implemented')
  return {
    totalRatings: 0,
    averageRating: 0,
    ratedMugshots: 0
  }
}

export async function getMugshotsWithRatings(): Promise<MugshotWithRatings[]> {
  // TODO: Implement when /api/mugshots with ratings is created
  console.warn('getMugshotsWithRatings: API endpoint not yet implemented')
  return []
}

export async function deleteRating(ratingId: string): Promise<{ success: boolean; message: string }> {
  // Validate ratingId
  if (!ratingId || ratingId.trim() === '') {
    return {
      success: false,
      message: 'Invalid rating ID provided'
    }
  }
  
  // TODO: Implement when DELETE /api/ratings/[id] is created
  console.warn(`deleteRating: API endpoint not yet implemented for rating ${ratingId}`)
  return {
    success: false,
    message: 'Delete rating API not yet implemented'
  }
} 