import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { useAuthStore } from './auth-store'


// Filter state interface
export interface FilterState {
  // Basic filters
  searchTerm: string
  selectedState: string
  selectedCounty: string
  dateFrom: string
  dateTo: string
  categories: string[]
  tags: string[] // Selected tag display names for filtering (converted to TagType[] for API)
  
  // Display preferences
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
  perPage: number
  gridView: 'large' | 'compact'
  currentPage: number
  
  // Meta state
  isLoading: boolean
  isInitializing: boolean
  loadingFields: {
    search: boolean
    location: boolean
    date: boolean
    sort: boolean
    perPage: boolean
    gridView: boolean
    categories: boolean
  }
  error: string | null
  lastUpdate: number  // For change detection
  
  // Actions
  setSearchTerm: (term: string) => void
  setSelectedState: (state: string) => void
  setSelectedCounty: (county: string) => void
  setDateFrom: (date: string) => void
  setDateTo: (date: string) => void
  setCategories: (categories: string[]) => void
  addCategory: (category: string) => void
  removeCategory: (category: string) => void
  setTags: (tags: string[]) => void
  addTag: (tag: string) => void
  removeTag: (tag: string) => void
  setSortBy: (sort: 'newest' | 'top-rated' | 'most-viewed') => void
  setPerPage: (perPage: number) => void
  setGridView: (view: 'large' | 'compact') => void
  setCurrentPage: (page: number) => void
  setLoading: (loading: boolean) => void
  setInitializing: (initializing: boolean) => void
  setFieldLoading: (field: keyof FilterState['loadingFields'], loading: boolean) => void
  setError: (error: string | null) => void
  batchUpdate: (updates: Partial<FilterState>) => void  // For batch updates
  
  // Utility actions
  clearFilters: () => void
  resetPagination: () => void
  syncFromUrlParams: (params: URLSearchParams) => void
  toUrlParams: () => URLSearchParams
  
  // Location-aware actions
  initializeWithUserLocation: () => void
  canResetLocation: () => boolean
  resetToHomeLocation: () => void
  clearAllFilters: () => void
  
  // NEW: Enhanced loading management
  clearAllLoadingStates: () => void
  setDataLoadingStates: (loading: boolean) => void
}

// Default state
const defaultState = {
  searchTerm: '',
  selectedState: '',
  selectedCounty: '',
  dateFrom: '',
  dateTo: '',
  categories: [],
  tags: [],
  sortBy: 'newest' as const,
  perPage: 12,
  gridView: 'large' as const,
  currentPage: 1,
  isLoading: false,
  isInitializing: true,
  loadingFields: {
    search: false,
    location: false,
    date: false,
    sort: false,
    perPage: false,
    gridView: false,
    categories: false,
  },
  error: null,
  lastUpdate: Date.now(),
}

// Create the store
export const useFilterStore = create<FilterState>()(
  persist(
    (set, get) => ({
      ...defaultState,
      
      // Batch update function to prevent multiple re-renders
      batchUpdate: (updates) => {
        set(state => ({
          ...state,
          ...updates,
          lastUpdate: Date.now()
        }))
      },
      
      // Basic setters with optimized change detection
      setSearchTerm: (term) => {
        const current = get()
        if (current.searchTerm === term) return
        set({ 
          searchTerm: term, 
          currentPage: 1, 
          lastUpdate: Date.now() 
        })
      },
      
      setSelectedState: (state) => {
        const current = get()
        if (current.selectedState === state) return
        set({ 
          selectedState: state,
          selectedCounty: '', // Reset county when state changes
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setSelectedCounty: (county) => {
        const current = get()
        if (current.selectedCounty === county) return
        set({ 
          selectedCounty: county, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setDateFrom: (date) => {
        const current = get()
        if (current.dateFrom === date) return
        set({ 
          dateFrom: date, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setDateTo: (date) => {
        const current = get()
        if (current.dateTo === date) return
        set({ 
          dateTo: date, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setCategories: (categories) => {
        const current = get()
        // Safely handle undefined/null categories input
        const safeCategories = categories || []
        const currentCats = current.categories.slice().sort().join(',')
        const newCats = safeCategories.slice().sort().join(',')
        if (currentCats === newCats) return
        set({ 
          categories: safeCategories, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      addCategory: (category) => {
        const state = get()
        if (state.categories.includes(category)) return
        set({ 
          categories: [...state.categories, category],
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      removeCategory: (category) => {
        const state = get()
        const newCategories = state.categories.filter(c => c !== category)
        if (newCategories.length === state.categories.length) return
        set({ 
          categories: newCategories,
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },

      setTags: (tags) => {
        const current = get()
        // Safely handle undefined/null tags input
        const safeTags = tags || []
        const currentTags = current.tags.slice().sort().join(',')
        const newTags = safeTags.slice().sort().join(',')
        if (currentTags === newTags) return
        set({ 
          tags: safeTags, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      addTag: (tag) => {
        const state = get()
        if (state.tags.includes(tag)) return
        set({ 
          tags: [...state.tags, tag],
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      removeTag: (tag) => {
        const state = get()
        const newTags = state.tags.filter(t => t !== tag)
        if (newTags.length === state.tags.length) return
        set({ 
          tags: newTags,
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setSortBy: (sort) => {
        const current = get()
        if (current.sortBy === sort) return
        set({ 
          sortBy: sort, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setPerPage: (perPage) => {
        const current = get()
        if (current.perPage === perPage) return
        set({ 
          perPage, 
          currentPage: 1,
          lastUpdate: Date.now()
        })
      },
      
      setGridView: (view) => {
        const current = get()
        if (current.gridView === view) return
        set({ gridView: view })
      },
      
      setCurrentPage: (page) => {
        const current = get()
        if (current.currentPage === page) return
        set({ 
          currentPage: page,
          lastUpdate: Date.now()
        })
      },
      
      setLoading: (loading) => {
        set({ isLoading: loading })
      },
      
      setInitializing: (initializing) => {
        set({ isInitializing: initializing })
      },
      
      setFieldLoading: (field, loading) => {
        set((state) => ({
          loadingFields: {
            ...state.loadingFields,
            [field]: loading
          }
        }))
      },
      
      setError: (error) => {
        set({ error })
      },
      
      // NEW: Enhanced loading management functions
      clearAllLoadingStates: () => {
        set({
          isLoading: false,
          loadingFields: {
            search: false,
            location: false,
            date: false,
            sort: false,
            perPage: false,
            gridView: false,
            categories: false,
          }
        })
      },
      
      setDataLoadingStates: (loading) => {
        set({
          isLoading: loading,
          loadingFields: {
            search: loading,
            location: loading,
            date: loading,
            sort: false, // Sort doesn't affect data loading as much
            perPage: false, // Per page is more UI preference
            gridView: false, // Grid view is UI only
            categories: loading,
          }
        })
      },
      
      // Utility actions
      clearFilters: () => {
        set({
          ...defaultState,
          // Keep display preferences
          sortBy: get().sortBy,
          perPage: get().perPage,
          gridView: get().gridView,
          lastUpdate: Date.now()
        })
      },
      
      // Location-aware actions
      initializeWithUserLocation: () => {
        // Note: User location initialization is now handled server-side
        // This function is kept for backward compatibility but does nothing
        console.log('initializeWithUserLocation called - now handled server-side')
      },
      
      canResetLocation: () => {
        const authStore = useAuthStore.getState()
        const { state: homeState, county: homeCounty } = authStore.getHomeLocation()
        const currentState = get()
        
        // Can reset if user has home location and current location is different
        return !!(homeState && homeCounty && 
          (currentState.selectedState !== homeState || currentState.selectedCounty !== homeCounty))
      },
      
      resetToHomeLocation: () => {
        const authStore = useAuthStore.getState()
        const { state: homeState, county: homeCounty } = authStore.getHomeLocation()
        
        if (homeState && homeCounty) {
          set({
            selectedState: homeState,
            selectedCounty: homeCounty,
            currentPage: 1,
            lastUpdate: Date.now()
          })
        }
      },
      
      clearAllFilters: () => {
        const authStore = useAuthStore.getState()
        const { state: homeState, county: homeCounty } = authStore.getHomeLocation()
        
        // ALWAYS reset to user's home location, no exceptions
        // This is the expected behavior - clear filters means "reset to my default location"
        set({
          ...defaultState,
          selectedState: homeState || '',
          selectedCounty: homeCounty || '',
          // Keep display preferences
          sortBy: get().sortBy,
          perPage: get().perPage,
          gridView: get().gridView,
          lastUpdate: Date.now()
        })
      },
      
      resetPagination: () => {
        set({ currentPage: 1 })
      },
      
      syncFromUrlParams: (params) => {
        const updates = {
          searchTerm: params.get('search') || '',
          selectedState: params.get('state') || '',
          selectedCounty: params.get('county') || '',
          dateFrom: params.get('dateFrom') || '',
          dateTo: params.get('dateTo') || '',
          categories: params.get('categories')?.split(',').filter(Boolean) || [],
          tags: params.get('tags')?.split(',').filter(Boolean) || [],
          sortBy: (params.get('sort') as 'newest' | 'top-rated' | 'most-viewed') || 'newest',
          perPage: parseInt(params.get('perPage') || '12'),
          gridView: (params.get('gridView') as 'large' | 'compact') || 'large',
          currentPage: parseInt(params.get('page') || '1'),
          lastUpdate: Date.now()
        }
        
        set(updates)
      },
      
      toUrlParams: () => {
        const state = get()
        const params = new URLSearchParams()
        
        if (state.searchTerm) params.set('search', state.searchTerm)
        if (state.selectedState && state.selectedState !== 'all-states') {
          params.set('state', state.selectedState)
        }
        if (state.selectedCounty && state.selectedCounty !== 'all-counties') {
          params.set('county', state.selectedCounty)
        }
        if (state.dateFrom) params.set('dateFrom', state.dateFrom)
        if (state.dateTo) params.set('dateTo', state.dateTo)
        if (state.categories.length > 0) {
          params.set('categories', state.categories.join(','))
        }
        if (state.tags.length > 0) {
          params.set('tags', state.tags.join(','))
        }
        if (state.sortBy !== 'newest') params.set('sort', state.sortBy)
        if (state.perPage !== 12) params.set('perPage', state.perPage.toString())
        if (state.gridView !== 'large') params.set('gridView', state.gridView)
        if (state.currentPage !== 1) params.set('page', state.currentPage.toString())
        
        return params
      },
    }),
    {
      name: 'mugshots-filter-storage',
      partialize: (state) => ({
        // Only persist preferences, not current filters
        sortBy: state.sortBy,
        perPage: state.perPage,
        gridView: state.gridView,
      }),
    }
  )
) 