"use client"

import { Button } from "@/components/ui/button"

type TimeFrame = "day" | "week" | "month" | "year"

interface TimeFrameToggleProps {
  currentTimeFrame: TimeFrame
  onTimeFrameChange: (timeFrame: TimeFrame) => void
}

const timeFrameOptions = [
  { value: "day" as <PERSON><PERSON>rame, label: "Day" },
  { value: "week" as Time<PERSON>rame, label: "Week" },
  { value: "month" as TimeFrame, label: "Month" },
  { value: "year" as TimeFrame, label: "Year" }
]

export default function TimeFrameToggle({ currentTimeFrame, onTimeFrameChange }: TimeFrameToggleProps) {
  return (
    <div className="flex items-center gap-1 bg-gray-900/50 p-1 rounded-lg border border-pink-500/30">
      {timeFrameOptions.map((option) => (
        <Button
          key={option.value}
          variant={currentTimeFrame === option.value ? "default" : "ghost"}
          size="sm"
          onClick={() => onTimeFrameChange(option.value)}
          className={`px-3 py-1 text-xs font-medium transition-all ${
            currentTimeFrame === option.value
              ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-glow"
              : "text-gray-400 hover:text-white hover:bg-gray-800"
          }`}
        >
          {option.label}
        </Button>
      ))}
    </div>
  )
}

export type { TimeFrame } 