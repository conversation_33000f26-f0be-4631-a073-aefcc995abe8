# Existing API Endpoints Analysis

## Core Data Endpoints

### GET /api/mugshots
- **Purpose:** Paginated mugshots listing with filters
- **Parameters:** page, perPage, state, county, searchTerm, includeTotal
- **Response:** { success: boolean, data: { mugshots: Mugshot[], pagination: PaginationData } }
- **Usage:** Main mugshots page, filtering, search
- **Cache Strategy:** 5 minutes stale time, background refetch
- **Performance Notes:** Includes ratings/tags data to reduce additional requests

### GET /api/mugshots/[id]
- **Purpose:** Single mugshot with complete ratings/tags statistics
- **Parameters:** id (mugshot ID)
- **Response:** { success: boolean, data: { mugshot: Mugshot, ratings: RatingData, tags: TagData } }
- **Usage:** Mugshot detail popup, fresh data display
- **Cache Strategy:** 1 minute stale time (fresher for popups)
- **Performance Notes:** Always fetches fresh data for real-time interactions

### GET /api/user/mugshot/[id]/data
- **Purpose:** User-specific rating/tag data (requires auth)
- **Parameters:** id (mugshot ID)
- **Response:** { success: boolean, data: { userRating: number, userTags: string[] } }
- **Usage:** Show user's previous ratings/tags
- **Cache Strategy:** 30 seconds stale time (user data changes frequently)
- **Auth Handling:** Redirects to login with return URL on UNAUTHENTICATED error

## Interaction Endpoints

### POST /api/ratings/submit
- **Purpose:** Submit user rating for mugshot
- **Body:** { mugshotId: string, rating: number }
- **Response:** { success: boolean, data: { averageRating: number, totalRatings: number } }
- **Usage:** Rating interactions, optimistic updates
- **Auth Required:** Yes
- **Optimistic Strategy:** Update UI immediately, rollback on error

### POST /api/tags/toggle
- **Purpose:** Toggle user tag for mugshot
- **Body:** { mugshotId: string, tagType: string }
- **Response:** { success: boolean, data: { tagCounts: Record<string, number> } }
- **Usage:** Tag interactions, optimistic updates
- **Auth Required:** Yes
- **Optimistic Strategy:** Update tag counts immediately, rollback on error

### GET /api/tags/statistics
- **Purpose:** Global tag statistics
- **Parameters:** mugshotId (optional)
- **Response:** { success: boolean, data: { tagCounts: Record<string, number> } }
- **Usage:** Tag display, statistics
- **Cache Strategy:** 5 minutes stale time

## Auth & User Endpoints

### POST /api/auth/signin
- **Purpose:** User authentication
- **Body:** { email: string, password: string }
- **Response:** { success: boolean, data: { user: User, redirectUrl?: string } }
- **Usage:** Login functionality
- **Redirect Handling:** Supports redirect URL parameter for post-login navigation

### POST /api/auth/signup
- **Purpose:** User registration
- **Body:** { email: string, password: string, fullName: string }
- **Response:** { success: boolean, data: { user: User } }
- **Usage:** Registration functionality

### GET /api/debug-profile
- **Purpose:** User profile data (development/debug)
- **Response:** { success: boolean, data: { profile: UserProfile } }
- **Usage:** Profile debugging, development
- **Auth Required:** Yes

## Current Data Flow Patterns

### 1. Initial Page Load
- **Endpoint:** `/api/mugshots` with filters and pagination
- **Data Included:** Mugshots with basic ratings/tags data
- **Caching:** Client-side with Zustand stores
- **Performance:** Single request includes most needed data

### 2. Mugshot Detail Popup
- **Endpoint:** `/api/mugshots/[id]` for complete data
- **Trigger:** User clicks on mugshot card
- **Data Fresh:** Always fetches fresh ratings/tags for real-time display
- **User Data:** Separate `/api/user/mugshot/[id]/data` call if authenticated

### 3. User Interactions
- **Rating:** POST to `/api/ratings/submit` with optimistic UI updates
- **Tagging:** POST to `/api/tags/toggle` with optimistic count updates
- **Error Handling:** Rollback optimistic changes on API failure

### 4. Authentication Flow
- **Login Redirect:** `/login?redirect=${encodeURIComponent(currentUrl)}`
- **Post-Login:** Navigate to redirect URL or default page
- **Session Management:** Supabase auth with automatic token refresh

### 5. Filter Updates
- **URL Sync:** Filters synchronized with URL parameters
- **API Calls:** Debounced requests to `/api/mugshots` with new filters
- **State Management:** Zustand store manages filter state and URL updates

## Migration Strategy for TanStack Query

### Phase 1: Add Query Hooks Alongside Existing Patterns
- Create query hooks that call existing API endpoints
- Maintain identical request/response formats
- Keep existing Zustand stores functioning

### Phase 2: Gradual Component Migration
- Replace direct API calls with query hooks
- Implement optimistic mutations for ratings/tags
- Add realtime cache invalidation

### Phase 3: Performance Optimizations
- Implement intelligent prefetching
- Add background refetching for stale data
- Optimize cache invalidation strategies

### Phase 4: Legacy Cleanup
- Remove old API calling patterns
- Keep client-side state in Zustand (auth, filters, preferences)
- Clean up unused code while maintaining compatibility

## Error Handling Patterns

### Current Error Handling
- Manual error state management in Zustand stores
- Component-level error display
- Basic retry mechanisms

### Enhanced Error Handling with TanStack Query
- Automatic retry with exponential backoff
- Global error boundaries for server state failures
- Optimistic update rollback mechanisms
- Network status awareness

## Performance Considerations

### Current Performance Limitations
- Manual cache management with potential stale data
- No automatic background refetching
- Limited optimistic update patterns
- No prefetching strategies

### TanStack Query Performance Benefits
- Automatic intelligent caching with configurable stale times
- Background refetching without UI blocking
- Built-in optimistic updates with automatic rollback
- Query deduplication and request batching
- Prefetching for improved navigation performance 