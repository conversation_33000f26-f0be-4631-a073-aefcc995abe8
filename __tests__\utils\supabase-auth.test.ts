import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createBrowserClient, createServerClient } from '@supabase/ssr'
import { signUpUser, signInUser, validateAuthForm } from '@/lib/auth-utils'

// Mock Supabase clients
vi.mock('@supabase/ssr', () => ({
  createBrowserClient: vi.fn(),
  createServerClient: vi.fn(),
}))

describe('Authentication Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('validateAuthForm', () => {
    it('should validate valid signup form data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'Password123!',
        fullName: '<PERSON>',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      }
      
      const result = validateAuthForm(validData, 'signup')
      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual({})
    })

    it('should validate valid signin form data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123!',
      }
      
      const result = validateAuthForm(validData, 'signin')
      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual({})
    })

    it('should return errors for invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123!',
      }
      
      const result = validateAuthForm(invalidData, 'signin')
      expect(result.isValid).toBe(false)
      expect(result.errors.email).toBeDefined()
    })

    it('should return errors for weak password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '123',
      }
      
      const result = validateAuthForm(invalidData, 'signin')
      expect(result.isValid).toBe(false)
      expect(result.errors.password).toBeDefined()
    })

    it('should return errors for mismatched passwords in signup', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'DifferentPassword123!',
        fullName: 'John Doe',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      }
      
      const result = validateAuthForm(invalidData, 'signup')
      expect(result.isValid).toBe(false)
      expect(result.errors.confirmPassword).toBeDefined()
    })
  })

  describe('signUpUser', () => {
    it('should call supabase signup with correct parameters', async () => {
      const mockSignUp = vi.fn().mockResolvedValue({ 
        data: { user: { id: '123', email: '<EMAIL>' } },
        error: null 
      })
      const mockSupabase = {
        auth: {
          signUp: mockSignUp
        }
      }

      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        fullName: 'John Doe',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      }

      const result = await signUpUser(mockSupabase as any, userData)
      
      expect(mockSignUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'Password123!',
        options: {
          data: {
            full_name: 'John Doe',
            state: 'CA',
            county: 'Los Angeles'
          }
        }
      })
      expect(result.success).toBe(true)
    })

    it('should handle signup errors', async () => {
      const mockSignUp = vi.fn().mockResolvedValue({ 
        data: null,
        error: { message: 'User already exists' }
      })
      const mockSupabase = {
        auth: {
          signUp: mockSignUp
        }
      }

      const userData = {
        email: '<EMAIL>',
        password: 'Password123!',
        fullName: 'John Doe',
        selectedState: 'CA',
        selectedCounty: 'Los Angeles'
      }

      const result = await signUpUser(mockSupabase as any, userData)
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('User already exists')
    })
  })

  describe('signInUser', () => {
    it('should call supabase signin with correct parameters', async () => {
      const mockSignIn = vi.fn().mockResolvedValue({ 
        data: { user: { id: '123', email: '<EMAIL>' } },
        error: null 
      })
      const mockSupabase = {
        auth: {
          signInWithPassword: mockSignIn
        }
      }

      const credentials = {
        email: '<EMAIL>',
        password: 'Password123!'
      }

      const result = await signInUser(mockSupabase as any, credentials)
      
      expect(mockSignIn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'Password123!'
      })
      expect(result.success).toBe(true)
    })

    it('should handle signin errors', async () => {
      const mockSignIn = vi.fn().mockResolvedValue({ 
        data: null,
        error: { message: 'Invalid credentials' }
      })
      const mockSupabase = {
        auth: {
          signInWithPassword: mockSignIn
        }
      }

      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      const result = await signInUser(mockSupabase as any, credentials)
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid credentials')
    })
  })
}) 