// Utility functions for rating time gate decision logic

import { isRatingEnabled } from './timezone-utils'

export interface MugshotTimeGateData {
  id: number | string
  dateOfBooking: string | null
  stateOfBooking?: string | null
  countyOfBooking?: string | null
  // Allow for different property names from different data sources
  state?: string | null
  county?: string | null
}

/**
 * Determine if the time gate component should be shown instead of the rating component
 * Returns true if rating is NOT yet enabled (show countdown)
 * Returns false if rating IS enabled (show rating component)
 */
export function shouldShowTimeGate(mugshot: MugshotTimeGateData): boolean {
  if (!mugshot.dateOfBooking) {
    return false // No date means rating should be disabled, not showing countdown
  }

  // Extract state and county from various possible property names
  const state = mugshot.stateOfBooking || mugshot.state || null
  const county = mugshot.countyOfBooking || mugshot.county || null

  // Check if rating is enabled
  const enabled = isRatingEnabled(mugshot.dateOfBooking, state, county)
  
  // Show time gate if rating is NOT enabled
  return !enabled
}

/**
 * Extract the necessary props for MugshotRatingTimeGate component
 */
export function getTimeGateProps(mugshot: MugshotTimeGateData) {
  return {
    mugshotId: mugshot.id.toString(),
    dateOfBooking: mugshot.dateOfBooking,
    state: mugshot.stateOfBooking || mugshot.state || null,
    county: mugshot.countyOfBooking || mugshot.county || null
  }
}

/**
 * Extract the necessary props for SimpleMugshotRating component
 */
export function getRatingProps(mugshot: MugshotTimeGateData) {
  return {
    mugshotId: mugshot.id.toString()
  }
} 