import { Skeleton } from '@/components/ui/skeleton'

interface RatingSectionSkeletonProps {
  className?: string
  showTags?: boolean
  compact?: boolean
}

export default function RatingSectionSkeleton({ 
  className = '', 
  showTags = true,
  compact = false 
}: RatingSectionSkeletonProps) {
  return (
    <div className={`bg-gray-800/30 rounded-lg ${compact ? 'p-3' : 'p-4'} ${className}`}>
      {/* Clean, minimal skeleton */}
      <div className="text-center space-y-3">
        {/* Rating display skeleton */}
        <div className="flex items-center justify-center gap-2">
          <Skeleton className="h-12 w-12 rounded bg-gray-700/50" />
          <div className="space-y-1">
            <Skeleton className="h-3 w-16 rounded bg-gray-700/50" />
            <Skeleton className="h-2 w-20 rounded bg-gray-700/30" />
          </div>
        </div>

        {/* Tags skeleton */}
        {showTags && (
          <div className="flex justify-center gap-2">
            <Skeleton className="h-6 w-16 rounded-full bg-gray-700/50" />
            <Skeleton className="h-6 w-14 rounded-full bg-gray-700/50" />
            <Skeleton className="h-6 w-18 rounded-full bg-gray-700/50" />
          </div>
        )}

        {/* Button skeleton */}
        <Skeleton className="h-9 w-24 rounded bg-gray-700/50 mx-auto" />
      </div>
    </div>
  )
}

// Export specialized versions for different use cases
export function CompactRatingSkeleton({ className = '' }: { className?: string }) {
  return (
    <RatingSectionSkeleton 
      className={className} 
      compact={true}
      showTags={false}
    />
  )
}

export function FullRatingSkeleton({ className = '' }: { className?: string }) {
  return (
    <RatingSectionSkeleton 
      className={className} 
      compact={false}
      showTags={true}
    />
  )
} 