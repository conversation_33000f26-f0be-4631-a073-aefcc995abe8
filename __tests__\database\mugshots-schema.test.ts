import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { createClient } from '@supabase/supabase-js'

// Test configuration for Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

describe('Mugshots Database Schema', () => {
  beforeAll(async () => {
    // Setup test environment - ensure we have a connection
    const { data, error } = await supabase.from('profiles').select('id').limit(1)
    if (error && !error.message.includes('relation "profiles" does not exist')) {
      throw new Error(`Database connection failed: ${error.message}`)
    }
  })

  afterAll(async () => {
    // Cleanup if needed
  })

  it('should have mugshots table with correct structure', async () => {
    // Test that mugshots table exists and has the expected columns
    const { data, error } = await supabase
      .from('mugshots')
      .select('*')
      .limit(1)

    // The table should exist (even if empty)
    expect(error).toBeNull()
    expect(data).toBeDefined()
  })

  it('should have required columns with correct types', async () => {
    // Query the information schema to validate column structure
    const { data: columns, error } = await supabase.rpc('get_table_columns', {
      table_name: 'mugshots'
    })

    expect(error).toBeNull()
    expect(columns).toBeDefined()

    // Check for required columns
    const columnNames = columns?.map((col: any) => col.column_name) || []
    const expectedColumns = [
      'id',
      'created_at',
      'firstName',
      'lastName', 
      'dateOfBooking',
      'stateOfBooking',
      'countyOfBooking',
      'offenseDescription',
      'additionalDetails',
      'imagePath'
    ]

    expectedColumns.forEach(col => {
      expect(columnNames).toContain(col)
    })
  })

  it('should have performance indexes for efficient querying', async () => {
    // Test that required indexes exist for performance
    const { data: indexes, error } = await supabase.rpc('get_table_indexes', {
      table_name: 'mugshots'
    })

    expect(error).toBeNull()
    expect(indexes).toBeDefined()

    // Check for performance indexes
    const indexNames = indexes?.map((idx: any) => idx.indexname) || []
    expect(indexNames.some((name: string) => name.includes('booking_date'))).toBe(true)
    expect(indexNames.some((name: string) => name.includes('location') || name.includes('state'))).toBe(true)
  })

  it('should support RLS policies for public read access', async () => {
    // Test that public read access is enabled
    const { data, error } = await supabase
      .from('mugshots')
      .select('id')
      .limit(1)

    // Should not error due to RLS (public read should be allowed)
    expect(error).toBeNull()
  })

  it('should validate data insertion with required fields', async () => {
    // Test data insertion structure (using a test record)
    const testMugshot = {
      firstName: 'Test',
      lastName: 'User',
      dateOfBooking: '2024-01-01',
      stateOfBooking: 'California',
      countyOfBooking: 'Los Angeles',
      offenseDescription: 'Test offense',
      imagePath: '/test/image.jpg'
    }

    // Note: This will likely fail due to admin-only insert policy, which is expected
    const { data, error } = await supabase
      .from('mugshots')
      .insert(testMugshot)
      .select()

    // For now, we expect this to fail due to RLS (admin-only insert)
    // The important thing is that the table structure accepts these fields
    if (error) {
      expect(error.message).toContain('policy') // RLS policy error expected
    } else {
      // If insert succeeds (admin context), verify structure
      expect(data).toBeDefined()
      expect(data?.[0]).toHaveProperty('id')
      expect(data?.[0]).toHaveProperty('firstName', 'Test')
    }
  })
}) 