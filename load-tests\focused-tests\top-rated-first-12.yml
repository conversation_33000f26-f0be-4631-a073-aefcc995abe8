# Top-Rated First 12 Test - Optimized Ratings Query
# Tests the most optimized approach: Direct ratings table query for top 12 mugshots
# Focus: Only ratings table, get mugshot_id + avg_rating + total_ratings, limit 12
# Expected behavior: Sort by avg_rating DESC, then total_ratings DESC for ties
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 30
      arrivalRate: 2
      name: "Warm-up: Top-rated first 12"
    - duration: 180
      arrivalRate: 8
      name: "Steady: Top-rated first 12 performance"
    - duration: 30
      arrivalRate: 2
      name: "Cool-down"

  # Performance expectations for optimized query
  ensure:
    - http.response_time.p95: 1200   # Should be faster than complex aggregation
    - http.response_time.median: 350 # Target sub-350ms median
    - http.codes.200: 96             # High success rate expected
    - http.codes.5xx: 2              # Minimal server errors

  http:
    timeout: 20
    pool: 12

  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  # Primary test: Pure top-rated first 12 (no filters)
  - name: "Top-Rated First 12 - Pure Query"
    weight: 60
    flow:
      - function: "generateTopRatedFirst12Filters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
            # No additional filters to focus purely on ratings performance
          name: "GET /api/mugshots - Top-Rated First 12 Pure"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 1

  # Edge case test: Different page sizes
  - name: "Top-Rated Edge Cases - Page Sizes"
    weight: 25
    flow:
      - function: "generateTopRatedFirst12EdgeCases"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - Top-Rated Edge Cases"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
      - think: 1.5

  # Pagination test: Beyond first 12
  - name: "Top-Rated Pagination Test"
    weight: 15
    flow:
      - function: "generateTopRatedPaginationTest"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - Top-Rated Pagination"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
      - think: 2

processor: "./load-tests/focused-tests/data-generators-top-rated-12.js"
