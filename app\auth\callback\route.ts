import { NextResponse } from 'next/server'
import { type NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'


export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const returnUrl = searchParams.get('returnUrl') ?? '/mugshots'
  
  // Handle OAuth callback (Google OAuth)
  if (code) {
    console.log('OAuth callback triggered with code:', code.substring(0, 10) + '...')
    const supabase = await createClient()

    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (!error && data?.user) {
      console.log('OAuth session created for user:', data.user.id, data.user.email)
      
      // Ensure profile exists for authenticated user (works for both OAuth and email confirmation)
      try {
        console.log('Ensuring profile exists for authenticated user...')
        const profileResponse = await fetch(new URL('/api/auth/create-profile', origin), {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${data.session?.access_token}`,
            'Content-Type': 'application/json'
          }
        })
        
        const profileResult = await profileResponse.json()
        
        if (profileResult.success) {
          console.log('Profile verified/created successfully:', profileResult.message)
        } else {
          console.warn('Profile creation/check failed:', profileResult.error)
          // Continue with redirect even if profile creation fails
        }
      } catch (profileError) {
        console.error('Profile creation request failed:', profileError)
        // Continue with redirect even if profile creation request fails
      }

      // Check if user needs location setup using account linking-aware profile lookup
      const { getOrCreateProfileWithLinking } = await import('@/lib/auth-account-linking')
      const { profile, wasLinked, message } = await getOrCreateProfileWithLinking(supabase, data.user)
      
      if (wasLinked && message) {
        console.log('Account linking result:', message)
      }
      
      const needsLocationSetup = !profile?.state || !profile?.county
      
      console.log('Profile Check:', {
        profileExists: !!profile,
        hasState: !!profile?.state,
        hasCounty: !!profile?.county,
        needsLocationSetup,
        wasLinked,
        returnUrl
      })
      
      if (needsLocationSetup) {
        // Redirect to location setup with return URL
        const locationSetupUrl = new URL('/auth/location-setup', origin)
        locationSetupUrl.searchParams.set('returnUrl', returnUrl)
        
        // Pass account linking info to location setup
        if (wasLinked) {
          locationSetupUrl.searchParams.set('account_linked', 'true')
          locationSetupUrl.searchParams.set('link_message', encodeURIComponent(message || ''))
        }
        
        console.log('Redirecting to location setup:', locationSetupUrl.toString())
        return NextResponse.redirect(locationSetupUrl)
      }

      // User has complete profile - redirect to return URL or default
      const finalRedirectUrl = new URL(returnUrl, origin)
      
      // Add account linking notification to final redirect if accounts were linked
      if (wasLinked) {
        finalRedirectUrl.searchParams.set('account_linked', 'true')
        finalRedirectUrl.searchParams.set('link_message', encodeURIComponent(message || ''))
      }
      
      return NextResponse.redirect(finalRedirectUrl)
    }
    
    // OAuth error - redirect to error page
    console.error('OAuth callback error:', error)
    return NextResponse.redirect(new URL('/error?message=oauth_callback_failed', origin))
  }

  // Handle email verification (existing functionality)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type')
  const next = searchParams.get('next') ?? '/mugshots'

  if (token_hash && type) {
    const supabase = await createClient()

    const { data, error } = await supabase.auth.verifyOtp({
      type: type as 'signup' | 'email_change' | 'recovery',
      token_hash,
    })
    
    if (!error && data?.user && data?.session) {
      console.log('Email confirmation successful - session established for user:', data.user.id, data.user.email)
      
      // Create profile for email-confirmed user using stored metadata
      const user = data.user
      const metadata = user.user_metadata
      
      console.log('Email confirmation callback - user metadata:', metadata)
      
      let profileCreated = false
      
      // Try to create profile if we have at least full_name
      if (metadata?.full_name) {
        console.log('Creating profile for email-confirmed user:', {
          userId: user.id,
          fullName: metadata.full_name,
          state: metadata.state,
          county: metadata.county
        })
        
        const { createProfile } = await import('@/lib/profile-utils')
        
        const profileResult = await createProfile(supabase, {
          userId: user.id,
          fullName: metadata.full_name,
          state: metadata.state || null, // Use null instead of empty string
          county: metadata.county || null // Use null instead of empty string
        })

        if (!profileResult.success) {
          console.error('Profile creation failed for email-confirmed user:', profileResult.error)
        } else {
          console.log('Profile created successfully for email-confirmed user:', profileResult.profile?.id)
          profileCreated = true
        }
      } else {
        console.log('No full_name in metadata, profile creation will be handled in location setup. Metadata:', metadata)
      }

      // Check if user needs location setup by checking profile and metadata
      let needsLocationSetup = false
      
      if (profileCreated && metadata?.state && metadata?.county) {
        // Profile was created with location data
        needsLocationSetup = false
      } else {
        // Either no profile was created, or profile lacks location data
        console.log('User needs location setup - profile creation status:', profileCreated)
        needsLocationSetup = true
      }
      
      console.log('Email confirmation - Location check:', {
        hasMetadataState: !!metadata?.state,
        hasMetadataCounty: !!metadata?.county,
        profileCreated,
        needsLocationSetup,
        nextUrl: next
      })
      
      if (needsLocationSetup) {
        // Redirect to location setup with return URL
        const locationSetupUrl = new URL('/auth/location-setup', origin)
        locationSetupUrl.searchParams.set('returnUrl', next)
        console.log('Email user needs location setup, redirecting to:', locationSetupUrl.toString())
        return NextResponse.redirect(locationSetupUrl)
      }
      
      // User is confirmed and has all required data - redirect to next URL
      console.log('Email confirmation complete, redirecting to:', next)
      return NextResponse.redirect(new URL(next, origin))
    } else {
      console.error('Email confirmation failed:', {
        error: error?.message,
        hasUser: !!data?.user,
        hasSession: !!data?.session
      })
    }
  }

  // redirect the user to an error page with instructions
  console.error('Invalid callback - missing token_hash/type or verification failed')
  return NextResponse.redirect(new URL('/error?message=invalid_callback', origin))
} 