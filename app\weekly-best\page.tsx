"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import MugshotModal from "@/components/MugshotModal"
import MugshotCard from "@/components/MugshotCard"
import CategoryOverlay from "@/components/CategoryOverlay"
import PageSkeleton from "@/components/PageSkeleton"
import { Trophy, Star, TrendingUp, Search, Calendar } from "lucide-react"
import { categoryEmojis } from "@/lib/constants"

const categories = ["All Categories", "Hot", "Funny", "Wild", "Scary"]
const timeRanges = ["This Week", "Last Week", "This Month", "Last Month"]

// Deterministic random generator
const seededRandom = (seed: number) => {
  const x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
}

// Mock weekly champions data with deterministic generation
const weeklyChampions = Array.from({ length: 24 }, (_, i) => {
  const id = i + 1
  const names = [
    "Johnny Champion", "Vicky Victor", "Rico Winner", "Tina Trophy", "Max Medal",
    "<PERSON> Badge", "Tony Top", "Luna Leader", "<PERSON> Rank", "Molly MVP"
  ]
  const categories = ["Hot", "Funny", "Wild", "Scary"]
  const states = ["Texas", "Florida", "California", "New York", "Nevada"]
  
  // Generate a week ending date (Sunday) within the last 8 weeks
  const now = new Date()
  const weekInMillis = 7 * 24 * 60 * 60 * 1000
  const weekEnding = new Date(now.getTime() - (Math.floor(seededRandom(id * 7) * 8) * weekInMillis))
  weekEnding.setDate(weekEnding.getDate() - weekEnding.getDay()) // Set to previous Sunday
  
  return {
    id,
    name: names[id % names.length],
    location: `${states[id % states.length]} County, ${states[id % states.length]}`,
    rating: Number((4.0 + seededRandom(id * 2) * 1.0).toFixed(1)),
    image: "/images/mugshot-placeholder.png",
    category: categories[id % 4],
    votes: Math.floor(500 + seededRandom(id * 3) * 2000),
    rank: id,
    weekEnding: weekEnding.toISOString(),
    arrestDate: new Date(weekEnding.getTime() - seededRandom(id * 4) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    birthDate: new Date(now.getTime() - seededRandom(id * 5) * 365 * 24 * 60 * 60 * 1000 * 25).toISOString().split('T')[0],
    offenses: ["Weekly Champion", "Hall of Fame"]
  }
})

function WeeklyBestPageContent() {
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState("All Categories")
  const [selectedTimeRange, setSelectedTimeRange] = useState("This Week")
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState<"rank" | "votes" | "rating">("rank")
  const [selectedMugshot, setSelectedMugshot] = useState<(typeof weeklyChampions)[0] | null>(null)
  const [isMugshotModalOpen, setIsMugshotModalOpen] = useState(false)
  const [animationTrigger, setAnimationTrigger] = useState<string | null>(null)

  const getFilteredChampions = () => {
    let filtered = weeklyChampions

    if (selectedCategory !== "All Categories") {
      filtered = filtered.filter(c => c.category === selectedCategory)
    }

    if (searchTerm) {
      filtered = filtered.filter(c => 
        c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.location.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Time range filtering would use real dates in production
    // Here we're just simulating filtering for the UI

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "rank":
          return a.rank - b.rank
        case "votes":
          return b.votes - a.votes
        case "rating":
          return b.rating - a.rating
        default:
          return a.rank - b.rank
      }
    })
  }

  const handleCategoryChange = (newCategory: string) => {
    // Only trigger animation if it's a specific category (not "All Categories")
    if (newCategory !== "All Categories" && newCategory !== selectedCategory) {
      setAnimationTrigger(newCategory)
    }
    setSelectedCategory(newCategory)
  }

  const handleAnimationComplete = () => {
    setAnimationTrigger(null)
  }

  const handleCardClick = (mugshot: (typeof weeklyChampions)[0]) => {
    setSelectedMugshot(mugshot)
    setIsMugshotModalOpen(true)
  }

  // Initial loading effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 300)

    return () => clearTimeout(timer)
  }, [])

  // Handle mugshotId URL parameter to open specific mugshot
  useEffect(() => {
    const mugshotId = searchParams.get('mugshotId')
    if (mugshotId) {
      const mugshot = weeklyChampions.find(m => m.id === parseInt(mugshotId))
      if (mugshot) {
        setSelectedMugshot(mugshot)
        setIsMugshotModalOpen(true)
      }
    }
  }, [searchParams])

  const filteredChampions = getFilteredChampions()

  if (isLoading) {
    return <PageSkeleton type="weekly" />
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Page Header - Centered */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold uppercase tracking-tight text-white mb-4">
            <Trophy className="inline h-8 w-8 text-yellow-400 mr-2" />
            <span className="text-yellow-400">WEEKLY</span> CHAMPIONS
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            The highest-rated mugshots from each week - America&apos;s most notorious faces
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
          <div className="card-neon p-4 text-center">
            <Trophy className="h-6 w-6 text-yellow-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">{weeklyChampions.length}</div>
            <div className="text-sm text-gray-400">Total Champions</div>
          </div>
          <div className="card-neon p-4 text-center">
            <TrendingUp className="h-6 w-6 text-pink-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">{Math.floor(weeklyChampions.reduce((sum, c) => sum + c.votes, 0) / 1000)}K</div>
            <div className="text-sm text-gray-400">Total Votes</div>
          </div>
          <div className="card-neon p-4 text-center">
            <Star className="h-6 w-6 text-purple-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">{(weeklyChampions.reduce((sum, c) => sum + c.rating, 0) / weeklyChampions.length).toFixed(1)}</div>
            <div className="text-sm text-gray-400">Avg Rating</div>
          </div>
        </div>

        {/* Filters Section - Clean Card Layout */}
        <div className="card-neon mb-8">
          <div className="space-y-6">
            {/* Filter Controls */}
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
              {/* Category */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Category</label>
                <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                  <SelectTrigger className="input-neon cursor-pointer">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800/90 border-cyan-500/30">
                    {categories.map((category) => (
                      <SelectItem key={category} value={category} className="text-white hover:bg-gray-700 cursor-pointer">
                        {category !== "All Categories" && categoryEmojis[category as keyof typeof categoryEmojis]} {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Time Range */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Time Range</label>
                <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                  <SelectTrigger className="input-neon cursor-pointer">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800/90 border-cyan-500/30">
                    {timeRanges.map((range) => (
                      <SelectItem key={range} value={range} className="text-white hover:bg-gray-700 cursor-pointer">
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Name or location..."
                    className="input-neon pl-10 cursor-pointer"
                  />
                </div>
              </div>

              {/* Sort by */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Sort By</label>
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as "rank" | "votes" | "rating")}>
                  <SelectTrigger className="input-neon cursor-pointer">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800/90 border-cyan-500/30">
                    <SelectItem value="rank" className="text-white hover:bg-gray-700 cursor-pointer">Rank</SelectItem>
                    <SelectItem value="votes" className="text-white hover:bg-gray-700 cursor-pointer">Most Votes</SelectItem>
                    <SelectItem value="rating" className="text-white hover:bg-gray-700 cursor-pointer">Highest Rated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Results Count */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Results</label>
                <div className="flex items-center justify-center h-10 bg-gray-800/90 border border-cyan-500/30 rounded-md">
                  <span className="text-cyan-400 font-bold">{filteredChampions.length}</span>
                  <span className="text-gray-400 ml-1">champions</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Champions Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {filteredChampions.map((champion) => (
            <MugshotCard
              key={champion.id}
              mugshot={champion}
              onClick={(mugshot) => handleCardClick(mugshot as typeof weeklyChampions[0])}
              cardSize="large"
              showRankBadge={true}
              customInfo={
                <p className="text-pink-400 text-xs">
                  <Calendar className="h-3 w-3 inline mr-1" />
                  Week ending {new Date(champion.weekEnding).toLocaleDateString()}
                </p>
              }
              customFooter={
                <div className="text-xs text-gray-500">
                  {champion.rank <= 3 ? "🏆 Hall of Fame" : champion.rank <= 10 ? "⭐ Top 10" : "🎖️ Champion"}
                </div>
              }
            />
          ))}
        </div>

        {/* No Results */}
        {filteredChampions.length === 0 && (
          <div className="text-center py-12">
            <Trophy className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-400 mb-2">No Champions Found</h3>
            <p className="text-gray-500">Try adjusting your filters to see more results</p>
          </div>
        )}

        {/* Mugshot Modal */}
        {selectedMugshot && (
          <MugshotModal
            mugshot={selectedMugshot}
            isOpen={isMugshotModalOpen}
            onClose={() => setIsMugshotModalOpen(false)}
          />
        )}

        {/* Category Animation Overlay */}
        <CategoryOverlay 
          trigger={animationTrigger as "Hot" | "Funny" | "Wild" | "Scary" | null} 
          onComplete={handleAnimationComplete}
        />
      </div>
    </div>
  )
}

export default function WeeklyBestPage() {
  return (
    <Suspense fallback={<PageSkeleton type="weekly" />}>
      <WeeklyBestPageContent />
    </Suspense>
  )
} 