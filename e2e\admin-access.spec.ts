import { test, expect } from '@playwright/test'

test.describe('Admin Access Control', () => {
  test.beforeEach(async ({ page }) => {
    // Set up initial state before each test
    await page.goto('/')
  })

  test('admin user can access admin panel', async ({ page }) => {
    // Mock admin user in session storage to simulate logged-in admin
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'admin')
    })

    // Navigate to admin panel
    await page.goto('/admin')

    // Should see admin dashboard content
    await expect(page.locator('text=Admin Dashboard')).toBeVisible()
    await expect(page.locator('text=Moderation Tools')).toBeVisible()
    
    // Should see admin navigation
    await expect(page.locator('text=Dashboard')).toBeVisible()
    await expect(page.locator('text=Content Moderation')).toBeVisible()
  })

  test('regular user is redirected from admin panel', async ({ page }) => {
    // Mock regular user in session storage
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'user')
    })

    // Try to navigate to admin panel
    await page.goto('/admin')

    // Should be redirected to mugshots page
    await expect(page).toHaveURL('/mugshots')
    
    // Should see toast notification (if implemented)
    // This would depend on your toast implementation
    // await expect(page.locator('text=Access denied')).toBeVisible({ timeout: 5000 })
  })

  test('unauthenticated user is redirected from admin panel', async ({ page }) => {
    // No user in session storage (unauthenticated)
    
    // Try to navigate to admin panel
    await page.goto('/admin')

    // Should be redirected to login or home page
    // This depends on your middleware implementation
    await expect(page).toHaveURL(/\/(login|mugshots|$)/)
  })

  test('admin link is visible in header for admin users', async ({ page }) => {
    // Mock admin user
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'admin')
    })

    await page.goto('/')

    // Admin panel link should be visible in header
    await expect(page.locator('a[href="/admin"]')).toBeVisible()
    await expect(page.locator('text=Admin Panel')).toBeVisible()
  })

  test('admin link is not visible for regular users', async ({ page }) => {
    // Mock regular user
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'user')
    })

    await page.goto('/')

    // Admin panel link should not be visible
    await expect(page.locator('a[href="/admin"]')).not.toBeVisible()
    await expect(page.locator('text=Admin Panel')).not.toBeVisible()
  })

  test('admin panel shows loading skeleton initially', async ({ page }) => {
    // Mock admin user
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'admin')
    })

    await page.goto('/admin')

    // Should show loading skeleton or loading state initially
    // This would appear briefly before content loads
    await expect(page.locator('[data-testid="loading-skeleton"]').first()).toBeVisible({ timeout: 1000 })
  })

  test('direct admin route navigation works for admin', async ({ page }) => {
    // Mock admin user
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'admin')
    })

    // Directly navigate to admin URL
    await page.goto('/admin')

    // Should load successfully
    await expect(page).toHaveURL('/admin')
    await expect(page.locator('text=Admin Dashboard')).toBeVisible()
  })

  test('middleware protects admin routes from direct access', async ({ page }) => {
    // Mock regular user
    await page.addInitScript(() => {
      window.localStorage.setItem('test-user-role', 'user')
    })

    // Try multiple admin routes
    const adminRoutes = ['/admin', '/admin/users', '/admin/content']
    
    for (const route of adminRoutes) {
      await page.goto(route)
      
      // Should be redirected away from admin routes
      await expect(page).not.toHaveURL(route)
      await expect(page).toHaveURL(/\/(mugshots|login|$)/)
    }
  })
}) 