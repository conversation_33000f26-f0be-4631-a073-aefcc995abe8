import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const mugshotIdParam = searchParams.get('mugshotId')
    
    // Validate mugshot ID
    if (!mugshotIdParam) {
      return NextResponse.json(
        { success: false, message: 'Mugshot ID is required', error: 'MISSING_MUGSHOT_ID' },
        { status: 400 }
      )
    }
    
    const mugshotId = parseInt(mugshotIdParam, 10)
    if (isNaN(mugshotId) || mugshotId <= 0) {
      return NextResponse.json(
        { success: false, message: 'Valid mugshot ID is required', error: 'INVALID_MUGSHOT_ID' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    
    // Get tag statistics using aggregation
    const { data: tagStats, error: tagError } = await supabase
      .from('tags')
      .select('tag_type')
      .eq('mugshot_id', mugshotId)
    
    if (tagError) {
      console.error('Error fetching tag statistics:', tagError)
      return NextResponse.json(
        { success: false, message: 'Failed to fetch tag statistics', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }
    
    // Calculate tag counts
    const tags = tagStats || []
    const tagCounts = {
      wild: tags.filter(t => t.tag_type === 'wild').length,
      funny: tags.filter(t => t.tag_type === 'funny').length,
      spooky: tags.filter(t => t.tag_type === 'spooky').length,
      totalTags: tags.length
    }
    
    return NextResponse.json({
      success: true,
      data: tagCounts
    })
    
  } catch (error) {
    console.error('Tag statistics API error:', error)
    return NextResponse.json(
      { success: false, message: 'An unexpected error occurred', error: 'UNEXPECTED_ERROR' },
      { status: 500 }
    )
  }
}

// POST endpoint for bulk tag statistics
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { mugshotIds } = body

    // Validate input
    if (!mugshotIds || !Array.isArray(mugshotIds) || mugshotIds.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Array of mugshot IDs is required', error: 'INVALID_MUGSHOT_IDS' },
        { status: 400 }
      )
    }

    // Validate all IDs are numbers
    const validIds = mugshotIds.filter(id => typeof id === 'number' && !isNaN(id))
    if (validIds.length !== mugshotIds.length) {
      return NextResponse.json(
        { success: false, message: 'All mugshot IDs must be valid numbers', error: 'INVALID_MUGSHOT_IDS' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Get bulk tag statistics
    const { data: tags, error } = await supabase
      .from('tags')
      .select('mugshot_id, tag_type')
      .in('mugshot_id', validIds)

    if (error) {
      console.error('Error fetching bulk tag statistics:', error)
      return NextResponse.json(
        { success: false, message: 'Failed to fetch tag statistics', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }

    // Calculate statistics by mugshot
    const statisticsMap: Record<number, { wild: number; funny: number; spooky: number }> = {}
    
    // Initialize all mugshots with zero stats
    validIds.forEach(id => {
      statisticsMap[id] = { wild: 0, funny: 0, spooky: 0 }
    })

    // Count tags by mugshot and type
    tags?.forEach(tag => {
      if (tag.tag_type === 'wild') statisticsMap[tag.mugshot_id].wild++
      else if (tag.tag_type === 'funny') statisticsMap[tag.mugshot_id].funny++
      else if (tag.tag_type === 'spooky') statisticsMap[tag.mugshot_id].spooky++
    })

    return NextResponse.json({
      success: true,
      statistics: statisticsMap
    })

  } catch (error) {
    console.error('Bulk tag statistics error:', error)
    return NextResponse.json(
      { success: false, message: 'An unexpected error occurred. Please try again.', error: 'UNEXPECTED_ERROR' },
      { status: 500 }
    )
  }
} 