{"aggregate": {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 720, "vusers.created": 720, "errors.Undefined function \"generateBaselineFilters\"": 720, "http.requests": 720, "http.codes.200": 720, "http.responses": 720, "http.downloaded_bytes": 3019680, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 720, "vusers.failed": 0, "vusers.completed": 720}, "rates": {"http.request_rate": 2}, "firstCounterAt": 1753660248358, "firstHistogramAt": 1753660249260, "lastCounterAt": 1753660429807, "lastHistogramAt": 1753660429807, "firstMetricAt": 1753660248358, "lastMetricAt": 1753660429807, "period": 1753660420000, "summaries": {"http.response_time": {"min": 329, "max": 3240, "count": 720, "mean": 497.8, "p50": 399.5, "median": 399.5, "p75": 432.7, "p90": 645.6, "p95": 963.1, "p99": 2671, "p999": 3197.8}, "http.response_time.2xx": {"min": 329, "max": 3240, "count": 720, "mean": 497.8, "p50": 399.5, "median": 399.5, "p75": 432.7, "p90": 645.6, "p95": 963.1, "p99": 2671, "p999": 3197.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 329, "max": 3240, "count": 720, "mean": 497.8, "p50": 399.5, "median": 399.5, "p75": 432.7, "p90": 645.6, "p95": 963.1, "p99": 2671, "p999": 3197.8}, "vusers.session_length": {"min": 2331.4, "max": 5260.6, "count": 720, "mean": 2507.1, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2671, "p95": 2951.9, "p99": 4676.2, "p999": 5168}}, "histograms": {"http.response_time": {"min": 329, "max": 3240, "count": 720, "mean": 497.8, "p50": 399.5, "median": 399.5, "p75": 432.7, "p90": 645.6, "p95": 963.1, "p99": 2671, "p999": 3197.8}, "http.response_time.2xx": {"min": 329, "max": 3240, "count": 720, "mean": 497.8, "p50": 399.5, "median": 399.5, "p75": 432.7, "p90": 645.6, "p95": 963.1, "p99": 2671, "p999": 3197.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 329, "max": 3240, "count": 720, "mean": 497.8, "p50": 399.5, "median": 399.5, "p75": 432.7, "p90": 645.6, "p95": 963.1, "p99": 2671, "p999": 3197.8}, "vusers.session_length": {"min": 2331.4, "max": 5260.6, "count": 720, "mean": 2507.1, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2671, "p95": 2951.9, "p99": 4676.2, "p999": 5168}}}, "intermediate": [{"counters": {"vusers.created_by_name.Baseline - Gentle Load": 4, "vusers.created": 4, "errors.Undefined function \"generateBaselineFilters\"": 4, "http.requests": 4, "http.codes.200": 4, "http.responses": 4, "http.downloaded_bytes": 16776, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 4}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753660248358, "firstHistogramAt": 1753660249260, "lastCounterAt": 1753660249880, "lastHistogramAt": 1753660249880, "firstMetricAt": 1753660248358, "lastMetricAt": 1753660249880, "period": "1753660240000", "summaries": {"http.response_time": {"min": 379, "max": 900, "count": 4, "mean": 644.3, "p50": 432.7, "median": 432.7, "p75": 871.5, "p90": 871.5, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 379, "max": 900, "count": 4, "mean": 644.3, "p50": 432.7, "median": 432.7, "p75": 871.5, "p90": 871.5, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 379, "max": 900, "count": 4, "mean": 644.3, "p50": 432.7, "median": 432.7, "p75": 871.5, "p90": 871.5, "p95": 871.5, "p99": 871.5, "p999": 871.5}}, "histograms": {"http.response_time": {"min": 379, "max": 900, "count": 4, "mean": 644.3, "p50": 432.7, "median": 432.7, "p75": 871.5, "p90": 871.5, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 379, "max": 900, "count": 4, "mean": 644.3, "p50": 432.7, "median": 432.7, "p75": 871.5, "p90": 871.5, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 379, "max": 900, "count": 4, "mean": 644.3, "p50": 432.7, "median": 432.7, "p75": 871.5, "p90": 871.5, "p95": 871.5, "p99": 871.5, "p999": 871.5}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateBaselineFilters\"": 20, "http.requests": 20, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83880, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 20, "vusers.failed": 0, "vusers.completed": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753660250354, "firstHistogramAt": 1753660250732, "lastCounterAt": 1753660259935, "lastHistogramAt": 1753660259935, "firstMetricAt": 1753660250354, "lastMetricAt": 1753660259935, "period": "1753660250000", "summaries": {"http.response_time": {"min": 356, "max": 541, "count": 20, "mean": 405.5, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 478.3, "p95": 518.1, "p99": 518.1, "p999": 518.1}, "http.response_time.2xx": {"min": 356, "max": 541, "count": 20, "mean": 405.5, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 478.3, "p95": 518.1, "p99": 518.1, "p999": 518.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 356, "max": 541, "count": 20, "mean": 405.5, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 478.3, "p95": 518.1, "p99": 518.1, "p999": 518.1}, "vusers.session_length": {"min": 2366.8, "max": 2966.7, "count": 20, "mean": 2462.8, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2515.5, "p95": 2893.5, "p99": 2893.5, "p999": 2893.5}}, "histograms": {"http.response_time": {"min": 356, "max": 541, "count": 20, "mean": 405.5, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 478.3, "p95": 518.1, "p99": 518.1, "p999": 518.1}, "http.response_time.2xx": {"min": 356, "max": 541, "count": 20, "mean": 405.5, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 478.3, "p95": 518.1, "p99": 518.1, "p999": 518.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 356, "max": 541, "count": 20, "mean": 405.5, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 478.3, "p95": 518.1, "p99": 518.1, "p999": 518.1}, "vusers.session_length": {"min": 2366.8, "max": 2966.7, "count": 20, "mean": 2462.8, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2515.5, "p95": 2893.5, "p99": 2893.5, "p999": 2893.5}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateBaselineFilters\"": 20, "http.requests": 20, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83880, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 20, "vusers.failed": 0, "vusers.completed": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753660260354, "firstHistogramAt": 1753660260719, "lastCounterAt": 1753660269819, "lastHistogramAt": 1753660269819, "firstMetricAt": 1753660260354, "lastMetricAt": 1753660269819, "period": "1753660260000", "summaries": {"http.response_time": {"min": 339, "max": 649, "count": 20, "mean": 409.2, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 487.9, "p95": 608, "p99": 608, "p999": 608}, "http.response_time.2xx": {"min": 339, "max": 649, "count": 20, "mean": 409.2, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 487.9, "p95": 608, "p99": 608, "p999": 608}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 339, "max": 649, "count": 20, "mean": 409.2, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 487.9, "p95": 608, "p99": 608, "p999": 608}, "vusers.session_length": {"min": 2353.6, "max": 2658, "count": 20, "mean": 2433.1, "p50": 2369, "median": 2369, "p75": 2465.6, "p90": 2566.3, "p95": 2618.1, "p99": 2618.1, "p999": 2618.1}}, "histograms": {"http.response_time": {"min": 339, "max": 649, "count": 20, "mean": 409.2, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 487.9, "p95": 608, "p99": 608, "p999": 608}, "http.response_time.2xx": {"min": 339, "max": 649, "count": 20, "mean": 409.2, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 487.9, "p95": 608, "p99": 608, "p999": 608}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 339, "max": 649, "count": 20, "mean": 409.2, "p50": 376.2, "median": 376.2, "p75": 399.5, "p90": 487.9, "p95": 608, "p99": 608, "p999": 608}, "vusers.session_length": {"min": 2353.6, "max": 2658, "count": 20, "mean": 2433.1, "p50": 2369, "median": 2369, "p75": 2465.6, "p90": 2566.3, "p95": 2618.1, "p99": 2618.1, "p999": 2618.1}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 26, "vusers.created": 26, "errors.Undefined function \"generateBaselineFilters\"": 26, "http.requests": 26, "http.codes.200": 25, "http.responses": 25, "http.downloaded_bytes": 104850, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 25, "vusers.failed": 0, "vusers.completed": 20}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753660270354, "firstHistogramAt": 1753660270699, "lastCounterAt": 1753660279990, "lastHistogramAt": 1753660279990, "firstMetricAt": 1753660270354, "lastMetricAt": 1753660279990, "period": "1753660270000", "summaries": {"http.response_time": {"min": 344, "max": 930, "count": 25, "mean": 463.6, "p50": 391.6, "median": 391.6, "p75": 432.7, "p90": 550.1, "p95": 889.1, "p99": 907, "p999": 907}, "http.response_time.2xx": {"min": 344, "max": 930, "count": 25, "mean": 463.6, "p50": 391.6, "median": 391.6, "p75": 432.7, "p90": 550.1, "p95": 889.1, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 344, "max": 930, "count": 25, "mean": 463.6, "p50": 391.6, "median": 391.6, "p75": 432.7, "p90": 550.1, "p95": 889.1, "p99": 907, "p999": 907}, "vusers.session_length": {"min": 2347.9, "max": 2501.6, "count": 20, "mean": 2395.2, "p50": 2369, "median": 2369, "p75": 2416.8, "p90": 2416.8, "p95": 2416.8, "p99": 2416.8, "p999": 2416.8}}, "histograms": {"http.response_time": {"min": 344, "max": 930, "count": 25, "mean": 463.6, "p50": 391.6, "median": 391.6, "p75": 432.7, "p90": 550.1, "p95": 889.1, "p99": 907, "p999": 907}, "http.response_time.2xx": {"min": 344, "max": 930, "count": 25, "mean": 463.6, "p50": 391.6, "median": 391.6, "p75": 432.7, "p90": 550.1, "p95": 889.1, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 344, "max": 930, "count": 25, "mean": 463.6, "p50": 391.6, "median": 391.6, "p75": 432.7, "p90": 550.1, "p95": 889.1, "p99": 907, "p999": 907}, "vusers.session_length": {"min": 2347.9, "max": 2501.6, "count": 20, "mean": 2395.2, "p50": 2369, "median": 2369, "p75": 2416.8, "p90": 2416.8, "p95": 2416.8, "p99": 2416.8, "p999": 2416.8}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 209700, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 50, "vusers.failed": 0, "vusers.completed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660280032, "firstHistogramAt": 1753660280032, "lastCounterAt": 1753660289981, "lastHistogramAt": 1753660289981, "firstMetricAt": 1753660280032, "lastMetricAt": 1753660289981, "period": "1753660280000", "summaries": {"http.response_time": {"min": 339, "max": 837, "count": 50, "mean": 413.6, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 487.9, "p95": 550.1, "p99": 620.3, "p999": 620.3}, "http.response_time.2xx": {"min": 339, "max": 837, "count": 50, "mean": 413.6, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 487.9, "p95": 550.1, "p99": 620.3, "p999": 620.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 339, "max": 837, "count": 50, "mean": 413.6, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 487.9, "p95": 550.1, "p99": 620.3, "p999": 620.3}, "vusers.session_length": {"min": 2345.8, "max": 2983.9, "count": 50, "mean": 2466.5, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 2618.1, "p95": 2836.2, "p99": 2951.9, "p999": 2951.9}}, "histograms": {"http.response_time": {"min": 339, "max": 837, "count": 50, "mean": 413.6, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 487.9, "p95": 550.1, "p99": 620.3, "p999": 620.3}, "http.response_time.2xx": {"min": 339, "max": 837, "count": 50, "mean": 413.6, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 487.9, "p95": 550.1, "p99": 620.3, "p999": 620.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 339, "max": 837, "count": 50, "mean": 413.6, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 487.9, "p95": 550.1, "p99": 620.3, "p999": 620.3}, "vusers.session_length": {"min": 2345.8, "max": 2983.9, "count": 50, "mean": 2466.5, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 2618.1, "p95": 2836.2, "p99": 2951.9, "p999": 2951.9}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 50, "http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 213894, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 51}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660290025, "firstHistogramAt": 1753660290025, "lastCounterAt": 1753660299997, "lastHistogramAt": 1753660299997, "firstMetricAt": 1753660290025, "lastMetricAt": 1753660299997, "period": "1753660290000", "summaries": {"vusers.session_length": {"min": 2346.3, "max": 2906.4, "count": 50, "mean": 2448, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2725, "p95": 2836.2, "p99": 2893.5, "p999": 2893.5}, "http.response_time": {"min": 340, "max": 892, "count": 51, "mean": 436.5, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 727.9, "p95": 854.2, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 340, "max": 892, "count": 51, "mean": 436.5, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 727.9, "p95": 854.2, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 340, "max": 892, "count": 51, "mean": 436.5, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 727.9, "p95": 854.2, "p99": 871.5, "p999": 871.5}}, "histograms": {"vusers.session_length": {"min": 2346.3, "max": 2906.4, "count": 50, "mean": 2448, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2725, "p95": 2836.2, "p99": 2893.5, "p999": 2893.5}, "http.response_time": {"min": 340, "max": 892, "count": 51, "mean": 436.5, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 727.9, "p95": 854.2, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 340, "max": 892, "count": 51, "mean": 436.5, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 727.9, "p95": 854.2, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 340, "max": 892, "count": 51, "mean": 436.5, "p50": 383.8, "median": 383.8, "p75": 407.5, "p90": 727.9, "p95": 854.2, "p99": 871.5, "p999": 871.5}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 49, "http.codes.200": 49, "http.responses": 49, "http.downloaded_bytes": 205506, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 49}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660300354, "firstHistogramAt": 1753660300720, "lastCounterAt": 1753660309979, "lastHistogramAt": 1753660309979, "firstMetricAt": 1753660300354, "lastMetricAt": 1753660309979, "period": "1753660300000", "summaries": {"vusers.session_length": {"min": 2346.5, "max": 2534.3, "count": 49, "mean": 2400.7, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2416.8, "p95": 2416.8, "p99": 2515.5, "p999": 2515.5}, "http.response_time": {"min": 348, "max": 523, "count": 49, "mean": 397.4, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 507.8, "p999": 507.8}, "http.response_time.2xx": {"min": 348, "max": 523, "count": 49, "mean": 397.4, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 507.8, "p999": 507.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 348, "max": 523, "count": 49, "mean": 397.4, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 507.8, "p999": 507.8}}, "histograms": {"vusers.session_length": {"min": 2346.5, "max": 2534.3, "count": 49, "mean": 2400.7, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2416.8, "p95": 2416.8, "p99": 2515.5, "p999": 2515.5}, "http.response_time": {"min": 348, "max": 523, "count": 49, "mean": 397.4, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 507.8, "p999": 507.8}, "http.response_time.2xx": {"min": 348, "max": 523, "count": 49, "mean": 397.4, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 507.8, "p999": 507.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 348, "max": 523, "count": 49, "mean": 397.4, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 507.8, "p999": 507.8}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 213894, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 51, "vusers.failed": 0, "vusers.completed": 51}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660310001, "firstHistogramAt": 1753660310001, "lastCounterAt": 1753660319996, "lastHistogramAt": 1753660319996, "firstMetricAt": 1753660310001, "lastMetricAt": 1753660319996, "period": "1753660310000", "summaries": {"http.response_time": {"min": 346, "max": 670, "count": 51, "mean": 416.5, "p50": 399.5, "median": 399.5, "p75": 407.5, "p90": 432.7, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 346, "max": 670, "count": 51, "mean": 416.5, "p50": 399.5, "median": 399.5, "p75": 407.5, "p90": 432.7, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 346, "max": 670, "count": 51, "mean": 416.5, "p50": 399.5, "median": 399.5, "p75": 407.5, "p90": 432.7, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "vusers.session_length": {"min": 2352.3, "max": 2681.2, "count": 51, "mean": 2428.5, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2416.8, "p95": 2618.1, "p99": 2671, "p999": 2671}}, "histograms": {"http.response_time": {"min": 346, "max": 670, "count": 51, "mean": 416.5, "p50": 399.5, "median": 399.5, "p75": 407.5, "p90": 432.7, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 346, "max": 670, "count": 51, "mean": 416.5, "p50": 399.5, "median": 399.5, "p75": 407.5, "p90": 432.7, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 346, "max": 670, "count": 51, "mean": 416.5, "p50": 399.5, "median": 399.5, "p75": 407.5, "p90": 432.7, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "vusers.session_length": {"min": 2352.3, "max": 2681.2, "count": 51, "mean": 2428.5, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2416.8, "p95": 2618.1, "p99": 2671, "p999": 2671}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 48, "http.responses": 48, "http.downloaded_bytes": 201312, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 48, "vusers.failed": 0, "vusers.completed": 49}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660320354, "firstHistogramAt": 1753660320720, "lastCounterAt": 1753660329981, "lastHistogramAt": 1753660329981, "firstMetricAt": 1753660320354, "lastMetricAt": 1753660329981, "period": "1753660320000", "summaries": {"http.response_time": {"min": 347, "max": 649, "count": 48, "mean": 429.9, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 528.6, "p95": 572.6, "p99": 632.8, "p999": 632.8}, "http.response_time.2xx": {"min": 347, "max": 649, "count": 48, "mean": 429.9, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 528.6, "p95": 572.6, "p99": 632.8, "p999": 632.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 347, "max": 649, "count": 48, "mean": 429.9, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 528.6, "p95": 572.6, "p99": 632.8, "p999": 632.8}, "vusers.session_length": {"min": 2351.5, "max": 2661.9, "count": 49, "mean": 2427.7, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2465.6, "p95": 2566.3, "p99": 2671, "p999": 2671}}, "histograms": {"http.response_time": {"min": 347, "max": 649, "count": 48, "mean": 429.9, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 528.6, "p95": 572.6, "p99": 632.8, "p999": 632.8}, "http.response_time.2xx": {"min": 347, "max": 649, "count": 48, "mean": 429.9, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 528.6, "p95": 572.6, "p99": 632.8, "p999": 632.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 347, "max": 649, "count": 48, "mean": 429.9, "p50": 407.5, "median": 407.5, "p75": 432.7, "p90": 528.6, "p95": 572.6, "p99": 632.8, "p999": 632.8}, "vusers.session_length": {"min": 2351.5, "max": 2661.9, "count": 49, "mean": 2427.7, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2465.6, "p95": 2566.3, "p99": 2671, "p999": 2671}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 50, "http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 213894, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 51}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660330012, "firstHistogramAt": 1753660330012, "lastCounterAt": 1753660339971, "lastHistogramAt": 1753660339971, "firstMetricAt": 1753660330012, "lastMetricAt": 1753660339971, "period": "1753660330000", "summaries": {"vusers.session_length": {"min": 2381.9, "max": 5260.6, "count": 50, "mean": 3220.9, "p50": 2566.3, "median": 2566.3, "p75": 3905.8, "p90": 4770.6, "p95": 5168, "p99": 5168, "p999": 5168}, "http.response_time": {"min": 374, "max": 3240, "count": 51, "mean": 1186.8, "p50": 539.2, "median": 539.2, "p75": 1863.5, "p90": 2780, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "http.response_time.2xx": {"min": 374, "max": 3240, "count": 51, "mean": 1186.8, "p50": 539.2, "median": 539.2, "p75": 1863.5, "p90": 2780, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 374, "max": 3240, "count": 51, "mean": 1186.8, "p50": 539.2, "median": 539.2, "p75": 1863.5, "p90": 2780, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}}, "histograms": {"vusers.session_length": {"min": 2381.9, "max": 5260.6, "count": 50, "mean": 3220.9, "p50": 2566.3, "median": 2566.3, "p75": 3905.8, "p90": 4770.6, "p95": 5168, "p99": 5168, "p999": 5168}, "http.response_time": {"min": 374, "max": 3240, "count": 51, "mean": 1186.8, "p50": 539.2, "median": 539.2, "p75": 1863.5, "p90": 2780, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "http.response_time.2xx": {"min": 374, "max": 3240, "count": 51, "mean": 1186.8, "p50": 539.2, "median": 539.2, "p75": 1863.5, "p90": 2780, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 374, "max": 3240, "count": 51, "mean": 1186.8, "p50": 539.2, "median": 539.2, "p75": 1863.5, "p90": 2780, "p95": 3134.5, "p99": 3197.8, "p999": 3197.8}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 50, "http.responses": 50, "http.downloaded_bytes": 209700, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 50, "vusers.failed": 0, "vusers.completed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660340023, "firstHistogramAt": 1753660340023, "lastCounterAt": 1753660349995, "lastHistogramAt": 1753660349995, "firstMetricAt": 1753660340023, "lastMetricAt": 1753660349995, "period": "1753660340000", "summaries": {"http.response_time": {"min": 353, "max": 961, "count": 50, "mean": 449.2, "p50": 407.5, "median": 407.5, "p75": 441.5, "p90": 596, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 353, "max": 961, "count": 50, "mean": 449.2, "p50": 407.5, "median": 407.5, "p75": 441.5, "p90": 596, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 353, "max": 961, "count": 50, "mean": 449.2, "p50": 407.5, "median": 407.5, "p75": 441.5, "p90": 596, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "vusers.session_length": {"min": 2357.5, "max": 2965.9, "count": 50, "mean": 2459.7, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 2618.1, "p95": 2618.1, "p99": 2671, "p999": 2671}}, "histograms": {"http.response_time": {"min": 353, "max": 961, "count": 50, "mean": 449.2, "p50": 407.5, "median": 407.5, "p75": 441.5, "p90": 596, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 353, "max": 961, "count": 50, "mean": 449.2, "p50": 407.5, "median": 407.5, "p75": 441.5, "p90": 596, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 353, "max": 961, "count": 50, "mean": 449.2, "p50": 407.5, "median": 407.5, "p75": 441.5, "p90": 596, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "vusers.session_length": {"min": 2357.5, "max": 2965.9, "count": 50, "mean": 2459.7, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 2618.1, "p95": 2618.1, "p99": 2671, "p999": 2671}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "vusers.failed": 0, "vusers.completed": 48, "http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 213894, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 51}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660350018, "firstHistogramAt": 1753660350018, "lastCounterAt": 1753660359994, "lastHistogramAt": 1753660359994, "firstMetricAt": 1753660350018, "lastMetricAt": 1753660359994, "period": "1753660350000", "summaries": {"vusers.session_length": {"min": 2353.7, "max": 2980.7, "count": 48, "mean": 2465.2, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2566.3, "p95": 2671, "p99": 2951.9, "p999": 2951.9}, "http.response_time": {"min": 345, "max": 975, "count": 51, "mean": 460.2, "p50": 407.5, "median": 407.5, "p75": 450.4, "p90": 561.2, "p95": 699.4, "p99": 963.1, "p999": 963.1}, "http.response_time.2xx": {"min": 345, "max": 975, "count": 51, "mean": 460.2, "p50": 407.5, "median": 407.5, "p75": 450.4, "p90": 561.2, "p95": 699.4, "p99": 963.1, "p999": 963.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 345, "max": 975, "count": 51, "mean": 460.2, "p50": 407.5, "median": 407.5, "p75": 450.4, "p90": 561.2, "p95": 699.4, "p99": 963.1, "p999": 963.1}}, "histograms": {"vusers.session_length": {"min": 2353.7, "max": 2980.7, "count": 48, "mean": 2465.2, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2566.3, "p95": 2671, "p99": 2951.9, "p999": 2951.9}, "http.response_time": {"min": 345, "max": 975, "count": 51, "mean": 460.2, "p50": 407.5, "median": 407.5, "p75": 450.4, "p90": 561.2, "p95": 699.4, "p99": 963.1, "p999": 963.1}, "http.response_time.2xx": {"min": 345, "max": 975, "count": 51, "mean": 460.2, "p50": 407.5, "median": 407.5, "p75": 450.4, "p90": 561.2, "p95": 699.4, "p99": 963.1, "p999": 963.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 345, "max": 975, "count": 51, "mean": 460.2, "p50": 407.5, "median": 407.5, "p75": 450.4, "p90": 561.2, "p95": 699.4, "p99": 963.1, "p999": 963.1}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 49, "http.responses": 49, "http.downloaded_bytes": 205506, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 49, "vusers.failed": 0, "vusers.completed": 52}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660360050, "firstHistogramAt": 1753660360050, "lastCounterAt": 1753660369994, "lastHistogramAt": 1753660369994, "firstMetricAt": 1753660360050, "lastMetricAt": 1753660369994, "period": "1753660360000", "summaries": {"http.response_time": {"min": 337, "max": 1401, "count": 49, "mean": 550.9, "p50": 424.2, "median": 424.2, "p75": 450.4, "p90": 1130.2, "p95": 1326.4, "p99": 1408.4, "p999": 1408.4}, "http.response_time.2xx": {"min": 337, "max": 1401, "count": 49, "mean": 550.9, "p50": 424.2, "median": 424.2, "p75": 450.4, "p90": 1130.2, "p95": 1326.4, "p99": 1408.4, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 337, "max": 1401, "count": 49, "mean": 550.9, "p50": 424.2, "median": 424.2, "p75": 450.4, "p90": 1130.2, "p95": 1326.4, "p99": 1408.4, "p999": 1408.4}, "vusers.session_length": {"min": 2345.1, "max": 3414.2, "count": 52, "mean": 2553.8, "p50": 2416.8, "median": 2416.8, "p75": 2515.5, "p90": 3134.5, "p95": 3328.3, "p99": 3395.5, "p999": 3395.5}}, "histograms": {"http.response_time": {"min": 337, "max": 1401, "count": 49, "mean": 550.9, "p50": 424.2, "median": 424.2, "p75": 450.4, "p90": 1130.2, "p95": 1326.4, "p99": 1408.4, "p999": 1408.4}, "http.response_time.2xx": {"min": 337, "max": 1401, "count": 49, "mean": 550.9, "p50": 424.2, "median": 424.2, "p75": 450.4, "p90": 1130.2, "p95": 1326.4, "p99": 1408.4, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 337, "max": 1401, "count": 49, "mean": 550.9, "p50": 424.2, "median": 424.2, "p75": 450.4, "p90": 1130.2, "p95": 1326.4, "p99": 1408.4, "p999": 1408.4}, "vusers.session_length": {"min": 2345.1, "max": 3414.2, "count": 52, "mean": 2553.8, "p50": 2416.8, "median": 2416.8, "p75": 2515.5, "p90": 3134.5, "p95": 3328.3, "p99": 3395.5, "p999": 3395.5}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 51, "http.responses": 51, "http.downloaded_bytes": 213894, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 51, "vusers.failed": 0, "vusers.completed": 51}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660370029, "firstHistogramAt": 1753660370029, "lastCounterAt": 1753660379988, "lastHistogramAt": 1753660379988, "firstMetricAt": 1753660370029, "lastMetricAt": 1753660379988, "period": "1753660370000", "summaries": {"http.response_time": {"min": 337, "max": 863, "count": 51, "mean": 399.1, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 459.5, "p999": 459.5}, "http.response_time.2xx": {"min": 337, "max": 863, "count": 51, "mean": 399.1, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 459.5, "p999": 459.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 337, "max": 863, "count": 51, "mean": 399.1, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 459.5, "p999": 459.5}, "vusers.session_length": {"min": 2343.3, "max": 2869.7, "count": 51, "mean": 2413.8, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2465.6, "p95": 2465.6, "p99": 2465.6, "p999": 2465.6}}, "histograms": {"http.response_time": {"min": 337, "max": 863, "count": 51, "mean": 399.1, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 459.5, "p999": 459.5}, "http.response_time.2xx": {"min": 337, "max": 863, "count": 51, "mean": 399.1, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 459.5, "p999": 459.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 337, "max": 863, "count": 51, "mean": 399.1, "p50": 391.6, "median": 391.6, "p75": 415.8, "p90": 424.2, "p95": 432.7, "p99": 459.5, "p999": 459.5}, "vusers.session_length": {"min": 2343.3, "max": 2869.7, "count": 51, "mean": 2413.8, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2465.6, "p95": 2465.6, "p99": 2465.6, "p999": 2465.6}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 50, "vusers.created": 50, "errors.Undefined function \"generateBaselineFilters\"": 50, "http.requests": 50, "http.codes.200": 45, "http.responses": 45, "http.downloaded_bytes": 188730, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 45, "vusers.failed": 0, "vusers.completed": 49}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660380354, "firstHistogramAt": 1753660380701, "lastCounterAt": 1753660389971, "lastHistogramAt": 1753660389971, "firstMetricAt": 1753660380354, "lastMetricAt": 1753660389971, "period": "1753660380000", "summaries": {"http.response_time": {"min": 336, "max": 902, "count": 45, "mean": 438, "p50": 399.5, "median": 399.5, "p75": 424.2, "p90": 424.2, "p95": 757.6, "p99": 889.1, "p999": 889.1}, "http.response_time.2xx": {"min": 336, "max": 902, "count": 45, "mean": 438, "p50": 399.5, "median": 399.5, "p75": 424.2, "p90": 424.2, "p95": 757.6, "p99": 889.1, "p999": 889.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 336, "max": 902, "count": 45, "mean": 438, "p50": 399.5, "median": 399.5, "p75": 424.2, "p90": 424.2, "p95": 757.6, "p99": 889.1, "p999": 889.1}, "vusers.session_length": {"min": 2341.9, "max": 2917.1, "count": 49, "mean": 2439.6, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2465.6, "p95": 2780, "p99": 2893.5, "p999": 2893.5}}, "histograms": {"http.response_time": {"min": 336, "max": 902, "count": 45, "mean": 438, "p50": 399.5, "median": 399.5, "p75": 424.2, "p90": 424.2, "p95": 757.6, "p99": 889.1, "p999": 889.1}, "http.response_time.2xx": {"min": 336, "max": 902, "count": 45, "mean": 438, "p50": 399.5, "median": 399.5, "p75": 424.2, "p90": 424.2, "p95": 757.6, "p99": 889.1, "p999": 889.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 336, "max": 902, "count": 45, "mean": 438, "p50": 399.5, "median": 399.5, "p75": 424.2, "p90": 424.2, "p95": 757.6, "p99": 889.1, "p999": 889.1}, "vusers.session_length": {"min": 2341.9, "max": 2917.1, "count": 49, "mean": 2439.6, "p50": 2416.8, "median": 2416.8, "p75": 2416.8, "p90": 2465.6, "p95": 2780, "p99": 2893.5, "p999": 2893.5}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 44, "vusers.created": 44, "errors.Undefined function \"generateBaselineFilters\"": 44, "http.requests": 44, "http.codes.200": 49, "http.responses": 49, "http.downloaded_bytes": 205506, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 49, "vusers.failed": 0, "vusers.completed": 50}, "rates": {"http.request_rate": 5}, "http.request_rate": null, "firstCounterAt": 1753660390012, "firstHistogramAt": 1753660390012, "lastCounterAt": 1753660399954, "lastHistogramAt": 1753660399954, "firstMetricAt": 1753660390012, "lastMetricAt": 1753660399954, "period": "1753660390000", "summaries": {"http.response_time": {"min": 352, "max": 1330, "count": 49, "mean": 517.6, "p50": 407.5, "median": 407.5, "p75": 478.3, "p90": 1043.3, "p95": 1274.3, "p99": 1300.1, "p999": 1300.1}, "http.response_time.2xx": {"min": 352, "max": 1330, "count": 49, "mean": 517.6, "p50": 407.5, "median": 407.5, "p75": 478.3, "p90": 1043.3, "p95": 1274.3, "p99": 1300.1, "p999": 1300.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 352, "max": 1330, "count": 49, "mean": 517.6, "p50": 407.5, "median": 407.5, "p75": 478.3, "p90": 1043.3, "p95": 1274.3, "p99": 1300.1, "p999": 1300.1}, "vusers.session_length": {"min": 2352.1, "max": 3340.6, "count": 50, "mean": 2522.4, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 3072.4, "p95": 3262.4, "p99": 3328.3, "p999": 3328.3}}, "histograms": {"http.response_time": {"min": 352, "max": 1330, "count": 49, "mean": 517.6, "p50": 407.5, "median": 407.5, "p75": 478.3, "p90": 1043.3, "p95": 1274.3, "p99": 1300.1, "p999": 1300.1}, "http.response_time.2xx": {"min": 352, "max": 1330, "count": 49, "mean": 517.6, "p50": 407.5, "median": 407.5, "p75": 478.3, "p90": 1043.3, "p95": 1274.3, "p99": 1300.1, "p999": 1300.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 352, "max": 1330, "count": 49, "mean": 517.6, "p50": 407.5, "median": 407.5, "p75": 478.3, "p90": 1043.3, "p95": 1274.3, "p99": 1300.1, "p999": 1300.1}, "vusers.session_length": {"min": 2352.1, "max": 3340.6, "count": 50, "mean": 2522.4, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 3072.4, "p95": 3262.4, "p99": 3328.3, "p999": 3328.3}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateBaselineFilters\"": 20, "http.requests": 20, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83880, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 20, "vusers.failed": 0, "vusers.completed": 19}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753660400037, "firstHistogramAt": 1753660400037, "lastCounterAt": 1753660409900, "lastHistogramAt": 1753660409900, "firstMetricAt": 1753660400037, "lastMetricAt": 1753660409900, "period": "1753660400000", "summaries": {"http.response_time": {"min": 344, "max": 909, "count": 20, "mean": 435.8, "p50": 391.6, "median": 391.6, "p75": 399.5, "p90": 478.3, "p95": 907, "p99": 907, "p999": 907}, "http.response_time.2xx": {"min": 344, "max": 909, "count": 20, "mean": 435.8, "p50": 391.6, "median": 391.6, "p75": 399.5, "p90": 478.3, "p95": 907, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 344, "max": 909, "count": 20, "mean": 435.8, "p50": 391.6, "median": 391.6, "p75": 399.5, "p90": 478.3, "p95": 907, "p99": 907, "p999": 907}, "vusers.session_length": {"min": 2353.9, "max": 2433.3, "count": 19, "mean": 2384.8, "p50": 2369, "median": 2369, "p75": 2416.8, "p90": 2416.8, "p95": 2416.8, "p99": 2416.8, "p999": 2416.8}}, "histograms": {"http.response_time": {"min": 344, "max": 909, "count": 20, "mean": 435.8, "p50": 391.6, "median": 391.6, "p75": 399.5, "p90": 478.3, "p95": 907, "p99": 907, "p999": 907}, "http.response_time.2xx": {"min": 344, "max": 909, "count": 20, "mean": 435.8, "p50": 391.6, "median": 391.6, "p75": 399.5, "p90": 478.3, "p95": 907, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 344, "max": 909, "count": 20, "mean": 435.8, "p50": 391.6, "median": 391.6, "p75": 399.5, "p90": 478.3, "p95": 907, "p99": 907, "p999": 907}, "vusers.session_length": {"min": 2353.9, "max": 2433.3, "count": 19, "mean": 2384.8, "p50": 2369, "median": 2369, "p75": 2416.8, "p90": 2416.8, "p95": 2416.8, "p99": 2416.8, "p999": 2416.8}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 22, "vusers.created_by_name.Baseline - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateBaselineFilters\"": 20, "http.requests": 20, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83880, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753660410266, "firstHistogramAt": 1753660410266, "lastCounterAt": 1753660419802, "lastHistogramAt": 1753660419802, "firstMetricAt": 1753660410266, "lastMetricAt": 1753660419802, "period": "1753660410000", "summaries": {"vusers.session_length": {"min": 2349.9, "max": 3352.8, "count": 22, "mean": 2577.4, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 2893.5, "p95": 2951.9, "p99": 3328.3, "p999": 3328.3}, "http.response_time": {"min": 336, "max": 1337, "count": 20, "mean": 521.3, "p50": 368.8, "median": 368.8, "p75": 432.7, "p90": 907, "p95": 1326.4, "p99": 1326.4, "p999": 1326.4}, "http.response_time.2xx": {"min": 336, "max": 1337, "count": 20, "mean": 521.3, "p50": 368.8, "median": 368.8, "p75": 432.7, "p90": 907, "p95": 1326.4, "p99": 1326.4, "p999": 1326.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 336, "max": 1337, "count": 20, "mean": 521.3, "p50": 368.8, "median": 368.8, "p75": 432.7, "p90": 907, "p95": 1326.4, "p99": 1326.4, "p999": 1326.4}}, "histograms": {"vusers.session_length": {"min": 2349.9, "max": 3352.8, "count": 22, "mean": 2577.4, "p50": 2416.8, "median": 2416.8, "p75": 2465.6, "p90": 2893.5, "p95": 2951.9, "p99": 3328.3, "p999": 3328.3}, "http.response_time": {"min": 336, "max": 1337, "count": 20, "mean": 521.3, "p50": 368.8, "median": 368.8, "p75": 432.7, "p90": 907, "p95": 1326.4, "p99": 1326.4, "p999": 1326.4}, "http.response_time.2xx": {"min": 336, "max": 1337, "count": 20, "mean": 521.3, "p50": 368.8, "median": 368.8, "p75": 432.7, "p90": 907, "p95": 1326.4, "p99": 1326.4, "p999": 1326.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 336, "max": 1337, "count": 20, "mean": 521.3, "p50": 368.8, "median": 368.8, "p75": 432.7, "p90": 907, "p95": 1326.4, "p99": 1326.4, "p999": 1326.4}}}, {"counters": {"vusers.created_by_name.Baseline - Gentle Load": 16, "vusers.created": 16, "errors.Undefined function \"generateBaselineFilters\"": 16, "http.requests": 16, "http.codes.200": 16, "http.responses": 16, "http.downloaded_bytes": 67104, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Baseline (Gentle).codes.200": 16, "vusers.failed": 0, "vusers.completed": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753660420354, "firstHistogramAt": 1753660420689, "lastCounterAt": 1753660429807, "lastHistogramAt": 1753660429807, "firstMetricAt": 1753660420354, "lastMetricAt": 1753660429807, "period": "1753660420000", "summaries": {"http.response_time": {"min": 329, "max": 383, "count": 16, "mean": 352.4, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "http.response_time.2xx": {"min": 329, "max": 383, "count": 16, "mean": 352.4, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 329, "max": 383, "count": 16, "mean": 352.4, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "vusers.session_length": {"min": 2331.4, "max": 2390.2, "count": 20, "mean": 2360.3, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}, "histograms": {"http.response_time": {"min": 329, "max": 383, "count": 16, "mean": 352.4, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "http.response_time.2xx": {"min": 329, "max": 383, "count": 16, "mean": 352.4, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Baseline (Gentle)": {"min": 329, "max": 383, "count": 16, "mean": 352.4, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "vusers.session_length": {"min": 2331.4, "max": 2390.2, "count": 20, "mean": 2360.3, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}}]}