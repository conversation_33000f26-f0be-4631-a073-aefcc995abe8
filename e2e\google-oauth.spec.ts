import { test, expect } from '@playwright/test'

test.describe('Google OAuth Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login')
  })

  test('should show Google OAuth button', async ({ page }) => {
    // Verify Google OAuth button is present and enabled
    const googleButton = page.getByRole('button', { name: /continue with google/i })
    await expect(googleButton).toBeVisible()
    await expect(googleButton).toBeEnabled()
  })

  test('should initiate Google OAuth flow when button is clicked', async ({ page }) => {
    // Mock the Google OAuth initialization to avoid actual redirect
    await page.route('**/auth/callback*', async route => {
      await route.fulfill({
        status: 302,
        headers: {
          'Location': '/mugshots'
        }
      })
    })

    // Click the Google OAuth button
    const googleButton = page.getByRole('button', { name: /continue with google/i })
    
    // We expect this to trigger the OAuth flow
    // In a real test environment, we would need to mock the Supabase OAuth response
    // For now, we'll verify the button triggers the function
    await googleButton.click()
    
    // The button should show loading state
    await expect(googleButton).toBeDisabled()
  })

  test('should redirect to returnUrl after OAuth completion', async ({ page }) => {
    // Test with a return URL parameter
    await page.goto('/login?returnUrl=/profile')
    
    // Mock successful OAuth callback
    await page.route('**/auth/callback*', async route => {
      await route.fulfill({
        status: 302,
        headers: {
          'Location': '/profile'
        }
      })
    })

    const googleButton = page.getByRole('button', { name: /continue with google/i })
    await googleButton.click()

    // Verify the return URL parameter would be used
    // In a real scenario, this would redirect to /profile
  })

  test('should handle OAuth errors gracefully', async ({ page }) => {
    // Mock OAuth error response
    await page.route('**/auth/callback*', async route => {
      await route.fulfill({
        status: 302,
        headers: {
          'Location': '/error?message=oauth_callback_failed'
        }
      })
    })

    const googleButton = page.getByRole('button', { name: /continue with google/i })
    await googleButton.click()

    // Should remain on login page and show error
    // Note: In a real implementation, we'd check for error messages
  })

  test('should handle OAuth cancellation', async ({ page }) => {
    // Test scenario where user cancels OAuth at Google
    // This would typically result in returning to the login page without changes
    
    const googleButton = page.getByRole('button', { name: /continue with google/i })
    
    // Verify initial state
    await expect(googleButton).toBeVisible()
    await expect(page.url()).toContain('/login')
  })

  test('should redirect new OAuth users to location setup', async ({ page }) => {
    // Mock OAuth callback that creates new user without location
    await page.route('**/auth/callback*', async route => {
      // Simulate new OAuth user needing location setup
      await route.fulfill({
        status: 302,
        headers: {
          'Location': '/auth/location-setup?returnUrl=%2Fmugshots'
        }
      })
    })

    const googleButton = page.getByRole('button', { name: /continue with google/i })
    await googleButton.click()

    // In a real scenario, this would redirect to location setup
    // We're testing the redirect behavior
  })

  test('should handle location setup completion', async ({ page }) => {
    // Navigate directly to location setup page
    await page.goto('/auth/location-setup?returnUrl=/profile')

    // Mock authenticated user
    await page.route('**/supabase/auth/v1/user*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          }
        })
      })
    })

    // Verify location setup page elements
    await expect(page.getByText('Complete Your Profile')).toBeVisible()
    await expect(page.getByText('Skip for Now')).toBeVisible()
    await expect(page.getByText('Continue')).toBeVisible()

    // Test skip functionality
    const skipButton = page.getByText('Skip for Now')
    await skipButton.click()

    // Should redirect to return URL
    await expect(page.url()).toContain('/profile')
  })
}) 