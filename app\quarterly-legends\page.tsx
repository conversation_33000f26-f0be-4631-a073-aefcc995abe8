import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Trophy, Calendar, Users, Star } from 'lucide-react'
import Image from 'next/image'

// Mock data for quarterly competition
const mockCompetitionStatus = {
  currentQuarter: 'Q4 2024',
  isActive: true,
  startDate: '2024-10-01',
  endDate: '2024-12-31',
  totalParticipants: 1247,
  topContenders: [
    { id: 1, name: '<PERSON>', state: 'FL', votes: 2341, mugshot: '/images/mugshot-placeholder.png' },
    { id: 2, name: '<PERSON>', state: 'TX', votes: 2287, mugshot: '/images/mugshot-placeholder.png' },
    { id: 3, name: '<PERSON>', state: 'CA', votes: 2156, mugshot: '/images/mugshot-placeholder.png' },
  ]
}

export default async function QuarterlyLegendsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            Quarterly Legends
          </h1>
          <p className="text-xl text-gray-300 mb-6">
            The ultimate quarterly competition - only the most legendary mugshots make it here
          </p>
          <Badge variant="secondary" className="text-lg px-4 py-2">
            <Calendar className="w-4 h-4 mr-2" />
            {mockCompetitionStatus.currentQuarter}
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Competition Stats */}
          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-400" />
                Competition Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-300">Status:</span>
                <Badge variant={mockCompetitionStatus.isActive ? "default" : "secondary"}>
                  {mockCompetitionStatus.isActive ? "Active" : "Ended"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Participants:</span>
                <span className="text-white font-semibold">{mockCompetitionStatus.totalParticipants}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Quarter:</span>
                <span className="text-white font-semibold">{mockCompetitionStatus.currentQuarter}</span>
              </div>
            </CardContent>
          </Card>

          {/* Competition Timeline */}
          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Calendar className="w-5 h-5 text-cyan-400" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-300">Started:</span>
                <span className="text-white">{mockCompetitionStatus.startDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Ends:</span>
                <span className="text-white">{mockCompetitionStatus.endDate}</span>
              </div>
              <div className="text-sm text-gray-400">
                Voting continues until the end of the quarter
              </div>
            </CardContent>
          </Card>

          {/* How It Works */}
          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Users className="w-5 h-5 text-green-400" />
                How It Works
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm text-gray-300">
                • Monthly winners compete
              </div>
              <div className="text-sm text-gray-300">
                • Community votes for favorites
              </div>
              <div className="text-sm text-gray-300">
                • Highest rated becomes legend
              </div>
              <div className="text-sm text-gray-300">
                • Trophy and recognition
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Contenders */}
        <Card className="bg-gray-800/50 border-purple-500/30">
          <CardHeader>
            <CardTitle className="text-white text-2xl flex items-center gap-2">
              <Star className="w-6 h-6 text-yellow-400" />
              Top Contenders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {mockCompetitionStatus.topContenders.map((contender, index) => (
                <div key={contender.id} className="text-center">
                  <div className="relative mb-4">
                    <Image
                      src={contender.mugshot}
                      alt={contender.name}
                      width={128}
                      height={160}
                      className="w-32 h-40 mx-auto rounded-lg border-2 border-purple-500/50 object-cover"
                    />
                    <Badge
                      variant={index === 0 ? "default" : "secondary"}
                      className="absolute -top-2 -right-2"
                    >
                      #{index + 1}
                    </Badge>
                  </div>
                  <h3 className="text-white font-semibold">{contender.name}</h3>
                  <p className="text-gray-400">{contender.state}</p>
                  <p className="text-cyan-400 font-semibold">{contender.votes} votes</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-500/30">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-white mb-4">
                Vote for Your Quarterly Legend!
              </h3>
              <p className="text-gray-300 mb-6">
                Help decide who becomes the ultimate quarterly legend. Cast your votes now!
              </p>
              <Badge variant="outline" className="text-purple-300 border-purple-300">
                Competition Ends December 31st
              </Badge>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 