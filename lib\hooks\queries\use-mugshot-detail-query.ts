import { useQuery } from '@tanstack/react-query'
import type { SingleMugshotResponse } from '@/lib/services/api-client'

/**
 * Mugshot detail query hook for individual mugshot data with fresh ratings/tags
 * Used in mugshot detail popups and individual mugshot pages
 * 
 * @param mugshotId - The ID of the mugshot to fetch
 * @param enabled - Whether the query should run (default: true)
 * @returns TanStack Query result with mugshot detail data
 */
export function useMugshotDetailQuery(mugshotId: string, enabled = true) {
  return useQuery({
    queryKey: ['mugshot', mugshotId, 'detail'],
    queryFn: async (): Promise<SingleMugshotResponse['data']> => {
      const response = await fetch(`/api/mugshots/${mugshotId}`)
      const result = await response.json()
      
      // Handle API errors by throwing to trigger Query error boundaries
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to fetch mugshot detail')
      }
      
      // Return the data portion that components expect
      return result.data
    },
    enabled,
    // Shorter stale time for fresher data in popups
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    // Always refetch when popup opens for fresh ratings/tags
    refetchOnMount: 'always',
  })
}

/**
 * Prefetch utility for mugshot detail data
 * Can be used to prefetch data on hover or when detail popup is likely to open
 */
export function prefetchMugshotDetail(
  queryClient: { prefetchQuery: (config: object) => Promise<void> },
  mugshotId: string
) {
  return queryClient.prefetchQuery({
    queryKey: ['mugshot', mugshotId, 'detail'],
    queryFn: async () => {
      const response = await fetch(`/api/mugshots/${mugshotId}`)
      const result = await response.json()
      
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to fetch mugshot detail')
      }
      
      return result.data
    },
    staleTime: 1 * 60 * 1000,
  })
} 