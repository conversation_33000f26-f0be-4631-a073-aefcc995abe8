import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, message: 'Authentication required to add tags', error: 'UNAUTHENTICATED' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { mugshotId, tagType } = body

    // Validate input
    if (!mugshotId || typeof mugshotId !== 'number') {
      return NextResponse.json(
        { success: false, message: 'Valid mugshot ID is required', error: 'INVALID_MUGSHOT_ID' },
        { status: 400 }
      )
    }

    if (!tagType || !['wild', 'funny', 'spooky'].includes(tagType)) {
      return NextResponse.json(
        { success: false, message: 'Tag type must be wild, funny, or spooky', error: 'INVALID_TAG_TYPE' },
        { status: 400 }
      )
    }

    // Use database function for tag toggle
    const { data: result, error: dbError } = await supabase
      .rpc('add_tag_to_mugshot', {
        p_mugshot_id: mugshotId,
        p_user_id: user.id,
        p_tag_type: tagType
      })

    if (dbError) {
      console.error('Database error toggling tag:', dbError)
      return NextResponse.json(
        { success: false, message: 'Failed to toggle tag. Please try again.', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }

    // Parse the JSON result from the database function
    if (!result?.success) {
      return NextResponse.json(
        { success: false, message: result?.error || 'Failed to toggle tag', error: 'TAG_OPERATION_FAILED' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `Tag ${result.action} successfully!`,
      data: {
        mugshotId,
        tagType,
        action: result.action, // 'added' or 'removed'
        userId: user.id
      }
    })

  } catch (error) {
    console.error('Tag toggle error:', error)
    return NextResponse.json(
      { success: false, message: 'An unexpected error occurred. Please try again.', error: 'UNEXPECTED_ERROR' },
      { status: 500 }
    )
  }
} 