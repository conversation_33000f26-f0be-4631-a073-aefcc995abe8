/**
 * Location Error Handling Utilities
 * 
 * Provides comprehensive error handling for location detection system
 * with appropriate fallback strategies and user-friendly error messages.
 */

// Error types for location detection
export enum LocationErrorType {
  IP_API_TIMEOUT = 'IP_API_TIMEOUT',
  IP_API_FAILED = 'IP_API_FAILED',
  INVALID_COORDINATES = 'INVALID_COORDINATES',
  DATABASE_QUERY_FAILED = 'DATABASE_QUERY_FAILED',
  NO_AVAILABLE_STATES = 'NO_AVAILABLE_STATES',
  DISTANCE_CALCULATION_FAILED = 'DISTANCE_CALCULATION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Error details interface
export interface LocationError {
  type: LocationErrorType
  message: string
  userMessage: string
  fallbackAction: string
  retryable: boolean
  originalError?: Error
}

// Fallback state configuration
export interface FallbackConfig {
  defaultState: string
  fallbackStates: string[]
  coordinates: {
    latitude: number
    longitude: number
  }
}

// Default fallback configuration
export const DEFAULT_FALLBACK_CONFIG: FallbackConfig = {
  defaultState: 'California',
  fallbackStates: ['California', 'Texas', 'Florida', 'New York', 'Illinois'],
  coordinates: {
    latitude: 39.8283, // US geographic center
    longitude: -98.5795
  }
}

/**
 * Location Error Handler Class
 */
export class LocationErrorHandler {
  private static instance: LocationErrorHandler
  private fallbackConfig: FallbackConfig

  private constructor(config: FallbackConfig = DEFAULT_FALLBACK_CONFIG) {
    this.fallbackConfig = config
  }

  static getInstance(config?: FallbackConfig): LocationErrorHandler {
    if (!LocationErrorHandler.instance) {
      LocationErrorHandler.instance = new LocationErrorHandler(config)
    }
    return LocationErrorHandler.instance
  }

  /**
   * Parse and categorize errors from location detection
   */
  parseError(error: unknown, context: string = 'location-detection'): LocationError {
    const errorMessage = error instanceof Error ? error.message : String(error)
    const lowerMessage = errorMessage.toLowerCase()

    // IP API specific errors
    if (lowerMessage.includes('timeout') || lowerMessage.includes('aborted')) {
      return {
        type: LocationErrorType.IP_API_TIMEOUT,
        message: `IP detection timeout: ${errorMessage}`,
        userMessage: 'Location detection is taking too long. Using default location.',
        fallbackAction: 'Use default state',
        retryable: true,
        originalError: error instanceof Error ? error : undefined
      }
    }

    if (lowerMessage.includes('freeipapi') || lowerMessage.includes('http error')) {
      return {
        type: LocationErrorType.IP_API_FAILED,
        message: `IP API failed: ${errorMessage}`,
        userMessage: 'Unable to detect your location automatically. Please select manually.',
        fallbackAction: 'Use default state',
        retryable: true,
        originalError: error instanceof Error ? error : undefined
      }
    }

    // Coordinate validation errors
    if (lowerMessage.includes('invalid coordinates') || lowerMessage.includes('coordinate')) {
      return {
        type: LocationErrorType.INVALID_COORDINATES,
        message: `Invalid coordinates: ${errorMessage}`,
        userMessage: 'Location data is invalid. Using default location.',
        fallbackAction: 'Use default coordinates',
        retryable: false,
        originalError: error instanceof Error ? error : undefined
      }
    }

    // Database errors
    if (lowerMessage.includes('database') || lowerMessage.includes('supabase') || lowerMessage.includes('query')) {
      return {
        type: LocationErrorType.DATABASE_QUERY_FAILED,
        message: `Database query failed: ${errorMessage}`,
        userMessage: 'Unable to load location data. Please try again.',
        fallbackAction: 'Use cached data or default state',
        retryable: true,
        originalError: error instanceof Error ? error : undefined
      }
    }

    // Network errors
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || lowerMessage.includes('connection')) {
      return {
        type: LocationErrorType.NETWORK_ERROR,
        message: `Network error: ${errorMessage}`,
        userMessage: 'Network connection issue. Please check your internet connection.',
        fallbackAction: 'Use cached data or default state',
        retryable: true,
        originalError: error instanceof Error ? error : undefined
      }
    }

    // Distance calculation errors
    if (lowerMessage.includes('distance') || lowerMessage.includes('haversine')) {
      return {
        type: LocationErrorType.DISTANCE_CALCULATION_FAILED,
        message: `Distance calculation failed: ${errorMessage}`,
        userMessage: 'Unable to calculate nearest location. Using default.',
        fallbackAction: 'Use first available state',
        retryable: false,
        originalError: error instanceof Error ? error : undefined
      }
    }

    // No available states
    if (lowerMessage.includes('no available states') || lowerMessage.includes('no states found')) {
      return {
        type: LocationErrorType.NO_AVAILABLE_STATES,
        message: `No available states: ${errorMessage}`,
        userMessage: 'No location data available. Please try again later.',
        fallbackAction: 'Use hardcoded default state',
        retryable: true,
        originalError: error instanceof Error ? error : undefined
      }
    }

    // Unknown error
    return {
      type: LocationErrorType.UNKNOWN_ERROR,
      message: `Unknown error in ${context}: ${errorMessage}`,
      userMessage: 'Something went wrong with location detection. Using default location.',
      fallbackAction: 'Use default state',
      retryable: true,
      originalError: error instanceof Error ? error : undefined
    }
  }

  /**
   * Get fallback state based on error type and available states
   */
  getFallbackState(_error: LocationError, availableStates?: string[]): string {
    // If we have available states, try to use one of our preferred fallbacks
    if (availableStates && availableStates.length > 0) {
      for (const fallbackState of this.fallbackConfig.fallbackStates) {
        if (availableStates.includes(fallbackState)) {
          console.log(`🔄 Using fallback state: ${fallbackState}`)
          return fallbackState
        }
      }
      
      // If none of our preferred fallbacks are available, use the first available
      console.log(`🔄 Using first available state: ${availableStates[0]}`)
      return availableStates[0]
    }

    // Final fallback to default state
    console.log(`🔄 Using default fallback state: ${this.fallbackConfig.defaultState}`)
    return this.fallbackConfig.defaultState
  }

  /**
   * Get fallback coordinates
   */
  getFallbackCoordinates(): { latitude: number; longitude: number } {
    return { ...this.fallbackConfig.coordinates }
  }

  /**
   * Log error with appropriate level based on severity
   */
  logError(error: LocationError, context: string = 'location-detection'): void {
    const logData = {
      type: error.type,
      message: error.message,
      context,
      retryable: error.retryable,
      timestamp: new Date().toISOString()
    }

    // Log based on error severity
    switch (error.type) {
      case LocationErrorType.IP_API_TIMEOUT:
      case LocationErrorType.NETWORK_ERROR:
        console.warn('⚠️ Location detection warning:', logData)
        break
      
      case LocationErrorType.DATABASE_QUERY_FAILED:
      case LocationErrorType.NO_AVAILABLE_STATES:
        console.error('❌ Location detection error:', logData)
        break
      
      case LocationErrorType.INVALID_COORDINATES:
      case LocationErrorType.DISTANCE_CALCULATION_FAILED:
        console.error('🔧 Location calculation error:', logData)
        break
      
      default:
        console.error('🚨 Unknown location error:', logData)
    }
  }

  /**
   * Check if error should trigger a retry
   */
  shouldRetry(error: LocationError, attemptCount: number, maxAttempts: number = 3): boolean {
    if (attemptCount >= maxAttempts) {
      return false
    }

    return error.retryable && (
      error.type === LocationErrorType.IP_API_TIMEOUT ||
      error.type === LocationErrorType.IP_API_FAILED ||
      error.type === LocationErrorType.NETWORK_ERROR ||
      error.type === LocationErrorType.DATABASE_QUERY_FAILED
    )
  }

  /**
   * Get retry delay based on attempt count (exponential backoff)
   */
  getRetryDelay(attemptCount: number): number {
    return Math.min(1000 * Math.pow(2, attemptCount), 10000) // Max 10 seconds
  }

  /**
   * Update fallback configuration
   */
  updateFallbackConfig(config: Partial<FallbackConfig>): void {
    this.fallbackConfig = { ...this.fallbackConfig, ...config }
  }
}

// Export singleton instance
export const locationErrorHandler = LocationErrorHandler.getInstance()

/**
 * Utility function for handling location errors in components
 */
export function handleLocationError(
  error: unknown,
  context: string = 'component',
  availableStates?: string[]
): {
  errorInfo: LocationError
  fallbackState: string
  fallbackCoordinates: { latitude: number; longitude: number }
} {
  const errorInfo = locationErrorHandler.parseError(error, context)
  locationErrorHandler.logError(errorInfo, context)
  
  return {
    errorInfo,
    fallbackState: locationErrorHandler.getFallbackState(errorInfo, availableStates),
    fallbackCoordinates: locationErrorHandler.getFallbackCoordinates()
  }
}
