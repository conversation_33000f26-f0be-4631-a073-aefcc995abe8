import { SupabaseClient } from '@supabase/supabase-js'

export interface ProfileData {
  userId: string
  fullName: string
  state: string
  county: string
}

export interface Profile {
  id: string
  user_id: string
  full_name: string
  state?: string
  county?: string
  email?: string
  avatar_url?: string
  role: string
  created_at?: string
  updated_at?: string
}

export interface ProfileResult {
  success: boolean
  error?: string
  profile?: Profile
}

export async function createProfile(
  supabase: SupabaseClient,
  profileData: ProfileData
): Promise<ProfileResult> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .insert({
        user_id: profileData.userId,
        full_name: profileData.fullName,
        state: profileData.state || null, // Convert empty string to null
        county: profileData.county || null, // Convert empty string to null
        role: 'user'
      })
      .select() // Add select to return the created profile
      .single()

    if (error) {
      console.error('Profile creation error:', error)
      return {
        success: false,
        error: error.message
      }
    }

    console.log('Profile created successfully:', data)
    return {
      success: true,
      profile: data
    }
  } catch (error) {
    console.error('Profile creation exception:', error)
    return {
      success: false,
      error: 'An unexpected error occurred while creating profile'
    }
  }
}

export async function getProfile(
  supabase: SupabaseClient,
  userId: string
): Promise<ProfileResult> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      return {
        success: false,
        error: error.message
      }
    }

    return {
      success: true,
      profile: data
    }
  } catch {
    return {
      success: false,
      error: 'An unexpected error occurred while fetching profile'
    }
  }
}

export async function updateProfile(
  supabase: SupabaseClient,
  userId: string,
  updates: Partial<Omit<Profile, 'id' | 'user_id' | 'created_at'>>
): Promise<ProfileResult> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      return {
        success: false,
        error: error.message
      }
    }

    return {
      success: true,
      profile: data
    }
  } catch {
    return {
      success: false,
      error: 'An unexpected error occurred while updating profile'
    }
  }
}

// OAuth-specific profile management
export interface OAuthUser {
  id: string
  email?: string
  user_metadata: {
    full_name?: string
    avatar_url?: string
  }
}

export interface OAuthProfileResult {
  success: boolean
  error?: string
  profile?: Profile
}

// Note: createUserProfile moved to server actions in auth-actions.ts
// Use createOAuthProfileAction instead for OAuth profile creation

// Note: updateUserLocation moved to server actions in auth-actions.ts  
// Use updateUserLocationAction instead for location updates 