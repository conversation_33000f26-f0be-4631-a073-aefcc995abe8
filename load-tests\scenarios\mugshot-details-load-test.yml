config:
  target: 'http://localhost:3000'
  phases:
    # Load test specifically for mugshot details API
    # Simulating users viewing individual mugshot details
    - duration: 60
      arrivalRate: 20
      name: "Warm-up for details API"
    - duration: 180
      arrivalRate: 50
      rampTo: 150
      name: "Ramp-up to peak details load"
    - duration: 240
      arrivalRate: 150
      name: "Peak details load"
    - duration: 60
      arrivalRate: 150
      rampTo: 10
      name: "Cool-down"
  
  # Performance requirements for detail page loads
  ensure:
    # Response time SLAs (details pages can be slightly slower due to more data)
    - http.response_time.p95: 2000   # 95% under 2 seconds
    - http.response_time.p99: 4000   # 99% under 4 seconds
    - http.response_time.median: 500 # Median under 500ms
    
    # Error rate requirements
    - http.codes.200: 95  # At least 95% success rate (some IDs may not exist)
    - http.codes.404: 4   # Up to 4% not found (expected for random IDs)
    - http.codes.5xx: 1   # Less than 1% server errors

  http:
    timeout: 25
    pool: 75
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  # Primary scenario: Load test mugshot details endpoint
  - name: "Mugshot Details API Load Test"
    weight: 80
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - Load Test"
          capture:
            - json: "$.success"
              as: "requestSuccess"
            - json: "$.data.mugshot.id"
              as: "mugshotId"
            - json: "$.data.ratings.totalRatings"
              as: "totalRatings"
            - json: "$.data.tags.totalTags"
              as: "totalTags"
          expect:
            - statusCode: [200, 404]  # Accept both success and not found
      
      # Think time simulating user reading the details
      - think: 2

  # Secondary scenario: Sequential detail browsing (realistic user behavior)
  - name: "Sequential Detail Browsing"
    weight: 20
    flow:
      # Start with a random ID
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - Sequential 1"
          capture:
            - json: "$.data.mugshot.id"
              as: "currentId"
      - think: 3
      
      # Browse to next ID (simulate user clicking next/previous)
      - loop:
          - get:
              url: "/api/mugshots/{{ $loopElement }}"
              name: "GET /api/mugshots/[id] - Sequential Browse"
              capture:
                - json: "$.success"
                  as: "browseSuccess"
          - think: 1.5
        over:
          - "{{ currentId + 1 }}"
          - "{{ currentId + 2 }}"
          - "{{ currentId - 1 }}"

processor: "./load-tests/scenarios/data-generators.js"
