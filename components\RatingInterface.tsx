'use client'

import { useState, useEffect } from 'react'
import { Star, Clock, AlertTriangle, CheckCircle, Loader2, Heart } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useRatingMutation, useCanRate, handleUnauthenticatedRating } from '@/lib/hooks/mutations/use-rating-mutation'
import { useUserRatingQuery } from '@/lib/hooks/queries/use-user-rating-query'
import { useRatingStatisticsQuery } from '@/lib/hooks/queries/use-rating-statistics-query'
import { 
  checkTimeGate,
  type TimeGateResult
} from '@/lib/services/rating-service'
import { toast } from 'sonner'

interface RatingInterfaceProps {
  mugshotId: string
  className?: string
  compact?: boolean
}

export default function RatingInterface({ 
  mugshotId, 
  className = '',
  compact = false 
}: RatingInterfaceProps) {
  const { user, isLoading: authLoading } = useAuthStore()
  
  // TanStack Query hooks for rating data
  const { 
    data: ratingStats, 
    isLoading: isLoadingStats 
  } = useRatingStatisticsQuery(mugshotId)
  
  const { 
    data: userRatingData, 
    isLoading: isLoadingUserRating 
  } = useUserRatingQuery(mugshotId)
  
  const ratingMutation = useRatingMutation(mugshotId)
  const canRate = useCanRate(mugshotId)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  
  // Local UI state only
  const [hoveredRating, setHoveredRating] = useState<number>(0)
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle')
  
  // Get current data from TanStack Query
  const statistics = ratingStats || { averageRating: 0, totalRatings: 0 }
  const userRating = userRatingData?.userRating ?? null
  const isSubmitting = ratingMutation.isPending
  
  // Time gate state
  const [timeGate, setTimeGate] = useState<TimeGateResult | null>(null)

  // Load time gate data
  useEffect(() => {
    const loadTimeGate = async () => {
      if (user) {
        try {
          const timeGateResult = await checkTimeGate(mugshotId)
          setTimeGate(timeGateResult)
        } catch (error) {
          console.error('Error loading time gate:', error)
        }
      }
    }
    
    loadTimeGate()
  }, [mugshotId, user])
  
  // Manage loading state based on queries
  useEffect(() => {
    setIsLoading(isLoadingStats || isLoadingUserRating)
  }, [isLoadingStats, isLoadingUserRating])

  // Handle rating submission using TanStack Query
  const handleRating = async (rating: number) => {
    if (!user || !canRate) {
      if (!user) {
        toast.error('Please log in to submit ratings')
        handleUnauthenticatedRating()
      } else if (timeGate && !timeGate.canRate) {
        toast.error(timeGate.message || 'Rating not allowed')
      }
      return
    }

    setIsPopoverOpen(false) // Close popover after rating

    try {
      await ratingMutation.mutateAsync(rating)
      setSubmissionStatus('success')
      toast.success('Rating submitted successfully!')
      
      // Clear success state after delay
      setTimeout(() => {
        setSubmissionStatus('idle')
      }, 1000)
      
    } catch (error) {
      console.error('Rating submission error:', error)
      setSubmissionStatus('error')
      toast.error('Failed to submit rating')
      
      // Clear error state after delay
      setTimeout(() => {
        setSubmissionStatus('idle')
      }, 2000)
    }
  }

  // Get gradient color based on rating (blue to orange spectrum)
  const getGradientColor = (rating: number) => {
    const intensity = rating / 10
    const hue = 240 - (intensity * 60) // Blue (240) to Orange (180->0)
    return `hsl(${hue}, 70%, 60%)`
  }

  // Show loading state
  if (isLoading || authLoading) {
    return (
      <Card className={className}>
        <CardContent className={compact ? "p-4" : "p-6"}>
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading rating...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show time gate message if not allowed
  if (timeGate && !timeGate.canRate) {
    return (
      <Card className={className}>
        <CardContent className={compact ? "p-4" : "p-6"}>
          <div className="text-center space-y-3">
            <div className="flex items-center justify-center space-x-2 text-amber-600">
              <Clock className="h-5 w-5" />
              <span className="font-medium">Rating Not Yet Available</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {timeGate.message}
            </p>
            {timeGate.timeUntilEnabled && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                {Math.ceil(timeGate.timeUntilEnabled / (1000 * 60 * 60))} hours remaining
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Get display rating (from TanStack Query)
  const displayRating = userRating || 0
  const showUserRating = userRating !== null

  return (
    <Card className={className}>
      <CardContent className={compact ? "p-4" : "p-6"}>
        <div className="space-y-4">
          {/* Header */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center space-x-2">
              <Heart className="h-5 w-5 text-red-500" />
              <h3 className="font-semibold text-lg">Rate Attractiveness</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              Rate from 1-10 how attractive you find this person
            </p>
          </div>

          {/* Rating Statistics */}
          {statistics.totalRatings > 0 && (
            <div className="text-center space-y-1">
              <div className="flex items-center justify-center space-x-2">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span className="font-medium">
                  {statistics.averageRating.toFixed(1)}
                </span>
                <span className="text-sm text-muted-foreground">
                  ({statistics.totalRatings} rating{statistics.totalRatings !== 1 ? 's' : ''})
                </span>
              </div>
            </div>
          )}

          {/* Enhanced Rating Interface with Popover */}
          <div className="flex justify-center">
            <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
              <PopoverTrigger asChild>
                <Button 
                  variant="outline" 
                  className={`
                    ${compact ? 'h-8 px-3 text-sm' : 'h-10 px-4'}
                    border-2 border-blue-500/30 hover:border-blue-500/60
                    bg-gradient-to-r from-blue-50 to-purple-50
                    hover:from-blue-100 hover:to-purple-100
                    transition-all duration-200
                  `}
                >
                  <Heart className={`${compact ? 'h-3 w-3' : 'h-4 w-4'} mr-2 text-red-500`} />
                  Rate
                </Button>
              </PopoverTrigger>
              <PopoverContent 
                className={`w-80 p-6 bg-gray-900 border-gray-700 max-w-sm sm:max-w-md`}
                data-testid="rating-popover-content"
              >
                <div className="space-y-4">
                  {/* Popover Header */}
                  <div className="text-center">
                    <h4 className="font-semibold text-white mb-2">Rate Attractiveness</h4>
                    <p className="text-sm text-gray-400">Click a number from 1-10</p>
                  </div>

                  {/* 2x5 Number Grid */}
                  <div 
                    className="grid grid-cols-5 grid-rows-2 gap-2"
                    data-testid="rating-grid"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => {
                      const isActive = rating === displayRating
                      const isHovered = rating === hoveredRating
                      const isCurrentlySubmitting = isSubmitting
                      
                      return (
                        <button
                          key={rating}
                          onClick={() => user && handleRating(rating)}
                          onMouseEnter={() => setHoveredRating(rating)}
                          onMouseLeave={() => setHoveredRating(0)}
                          disabled={!user || isCurrentlySubmitting}
                          className={`
                            relative h-12 w-12 border-2 rounded-lg font-semibold text-lg
                            transition-all duration-300 transform
                            ${user ? 'hover:scale-105 cursor-pointer' : 'cursor-not-allowed opacity-50'}
                            ${isActive ? 'ring-2 ring-blue-400 ring-offset-2 ring-offset-gray-900' : ''}
                            ${isHovered || isActive 
                              ? 'bg-gradient-to-r from-blue-500 to-orange-500 text-white border-transparent shadow-lg' 
                              : 'bg-transparent border-gray-500 text-gray-300 hover:border-gray-400'
                            }
                            ${isCurrentlySubmitting ? 'animate-pulse' : ''}
                          `}
                          style={{
                            backgroundImage: isHovered && user ? 
                              `linear-gradient(135deg, ${getGradientColor(rating)}, ${getGradientColor(rating + 1)})` : 
                              undefined
                          }}
                        >
                          {rating}
                          {isCurrentlySubmitting && (
                            <Loader2 className="absolute inset-0 h-4 w-4 m-auto animate-spin text-white" />
                          )}
                        </button>
                      )
                    })}
                  </div>

                  {/* Tag Selection Buttons - Restored from story 3.3 */}
                  <div className="space-y-3">
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Add a tag</p>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <button
                        disabled={!user}
                        className={`
                          flex items-center justify-center gap-1 px-3 py-2 rounded-lg
                          border border-gray-600 text-sm font-medium
                          transition-all duration-200
                          ${user 
                            ? 'hover:bg-yellow-500/20 hover:border-yellow-500/50 text-gray-300 hover:text-yellow-300' 
                            : 'opacity-50 cursor-not-allowed text-gray-500'
                          }
                        `}
                      >
                        😂 <span>Funny</span>
                      </button>
                      <button
                        disabled={!user}
                        className={`
                          flex items-center justify-center gap-1 px-3 py-2 rounded-lg
                          border border-gray-600 text-sm font-medium
                          transition-all duration-200
                          ${user 
                            ? 'hover:bg-purple-500/20 hover:border-purple-500/50 text-gray-300 hover:text-purple-300' 
                            : 'opacity-50 cursor-not-allowed text-gray-500'
                          }
                        `}
                      >
                        🤪 <span>Wild</span>
                      </button>
                      <button
                        disabled={!user}
                        className={`
                          flex items-center justify-center gap-1 px-3 py-2 rounded-lg
                          border border-gray-600 text-sm font-medium
                          transition-all duration-200
                          ${user 
                            ? 'hover:bg-red-500/20 hover:border-red-500/50 text-gray-300 hover:text-red-300' 
                            : 'opacity-50 cursor-not-allowed text-gray-500'
                          }
                        `}
                      >
                        😱 <span>Scary</span>
                      </button>
                    </div>
                  </div>

                  {/* Authentication prompt for unauthenticated users */}
                  {!user && (
                    <div className="text-center p-3 bg-blue-900/20 rounded-lg border border-blue-700/30">
                      <p className="text-sm text-blue-300 font-medium">
                        Login required to rate
                      </p>
                    </div>
                  )}

                  {/* Status Messages */}
                  {submissionStatus === 'success' && (
                    <div className="flex items-center justify-center space-x-2 text-green-400">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Rating saved!</span>
                    </div>
                  )}

                  {submissionStatus === 'error' && (
                    <div className="flex items-center justify-center space-x-2 text-red-400">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm font-medium">Failed to save rating</span>
                    </div>
                  )}

                  {/* Current Rating Display */}
                  {showUserRating && (
                    <div className="text-center text-sm text-gray-400">
                      Your current rating: <span className="text-green-400 font-medium">{userRating}/10</span>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* User Rating Update Hint */}
          {showUserRating && !isSubmitting && (
            <p className="text-xs text-center text-muted-foreground">
              Click &quot;Rate&quot; to update your rating
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 