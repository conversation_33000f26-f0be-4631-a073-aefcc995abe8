{"aggregate": {"counters": {"vusers.created_by_name.Top-Rated - Gentle Load": 420, "vusers.created": 420, "errors.Undefined function \"generateTopRatedFilters\"": 420, "http.requests": 420, "http.codes.200": 420, "http.responses": 420, "http.downloaded_bytes": 1714442, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 420, "vusers.failed": 0, "vusers.completed": 420}, "rates": {"http.request_rate": 2}, "firstCounterAt": 1753686436476, "firstHistogramAt": 1753686438466, "lastCounterAt": 1753686619042, "lastHistogramAt": 1753686619042, "firstMetricAt": 1753686436476, "lastMetricAt": 1753686619042, "period": 1753686610000, "summaries": {"http.response_time": {"min": 536, "max": 1968, "count": 420, "mean": 583.1, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 757.6, "p99": 1085.9, "p999": 1790.4}, "http.response_time.2xx": {"min": 536, "max": 1968, "count": 420, "mean": 583.1, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 757.6, "p99": 1085.9, "p999": 1790.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 536, "max": 1968, "count": 420, "mean": 583.1, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 757.6, "p99": 1085.9, "p999": 1790.4}, "vusers.session_length": {"min": 3544.7, "max": 4993.8, "count": 420, "mean": 3590.9, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3752.7, "p99": 4065.2, "p999": 4770.6}}, "histograms": {"http.response_time": {"min": 536, "max": 1968, "count": 420, "mean": 583.1, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 757.6, "p99": 1085.9, "p999": 1790.4}, "http.response_time.2xx": {"min": 536, "max": 1968, "count": 420, "mean": 583.1, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 757.6, "p99": 1085.9, "p999": 1790.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 536, "max": 1968, "count": 420, "mean": 583.1, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 757.6, "p99": 1085.9, "p999": 1790.4}, "vusers.session_length": {"min": 3544.7, "max": 4993.8, "count": 420, "mean": 3590.9, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3752.7, "p99": 4065.2, "p999": 4770.6}}}, "intermediate": [{"counters": {"vusers.created_by_name.Top-Rated - Gentle Load": 4, "vusers.created": 4, "errors.Undefined function \"generateTopRatedFilters\"": 4, "http.requests": 4, "http.codes.200": 3, "http.responses": 3, "http.downloaded_bytes": 12247, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 3}, "rates": {"http.request_rate": 1}, "http.request_rate": null, "firstCounterAt": 1753686436476, "firstHistogramAt": 1753686438466, "lastCounterAt": 1753686439476, "lastHistogramAt": 1753686439028, "firstMetricAt": 1753686436476, "lastMetricAt": 1753686439476, "period": "1753686430000", "summaries": {"http.response_time": {"min": 550, "max": 1968, "count": 3, "mean": 1186.3, "p50": 1043.3, "median": 1043.3, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}, "http.response_time.2xx": {"min": 550, "max": 1968, "count": 3, "mean": 1186.3, "p50": 1043.3, "median": 1043.3, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 550, "max": 1968, "count": 3, "mean": 1186.3, "p50": 1043.3, "median": 1043.3, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}}, "histograms": {"http.response_time": {"min": 550, "max": 1968, "count": 3, "mean": 1186.3, "p50": 1043.3, "median": 1043.3, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}, "http.response_time.2xx": {"min": 550, "max": 1968, "count": 3, "mean": 1186.3, "p50": 1043.3, "median": 1043.3, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 550, "max": 1968, "count": 3, "mean": 1186.3, "p50": 1043.3, "median": 1043.3, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}}}, {"counters": {"http.codes.200": 10, "http.responses": 10, "http.downloaded_bytes": 40820, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 10, "vusers.created_by_name.Top-Rated - Gentle Load": 10, "vusers.created": 10, "errors.Undefined function \"generateTopRatedFilters\"": 10, "http.requests": 10, "vusers.failed": 0, "vusers.completed": 10}, "rates": {"http.request_rate": 1}, "http.request_rate": null, "firstCounterAt": 1753686440027, "firstHistogramAt": 1753686440027, "lastCounterAt": 1753686449476, "lastHistogramAt": 1753686449032, "firstMetricAt": 1753686440027, "lastMetricAt": 1753686449476, "period": "1753686440000", "summaries": {"http.response_time": {"min": 547, "max": 560, "count": 10, "mean": 551.6, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "http.response_time.2xx": {"min": 547, "max": 560, "count": 10, "mean": 551.6, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 547, "max": 560, "count": 10, "mean": 551.6, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "vusers.session_length": {"min": 3554.3, "max": 4993.8, "count": 10, "mean": 3752.9, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 4065.2, "p95": 4065.2, "p99": 4065.2, "p999": 4065.2}}, "histograms": {"http.response_time": {"min": 547, "max": 560, "count": 10, "mean": 551.6, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "http.response_time.2xx": {"min": 547, "max": 560, "count": 10, "mean": 551.6, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 547, "max": 560, "count": 10, "mean": 551.6, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "vusers.session_length": {"min": 3554.3, "max": 4993.8, "count": 10, "mean": 3752.9, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 4065.2, "p95": 4065.2, "p99": 4065.2, "p999": 4065.2}}}, {"counters": {"http.codes.200": 10, "http.responses": 10, "http.downloaded_bytes": 40820, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 10, "vusers.failed": 0, "vusers.completed": 10, "vusers.created_by_name.Top-Rated - Gentle Load": 10, "vusers.created": 10, "errors.Undefined function \"generateTopRatedFilters\"": 10, "http.requests": 10}, "rates": {"http.request_rate": 1}, "http.request_rate": null, "firstCounterAt": 1753686450034, "firstHistogramAt": 1753686450034, "lastCounterAt": 1753686459476, "lastHistogramAt": 1753686459037, "firstMetricAt": 1753686450034, "lastMetricAt": 1753686459476, "period": "1753686450000", "summaries": {"http.response_time": {"min": 548, "max": 754, "count": 10, "mean": 575.2, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "http.response_time.2xx": {"min": 548, "max": 754, "count": 10, "mean": 575.2, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 548, "max": 754, "count": 10, "mean": 575.2, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "vusers.session_length": {"min": 3550.4, "max": 3756.9, "count": 10, "mean": 3581.7, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}, "histograms": {"http.response_time": {"min": 548, "max": 754, "count": 10, "mean": 575.2, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "http.response_time.2xx": {"min": 548, "max": 754, "count": 10, "mean": 575.2, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 548, "max": 754, "count": 10, "mean": 575.2, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "vusers.session_length": {"min": 3550.4, "max": 3756.9, "count": 10, "mean": 3581.7, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}}, {"counters": {"http.codes.200": 16, "http.responses": 16, "http.downloaded_bytes": 65312, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 16, "vusers.failed": 0, "vusers.completed": 10, "vusers.created_by_name.Top-Rated - Gentle Load": 18, "vusers.created": 18, "errors.Undefined function \"generateTopRatedFilters\"": 18, "http.requests": 18}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686460031, "firstHistogramAt": 1753686460031, "lastCounterAt": 1753686469596, "lastHistogramAt": 1753686469192, "firstMetricAt": 1753686460031, "lastMetricAt": 1753686469596, "period": "1753686460000", "summaries": {"http.response_time": {"min": 537, "max": 1048, "count": 16, "mean": 614.7, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 596, "p95": 1022.7, "p99": 1022.7, "p999": 1022.7}, "http.response_time.2xx": {"min": 537, "max": 1048, "count": 16, "mean": 614.7, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 596, "p95": 1022.7, "p99": 1022.7, "p999": 1022.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 537, "max": 1048, "count": 16, "mean": 614.7, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 596, "p95": 1022.7, "p99": 1022.7, "p999": 1022.7}, "vusers.session_length": {"min": 3554.2, "max": 3571, "count": 10, "mean": 3562, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}, "histograms": {"http.response_time": {"min": 537, "max": 1048, "count": 16, "mean": 614.7, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 596, "p95": 1022.7, "p99": 1022.7, "p999": 1022.7}, "http.response_time.2xx": {"min": 537, "max": 1048, "count": 16, "mean": 614.7, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 596, "p95": 1022.7, "p99": 1022.7, "p999": 1022.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 537, "max": 1048, "count": 16, "mean": 614.7, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 596, "p95": 1022.7, "p99": 1022.7, "p999": 1022.7}, "vusers.session_length": {"min": 3554.2, "max": 3571, "count": 10, "mean": 3562, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30, "vusers.failed": 0, "vusers.completed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686470022, "firstHistogramAt": 1753686470022, "lastCounterAt": 1753686479594, "lastHistogramAt": 1753686479176, "firstMetricAt": 1753686470022, "lastMetricAt": 1753686479594, "period": "1753686470000", "summaries": {"http.response_time": {"min": 543, "max": 632, "count": 30, "mean": 559.4, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "http.response_time.2xx": {"min": 543, "max": 632, "count": 30, "mean": 559.4, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 632, "count": 30, "mean": 559.4, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "vusers.session_length": {"min": 3544.7, "max": 4090, "count": 30, "mean": 3602.9, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3605.5, "p95": 3605.5, "p99": 4065.2, "p999": 4065.2}}, "histograms": {"http.response_time": {"min": 543, "max": 632, "count": 30, "mean": 559.4, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "http.response_time.2xx": {"min": 543, "max": 632, "count": 30, "mean": 559.4, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 632, "count": 30, "mean": 559.4, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "vusers.session_length": {"min": 3544.7, "max": 4090, "count": 30, "mean": 3602.9, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3605.5, "p95": 3605.5, "p99": 4065.2, "p999": 4065.2}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686480016, "firstHistogramAt": 1753686480016, "lastCounterAt": 1753686489594, "lastHistogramAt": 1753686489159, "firstMetricAt": 1753686480016, "lastMetricAt": 1753686489594, "period": "1753686480000", "summaries": {"http.response_time": {"min": 540, "max": 729, "count": 30, "mean": 567.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 632.8, "p95": 632.8, "p99": 685.5, "p999": 685.5}, "http.response_time.2xx": {"min": 540, "max": 729, "count": 30, "mean": 567.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 632.8, "p95": 632.8, "p99": 685.5, "p999": 685.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 540, "max": 729, "count": 30, "mean": 567.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 632.8, "p95": 632.8, "p99": 685.5, "p999": 685.5}, "vusers.session_length": {"min": 3544.8, "max": 3638.3, "count": 30, "mean": 3562.4, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}, "histograms": {"http.response_time": {"min": 540, "max": 729, "count": 30, "mean": 567.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 632.8, "p95": 632.8, "p99": 685.5, "p999": 685.5}, "http.response_time.2xx": {"min": 540, "max": 729, "count": 30, "mean": 567.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 632.8, "p95": 632.8, "p99": 685.5, "p999": 685.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 540, "max": 729, "count": 30, "mean": 567.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 632.8, "p95": 632.8, "p99": 685.5, "p999": 685.5}, "vusers.session_length": {"min": 3544.8, "max": 3638.3, "count": 30, "mean": 3562.4, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686490017, "firstHistogramAt": 1753686490017, "lastCounterAt": 1753686499594, "lastHistogramAt": 1753686499164, "firstMetricAt": 1753686490017, "lastMetricAt": 1753686499594, "period": "1753686490000", "summaries": {"http.response_time": {"min": 541, "max": 819, "count": 30, "mean": 575.9, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 757.6, "p99": 804.5, "p999": 804.5}, "http.response_time.2xx": {"min": 541, "max": 819, "count": 30, "mean": 575.9, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 757.6, "p99": 804.5, "p999": 804.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 541, "max": 819, "count": 30, "mean": 575.9, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 757.6, "p99": 804.5, "p999": 804.5}, "vusers.session_length": {"min": 3546.5, "max": 3831.8, "count": 30, "mean": 3596.6, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3752.7, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}}, "histograms": {"http.response_time": {"min": 541, "max": 819, "count": 30, "mean": 575.9, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 757.6, "p99": 804.5, "p999": 804.5}, "http.response_time.2xx": {"min": 541, "max": 819, "count": 30, "mean": 575.9, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 757.6, "p99": 804.5, "p999": 804.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 541, "max": 819, "count": 30, "mean": 575.9, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 757.6, "p99": 804.5, "p999": 804.5}, "vusers.session_length": {"min": 3546.5, "max": 3831.8, "count": 30, "mean": 3596.6, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3752.7, "p95": 3752.7, "p99": 3828.5, "p999": 3828.5}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686500020, "firstHistogramAt": 1753686500020, "lastCounterAt": 1753686509594, "lastHistogramAt": 1753686509154, "firstMetricAt": 1753686500020, "lastMetricAt": 1753686509594, "period": "1753686500000", "summaries": {"http.response_time": {"min": 543, "max": 559, "count": 30, "mean": 551.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "http.response_time.2xx": {"min": 543, "max": 559, "count": 30, "mean": 551.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 559, "count": 30, "mean": 551.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "vusers.session_length": {"min": 3547.5, "max": 3575.7, "count": 30, "mean": 3558.6, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}, "histograms": {"http.response_time": {"min": 543, "max": 559, "count": 30, "mean": 551.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "http.response_time.2xx": {"min": 543, "max": 559, "count": 30, "mean": 551.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 559, "count": 30, "mean": 551.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "vusers.session_length": {"min": 3547.5, "max": 3575.7, "count": 30, "mean": 3558.6, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686510022, "firstHistogramAt": 1753686510022, "lastCounterAt": 1753686519594, "lastHistogramAt": 1753686519159, "firstMetricAt": 1753686510022, "lastMetricAt": 1753686519594, "period": "1753686510000", "summaries": {"http.response_time": {"min": 544, "max": 627, "count": 30, "mean": 556.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 572.6, "p99": 572.6, "p999": 572.6}, "http.response_time.2xx": {"min": 544, "max": 627, "count": 30, "mean": 556.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 572.6, "p99": 572.6, "p999": 572.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 544, "max": 627, "count": 30, "mean": 556.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 572.6, "p99": 572.6, "p999": 572.6}, "vusers.session_length": {"min": 3548.1, "max": 3630.9, "count": 30, "mean": 3561.8, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}, "histograms": {"http.response_time": {"min": 544, "max": 627, "count": 30, "mean": 556.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 572.6, "p99": 572.6, "p999": 572.6}, "http.response_time.2xx": {"min": 544, "max": 627, "count": 30, "mean": 556.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 572.6, "p99": 572.6, "p999": 572.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 544, "max": 627, "count": 30, "mean": 556.5, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 561.2, "p95": 572.6, "p99": 572.6, "p999": 572.6}, "vusers.session_length": {"min": 3548.1, "max": 3630.9, "count": 30, "mean": 3561.8, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686520020, "firstHistogramAt": 1753686520020, "lastCounterAt": 1753686529594, "lastHistogramAt": 1753686529153, "firstMetricAt": 1753686520020, "lastMetricAt": 1753686529594, "period": "1753686520000", "summaries": {"http.response_time": {"min": 540, "max": 1109, "count": 30, "mean": 610, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 645.6, "p95": 1043.3, "p99": 1085.9, "p999": 1085.9}, "http.response_time.2xx": {"min": 540, "max": 1109, "count": 30, "mean": 610, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 645.6, "p95": 1043.3, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 540, "max": 1109, "count": 30, "mean": 610, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 645.6, "p95": 1043.3, "p99": 1085.9, "p999": 1085.9}, "vusers.session_length": {"min": 3545.3, "max": 4116.4, "count": 30, "mean": 3619.2, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3678.4, "p95": 4065.2, "p99": 4065.2, "p999": 4065.2}}, "histograms": {"http.response_time": {"min": 540, "max": 1109, "count": 30, "mean": 610, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 645.6, "p95": 1043.3, "p99": 1085.9, "p999": 1085.9}, "http.response_time.2xx": {"min": 540, "max": 1109, "count": 30, "mean": 610, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 645.6, "p95": 1043.3, "p99": 1085.9, "p999": 1085.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 540, "max": 1109, "count": 30, "mean": 610, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 645.6, "p95": 1043.3, "p99": 1085.9, "p999": 1085.9}, "vusers.session_length": {"min": 3545.3, "max": 4116.4, "count": 30, "mean": 3619.2, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3678.4, "p95": 4065.2, "p99": 4065.2, "p999": 4065.2}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686530021, "firstHistogramAt": 1753686530021, "lastCounterAt": 1753686539594, "lastHistogramAt": 1753686539149, "firstMetricAt": 1753686530021, "lastMetricAt": 1753686539594, "period": "1753686530000", "summaries": {"http.response_time": {"min": 541, "max": 573, "count": 30, "mean": 552.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "http.response_time.2xx": {"min": 541, "max": 573, "count": 30, "mean": 552.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 541, "max": 573, "count": 30, "mean": 552.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "vusers.session_length": {"min": 3545.3, "max": 3581.3, "count": 30, "mean": 3558.1, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}, "histograms": {"http.response_time": {"min": 541, "max": 573, "count": 30, "mean": 552.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "http.response_time.2xx": {"min": 541, "max": 573, "count": 30, "mean": 552.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 541, "max": 573, "count": 30, "mean": 552.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 561.2, "p95": 561.2, "p99": 561.2, "p999": 561.2}, "vusers.session_length": {"min": 3545.3, "max": 3581.3, "count": 30, "mean": 3558.1, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122461, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686540025, "firstHistogramAt": 1753686540025, "lastCounterAt": 1753686549594, "lastHistogramAt": 1753686549154, "firstMetricAt": 1753686540025, "lastMetricAt": 1753686549594, "period": "1753686540000", "summaries": {"http.response_time": {"min": 543, "max": 1795, "count": 30, "mean": 652, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 1002.4, "p95": 1107.9, "p99": 1300.1, "p999": 1300.1}, "http.response_time.2xx": {"min": 543, "max": 1795, "count": 30, "mean": 652, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 1002.4, "p95": 1107.9, "p99": 1300.1, "p999": 1300.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 1795, "count": 30, "mean": 652, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 1002.4, "p95": 1107.9, "p99": 1300.1, "p999": 1300.1}, "vusers.session_length": {"min": 3545, "max": 4806.1, "count": 30, "mean": 3661.3, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3984.7, "p95": 4147.4, "p99": 4316.6, "p999": 4316.6}}, "histograms": {"http.response_time": {"min": 543, "max": 1795, "count": 30, "mean": 652, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 1002.4, "p95": 1107.9, "p99": 1300.1, "p999": 1300.1}, "http.response_time.2xx": {"min": 543, "max": 1795, "count": 30, "mean": 652, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 1002.4, "p95": 1107.9, "p99": 1300.1, "p999": 1300.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 1795, "count": 30, "mean": 652, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 1002.4, "p95": 1107.9, "p99": 1300.1, "p999": 1300.1}, "vusers.session_length": {"min": 3545, "max": 4806.1, "count": 30, "mean": 3661.3, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3984.7, "p95": 4147.4, "p99": 4316.6, "p999": 4316.6}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 30, "http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686550035, "firstHistogramAt": 1753686550035, "lastCounterAt": 1753686559595, "lastHistogramAt": 1753686559161, "firstMetricAt": 1753686550035, "lastMetricAt": 1753686559595, "period": "1753686550000", "summaries": {"vusers.session_length": {"min": 3546.6, "max": 3961.3, "count": 30, "mean": 3598.1, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3905.8, "p99": 3905.8, "p999": 3905.8}, "http.response_time": {"min": 539, "max": 955, "count": 30, "mean": 591, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 871.5, "p99": 925.4, "p999": 925.4}, "http.response_time.2xx": {"min": 539, "max": 955, "count": 30, "mean": 591, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 871.5, "p99": 925.4, "p999": 925.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 539, "max": 955, "count": 30, "mean": 591, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 871.5, "p99": 925.4, "p999": 925.4}}, "histograms": {"vusers.session_length": {"min": 3546.6, "max": 3961.3, "count": 30, "mean": 3598.1, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3905.8, "p99": 3905.8, "p999": 3905.8}, "http.response_time": {"min": 539, "max": 955, "count": 30, "mean": 591, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 871.5, "p99": 925.4, "p999": 925.4}, "http.response_time.2xx": {"min": 539, "max": 955, "count": 30, "mean": 591, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 871.5, "p99": 925.4, "p999": 925.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 539, "max": 955, "count": 30, "mean": 591, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 584.2, "p95": 871.5, "p99": 925.4, "p999": 925.4}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686560020, "firstHistogramAt": 1753686560020, "lastCounterAt": 1753686569594, "lastHistogramAt": 1753686569157, "firstMetricAt": 1753686560020, "lastMetricAt": 1753686569594, "period": "1753686560000", "summaries": {"http.response_time": {"min": 543, "max": 735, "count": 30, "mean": 566, "p50": 561.2, "median": 561.2, "p75": 561.2, "p90": 561.2, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 543, "max": 735, "count": 30, "mean": 566, "p50": 561.2, "median": 561.2, "p75": 561.2, "p90": 561.2, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 735, "count": 30, "mean": 566, "p50": 561.2, "median": 561.2, "p75": 561.2, "p90": 561.2, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "vusers.session_length": {"min": 3546.4, "max": 3582.8, "count": 30, "mean": 3561.1, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}, "histograms": {"http.response_time": {"min": 543, "max": 735, "count": 30, "mean": 566, "p50": 561.2, "median": 561.2, "p75": 561.2, "p90": 561.2, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "http.response_time.2xx": {"min": 543, "max": 735, "count": 30, "mean": 566, "p50": 561.2, "median": 561.2, "p75": 561.2, "p90": 561.2, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 543, "max": 735, "count": 30, "mean": 566, "p50": 561.2, "median": 561.2, "p75": 561.2, "p90": 561.2, "p95": 632.8, "p99": 671.9, "p999": 671.9}, "vusers.session_length": {"min": 3546.4, "max": 3582.8, "count": 30, "mean": 3561.1, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3605.5, "p95": 3605.5, "p99": 3605.5, "p999": 3605.5}}}, {"counters": {"http.codes.200": 30, "http.responses": 30, "http.downloaded_bytes": 122460, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 30, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 30, "vusers.created": 30, "errors.Undefined function \"generateTopRatedFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686570023, "firstHistogramAt": 1753686570023, "lastCounterAt": 1753686579594, "lastHistogramAt": 1753686579158, "firstMetricAt": 1753686570023, "lastMetricAt": 1753686579594, "period": "1753686570000", "summaries": {"http.response_time": {"min": 540, "max": 880, "count": 30, "mean": 597.3, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 788.5, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "http.response_time.2xx": {"min": 540, "max": 880, "count": 30, "mean": 597.3, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 788.5, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 540, "max": 880, "count": 30, "mean": 597.3, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 788.5, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "vusers.session_length": {"min": 3546.4, "max": 3786.9, "count": 30, "mean": 3589.2, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3678.4, "p95": 3752.7, "p99": 3752.7, "p999": 3752.7}}, "histograms": {"http.response_time": {"min": 540, "max": 880, "count": 30, "mean": 597.3, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 788.5, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "http.response_time.2xx": {"min": 540, "max": 880, "count": 30, "mean": 597.3, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 788.5, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 540, "max": 880, "count": 30, "mean": 597.3, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 788.5, "p95": 788.5, "p99": 837.3, "p999": 837.3}, "vusers.session_length": {"min": 3546.4, "max": 3786.9, "count": 30, "mean": 3589.2, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3678.4, "p95": 3752.7, "p99": 3752.7, "p999": 3752.7}}}, {"counters": {"http.codes.200": 24, "http.responses": 24, "http.downloaded_bytes": 97968, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 24, "vusers.failed": 0, "vusers.completed": 30, "vusers.created_by_name.Top-Rated - Gentle Load": 22, "vusers.created": 22, "errors.Undefined function \"generateTopRatedFilters\"": 22, "http.requests": 22}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753686580018, "firstHistogramAt": 1753686580018, "lastCounterAt": 1753686589478, "lastHistogramAt": 1753686589175, "firstMetricAt": 1753686580018, "lastMetricAt": 1753686589478, "period": "1753686580000", "summaries": {"http.response_time": {"min": 541, "max": 636, "count": 24, "mean": 561.1, "p50": 550.1, "median": 550.1, "p75": 572.6, "p90": 584.2, "p95": 584.2, "p99": 596, "p999": 596}, "http.response_time.2xx": {"min": 541, "max": 636, "count": 24, "mean": 561.1, "p50": 550.1, "median": 550.1, "p75": 572.6, "p90": 584.2, "p95": 584.2, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 541, "max": 636, "count": 24, "mean": 561.1, "p50": 550.1, "median": 550.1, "p75": 572.6, "p90": 584.2, "p95": 584.2, "p99": 596, "p999": 596}, "vusers.session_length": {"min": 3545.2, "max": 3894.4, "count": 30, "mean": 3596.3, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3678.4, "p95": 3828.5, "p99": 3828.5, "p999": 3828.5}}, "histograms": {"http.response_time": {"min": 541, "max": 636, "count": 24, "mean": 561.1, "p50": 550.1, "median": 550.1, "p75": 572.6, "p90": 584.2, "p95": 584.2, "p99": 596, "p999": 596}, "http.response_time.2xx": {"min": 541, "max": 636, "count": 24, "mean": 561.1, "p50": 550.1, "median": 550.1, "p75": 572.6, "p90": 584.2, "p95": 584.2, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 541, "max": 636, "count": 24, "mean": 561.1, "p50": 550.1, "median": 550.1, "p75": 572.6, "p90": 584.2, "p95": 584.2, "p99": 596, "p999": 596}, "vusers.session_length": {"min": 3545.2, "max": 3894.4, "count": 30, "mean": 3596.3, "p50": 3534.1, "median": 3534.1, "p75": 3605.5, "p90": 3678.4, "p95": 3828.5, "p99": 3828.5, "p999": 3828.5}}}, {"counters": {"http.codes.200": 10, "http.responses": 10, "http.downloaded_bytes": 40820, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 10, "vusers.failed": 0, "vusers.completed": 10, "vusers.created_by_name.Top-Rated - Gentle Load": 10, "vusers.created": 10, "errors.Undefined function \"generateTopRatedFilters\"": 10, "http.requests": 10}, "rates": {"http.request_rate": 1}, "http.request_rate": null, "firstCounterAt": 1753686590026, "firstHistogramAt": 1753686590026, "lastCounterAt": 1753686599477, "lastHistogramAt": 1753686599028, "firstMetricAt": 1753686590026, "lastMetricAt": 1753686599477, "period": "1753686590000", "summaries": {"http.response_time": {"min": 545, "max": 555, "count": 10, "mean": 549.8, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "http.response_time.2xx": {"min": 545, "max": 555, "count": 10, "mean": 549.8, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 545, "max": 555, "count": 10, "mean": 549.8, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "vusers.session_length": {"min": 3546.2, "max": 3563.4, "count": 10, "mean": 3554, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}, "histograms": {"http.response_time": {"min": 545, "max": 555, "count": 10, "mean": 549.8, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "http.response_time.2xx": {"min": 545, "max": 555, "count": 10, "mean": 549.8, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 545, "max": 555, "count": 10, "mean": 549.8, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "vusers.session_length": {"min": 3546.2, "max": 3563.4, "count": 10, "mean": 3554, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 10, "http.codes.200": 10, "http.responses": 10, "http.downloaded_bytes": 40820, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 10, "vusers.created_by_name.Top-Rated - Gentle Load": 10, "vusers.created": 10, "errors.Undefined function \"generateTopRatedFilters\"": 10, "http.requests": 10}, "rates": {"http.request_rate": 1}, "http.request_rate": null, "firstCounterAt": 1753686600043, "firstHistogramAt": 1753686600043, "lastCounterAt": 1753686609477, "lastHistogramAt": 1753686609026, "firstMetricAt": 1753686600043, "lastMetricAt": 1753686609477, "period": "1753686600000", "summaries": {"vusers.session_length": {"min": 3549.6, "max": 3749.9, "count": 10, "mean": 3576.5, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}, "http.response_time": {"min": 548, "max": 907, "count": 10, "mean": 605.7, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 742.6, "p95": 742.6, "p99": 742.6, "p999": 742.6}, "http.response_time.2xx": {"min": 548, "max": 907, "count": 10, "mean": 605.7, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 742.6, "p95": 742.6, "p99": 742.6, "p999": 742.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 548, "max": 907, "count": 10, "mean": 605.7, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 742.6, "p95": 742.6, "p99": 742.6, "p999": 742.6}}, "histograms": {"vusers.session_length": {"min": 3549.6, "max": 3749.9, "count": 10, "mean": 3576.5, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}, "http.response_time": {"min": 548, "max": 907, "count": 10, "mean": 605.7, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 742.6, "p95": 742.6, "p99": 742.6, "p999": 742.6}, "http.response_time.2xx": {"min": 548, "max": 907, "count": 10, "mean": 605.7, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 742.6, "p95": 742.6, "p99": 742.6, "p999": 742.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 548, "max": 907, "count": 10, "mean": 605.7, "p50": 550.1, "median": 550.1, "p75": 561.2, "p90": 742.6, "p95": 742.6, "p99": 742.6, "p999": 742.6}}}, {"counters": {"http.codes.200": 7, "http.responses": 7, "http.downloaded_bytes": 28574, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Top-Rated (Gentle).codes.200": 7, "vusers.failed": 0, "vusers.completed": 10, "vusers.created_by_name.Top-Rated - Gentle Load": 6, "vusers.created": 6, "errors.Undefined function \"generateTopRatedFilters\"": 6, "http.requests": 6}, "rates": {"http.request_rate": 1}, "http.request_rate": null, "firstCounterAt": 1753686610020, "firstHistogramAt": 1753686610020, "lastCounterAt": 1753686619042, "lastHistogramAt": 1753686619042, "firstMetricAt": 1753686610020, "lastMetricAt": 1753686619042, "period": "1753686610000", "summaries": {"http.response_time": {"min": 536, "max": 552, "count": 7, "mean": 546.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "http.response_time.2xx": {"min": 536, "max": 552, "count": 7, "mean": 546.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 536, "max": 552, "count": 7, "mean": 546.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "vusers.session_length": {"min": 3545, "max": 3920.1, "count": 10, "mean": 3591.3, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}, "histograms": {"http.response_time": {"min": 536, "max": 552, "count": 7, "mean": 546.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "http.response_time.2xx": {"min": 536, "max": 552, "count": 7, "mean": 546.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Top-Rated (Gentle)": {"min": 536, "max": 552, "count": 7, "mean": 546.3, "p50": 550.1, "median": 550.1, "p75": 550.1, "p90": 550.1, "p95": 550.1, "p99": 550.1, "p999": 550.1}, "vusers.session_length": {"min": 3545, "max": 3920.1, "count": 10, "mean": 3591.3, "p50": 3534.1, "median": 3534.1, "p75": 3534.1, "p90": 3534.1, "p95": 3534.1, "p99": 3534.1, "p999": 3534.1}}}]}