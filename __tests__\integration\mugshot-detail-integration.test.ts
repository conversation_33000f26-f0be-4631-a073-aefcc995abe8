import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mugshotsServiceServer } from '../../lib/services/mugshots-service-server'

// Mock Supabase client
const mockSupabase = {
  from: vi.fn(() => mockSupabase),
  select: vi.fn(() => mockSupabase),
  eq: vi.fn(() => mockSupabase),
  single: vi.fn(),
}

vi.mock('../../lib/supabase/server', () => ({
  createClient: vi.fn(() => Promise.resolve(mockSupabase)),
}))

describe('Mugshot Detail Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getMugshotById', () => {
    it('should fetch a single mugshot by ID successfully', async () => {
      const mockMugshot = {
        id: 1,
        created_at: '2024-01-01T00:00:00Z',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBooking: '2024-01-01',
        stateOfBooking: 'California',
        countyOfBooking: 'Los Angeles',
        offenseDescription: 'Public Intoxication',
        additionalDetails: 'Additional information',
        imagePath: '/images/mugshot-1.jpg',
        fb_status: null,
        adsText: null,
        jb_post_link: null,
        jb_fb_post: false,
      }

      mockSupabase.single.mockResolvedValueOnce({
        data: mockMugshot,
        error: null,
      })

      const result = await mugshotsServiceServer.getMugshotById(1)

      expect(mockSupabase.from).toHaveBeenCalledWith('mugshots')
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('id'))
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 1)
      expect(mockSupabase.single).toHaveBeenCalled()
      expect(result).toEqual(mockMugshot)
    })

    it('should return null when mugshot is not found', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116', message: 'No rows returned' },
      })

      const result = await mugshotsServiceServer.getMugshotById(999)

      expect(result).toBeNull()
    })

    it('should throw error for database connection issues', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'CONNECTION_ERROR', message: 'Connection failed' },
      })

      await expect(mugshotsServiceServer.getMugshotById(1)).rejects.toThrow('Failed to fetch mugshot')
    })

    it('should validate ID parameter types', async () => {
      const validIds = [1, 100, 9999]
      const invalidIds = [0, -1, NaN]

      // Test valid IDs (should make database calls)
      for (const id of validIds) {
        mockSupabase.single.mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116', message: 'No rows returned' },
        })
        
        await mugshotsServiceServer.getMugshotById(id)
        expect(mockSupabase.eq).toHaveBeenCalledWith('id', id)
      }

      // Reset mocks for invalid tests
      vi.clearAllMocks()

      // Test behavior with edge case IDs (should still make calls, DB will handle validation)
      for (const id of invalidIds) {
        mockSupabase.single.mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116', message: 'No rows returned' },
        })
        
        const result = await mugshotsServiceServer.getMugshotById(id)
        expect(result).toBeNull()
      }
    })

    it('should handle partial data gracefully', async () => {
      const partialMugshot = {
        id: 1,
        created_at: '2024-01-01T00:00:00Z',
        firstName: 'John',
        lastName: null, // Missing lastName
        dateOfBooking: null, // Missing booking date
        stateOfBooking: 'California',
        countyOfBooking: null, // Missing county
        offenseDescription: null, // Missing offense
        additionalDetails: null,
        imagePath: null, // Missing image
        fb_status: null,
        adsText: null,
        jb_post_link: null,
        jb_fb_post: false,
      }

      mockSupabase.single.mockResolvedValueOnce({
        data: partialMugshot,
        error: null,
      })

      const result = await mugshotsServiceServer.getMugshotById(1)

      expect(result).toEqual(partialMugshot)
      expect(result?.id).toBe(1)
      expect(result?.firstName).toBe('John')
      expect(result?.lastName).toBeNull()
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeouts gracefully', async () => {
      mockSupabase.single.mockRejectedValueOnce(new Error('Network timeout'))

      const result = await mugshotsServiceServer.getMugshotById(1)
      
      expect(result).toBeNull()
    })

    it('should handle malformed data responses', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: { invalid: 'data structure' },
        error: null,
      })

      const result = await mugshotsServiceServer.getMugshotById(1)

      // Should still return the data as-is (transformation happens in UI layer)
      expect(result).toEqual({ invalid: 'data structure' })
    })

    it('should handle very large ID numbers', async () => {
      const largeId = 9007199254740991 // Number.MAX_SAFE_INTEGER
      
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116', message: 'No rows returned' },
      })

      const result = await mugshotsServiceServer.getMugshotById(largeId)
      
      expect(mockSupabase.eq).toHaveBeenCalledWith('id', largeId)
      expect(result).toBeNull()
    })
  })

  describe('Database Query Structure', () => {
    it('should select all required fields', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116', message: 'No rows returned' },
      })

      await mugshotsServiceServer.getMugshotById(1)

      // Verify the select method was called with a string containing field names
      expect(mockSupabase.select).toHaveBeenCalled()
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('id'))
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('firstName'))
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('lastName'))
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('offenseDescription'))
    })

    it('should use correct table name', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116', message: 'No rows returned' },
      })

      await mugshotsServiceServer.getMugshotById(1)

      expect(mockSupabase.from).toHaveBeenCalledWith('mugshots')
    })

    it('should use single() method for individual record retrieval', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116', message: 'No rows returned' },
      })

      await mugshotsServiceServer.getMugshotById(1)

      expect(mockSupabase.single).toHaveBeenCalled()
    })
  })
}) 