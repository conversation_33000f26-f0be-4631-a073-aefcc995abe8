import { createClient } from '@/lib/supabase/server'
import type { TagType } from '@/lib/constants'
import type { SearchFilteredMugshotsResult } from '@/lib/types/database'
import { mugshotsNativeService } from './mugshots-native-service'

// Types (reuse from client service)
export interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  category?: string[] // Legacy field, will be deprecated
  tags?: TagType[] // Updated to use proper TagType from database
}

export interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

export interface PaginationOptions {
  page: number
  perPage: number
}

// Updated interface to include rating and tag data
export interface DatabaseMugshot {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
  // Rating statistics
  average_rating?: number
  total_ratings?: number
  // Tag counts
  wild_count?: number
  funny_count?: number
  spooky_count?: number
  // User-specific data (when user is authenticated)
  user_rating?: number | null
  user_tags?: string[] // Array of tag types user has applied
}

class MugshotsServiceServer {
  async healthCheck(): Promise<boolean> {
    try {
      const supabase = await createClient()
      const { error } = await supabase.from('mugshots').select('id').limit(1)
      return !error
    } catch {
      return false
    }
  }

  async getMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    userId?: string // Optional user ID for user-specific data
  ): Promise<DatabaseMugshot[]> {
    try {
      console.log('🔄 [Server Service] Using native service instead of Supabase functions')
      
      // Use the native service directly instead of Supabase functions
      return await mugshotsNativeService.getMugshots(filters, sortOptions, pagination, userId)

    } catch (error) {
      console.error('❌ [Server Service] Error fetching mugshots:', error)
      return []
    }
  }

  // ============================================================================
  // DEPRECATED METHODS - Replaced by native service (kept for reference)
  // ============================================================================
  
  /*
   * DEPRECATED: Unified method using single database function for all sorting and filtering
   * REPLACED BY: mugshotsNativeService.getMugshots()
   * This method has been replaced by the native service implementation
   */
  private async getMugshotsUnified_DEPRECATED(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      const supabase = await createClient()
      
      // Try using the unified database function for all sorting and filtering
      const offset = (pagination.page - 1) * pagination.perPage
      const tagsFilter = filters.tags && filters.tags.length > 0 ? filters.tags : null
      
      // Use the same function for all sorting types
      const sortBy = sortOptions.sortBy === 'most-viewed' ? 'newest' : sortOptions.sortBy // Fallback for most-viewed

      const { data: mugshotsData, error: mugshotsError } = await supabase.rpc(
        'search_filtered_mugshots',
        {
          search_term: filters.searchTerm || null,
          state_filter: filters.state && filters.state !== 'all-states' ? filters.state : null,
          county_filter: filters.county && filters.county !== 'all-counties' ? filters.county : null,
          date_from: filters.dateFrom || null,
          date_to: filters.dateTo || null,
          tags_filter: tagsFilter,
          sort_by: sortBy,
          limit_count: pagination.perPage,
          offset_count: offset
        }
      )

      // If database function fails, fall back to regular query without tag filtering
      if (mugshotsError) {
        console.warn('Database function failed, using fallback:', mugshotsError)
        return this.getMugshotsFallback_DEPRECATED(filters, sortOptions, pagination, userId)
      }

      if (!mugshotsData || mugshotsData.length === 0) {
        return []
      }

      // Type the response properly according to the SQL function
      const typedMugshotsData = mugshotsData as SearchFilteredMugshotsResult[]

      // Get mugshot IDs for batch fetching user-specific data
      const mugshotIds = typedMugshotsData.map((m) => m.id)

      // Fetch user-specific data in parallel (rating and tag stats already included from function)
      const [userRatings, userTags] = await Promise.all([
        userId ? this.getBatchUserRatings(mugshotIds, userId) : Promise.resolve({} as Record<number, number>),
        userId ? this.getBatchUserTags(mugshotIds, userId) : Promise.resolve({} as Record<number, string[]>)
      ])

      // Transform and enhance mugshots with rating and tag data
      const enhancedMugshots: DatabaseMugshot[] = typedMugshotsData.map((mugshot) => {
        // Parse tag_counts jsonb from SQL function - now always includes all tag counts
        const tagCounts = (mugshot.tag_counts as Record<string, number>) || {}
        
        return {
          id: mugshot.id,
          created_at: mugshot.created_at,
          firstName: mugshot.firstName,
          lastName: mugshot.lastName,
          dateOfBooking: mugshot.dateOfBooking,
          stateOfBooking: mugshot.stateOfBooking,
          countyOfBooking: mugshot.countyOfBooking,
          offenseDescription: mugshot.offenseDescription,
          additionalDetails: mugshot.additionalDetails,
          imagePath: mugshot.imagePath,
          fb_status: mugshot.fb_status,
          adsText: mugshot.adsText,
          jb_post_link: mugshot.jb_post_link,
          jb_fb_post: mugshot.jb_fb_post,
          // Rating data comes directly from the database function
          average_rating: Number(mugshot.average_rating) || 0,
          total_ratings: Number(mugshot.total_ratings) || 0,
          // Tag counts from SQL function (preferred) or fallback to batch query
          wild_count: tagCounts.wild || 0,
          funny_count: tagCounts.funny || 0,
          spooky_count: tagCounts.spooky || 0,
          // User-specific data
          user_rating: userRatings[mugshot.id] || null,
          user_tags: userTags[mugshot.id] || []
        };
      });

      return enhancedMugshots

    } catch (error) {
      console.error('Error in getMugshotsUnified:', error)
      return this.getMugshotsFallback_DEPRECATED(filters, sortOptions, pagination, userId)
    }
  }

  /*
   * DEPRECATED: Fast fallback method for when database function fails
   * REPLACED BY: mugshotsNativeService.getMugshots() (which handles fallback internally)
   * This method has been replaced by the native service implementation
   */
  private async getMugshotsFallback_DEPRECATED(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    _userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      const supabase = await createClient()
      console.log('Using fast fallback method for mugshots')
      
      // Skip tag filtering in fallback to ensure speed
      const hasTagFilter = filters.tags && filters.tags.length > 0
      if (hasTagFilter) {
        console.log('Skipping tag filtering in fast fallback to avoid timeout')
      }
      
      // Use the fast database function that only queries mugshots table
      const offset = (pagination.page - 1) * pagination.perPage
      const sortBy = sortOptions.sortBy === 'most-viewed' ? 'newest' : 
                    sortOptions.sortBy === 'top-rated' ? 'newest' :  // Fallback top-rated to newest for speed
                    sortOptions.sortBy

      const { data: mugshotsData, error: mugshotsError } = await supabase.rpc(
        'fast_mugshot_fallback',
        {
          search_term: filters.searchTerm || null,
          state_filter: filters.state && filters.state !== 'all-states' ? filters.state : null,
          county_filter: filters.county && filters.county !== 'all-counties' ? filters.county : null,
          date_from: filters.dateFrom || null,
          date_to: filters.dateTo || null,
          sort_by: sortBy,
          limit_count: pagination.perPage,
          offset_count: offset
        }
      )

      if (mugshotsError) {
        console.error('Fast fallback function failed:', mugshotsError)
        return []
      }

      if (!mugshotsData || mugshotsData.length === 0) {
        return []
      }

             // For the fast fallback, we return basic mugshots without rating/tag data
       // This ensures maximum speed and avoids timeouts
       const basicMugshots: DatabaseMugshot[] = mugshotsData.map((mugshot: DatabaseMugshot) => ({
        ...mugshot,
        average_rating: 0,
        total_ratings: 0,
        wild_count: 0,
        funny_count: 0,
        spooky_count: 0,
        user_rating: null,
        user_tags: []
      }))

      return basicMugshots

    } catch (error) {
      console.error('Error in fast getMugshotsFallback:', error)
      return []
    }
  }

  // Helper methods for fetching rating/tag data
  private async getBatchRatingStatistics(mugshotIds: number[]): Promise<Record<number, { average_rating: number; total_ratings: number }>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('ratings')
        .select('mugshot_id, rating')
        .in('mugshot_id', mugshotIds)

      if (error) {
        console.error('Error fetching rating statistics:', error)
        return {}
      }

      const stats: Record<number, { ratings: number[]; average_rating: number; total_ratings: number }> = {}
      
      data?.forEach(rating => {
        if (!stats[rating.mugshot_id]) {
          stats[rating.mugshot_id] = { ratings: [], average_rating: 0, total_ratings: 0 }
        }
        stats[rating.mugshot_id].ratings.push(rating.rating)
      })

      const result: Record<number, { average_rating: number; total_ratings: number }> = {}
      Object.keys(stats).forEach(mugshotIdStr => {
        const mugshotId = parseInt(mugshotIdStr)
        const ratings = stats[mugshotId].ratings
        result[mugshotId] = {
          average_rating: ratings.length > 0 
            ? Math.round((ratings.reduce((sum, r) => sum + r, 0) / ratings.length) * 100) / 100
            : 0,
          total_ratings: ratings.length
        }
      })

      return result
    } catch (error) {
      console.error('Error in getBatchRatingStatistics:', error)
      return {}
    }
  }

  private async getBatchTagStatistics(mugshotIds: number[]): Promise<Record<number, { wild: number; funny: number; spooky: number }>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('tags')
        .select('mugshot_id, tag_type')
        .in('mugshot_id', mugshotIds)

      if (error) {
        console.error('Error fetching tag statistics:', error)
        return {}
      }

      const stats: Record<number, { wild: number; funny: number; spooky: number }> = {}
      
      data?.forEach(tag => {
        if (!stats[tag.mugshot_id]) {
          stats[tag.mugshot_id] = { wild: 0, funny: 0, spooky: 0 }
        }
        if (tag.tag_type === 'wild') stats[tag.mugshot_id].wild++
        else if (tag.tag_type === 'funny') stats[tag.mugshot_id].funny++
        else if (tag.tag_type === 'spooky') stats[tag.mugshot_id].spooky++
      })

      return stats
    } catch (error) {
      console.error('Error in getBatchTagStatistics:', error)
      return {}
    }
  }

  private async getBatchUserRatings(mugshotIds: number[], userId: string): Promise<Record<number, number>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('ratings')
        .select('mugshot_id, rating')
        .in('mugshot_id', mugshotIds)
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching user ratings:', error)
        return {}
      }

      const result: Record<number, number> = {}
      data?.forEach(rating => {
        result[rating.mugshot_id] = rating.rating
      })

      return result
    } catch (error) {
      console.error('Error in getBatchUserRatings:', error)
      return {}
    }
  }

  private async getBatchUserTags(mugshotIds: number[], userId: string): Promise<Record<number, string[]>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('tags')
        .select('mugshot_id, tag_type')
        .in('mugshot_id', mugshotIds)
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching user tags:', error)
        return {}
      }

      const result: Record<number, string[]> = {}
      data?.forEach(tag => {
        if (!result[tag.mugshot_id]) {
          result[tag.mugshot_id] = []
        }
        result[tag.mugshot_id].push(tag.tag_type)
      })

      return result
    } catch (error) {
      console.error('Error in getBatchUserTags:', error)
      return {}
    }
  }

  async getMugshotCount(filters: MugshotFilters = {}): Promise<number> {
    try {
      console.log('🔄 [Server Service] Using native service for count instead of Supabase functions')
      
      // Use the native service directly instead of Supabase functions
      return await mugshotsNativeService.getMugshotCount(filters)
      
    } catch (error) {
      console.error('❌ [Server Service] Error fetching mugshot count:', error)
      return 0
    }
  }

  async getMugshotById(id: number, userId?: string): Promise<DatabaseMugshot | null> {
    try {
      console.log('🔄 [Server Service] Using native service for single mugshot instead of direct queries')
      
      // Use the native service directly instead of manual queries
      return await mugshotsNativeService.getMugshotById(id, userId)
      
    } catch (error) {
      console.error('❌ [Server Service] Error fetching mugshot by ID:', error)
      return null
    }
  }
}

// Export a single instance for server-side use
export const mugshotsServiceServer = new MugshotsServiceServer() 