# Top-Rated Scaling Test - Find Maximum Capacity
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 30
      arrivalRate: 3
      name: "Phase 1: Proven capacity (3/sec)"
    - duration: 60
      arrivalRate: 5
      name: "Phase 2: Higher load (5/sec)"
    - duration: 60
      arrivalRate: 7
      name: "Phase 3: Push limits (7/sec)"
    - duration: 30
      arrivalRate: 3
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 2000   # Allow higher for scaling
    - http.response_time.median: 800 # Allow higher under load
    - http.codes.200: 85             # Still expect high success
    - http.codes.5xx: 10             # Allow some errors at limits

  http:
    timeout: 25
    pool: 15  # Increased for higher concurrency
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Top-Rated - Scaling Test"
    weight: 100
    flow:
      - function: "generateTopRatedFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "fast"
            sortBy: "top-rated"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
          name: "GET /api/mugshots-fast - Top-Rated Scaling"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 2

processor: "./load-tests/focused-tests/data-generators-focused.js"
