import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NextRequest, NextResponse } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'

// Mock Supabase
vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn()
}))

describe('Authentication Middleware', () => {
  let mockSupabase: any
  let mockRequest: NextRequest

  beforeEach(async () => {
    vi.clearAllMocks()
    
    mockSupabase = {
      auth: {
        getUser: vi.fn()
      }
    }

    const { createServerClient } = await import('@supabase/ssr')
    vi.mocked(createServerClient).mockReturnValue(mockSupabase)

    // Create mock request
    mockRequest = new NextRequest(new URL('http://localhost:3000/'))
  })

  it('should allow access to public routes for unauthenticated users', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    })

    const request = new NextRequest(new URL('http://localhost:3000/'))
    const response = await updateSession(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response.status).not.toBe(307) // Not a redirect
  })

  it('should redirect unauthenticated users from user-protected routes only', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    })

    const request = new NextRequest(new URL('http://localhost:3000/profile'))
    const response = await updateSession(request)

    expect(response.status).toBe(307) // Redirect status
    expect(response.headers.get('location')).toContain('/login')
    expect(response.headers.get('location')).toContain('returnUrl=%2Fprofile')
  })

  it('should allow access to user-protected routes for authenticated users', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { 
        user: { 
          id: 'user-123', 
          email: '<EMAIL>' 
        } 
      },
      error: null
    })

    const request = new NextRequest(new URL('http://localhost:3000/profile'))
    const response = await updateSession(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response.status).not.toBe(307) // Not a redirect
  })

  it('should allow access to login page for unauthenticated users', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    })

    const request = new NextRequest(new URL('http://localhost:3000/login'))
    const response = await updateSession(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response.status).not.toBe(307) // Not a redirect
  })

  it('should handle public vs protected routes correctly', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    })

    // Public routes - should NOT redirect
    const publicRoutes = ['/mugshots', '/popular', '/weekly-best', '/']
    for (const route of publicRoutes) {
      const request = new NextRequest(new URL(`http://localhost:3000${route}`))
      const response = await updateSession(request)
      expect(response.status).not.toBe(307) // Should NOT redirect
    }

    // Protected routes - should redirect
    const protectedRoutes = ['/profile', '/admin']
    for (const route of protectedRoutes) {
      const request = new NextRequest(new URL(`http://localhost:3000${route}`))
      const response = await updateSession(request)
      expect(response.status).toBe(307) // Should redirect
      expect(response.headers.get('location')).toContain('/login')
    }
  })

  it('should handle admin routes with role checking', async () => {
    // Mock non-admin user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { 
        user: { 
          id: 'user-123', 
          email: '<EMAIL>' 
        } 
      },
      error: null
    })

    // Mock profile query for non-admin user
    mockSupabase.from = vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: { role: 'user' },
            error: null
          })
        })
      })
    })

    const request = new NextRequest(new URL('http://localhost:3000/admin'))
    const response = await updateSession(request)

    expect(response.status).toBe(307) // Should redirect non-admin users
    expect(response.headers.get('location')).toContain('/mugshots')
  })
}) 