import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock Supabase clients
vi.mock('../../lib/supabase/server')
vi.mock('../../lib/supabase/client')

const mockServerSupabase = {
  auth: {
    getSession: vi.fn(),
    getUser: vi.fn()
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn()
      }))
    })),
    insert: vi.fn(() => ({
      select: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
}

const mockClientSupabase = {
  auth: {
    signInWithOAuth: vi.fn(),
    onAuthStateChange: vi.fn(() => ({
      data: { subscription: { unsubscribe: vi.fn() } }
    }))
  }
}

// Mock the actual functions we need
vi.mock('../../lib/supabase/server', () => ({
  createClient: vi.fn(() => mockServerSupabase)
}))

vi.mock('../../lib/supabase/client', () => ({
  createClient: vi.fn(() => mockClientSupabase)
}))

describe('OAuth Integration Flow', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('New User OAuth Flow', () => {
    it('should create profile for new Google OAuth user', async () => {
      // Mock new user scenario
      const newUser = {
        id: 'new-user-123',
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'New User',
          avatar_url: 'https://avatar.url'
        }
      }

      // Mock profile doesn't exist
      mockServerSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found
      })

      // Mock profile creation
      mockServerSupabase.from().insert().select().single.mockResolvedValue({
        data: {
          id: 'profile-123',
          user_id: 'new-user-123',
          email: '<EMAIL>',
          full_name: 'New User',
          avatar_url: 'https://avatar.url',
          role: 'user'
        },
        error: null
      })

      // Import the function to test
      const { createUserProfile } = await import('../../lib/profile-utils')
      
      const result = await createUserProfile(newUser)

      expect(mockServerSupabase.from).toHaveBeenCalledWith('profiles')
      expect(result).toEqual({
        success: true,
        profile: expect.objectContaining({
          user_id: 'new-user-123',
          email: '<EMAIL>',
          role: 'user'
        })
      })
    })

    it('should handle existing user OAuth login', async () => {
      const existingUser = {
        id: 'existing-user-123',
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'Existing User'
        }
      }

      // Mock profile exists
      mockServerSupabase.from().select().eq().single.mockResolvedValue({
        data: {
          id: 'profile-456',
          user_id: 'existing-user-123',
          email: '<EMAIL>',
          role: 'user'
        },
        error: null
      })

      const { createUserProfile } = await import('../../lib/profile-utils')
      
      const result = await createUserProfile(existingUser)

      expect(result).toEqual({
        success: true,
        profile: expect.objectContaining({
          user_id: 'existing-user-123',
          email: '<EMAIL>'
        })
      })
    })
  })

  describe('OAuth Error Handling', () => {
    it('should handle profile creation errors gracefully', async () => {
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        user_metadata: { full_name: 'Test User' }
      }

      // Mock profile doesn't exist
      mockServerSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' }
      })

      // Mock profile creation error
      mockServerSupabase.from().insert().select().single.mockResolvedValue({
        data: null,
        error: { message: 'Database connection error' }
      })

      const { createUserProfile } = await import('../../lib/profile-utils')
      
      const result = await createUserProfile(user)

      expect(result).toEqual({
        success: false,
        error: 'Database connection error'
      })
    })
  })
}) 