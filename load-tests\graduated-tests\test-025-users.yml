# Test 2: 25 Concurrent Users - Light Load Test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm-up: 5 users/sec"
    - duration: 180
      arrivalRate: 12
      name: "Steady: 12 users/sec (25 concurrent)"
    - duration: 60
      arrivalRate: 5
      name: "Cool-down: 5 users/sec"
  
  ensure:
    - http.response_time.p95: 1500   # 95% under 1.5 seconds
    - http.response_time.median: 400 # Median under 400ms
    - http.codes.200: 95             # 95% success rate
    - http.codes.5xx: 2              # Less than 2% server errors

  http:
    timeout: 15
    pool: 30
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "25 Users - Mugshots API"
    weight: 75
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - 25 users"
      - think: 2

  - name: "25 Users - Details API"
    weight: 25
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 25 users"
      - think: 3

processor: "../scenarios/data-generators.js"
