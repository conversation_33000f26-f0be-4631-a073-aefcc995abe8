'use client'

import { <PERSON>, <PERSON> } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { useRatingTimeGate } from '@/lib/hooks/use-rating-timegate'
import RatingSectionSkeleton from '@/components/RatingSectionSkeleton'

interface MugshotRatingTimeGateProps {
  mugshotId: string
  dateOfBooking: string | null
  state?: string | null
  county?: string | null
  className?: string
  onRatingEnabled?: () => void
}

export default function MugshotRatingTimeGate({
  mugshotId: _mugshotId,
  dateOfBooking,
  state,
  county,
  className = '',
  onRatingEnabled
}: MugshotRatingTimeGateProps) {
  const { 
    isEnabled, 
    formattedCountdown, 
    isLoading, 
    error 
  } = useRatingTimeGate({
    dateOfBooking,
    state,
    county
  })

  // Call the callback when rating becomes enabled
  if (isEnabled && onRatingEnabled) {
    onRatingEnabled()
  }

  // Show skeleton while loading
  if (isLoading) {
    return <RatingSectionSkeleton className={className} showTags={false} />
  }

  // Show error state
  if (error) {
    return (
      <div className={`bg-gray-800/50 rounded-lg p-4 ${className}`}>
        <div className="text-center">
          <div className="text-red-400 text-sm mb-2">⚠️ Unable to check rating status</div>
          <div className="text-gray-400 text-xs">{error}</div>
        </div>
      </div>
    )
  }

  // If rating is enabled, don't render this component
  // The parent should switch to SimpleMugshotRating
  if (isEnabled) {
    return null
  }

  return (
    <div className={`bg-gray-800/50 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold text-white mb-1">Rating Opens Soon</h3>
        <p className="text-gray-400 text-sm">Rating will be available at midnight</p>
      </div>

      {/* Countdown Display */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Clock className="h-5 w-5 text-purple-400" />
          <span className="text-gray-300 text-sm font-medium">Time Remaining</span>
        </div>
        
        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-4">
          <div className="text-3xl font-mono font-bold text-white tracking-wider">
            {formattedCountdown}
          </div>
          <div className="text-xs text-gray-400 mt-1 flex justify-between">
            <span>Hours</span>
            <span>Minutes</span>
            <span>Seconds</span>
          </div>
        </div>
      </div>

      {/* Notify Me Button */}
      <div className="text-center">
        <Button 
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-2 text-sm font-medium w-full sm:w-auto"
          disabled={false} // Button is always enabled for now
        >
          <Bell className="mr-2 h-4 w-4" />
          Notify Me
        </Button>
        <p className="text-xs text-gray-500 mt-2">
          Get notified when rating opens
        </p>
      </div>

      {/* Timezone Info */}
      <div className="text-center mt-4 pt-3 border-t border-gray-700/50">
        <p className="text-xs text-gray-500">
          Timer based on Central Time (America/Chicago)
        </p>
      </div>
    </div>
  )
} 