# Fast Queries Test - Simple Filters with Newest/Most-Viewed Sort
# Tests state, county, search, date filters with fast sort options (no joins)
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 8
      name: "Warm-up: Fast queries"
    - duration: 240
      arrivalRate: 20
      name: "Steady: Fast query performance"
    - duration: 60
      arrivalRate: 8
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 1200   # Should be reasonably fast
    - http.response_time.median: 300 # Should be fast
    - http.codes.200: 95             # High success rate
    - http.codes.5xx: 2              # Low server errors

  http:
    timeout: 15
    pool: 25
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Fast Queries - Simple Filters"
    weight: 100
    flow:
      - function: "generateFastQueryFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "fast"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
          name: "GET /api/mugshots-fast - Fast Queries (Optimized)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
      - think: 1.5

processor: "./load-tests/focused-tests/data-generators-focused.js"
