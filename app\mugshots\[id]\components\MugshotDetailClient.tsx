"use client"

import { useState } from 'react'
import { Share2, Link2, Twitter, Facebook, Smartphone } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface MugshotDetailClientProps {
  mugshot: {
    id: number
    name: string
    location: string
    arrestDate?: string
    birthDate?: string
    offenses?: string[]
    image: string
  }
}

export default function MugshotDetailClient({ mugshot }: MugshotDetailClientProps) {
  const [copied, setCopied] = useState(false)

  // NOTE: API call removed from here since MugshotModal already calls 
  // loadMugshotWithStoreUpdate when the popup opens. This prevents 
  // duplicate API calls and race conditions.

  const shareUrl = typeof window !== 'undefined' ? window.location.href : ''
  const shareTitle = `${mugshot.name} - ${mugshot.location} Mugshot`
  const shareDescription = `View ${mugshot.name}'s arrest record from ${mugshot.location}.`

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy URL:', error)
    }
  }

  const handleShareTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareTitle)}&url=${encodeURIComponent(shareUrl)}`
    window.open(twitterUrl, '_blank', 'noopener,noreferrer')
  }

  const handleShareFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`
    window.open(facebookUrl, '_blank', 'noopener,noreferrer')
  }

  const handleShareNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareDescription,
          url: shareUrl,
        })
      } catch (error) {
        console.error('Native sharing failed:', error)
        // Fallback to copy link
        handleCopyLink()
      }
    } else {
      // Fallback to copy link
      handleCopyLink()
    }
  }

  return (
    <Card className="bg-gray-800/50 border-pink-500/30 text-white">
      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Share2 className="h-5 w-5 text-cyan-400" />
          <h3 className="text-lg font-semibold">Share This Mugshot</h3>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyLink}
            className="border-cyan-500/30 text-white hover:bg-gray-700 flex items-center gap-2"
          >
            <Link2 className="h-4 w-4" />
            {copied ? 'Copied!' : 'Copy Link'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleShareTwitter}
            className="border-cyan-500/30 text-white hover:bg-gray-700 flex items-center gap-2"
          >
            <Twitter className="h-4 w-4" />
            Twitter
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleShareFacebook}
            className="border-cyan-500/30 text-white hover:bg-gray-700 flex items-center gap-2"
          >
            <Facebook className="h-4 w-4" />
            Facebook
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleShareNative}
            className="border-cyan-500/30 text-white hover:bg-gray-700 flex items-center gap-2"
          >
            <Smartphone className="h-4 w-4" />
            Share
          </Button>
        </div>
      </CardContent>
    </Card>
  )
} 