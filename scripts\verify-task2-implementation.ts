/**
 * Verification script for Task 2 implementation
 * Tests that all query hooks and mutations work correctly
 */

// Test imports
try {
  // Query hooks
  const mugshotsQuery = require('../lib/hooks/queries/use-mugshots-query')
  const mugshotDetailQuery = require('../lib/hooks/queries/use-mugshot-detail-query')  
  const userMugshotDataQuery = require('../lib/hooks/queries/use-user-mugshot-data-query')
  
  // Mutation hooks
  const ratingMutation = require('../lib/hooks/mutations/use-rating-mutation')
  const tagMutation = require('../lib/hooks/mutations/use-tag-mutation')
  
  // Query keys and types
  const queryKeys = require('../lib/query/query-keys')
  const queryTypes = require('../types/query-types')
  
  console.log('✅ All Task 2 files imported successfully')
  
  // Verify exports
  const requiredExports = {
    'useMugshotsQuery': mugshotsQuery.useMugshotsQuery,
    'prefetchMugshots': mugshotsQuery.prefetchMugshots,
    'useMugshotDetailQuery': mugshotDetailQuery.useMugshotDetailQuery,
    'prefetchMugshotDetail': mugshotDetailQuery.prefetchMugshotDetail,
    'useUserMugshotDataQuery': userMugshotDataQuery.useUserMugshotDataQuery,
    'prefetchUserMugshotData': userMugshotDataQuery.prefetchUserMugshotData,
    'useRatingMutation': ratingMutation.useRatingMutation,
    'useCanRate': ratingMutation.useCanRate,
    'handleUnauthenticatedRating': ratingMutation.handleUnauthenticatedRating,
    'useTagMutation': tagMutation.useTagMutation,
    'useCanTag': tagMutation.useCanTag,
    'handleUnauthenticatedTag': tagMutation.handleUnauthenticatedTag,
    'useUserTags': tagMutation.useUserTags,
    'useTagCounts': tagMutation.useTagCounts,
    'queryKeys': queryKeys.queryKeys,
    'invalidationHelpers': queryKeys.invalidationHelpers,
    'defaultCacheConfig': queryKeys.defaultCacheConfig,
  }
  
  let missingExports = []
  
  for (const [name, exportValue] of Object.entries(requiredExports)) {
    if (typeof exportValue === 'undefined') {
      missingExports.push(name)
    }
  }
  
  if (missingExports.length > 0) {
    console.log('❌ Missing exports:', missingExports)
    process.exit(1)
  }
  
  console.log('✅ All required exports are available')
  
  // Test query key patterns
  const testQueryKeys = queryKeys.queryKeys
  
  const testResults = {
    mugshots: testQueryKeys.mugshots({ search: 'test' }, { sortBy: 'newest' }, { page: 1, perPage: 12 }),
    mugshotDetail: testQueryKeys.mugshotDetail('123'),
    userMugshotData: testQueryKeys.userMugshotData('123'),
    ratings: testQueryKeys.ratings('123'),
    tags: testQueryKeys.tags('123'),
  }
  
  console.log('✅ Query key factory working:', testResults)
  
  // Test cache configurations
  const cacheConfig = queryKeys.defaultCacheConfig
  console.log('✅ Cache configurations available:', Object.keys(cacheConfig))
  
  console.log('\n🎉 Task 2 implementation verified successfully!')
  console.log('All query hooks, mutations, and utilities are ready for use.')
  
} catch (error) {
  console.error('❌ Task 2 verification failed:', error instanceof Error ? error.message : String(error))
  process.exit(1)
} 