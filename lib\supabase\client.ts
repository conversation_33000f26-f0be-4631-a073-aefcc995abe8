import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        // Increase session refresh threshold to reduce auth calls
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        // Reduce auto-refresh frequency to prevent timeout issues
        flowType: 'pkce'
      },
      global: {
        // Add timeout for all requests
        headers: {
          'X-Client-Info': 'atm-webapp'
        }
      },
      db: {
        // Add database timeout settings
        schema: 'public'
      },
      realtime: {
        // Disable realtime to reduce connection overhead
        params: {
          eventsPerSecond: 2
        }
      }
    }
  )
} 