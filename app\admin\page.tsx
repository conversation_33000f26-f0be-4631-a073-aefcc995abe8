import { Suspense } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Users, FileImage, Star, TrendingUp, Activity, Eye, ThumbsUp, Flag, BarChart3, Shield, ClipboardList, Brain, Trophy } from 'lucide-react'


// Admin Dashboard Skeleton
function AdminDashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <Skeleton className="h-8 w-64 mb-2" />
        <Skeleton className="h-4 w-96" />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="bg-gray-800/30 border-pink-500/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-5 w-5 rounded" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-800/30 border-pink-500/30">
          <CardHeader>
            <Skeleton className="h-5 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
        <Card className="bg-gray-800/30 border-pink-500/30">
          <CardHeader>
            <Skeleton className="h-5 w-40" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Mock data - in real app this would come from your database
const getDashboardStats = async () => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    totalUsers: 12547,
    totalMugshots: 8943,
    totalRatings: 45621,
    avgRating: 4.2,
    activeUsers: 1823,
    todaysViews: 23456,
    totalVotes: 18739,
    flaggedContent: 23
  }
}

async function AdminOverview() {
  const stats = await getDashboardStats()

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Total Users</CardTitle>
              <Users className="h-5 w-5 text-pink-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-green-400 flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Total Mugshots</CardTitle>
              <FileImage className="h-5 w-5 text-cyan-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalMugshots.toLocaleString()}</div>
            <p className="text-xs text-green-400 flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Total Ratings</CardTitle>
              <Star className="h-5 w-5 text-yellow-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalRatings.toLocaleString()}</div>
            <p className="text-xs text-gray-400">
              Avg: {stats.avgRating}/5.0
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Active Users</CardTitle>
              <Activity className="h-5 w-5 text-green-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.activeUsers.toLocaleString()}</div>
            <p className="text-xs text-gray-400">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Today&apos;s Views</CardTitle>
              <Eye className="h-5 w-5 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.todaysViews.toLocaleString()}</div>
            <p className="text-xs text-green-400 flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              +15% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Total Votes</CardTitle>
              <ThumbsUp className="h-5 w-5 text-purple-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.totalVotes.toLocaleString()}</div>
            <p className="text-xs text-gray-400">Weekly competitions</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-orange-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Flagged Content</CardTitle>
              <Flag className="h-5 w-5 text-orange-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.flaggedContent}</div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-orange-400 border-orange-400">
                Needs Review
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30 hover:bg-gray-800/50 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-300">Platform Health</CardTitle>
              <Activity className="h-5 w-5 text-green-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">Excellent</div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-green-400 border-green-400">
                All Systems Operational
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Placeholder Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-800/30 border-pink-500/30">
          <CardHeader>
            <CardTitle className="text-white">User Activity Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-gray-400">
              <div className="text-center">
                <TrendingUp className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Chart component will be implemented in future stories</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/30 border-pink-500/30">
          <CardHeader>
            <CardTitle className="text-white">Content Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-gray-400">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Chart component will be implemented in future stories</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

async function AdminDashboardContent() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Admin Dashboard</h1>
        <p className="text-gray-400">
          Manage platform operations and moderate content
        </p>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="moderation" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Moderation
          </TabsTrigger>
          <TabsTrigger value="winners" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Winners
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            Audit Log
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="auto-flagging" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Auto-Flagging
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Suspense fallback={<AdminDashboardSkeleton />}>
            <AdminOverview />
          </Suspense>
        </TabsContent>

        <TabsContent value="moderation" className="space-y-6">
          
        </TabsContent>

        <TabsContent value="winners" className="space-y-6">
         
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
         
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          
        </TabsContent>

        <TabsContent value="auto-flagging" className="space-y-6">
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function AdminDashboard() {
  return (
    <Suspense fallback={<AdminDashboardSkeleton />}>
      <AdminDashboardContent />
    </Suspense>
  )
} 