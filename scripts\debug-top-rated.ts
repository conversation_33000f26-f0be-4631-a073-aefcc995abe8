#!/usr/bin/env tsx

import { createClient } from '@/lib/supabase/server'
import { mugshotsServiceServer } from '@/lib/services/mugshots-service-server'

async function main() {
  console.log('🔍 Debugging Top-Rated Mugshots Functionality')
  console.log('=' .repeat(50))

  const supabase = await createClient()

  // Check if database functions exist
  console.log('\n1. Testing unified database functions...')
  try {
    // Test top-rated sorting
    const { data: topRatedData, error: topRatedError } = await supabase.rpc(
      'search_filtered_mugshots',
      {
        search_term: null,
        state_filter: null,
        county_filter: null,
        date_from: null,
        date_to: null,
        tags_filter: null,
        sort_by: 'top-rated',
        limit_count: 5,
        offset_count: 0
      }
    )

    if (topRatedError) {
      console.error('❌ Top-rated function error:', topRatedError)
      console.log('💡 This usually means the migration hasn\'t been run yet')
      console.log('   Run: npx supabase db push')
    } else {
      console.log('✅ Top-rated function works!')
      console.log(`   Found ${topRatedData?.length || 0} top-rated mugshots`)
    }

    // Test newest sorting
    const { data: newestData, error: newestError } = await supabase.rpc(
      'search_filtered_mugshots',
      {
        search_term: null,
        state_filter: null,
        county_filter: null,
        date_from: null,
        date_to: null,
        tags_filter: null,
        sort_by: 'newest',
        limit_count: 5,
        offset_count: 0
      }
    )

    if (newestError) {
      console.error('❌ Newest function error:', newestError)
    } else {
      console.log('✅ Newest function works!')
      console.log(`   Found ${newestData?.length || 0} newest mugshots`)
    }

    // Test count function
    const { data: countData, error: countError } = await supabase.rpc(
      'count_filtered_mugshots',
      {
        search_term: null,
        state_filter: null,
        county_filter: null,
        date_from: null,
        date_to: null,
        tags_filter: null
      }
    )

    if (countError) {
      console.error('❌ Count function error:', countError)
    } else {
      console.log(`✅ Count function works! Total: ${countData}`)
    }
  } catch (error) {
    console.error('❌ Function test failed:', error)
  }

  // Check total mugshots vs rated mugshots
  console.log('\n2. Checking mugshot data...')
  try {
    const { data: allMugshots, error: allError } = await supabase
      .from('mugshots')
      .select('id', { count: 'exact', head: true })

    if (allError) {
      console.error('❌ Error fetching all mugshots:', allError)
    } else {
      console.log(`📊 Total mugshots in database: ${allMugshots || 0}`)
    }

    const { data: ratings, error: ratingsError } = await supabase
      .from('ratings')
      .select('mugshot_id', { count: 'exact', head: true })

    if (ratingsError) {
      console.error('❌ Error fetching ratings:', ratingsError)
    } else {
      console.log(`⭐ Total ratings in database: ${ratings || 0}`)
    }

    // Get unique rated mugshots
    const { data: ratedMugshots, error: ratedError } = await supabase
      .from('ratings')
      .select('mugshot_id')

    if (!ratedError && ratedMugshots) {
      const uniqueRatedMugshots = new Set(ratedMugshots.map(r => r.mugshot_id)).size
      console.log(`🎯 Unique mugshots with ratings: ${uniqueRatedMugshots}`)
    }
  } catch (error) {
    console.error('❌ Data check failed:', error)
  }

  // Test the service directly
  console.log('\n3. Testing mugshots service...')
  try {
    const startTime = Date.now()
    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 5 }
    )
    const endTime = Date.now()

    console.log(`✅ Service call successful! (${endTime - startTime}ms)`)
    console.log(`📝 Retrieved ${results.length} top-rated mugshots`)

    if (results.length > 0) {
      console.log('\n   Top rated mugshots:')
      results.forEach((mugshot, index) => {
        console.log(`   ${index + 1}. ${mugshot.firstName} ${mugshot.lastName}`)
        console.log(`      Rating: ${mugshot.average_rating?.toFixed(2) || 'N/A'} (${mugshot.total_ratings || 0} ratings)`)
        console.log(`      State: ${mugshot.stateOfBooking || 'N/A'}`)
      })
    } else {
      console.log('   No top-rated mugshots found')
      console.log('   This could mean:')
      console.log('   - No mugshots have ratings yet')
      console.log('   - Database function is filtering too strictly')
      console.log('   - There\'s an issue with the query')
    }

    // Test count
    const count = await mugshotsServiceServer.getMugshotCount({})
    console.log(`\n📊 Total count for top-rated: ${count}`)

  } catch (error) {
    console.error('❌ Service test failed:', error)
  }

  // Test fallback
  console.log('\n4. Testing fallback method...')
  try {
    const fallbackResults = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'newest' },
      { page: 1, perPage: 5 }
    )

    console.log(`✅ Fallback call successful!`)
    console.log(`📝 Retrieved ${fallbackResults.length} newest mugshots (for comparison)`)

    if (fallbackResults.length > 0) {
      const ratedFallbackResults = fallbackResults.filter(m => (m.total_ratings || 0) > 0)
      console.log(`⭐ Of these, ${ratedFallbackResults.length} have ratings`)
    }
  } catch (error) {
    console.error('❌ Fallback test failed:', error)
  }

  // Test filters
  console.log('\n5. Testing with filters...')
  try {
    const filteredResults = await mugshotsServiceServer.getMugshots(
      { state: 'CA' },
      { sortBy: 'top-rated' },
      { page: 1, perPage: 5 }
    )

    console.log(`✅ State filter call successful!`)
    console.log(`📝 Retrieved ${filteredResults.length} top-rated mugshots from CA`)

    // Test tag filtering
    const tagFilteredResults = await mugshotsServiceServer.getMugshots(
      { tags: ['wild', 'funny'] },
      { sortBy: 'newest' },
      { page: 1, perPage: 5 }
    )

    console.log(`✅ Tag filter call successful!`)
    console.log(`📝 Retrieved ${tagFilteredResults.length} mugshots with wild/funny tags`)

    // Test combined filters
    const combinedFilteredResults = await mugshotsServiceServer.getMugshots(
      { state: 'CA', tags: ['wild'] },
      { sortBy: 'top-rated' },
      { page: 1, perPage: 5 }
    )

    console.log(`✅ Combined filter call successful!`)
    console.log(`📝 Retrieved ${combinedFilteredResults.length} top-rated wild mugshots from CA`)
  } catch (error) {
    console.error('❌ Filter test failed:', error)
  }

  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Debug complete!')
  console.log('\nNext steps if there are issues:')
  console.log('1. Run the migration: npx supabase db push')
  console.log('2. Check if mugshots have ratings in the database')
  console.log('3. Verify the database functions were created correctly')
  console.log('4. Check Supabase logs for any errors')
}

// Handle both direct execution and module import
if (require.main === module) {
  main().catch(console.error)
}

export { main as debugTopRated } 