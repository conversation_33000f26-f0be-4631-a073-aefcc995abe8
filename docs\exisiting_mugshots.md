create table public.mugshots (
  id bigserial not null,
  created_at timestamp with time zone null default now(),
  "firstName" text null default ''::text,
  "lastName" text null default ''::text,
  "dateOfBooking" date null,
  "stateOfBooking" text null,
  "countyOfBooking" text null,
  "offenseDescription" text null,
  "additionalDetails" text null,
  "imagePath" text null,
  fb_status text null,
  "adsText" text null,
  jb_post_link text null,
  jb_fb_post boolean not null default false,
  constraint mugshots_pkey primary key (id)
) TABLESPACE pg_default;