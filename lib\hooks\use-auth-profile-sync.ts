import { useEffect } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useUserProfileQuery } from './queries/use-user-profile-query'

/**
 * Hook that synchronizes TanStack Query profile data with Zustand auth store
 * This ensures that the auth store's cached homeState and homeCounty remain updated
 * while allowing TanStack Query to handle the server state management
 */
export function useAuthProfileSync() {
  const { isAuthenticated, setProfile } = useAuthStore()
  const { data: profile, isSuccess, isError, error } = useUserProfileQuery(isAuthenticated)

  useEffect(() => {
    if (isSuccess && profile) {
      // Sync profile data to auth store for cached location access
      setProfile(profile)
    }
  }, [isSuccess, profile, setProfile])

  useEffect(() => {
    if (isError && error) {
      // Handle profile loading errors
      console.error('Profile sync error:', error)
      
      // Don't clear auth on profile errors unless it's an auth issue
      if (error?.message?.includes('UNAUTHENTICATED') || 
          error?.message?.includes('not authenticated')) {
        // Let the query handle auth redirects
        return
      }
    }
  }, [isError, error])

  return {
    profile,
    isLoading: useUserProfileQuery(isAuthenticated).isLoading,
    isError,
    error,
    isSuccess,
  }
} 