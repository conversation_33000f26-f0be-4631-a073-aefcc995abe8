"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"

type CategoryType = "Hot" | "Funny" | "Wild" | "Scary" | null

interface CategoryOverlayProps {
  trigger: CategoryType
  onComplete?: () => void
}

// Working Google Noto animated emoji URLs (verified working)
const getEmojiConfig = (category: CategoryType) => {
  switch (category) {
    case "Hot":
      return {
        centerEmoji: "🥵", // Hot face
        waveEmojis: ["🔥", "💥", "⭐", "🌟", "✨", "🔴"] // Fire-related emojis
      }
    case "Funny":
      return {
        centerEmoji: "😂", // Tears of joy
        waveEmojis: ["🤣", "😄", "😆", "😊", "😁", "😋"] // Laughing emojis
      }
    case "Wild":
      return {
        centerEmoji: "🤪", // Zany face
        waveEmojis: ["⚡", "🌪️", "💨", "🌊", "🎆", "🎯"] // Wild emojis
      }
    case "Scary":
      return {
        centerEmoji: "💀", // Skull
        waveEmojis: ["👻", "🎃", "🕷️", "🦇", "😈", "👹"] // Scary emojis
      }
    default:
      return null
  }
}

export default function CategoryOverlay({ trigger, onComplete }: CategoryOverlayProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [currentAnimation, setCurrentAnimation] = useState<CategoryType>(null)

  useEffect(() => {
    if (trigger) {
      // Always start new animation when trigger changes
      setCurrentAnimation(trigger)
      setIsVisible(true)

      // Complete animation after 1.5 seconds
      const timeout = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => {
          setCurrentAnimation(null)
          onComplete?.()
        }, 300) // Allow exit animation to complete
      }, 1500)

      return () => clearTimeout(timeout)
    }
  }, [trigger, onComplete]) // Removed currentAnimation from dependencies

  const emojiConfig = getEmojiConfig(currentAnimation)
  if (!emojiConfig) return null

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 pointer-events-none"
        >
          {/* Center zoom animation - grows from bottom, gets bigger, then vanishes */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              initial={{ 
                scale: 0, 
                y: 50, 
                opacity: 0 
              }}
              animate={{ 
                scale: [0, 1.5, 1.2, 1.8, 0],
                y: [50, 0, -10, -20, -50],
                opacity: [0, 1, 1, 1, 0]
              }}
              transition={{ 
                duration: 1.5,
                times: [0, 0.3, 0.6, 0.8, 1],
                ease: "easeInOut"
              }}
              className="text-8xl select-none"
              style={{ fontSize: "8rem" }}
            >
              {emojiConfig.centerEmoji}
            </motion.div>
          </div>

          {/* Wave effect - multiple emojis flowing from bottom to top */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(15)].map((_, i) => {
              const emojiIndex = i % emojiConfig.waveEmojis.length
              const emoji = emojiConfig.waveEmojis[emojiIndex]
              const delay = (i * 80) / 1000 // Stagger in milliseconds converted to seconds
              const leftPosition = 5 + (i * 6) % 90 // Spread across width
              
              return (
                <motion.div
                  key={i}
                  initial={{
                    y: 0,
                    x: 0,
                    scale: 0.3,
                    opacity: 0,
                    rotate: 0
                  }}
                  animate={{
                    y: [-20, -100, -200, -350, -500],
                    x: [0, Math.sin(i) * 30, Math.sin(i * 2) * 50, Math.sin(i * 3) * 30, 0],
                    scale: [0.3, 0.8, 1.2, 0.9, 0.3],
                    opacity: [0, 0.7, 1, 0.8, 0],
                    rotate: [0, 90, 180, 270, 360]
                  }}
                  transition={{
                    duration: 1.3,
                    delay: delay,
                    times: [0, 0.2, 0.5, 0.8, 1],
                    ease: "easeOut"
                  }}
                  className="absolute text-4xl select-none"
                  style={{
                    left: `${leftPosition}%`,
                    bottom: "-60px",
                  }}
                >
                  {emoji}
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
} 