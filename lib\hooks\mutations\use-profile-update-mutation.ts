import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import type { DatabaseProfile } from '@/lib/types/database'

interface ProfileUpdateData {
  full_name?: string
  state?: string | null
  county?: string | null
  email?: string | null
}

interface ProfileUpdateResponse {
  success: boolean
  data?: DatabaseProfile
  error?: string
  message?: string
}

export function useProfileUpdateMutation() {
  const queryClient = useQueryClient()
  const { user, isAuthenticated, setProfile } = useAuthStore()

  return useMutation({
    mutationFn: async (updateData: ProfileUpdateData): Promise<DatabaseProfile> => {
      if (!isAuthenticated || !user?.id) {
        // Redirect to login with return URL
        const currentUrl = window.location.href
        window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
        throw new Error('Authentication required')
      }

      const response = await fetch('/api/update-location', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      const data: ProfileUpdateResponse = await response.json()

      if (!response.ok) {
        if (data.error === 'UNAUTHENTICATED') {
          const currentUrl = window.location.href
          window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
          throw new Error('Authentication required')
        }
        throw new Error(data.message || 'Failed to update profile')
      }

      if (!data.success || !data.data) {
        throw new Error(data.message || 'Profile update failed')
      }

      return data.data
    },
    onMutate: async (newProfileData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['user', 'profile', user?.id] })

      // Snapshot the previous profile data
      const previousProfile = queryClient.getQueryData(['user', 'profile', user?.id])

      // Optimistically update the profile data
      queryClient.setQueryData(['user', 'profile', user?.id], (old: DatabaseProfile | undefined) => {
        if (!old) return old
        return {
          ...old,
          ...newProfileData,
          updated_at: new Date().toISOString(),
        }
      })

      return { previousProfile }
    },
    onError: (_err, _newProfileData, context) => {
      // Rollback optimistic update
      if (context?.previousProfile && user?.id) {
        queryClient.setQueryData(['user', 'profile', user?.id], context.previousProfile)
      }
    },
    onSuccess: (updatedProfile) => {
      // Update auth store cache with new profile data
      setProfile(updatedProfile)
      
      // Invalidate and refetch profile query for server truth
      queryClient.invalidateQueries({ queryKey: ['user', 'profile', user?.id] })
    },
    onSettled: () => {
      // Always refetch after mutation settles
      queryClient.invalidateQueries({ queryKey: ['user', 'profile', user?.id] })
    }
  })
} 