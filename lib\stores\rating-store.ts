import { create } from 'zustand'
import { getUserMugshotData, type RatingStatistics } from '@/lib/services/api-client'
import { useAuthStore } from '@/lib/stores/auth-store'

interface RatingState {
  // Cache for rating statistics by mugshot ID
  statsCache: Record<string, RatingStatistics>
  
  // User's current ratings for each mugshot
  userRatings: Record<string, number | null>
  
  // Optimistic rating updates
  optimisticRatings: Record<string, number | null>
  
  // Loading states
  loadingStats: Set<string>
  loadingUserRating: Set<string>
  submittingRating: Set<string>
  
  // Last update timestamps for cache invalidation
  lastUpdated: Record<string, number>
  
  // Actions
  getUserRating: (mugshotId: string) => Promise<number | null>
  fetchUpdatedStats: (mugshotId: string) => Promise<RatingStatistics | null>
  setRatingStats: (mugshotId: string, stats: RatingStatistics) => void
  setUserRating: (mugshotId: string, rating: number | null) => void
  setOptimisticRating: (mugshotId: string, rating: number) => void
  confirmRatingUpdate: (mugshotId: string, newStats: RatingStatistics) => void
  revertOptimisticRating: (mugshotId: string) => void
  setLoadingUserRating: (mugshotId: string, isLoading: boolean) => void
  setSubmittingRating: (mugshotId: string, isSubmitting: boolean) => void
  invalidateStats: (mugshotId: string) => void
  clearCache: () => void
  clearMugshotCache: (mugshotId: string) => void
  isLoading: (mugshotId: string) => boolean
  isSubmitting: (mugshotId: string) => boolean
}

// Cache duration: 5 minutes (commented out as not currently used)
// const CACHE_DURATION = 5 * 60 * 1000

export const useRatingStore = create<RatingState>((set, get) => ({
  statsCache: {},
  userRatings: {},
  optimisticRatings: {},
  loadingStats: new Set(),
  loadingUserRating: new Set(),
  submittingRating: new Set(),
  lastUpdated: {},
  
  // REMOVED: getRatingStats function - rating stats now come from /api/mugshots/[id]
  // Only user-specific data (userRating) comes from user API
  
  getUserRating: async (mugshotId: string) => {
    const state = get()
    
    // Check if user is authenticated before making API call
    const authStore = useAuthStore.getState()
    if (!authStore.isAuthenticated || !authStore.user) {
      console.log('User not authenticated, skipping user rating load')
      // Set empty rating for unauthenticated users
      set(state => ({
        userRatings: {
          ...state.userRatings,
          [mugshotId]: null
        }
      }))
      return null
    }
    
    // Prevent duplicate requests
    if (state.loadingUserRating.has(mugshotId)) {
      // Wait for the existing request to complete
      while (get().loadingUserRating.has(mugshotId)) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return get().userRatings[mugshotId] || null
    }
    
    // Mark as loading
    set(state => ({
      loadingUserRating: new Set([...state.loadingUserRating, mugshotId])
    }))
    
    try {
      const response = await getUserMugshotData(parseInt(mugshotId))
      
      if (response.success && response.data) {
        // Update only user rating
        set(state => ({
          userRatings: {
            ...state.userRatings,
            [mugshotId]: response.data!.userRating
          },
          loadingUserRating: new Set([...state.loadingUserRating].filter(id => id !== mugshotId))
        }))
        
        return response.data.userRating
      } else {
        throw new Error(response.message || 'Failed to get user rating')
      }
    } catch (error) {
      console.error('Error fetching user rating:', error)
      
      // Remove from loading set
      set(state => ({
        loadingUserRating: new Set([...state.loadingUserRating].filter(id => id !== mugshotId))
      }))
      
      return null
    }
  },

  fetchUpdatedStats: async (mugshotId: string) => {
    try {
      // Fetch updated statistics from the mugshot API
      const response = await fetch(`/api/mugshots/${mugshotId}`)
      const data = await response.json()
      
      if (data.success && data.mugshot?.ratings) {
        const updatedStats: RatingStatistics = {
          averageRating: data.mugshot.ratings.averageRating || 0,
          totalRatings: data.mugshot.ratings.totalRatings || 0
        }
        
        // Update the store with fresh statistics
        set(state => ({
          statsCache: {
            ...state.statsCache,
            [mugshotId]: updatedStats
          },
          lastUpdated: {
            ...state.lastUpdated,
            [mugshotId]: Date.now()
          }
        }))
        
        return updatedStats
      } else {
        throw new Error('Failed to fetch updated statistics')
      }
    } catch (error) {
      console.error('Error fetching updated stats:', error)
      return null
    }
  },
  
  setRatingStats: (mugshotId: string, stats: RatingStatistics) => {
    set(state => ({
      statsCache: {
        ...state.statsCache,
        [mugshotId]: stats
      },
      userRatings: {
        ...state.userRatings,
        [mugshotId]: stats.userRating || null
      },
      lastUpdated: {
        ...state.lastUpdated,
        [mugshotId]: Date.now()
      }
    }))
  },
  
  invalidateStats: (mugshotId: string) => {
    set(state => {
      const newCache = { ...state.statsCache }
      const newLastUpdated = { ...state.lastUpdated }
      delete newCache[mugshotId]
      delete newLastUpdated[mugshotId]
      
      return {
        statsCache: newCache,
        lastUpdated: newLastUpdated
      }
    })
  },
  
  clearCache: () => {
    set({
      statsCache: {},
      userRatings: {},
      optimisticRatings: {},
      lastUpdated: {},
      loadingStats: new Set(),
      loadingUserRating: new Set(),
      submittingRating: new Set()
    })
  },
  
  setUserRating: (mugshotId: string, rating: number | null) => {
    set(state => ({
      userRatings: {
        ...state.userRatings,
        [mugshotId]: rating
      }
    }))
  },

  setOptimisticRating: (mugshotId: string, rating: number) => {
    set(state => ({
      optimisticRatings: {
        ...state.optimisticRatings,
        [mugshotId]: rating
      }
      // DO NOT modify statsCache optimistically - wait for server response
      // The user sees their optimistic rating, but stats stay accurate until server confirms
    }))
  },

  confirmRatingUpdate: (mugshotId: string, newStats: RatingStatistics) => {
    set(state => {
      const optimisticRating = state.optimisticRatings[mugshotId]
      const newOptimistic = { ...state.optimisticRatings }
      const newUserRatings = { ...state.userRatings }
      
      // Move optimistic rating to confirmed user rating
      if (optimisticRating !== undefined) {
        newUserRatings[mugshotId] = optimisticRating
        delete newOptimistic[mugshotId]
      }
      
      return {
        userRatings: newUserRatings,
        optimisticRatings: newOptimistic,
        statsCache: {
          ...state.statsCache,
          [mugshotId]: newStats
        },
        lastUpdated: {
          ...state.lastUpdated,
          [mugshotId]: Date.now()
        }
      }
    })
  },

  revertOptimisticRating: (mugshotId: string) => {
    set(state => {
      const newOptimistic = { ...state.optimisticRatings }
      delete newOptimistic[mugshotId]
      
      // Recalculate stats without optimistic rating
      
      // This should ideally revert to the last known good stats
      // For simplicity, we'll invalidate and let it refetch
      
      return {
        optimisticRatings: newOptimistic
      }
    })
  },

  setLoadingUserRating: (mugshotId: string, isLoading: boolean) => {
    set(state => {
      const newLoadingUserRating = new Set(state.loadingUserRating)
      if (isLoading) {
        newLoadingUserRating.add(mugshotId)
      } else {
        newLoadingUserRating.delete(mugshotId)
      }
      
      return {
        loadingUserRating: newLoadingUserRating
      }
    })
  },

  setSubmittingRating: (mugshotId: string, isSubmitting: boolean) => {
    set(state => {
      const newSubmittingRating = new Set(state.submittingRating)
      if (isSubmitting) {
        newSubmittingRating.add(mugshotId)
      } else {
        newSubmittingRating.delete(mugshotId)
      }
      
      return {
        submittingRating: newSubmittingRating
      }
    })
  },

  clearMugshotCache: (mugshotId: string) => {
    set(state => {
      const newStatsCache = { ...state.statsCache }
      const newUserRatings = { ...state.userRatings }
      const newOptimisticRatings = { ...state.optimisticRatings }
      const newLastUpdated = { ...state.lastUpdated }
      const newLoadingStats = new Set(state.loadingStats)
      const newLoadingUserRating = new Set(state.loadingUserRating)
      const newSubmittingRating = new Set(state.submittingRating)
      
      delete newStatsCache[mugshotId]
      delete newUserRatings[mugshotId]
      delete newOptimisticRatings[mugshotId]
      delete newLastUpdated[mugshotId]
      newLoadingStats.delete(mugshotId)
      newLoadingUserRating.delete(mugshotId)
      newSubmittingRating.delete(mugshotId)
      
      return {
        statsCache: newStatsCache,
        userRatings: newUserRatings,
        optimisticRatings: newOptimisticRatings,
        lastUpdated: newLastUpdated,
        loadingStats: newLoadingStats,
        loadingUserRating: newLoadingUserRating,
        submittingRating: newSubmittingRating
      }
    })
  },

  isLoading: (mugshotId: string) => {
    return get().loadingStats.has(mugshotId)
  },

  isSubmitting: (mugshotId: string) => {
    return get().submittingRating.has(mugshotId)
  }
})) 