# NPM Build Error Fix Task List

## ⚠️ **CRITICAL GUIDELINES**
- **NO EXISTING FUNCTIONALITY SHOULD BE BROKEN** - Only fix linting errors, do not change logic
- **NO UI ELEMENT CHANGES** - Do not modify any visual components, layouts, or styling
- **PRESERVE ALL BEHAVIOR** - Maintain exact same component behavior and user experience
- **TEST AFTER EACH SECTION** - Run `npm run build` after each major section to verify fixes

## Task Categories

### 📋 **Category 1: Component Files - Unused Imports & Variables**

#### ✅ Task 1.1: Fix QuarterlyVotingClient.tsx ✓ COMPLETED
- [x] Remove unused `Button` import (line 5)
- [x] Remove unused `Trophy` import (line 8)
- [x] Replace `'` with `&apos;` on lines 144, 169 (2 instances), 286 (2 instances)
- [x] Verify component still renders correctly

#### ✅ Task 1.2: Fix UnsubscribeClient.tsx ✓ COMPLETED
- [x] Replace `'` with `&apos;` on lines 113, 166, 259
- [x] Verify unsubscribe functionality works

#### ✅ Task 1.3: Fix VotingCandidate.tsx ✓ COMPLETED
- [x] Remove unused `totalVotes` variable (line 41) OR use it in component logic
- [x] Verify voting interface displays correctly

#### ✅ Task 1.4: Fix WeeklyCountdown.tsx ✓ COMPLETED
- [x] Replace `'` with `&apos;` on line 101
- [x] Verify countdown timer works

#### ✅ Task 1.5: Fix WeeklyVotingClient.tsx ✓ COMPLETED
- [x] Remove unused `Button` import (line 5)
- [x] Replace `'` with `&apos;` on lines 129, 154, 245 (2 instances)
- [x] Verify voting functionality works

#### ✅ Task 1.6: Fix WeeklyVotingClosed.tsx ✓ COMPLETED
- [x] Replace `'` with `&apos;` on line 318
- [x] Verify closed voting state displays correctly

### 📋 **Category 2: Component Files - Type Safety & React Hooks**

#### ✅ Task 2.1: Fix AccountLinkedNotification.tsx ✓ COMPLETED
- [x] Remove unused `isLinked` variable (line 11) OR use in component
- [x] Remove unused `message` variable (line 17) OR use in component
- [x] Replace `'` with `&apos;` on line 57
- [x] Verify account linking notifications work

#### ✅ Task 2.2: Fix AdminOnly.tsx ✓ COMPLETED
- [x] Replace `any` type on line 12 with proper TypeScript interface
- [x] Verify admin-only functionality works correctly

#### ✅ Task 2.3: Fix EmailCaptureForm.tsx ✓ COMPLETED
- [x] Replace `'` with `&apos;` on line 158
- [x] Verify email capture form submission works

#### ✅ Task 2.4: Fix ErrorFallbacks.tsx ✓ COMPLETED
- [x] Add underscore prefix to unused `error` parameters (lines 5, 29, 49, 68) OR use them
- [x] Replace `'` with `&apos;` on lines 18, 39
- [x] Verify error boundary fallbacks display correctly

#### ✅ Task 2.5: Fix FilteredResultsCount.tsx ✓ COMPLETED
- [x] Replace `"` with `&quot;` on line 73 (2 instances)
- [x] Verify filtered results count displays correctly

#### ✅ Task 2.6: Fix LocalMugshotsSection.tsx ✓ COMPLETED
- [x] Fix useEffect dependencies - add `getHomeLocation`, `hasHomeLocation`, `loadUserProfile` to dependency array OR use useCallback
- [x] Verify local mugshots section loads correctly

### 📋 **Category 3: UI Component Files**

#### ✅ Task 3.1: Fix LocationDropdown.tsx ✓ COMPLETED
- [x] Remove unused `Badge` import (line 6)
- [x] Remove unused `isCurrentSelectionHome` variable (line 49) OR use it
- [x] Verify location dropdown functionality works

#### ✅ Task 3.2: Fix MugshotCard.tsx ✓ COMPLETED
- [x] Remove unused `Star` import (line 4)
- [x] Verify mugshot cards display and interact correctly

#### ✅ Task 3.3: Fix MugshotModal.tsx ✓ COMPLETED
- [x] Remove unused `TagInput` import (line 8)
- [x] Remove unused `TagDisplay` import (line 9)
- [x] Remove unused `tags` variable (line 74) OR use it
- [x] Remove unused `handleTagAdded` function (line 106) OR use it
- [x] Remove unused `handleTagRemoved` function (line 110) OR use it
- [x] Add underscore prefix to unused `_` parameter (line 218)
- [x] Verify mugshot modal opens and functions correctly

#### ✅ Task 3.4: Fix NotificationPreferences.tsx ✓ COMPLETED
- [x] Remove unused `Button` import (line 4)
- [x] Fix useEffect dependency - add `loadPreferences` to dependency array OR use useCallback
- [x] Verify notification preferences save correctly

### 📋 **Category 4: Rating & Tag Components** ✅ COMPLETED

#### ✅ Task 4.1: Fix RatingInterface.tsx ✓ COMPLETED
- [x] Remove unused `RatingStatistics` import (line 17)
- [x] Remove unused variables: `submittingRating` (37), `setRatingStats` (40), `isSubmittingRating` (45)
- [x] Replace `"` with `&quot;` on line 400 (2 instances)
- [x] Verify rating interface works for all rating types

#### ✅ Task 4.2: Fix RatingStatistics.tsx ✓ COMPLETED
- [x] Remove unused imports: `TrendingUp`, `BarChart3` (line 4), `Progress` (line 8)
- [x] Remove unused variables: `userRatings` (34), `optimisticRatings` (35), `submittingRating` (36), `setRatingStats` (37), `setError` (41), `lastUpdate` (44)
- [x] Verify rating statistics display correctly

#### ✅ Task 4.3: Fix RatingTagPopover.tsx ✓ COMPLETED
- [x] Remove unused `setUserTags` variable (line 53)
- [x] Replace `any` types on lines 82, 87 with proper TypeScript interfaces
- [x] Verify tag popover functionality works

#### ✅ Task 4.4: Fix SimpleMugshotRating.tsx ✓ COMPLETED
- [x] Remove unused variables: `submittingRating` (33), `setUserRating` (37), `setOptimisticRating` (38), `confirmRatingUpdate` (39), `revertOptimisticRating` (40), `setSubmittingRating` (41), `setUserTags` (48), `setTagStatistics` (49), `setLoadingTagData` (50)
- [x] Fix useEffect dependency warning - wrap `currentTagStats` in useMemo (line 73)
- [x] Verify simple rating component works

#### ✅ Task 4.5: Fix TagDisplay.tsx & TagInput.tsx ✓ COMPLETED
- [x] TagDisplay: Remove unused `isPending` variable (line 46)
- [x] TagInput: Remove unused `X` import (line 4) and `isPending` variable (line 35)
- [x] Verify tag display and input functionality works

### 📋 **Category 5: Navigation & Utility Components** ✅ COMPLETED

#### ✅ Task 5.1: Fix TopProgressBar.tsx ✓ COMPLETED
- [x] Remove unused `handleNavigationStart` variable (line 58)
- [x] Verify progress bar shows during navigation

#### ✅ Task 5.2: Fix UniversalNavLink.tsx ✓ COMPLETED
- [x] Remove unused `Link` import (line 4)
- [x] Remove unused variables: `replace` (37), `scroll` (38), `prefetch` (39)
- [x] Verify navigation links work correctly

#### ✅ Task 5.3: Fix UserNavDebug.tsx ✓ COMPLETED
- [x] Replace `any` type on line 19 with proper TypeScript interface
- [x] Verify user navigation debug functionality works

#### ✅ Task 5.4: Fix ReportContentDialog.tsx ✓ COMPLETED
- [x] Replace `'` with `&apos;` on lines 116, 134, 173
- [x] Verify content reporting dialog works

### 📋 **Category 6: Library Files - Auth & Utils** ✅ COMPLETED

#### ✅ Task 6.1: Fix auth-account-linking.ts ✓ COMPLETED
- [x] Remove unused `createClient` import (line 1)
- [x] Replace `any` types on lines 7, 17, 147, 148 with proper TypeScript interfaces
- [x] Verify account linking functionality works

#### ✅ Task 6.2: Fix auth-actions.ts ✓ COMPLETED
- [x] Remove unused `data` variable (line 109)
- [x] Replace `any` type on line 142 with proper TypeScript interface
- [x] Verify authentication actions work

#### ✅ Task 6.3: Fix auth-context.ts ✓ COMPLETED
- [x] Replace `any` types on lines 5, 6, 76 with proper TypeScript interfaces
- [x] Verify authentication context provides correct data

#### ✅ Task 6.4: Fix auth-utils.ts ✓ COMPLETED
- [x] Replace `any` types on lines 20, 157, 161, 162 with proper TypeScript interfaces
- [x] Add underscore prefix to unused `error` variables (lines 117, 146, 192, 211)
- [x] Verify authentication utilities work correctly

#### ✅ Task 6.5: Fix profile-utils.ts ✓ COMPLETED
- [x] Add underscore prefix to unused `error` variables (lines 90, 125)
- [x] Verify profile utility functions work

### 📋 **Category 7: Hooks & Queries** ✅ COMPLETED

#### ✅ Task 7.1: Fix Hook Mutations ✓ COMPLETED
- [x] use-rating-mutation.ts: Replace `any` types (lines 64, 70), remove unused `mugshotId` (133)
- [x] use-tag-mutation.ts: Replace `any` types (lines 63, 78, 148, 160), remove unused `mugshotId` (126)
- [x] Verify rating and tag mutations work correctly

#### ✅ Task 7.2: Fix Hook Queries ✓ COMPLETED
- [x] use-mugshot-detail-query.ts: Replace `any` type on line 44
- [x] use-mugshots-query.ts: Replace `any` type on line 74
- [x] use-user-mugshot-data-query.ts: Replace `any` type on line 63
- [x] Verify all query hooks return correct data

#### ✅ Task 7.3: Fix Hook Dependencies & Variables ✓ COMPLETED
- [x] useBulkRatingStatistics.ts: Fix useEffect dependencies (line 40) and complex expression
- [x] useClientGeolocation.ts: Add underscore prefix to unused `error` (line 80)
- [x] Verify geolocation and statistics hooks work

### 📋 **Category 8: Services & Stores**

#### ✅ Task 8.1: Fix Query & API Services
- [x] query-keys.ts: Replace `any` types on lines 52, 58, 67, 72, 82, 92
- [x] api-client.ts: Replace `any` types on lines 3, 248, 334, 343, 359
- [x] Verify query keys and API client work correctly

#### ✅ Task 8.2: Fix Auto-flagging & Moderation Services
- [x] auto-flagging-service.ts: Replace `any` type (34), use `const` for `matchedKeywords` (252) and `matchedPatterns` (253), remove unused `content` (353)
- [x] moderation-service.ts: Replace `any` types on lines 37, 52, 75, 575, 602
- [x] Verify auto-flagging and moderation services work

#### ✅ Task 8.3: Fix Competition & Mugshot Services
- [x] monthly-quarterly-service.ts: Replace `any` type on line 295
- [x] mugshot-store-service.ts: Replace `any` type on line 93
- [ ] mugshots-service-server.ts: Replace `any` types (121, 131), remove unused `sortBy` (447) - IN PROGRESS (syntax error needs manual fix)
- [ ] Verify competition and mugshot services work

#### ✅ Task 8.4: Fix Notification, Rating & Tag Services
- [x] notification-service.ts: Remove unused `headersList` (105) and `data` variables (363, 408), replace `any` types (240, 278, 302)
- [x] rating-service.ts: Remove unused parameters `mugshotId` (66), `userId` (76), `ratingId` (104)
- [x] tag-service.ts: Remove unused variables `limit` (102, 112, 118) and parameters `tagId`, `reason`, `details` (136)
- [x] Verify notification, rating, and tag services work

#### ✅ Task 8.5: Fix Winner Management Service
- [x] Use `const` for `dateFilter` (174), `existingEvent` and `eventError` (211)
- [x] Replace `any` types on lines 202, 329, 410
- [x] Verify winner management functionality works

#### ✅ Task 8.6: Fix Store Files
- [x] auth-store.ts: Replace `any` types on lines 20, 34
- [x] filter-store.ts: Replace `any` types on lines 398, 400
- [x] rating-store.ts: Remove unused `CACHE_DURATION` (40), keep `userRating` and `currentStats` as they are used
- [x] Verify all Zustand stores work correctly

### 📋 **Category 9: Final Files & Testing**

#### ✅ Task 9.1: Fix Remaining Library Files
- [ ] middleware.ts: Remove unused `options` parameter (line 18)
- [ ] query-test-utils.tsx: Replace `any` types on lines 155, 160, 165 (2 instances)
- [ ] mugshot-transforms.ts: Use `const` for variables (14, 19, 67, 75), remove unused `generateMockCategory` (177), replace `any` type (425)
- [ ] Verify middleware and test utilities work

#### ✅ Task 9.2: Final Verification
- [ ] Run `npm run lint` to check for any remaining issues


## 🔧 **Development Commands**
```bash
# Check for linting errors
npm run lint

# Build the project
npm run build

# Run development server for testing
npm run dev
```

## 📝 **Notes**
- Work through categories in order for best results
- Test frequently to catch any breaking changes early
- Focus on removing unused code rather than adding new functionality
- When replacing `any` types, use existing interfaces from the codebase where possible
- For React Hook dependency warnings, prefer useCallback/useMemo over adding variables to dependency arrays 