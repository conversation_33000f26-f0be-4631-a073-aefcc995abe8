'use client'

import { useState, useEffect } from 'react'
import SimpleMugshotRating from '@/components/SimpleMugshotRating'
import MugshotRatingTimeGate from '@/components/MugshotRatingTimeGate'
import { shouldShowTimeGate } from '@/lib/utils/rating-timegate-utils'

interface MugshotRatingSectionProps {
  mugshotId: string
  arrestDate?: string
  location: string
}

export default function MugshotRatingSection({ 
  mugshotId, 
  arrestDate, 
  location 
}: MugshotRatingSectionProps) {
  const [showTimeGate, setShowTimeGate] = useState(false)

  // Parse location to get state and county
  const locationParts = location.split(', ')
  const state = locationParts[locationParts.length - 1] || ''
  const county = locationParts[locationParts.length - 2] || ''

  // Determine which component to show based on time gate logic
  useEffect(() => {
    const mugshotData = {
      id: mugshotId,
      dateOfBooking: arrestDate || null,
      stateOfBooking: state,
      countyOfBooking: county
    }
    setShowTimeGate(shouldShowTimeGate(mugshotData))
  }, [mugshotId, arrestDate, state, county])

  // Callback when rating becomes enabled
  const handleRatingEnabled = () => {
    setShowTimeGate(false)
  }

  return (
    <>
      {showTimeGate ? (
        <MugshotRatingTimeGate 
          mugshotId={mugshotId}
          dateOfBooking={arrestDate || null}
          state={state}
          county={county}
          onRatingEnabled={handleRatingEnabled}
        />
      ) : (
        <SimpleMugshotRating 
          mugshotId={mugshotId} 
        />
      )}
    </>
  )
} 