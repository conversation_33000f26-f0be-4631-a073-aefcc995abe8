// Client-side tag service that wraps API calls
import {
  toggleTag as apiToggleTag,
  getTagStatistics as apiGetTagStatistics,
  type TagStatistics,
  type TagToggleResult
} from './api-client'

// Export types for backward compatibility
export type { TagStatistics, TagToggleResult }

// Fixed TagSuggestion interface to match actual component usage
export interface TagSuggestion {
  id: string // Unique identifier for the suggestion
  name: string // Display name (e.g., "Wild", "Funny", "Spooky")
  tagType: 'wild' | 'funny' | 'spooky' // The actual tag type for backend
  usage_count: number // Number of times this tag has been used
  is_trending?: boolean // Whether this tag is currently trending
  relevanceScore?: number // How relevant this suggestion is to the search query
}

export interface MugshotTag {
  id: string
  mugshotId: number
  tagType: 'wild' | 'funny' | 'spooky'
  userId: string
  createdAt: string
  current_user_tagged?: boolean
  tag_type: 'wild' | 'funny' | 'spooky' // For backward compatibility
  
  // UI display properties
  name: string // Display name for the tag (e.g., "Wild", "Funny", "Spooky")
  user_count: number // Number of users who tagged this
  is_trending?: boolean // Whether this tag is currently trending
}

export interface TagSubmissionResult {
  success: boolean
  message: string
  tag?: {
    mugshotId: number
    tagType: string
    action: 'added' | 'removed'
    userId: string
  }
  error?: string
}

export interface TrendingTag {
  tagType: 'wild' | 'funny' | 'spooky'
  currentWeekUses: number
  previousWeekUses: number
  growthRate: number
  trendDirection: 'up' | 'down' | 'stable'
}

export interface TagSearchResult {
  tagType: 'wild' | 'funny' | 'spooky'
  relevanceScore: number
  description: string
  totalUses: number
  sampleMugshots: number[]
}

export interface TagRemovalResult {
  success: boolean
  message: string
  removedTag?: {
    tagType: string
    mugshotId: number
  }
}

export interface UserTagContribution {
  userId: string
  totalContributions: number
  tagBreakdown: {
    funny: number
    wild: number
    spooky: number
  }
  recentActivity: Array<{
    mugshotId: number
    tagType: string
    action: 'added' | 'removed'
    timestamp: string
  }>
  favoriteTagType: string
}

// Wrapper functions that maintain the same interface for backward compatibility
export async function addTagToMugshot(
  mugshotId: number,
  tagType: 'wild' | 'funny' | 'spooky'
): Promise<TagToggleResult> {
  return apiToggleTag({ mugshotId, tagType })
}

export async function removeTagFromMugshot(
  mugshotId: number,
  tagType: 'wild' | 'funny' | 'spooky'
): Promise<TagToggleResult> {
  return apiToggleTag({ mugshotId, tagType })
}

export async function getMugshotTags(mugshotId: number): Promise<TagStatistics> {
  return apiGetTagStatistics(mugshotId)
}

// Updated implementation with proper TagSuggestion format
export async function getPopularTags(query?: string, limit: number = 10): Promise<TagSuggestion[]> {
  try {
    // For now, return properly formatted fixed tags until API endpoint is created
    const allTags: TagSuggestion[] = [
      {
        id: 'wild',
        name: 'Wild',
        tagType: 'wild',
        usage_count: 1250,
        is_trending: true,
        relevanceScore: 1.0
      },
      {
        id: 'funny',
        name: 'Funny', 
        tagType: 'funny',
        usage_count: 2100,
        is_trending: false,
        relevanceScore: 0.9
      },
      {
        id: 'spooky',
        name: 'Spooky',
        tagType: 'spooky', 
        usage_count: 890,
        is_trending: true,
        relevanceScore: 0.8
      }
    ]
    
    // Filter based on query if provided
    let filteredTags = allTags
    if (query && query.trim()) {
      const searchTerm = query.toLowerCase().trim()
      filteredTags = allTags.filter(tag => 
        tag.name.toLowerCase().includes(searchTerm) ||
        tag.tagType.toLowerCase().includes(searchTerm)
      ).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
    }
    
    // Sort by usage count and trending status
    filteredTags.sort((a, b) => {
      if (a.is_trending && !b.is_trending) return -1
      if (!a.is_trending && b.is_trending) return 1
      return b.usage_count - a.usage_count
    })
    
    return filteredTags.slice(0, limit)
  } catch (error) {
    console.error('Error in getPopularTags:', error)
    return []
  }
}

export async function getTrendingTags(limit: number = 5): Promise<TrendingTag[]> {
  // TODO: Implement when /api/tags/trending is created
  console.warn(`getTrendingTags: API endpoint not yet implemented (limit: ${limit})`)
  return []
}

export async function searchTags(query: string, limit: number = 10): Promise<TagSearchResult[]> {
  // TODO: Implement when /api/tags/search is created
  console.warn(`searchTags: API endpoint not yet implemented (query: ${query}, limit: ${limit})`)
  
  // For now, simple filtering of the 3 fixed tags but respect the limit
  const fixedTags = ['funny', 'wild', 'spooky'] as const
  const filtered = fixedTags.filter(tag =>
    tag.toLowerCase().includes(query.toLowerCase())
  )

  return filtered.slice(0, limit).map(tagType => ({
    tagType,
    relevanceScore: 1.0,
    description: `${tagType.charAt(0).toUpperCase() + tagType.slice(1)} mugshots`,
    totalUses: 0,
    sampleMugshots: []
  }))
}

// Note: This function exists for API compatibility but is intentionally not implemented
// per user request. It may be needed for test mocking or future requirements.
export async function reportTag(
  _tagId: string, 
  _reason: string, 
  _details?: string
): Promise<{ success: boolean; message: string }> {
  console.warn('reportTag: Function removed - not needed for current use case')
  return {
    success: false,
    message: 'Tag reporting feature not implemented'
  }
}

export async function getUserTagContributions(userId: string): Promise<UserTagContribution> {
  // TODO: Implement when /api/tags/user/[userId] is created
  console.warn('getUserTagContributions: API endpoint not yet implemented')
  return {
    userId,
    totalContributions: 0,
    tagBreakdown: { funny: 0, wild: 0, spooky: 0 },
    recentActivity: [],
    favoriteTagType: 'funny'
  }
}

export async function updateTrendingTags(): Promise<{ success: boolean; message: string }> {
  // TODO: This might be handled by materialized view refresh
  console.warn('updateTrendingTags: Function may be replaced by materialized view refresh')
  return {
    success: false,
    message: 'Trending tags update not yet implemented'
  }
} 