/**
 * Focused Data Generators for Mugshots API Performance Testing
 * 
 * This file generates realistic filter combinations specifically targeting
 * different performance scenarios in the native mugshots service.
 */

// US states (based on your constants.ts)
const US_STATES = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado",
  "Connecticut", "Delaware", "Florida", "Georgia", "Hawaii", "Idaho",
  "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana",
  "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota",
  "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada",
  "New Hampshire", "New Jersey", "New Mexico", "New York",
  "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon",
  "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota",
  "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington",
  "West Virginia", "Wisconsin", "Wyoming"
];

// Counties by state (major ones)
const COUNTIES_BY_STATE = {
  "California": ["Los Angeles", "San Diego", "Orange", "Riverside", "San Bernardino", "Santa Clara", "Alameda", "Sacramento"],
  "Texas": ["Harris", "Dallas", "Tarrant", "Bexar", "Travis", "Collin", "Denton", "Fort Bend"],
  "Florida": ["Miami-Dade", "Broward", "Palm Beach", "Hillsborough", "Orange", "Pinellas", "Duval", "Lee"],
  "New York": ["Kings", "Queens", "New York", "Suffolk", "Bronx", "Nassau", "Westchester", "Erie"],
  "Illinois": ["Cook", "DuPage", "Lake", "Will", "Kane", "McHenry", "Winnebago", "Champaign"],
  "Pennsylvania": ["Philadelphia", "Allegheny", "Montgomery", "Bucks", "Chester", "Delaware", "Lancaster", "York"],
  "Ohio": ["Cuyahoga", "Franklin", "Hamilton", "Summit", "Montgomery", "Lucas", "Stark", "Butler"],
  "Georgia": ["Fulton", "Gwinnett", "Cobb", "DeKalb", "Clayton", "Cherokee", "Henry", "Forsyth"],
  "North Carolina": ["Mecklenburg", "Wake", "Guilford", "Forsyth", "Cumberland", "Durham", "Buncombe", "Union"],
  "Michigan": ["Wayne", "Oakland", "Macomb", "Kent", "Genesee", "Washtenaw", "Ottawa", "Ingham"]
};

// Tag types (from your database schema)
const TAG_TYPES = ["wild", "funny", "spooky"];

// Sort options (performance-focused)
const SORT_OPTIONS = {
  FAST: ["newest", "most-viewed"],  // No joins, fast queries
  SLOW: ["top-rated"]               // Complex calculation, slow queries
};

const ALL_SORT_OPTIONS = ["newest", "most-viewed", "top-rated"];

// Per page options
const PER_PAGE_OPTIONS = [12, 24, 48];

// Common search terms (realistic names)
const SEARCH_TERMS = [
  "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
  "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson",
  "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson",
  "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker",
  "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill"
];

// Utility functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomDateInPastYear() {
  const now = new Date();
  const pastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const randomTime = pastYear.getTime() + Math.random() * (now.getTime() - pastYear.getTime());
  return new Date(randomTime).toISOString().split('T')[0];
}

function shouldInclude(probability = 0.5) {
  return Math.random() < probability;
}

/**
 * Generate FAST query filters (newest/most-viewed sorts, simple filters)
 * These should perform well as they don't require joins or complex calculations
 */
function generateFastQueryFilters(context, events, done) {
  const filters = {};
  
  // Always use fast sort options
  filters.sortBy = getRandomElement(SORT_OPTIONS.FAST);
  
  // Page and perPage (always include)
  filters.page = getRandomInt(1, 5); // Stay in first 5 pages for realistic usage
  filters.perPage = getRandomElement(PER_PAGE_OPTIONS);
  
  // State filter (70% chance - common filter)
  if (shouldInclude(0.7)) {
    filters.state = getRandomElement(US_STATES);
    
    // County filter (40% chance if state is selected)
    if (shouldInclude(0.4) && COUNTIES_BY_STATE[filters.state]) {
      filters.county = getRandomElement(COUNTIES_BY_STATE[filters.state]);
    }
  }
  
  // Search term (30% chance - common user behavior)
  if (shouldInclude(0.3)) {
    filters.searchTerm = getRandomElement(SEARCH_TERMS);
  }
  
  // Date range filters (25% chance)
  if (shouldInclude(0.25)) {
    const fromDate = getRandomDateInPastYear();
    const toDate = getRandomDateInPastYear();
    
    if (new Date(fromDate) <= new Date(toDate)) {
      filters.dateFrom = fromDate;
      filters.dateTo = toDate;
    } else {
      filters.dateFrom = toDate;
      filters.dateTo = fromDate;
    }
  }
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });

  // Add sortBy for API compatibility
  context.vars.sortBy = filters.sortBy;

  return done();
}

/**
 * Generate SLOW query filters (top-rated sort)
 * These are expensive as they require fetching all ratings and calculating averages
 */
function generateSlowQueryFilters(context, events, done) {
  const filters = {};
  
  // Always use slow sort option
  filters.sortBy = "top-rated";
  
  // Page and perPage
  filters.page = getRandomInt(1, 3); // Limit to first 3 pages for slow queries
  filters.perPage = getRandomElement(PER_PAGE_OPTIONS);
  
  // Lighter filtering for slow queries to avoid compounding performance issues
  
  // State filter (50% chance - reduced from fast queries)
  if (shouldInclude(0.5)) {
    filters.state = getRandomElement(US_STATES);
  }
  
  // Search term (20% chance - reduced)
  if (shouldInclude(0.2)) {
    filters.searchTerm = getRandomElement(SEARCH_TERMS);
  }
  
  // Date range (15% chance - reduced)
  if (shouldInclude(0.15)) {
    const fromDate = getRandomDateInPastYear();
    const toDate = getRandomDateInPastYear();
    
    if (new Date(fromDate) <= new Date(toDate)) {
      filters.dateFrom = fromDate;
      filters.dateTo = toDate;
    } else {
      filters.dateFrom = toDate;
      filters.dateTo = fromDate;
    }
  }
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

/**
 * Generate TAG-BASED query filters (most expensive)
 * These require additional queries to the tags table plus intersection logic
 */
function generateTagQueryFilters(context, events, done) {
  const filters = {};
  
  // Mix of fast and slow sorts
  filters.sortBy = shouldInclude(0.3) ? "top-rated" : getRandomElement(SORT_OPTIONS.FAST);
  
  // Page and perPage
  filters.page = getRandomInt(1, 3); // Limit pages for tag queries
  filters.perPage = getRandomElement(PER_PAGE_OPTIONS);
  
  // Always include tags (this is the focus)
  const numTags = getRandomInt(1, 2); // 1-2 tags
  const selectedTags = [];
  for (let i = 0; i < numTags; i++) {
    const tag = getRandomElement(TAG_TYPES);
    if (!selectedTags.includes(tag)) {
      selectedTags.push(tag);
    }
  }
  filters.tags = selectedTags.join(',');
  
  // Minimal additional filtering to focus on tag performance
  
  // State filter (30% chance - reduced)
  if (shouldInclude(0.3)) {
    filters.state = getRandomElement(US_STATES);
  }
  
  // Search term (15% chance - minimal)
  if (shouldInclude(0.15)) {
    filters.searchTerm = getRandomElement(SEARCH_TERMS);
  }
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

/**
 * Generate MIXED realistic filters (combination of all scenarios)
 * This simulates real user behavior with various filter combinations
 */
function generateMixedQueryFilters(context, events, done) {
  const filters = {};
  
  // Random sort option (weighted towards fast)
  if (shouldInclude(0.8)) {
    filters.sortBy = getRandomElement(SORT_OPTIONS.FAST);
  } else {
    filters.sortBy = "top-rated";
  }
  
  // Page and perPage
  filters.page = getRandomInt(1, 8); // Realistic pagination
  filters.perPage = getRandomElement(PER_PAGE_OPTIONS);
  
  // State filter (60% chance)
  if (shouldInclude(0.6)) {
    filters.state = getRandomElement(US_STATES);
    
    // County filter (30% chance if state selected)
    if (shouldInclude(0.3) && COUNTIES_BY_STATE[filters.state]) {
      filters.county = getRandomElement(COUNTIES_BY_STATE[filters.state]);
    }
  }
  
  // Search term (25% chance)
  if (shouldInclude(0.25)) {
    filters.searchTerm = getRandomElement(SEARCH_TERMS);
  }
  
  // Tags (35% chance)
  if (shouldInclude(0.35)) {
    const numTags = getRandomInt(1, 2);
    const selectedTags = [];
    for (let i = 0; i < numTags; i++) {
      const tag = getRandomElement(TAG_TYPES);
      if (!selectedTags.includes(tag)) {
        selectedTags.push(tag);
      }
    }
    filters.tags = selectedTags.join(',');
  }
  
  // Date range (20% chance)
  if (shouldInclude(0.2)) {
    const fromDate = getRandomDateInPastYear();
    const toDate = getRandomDateInPastYear();
    
    if (new Date(fromDate) <= new Date(toDate)) {
      filters.dateFrom = fromDate;
      filters.dateTo = toDate;
    } else {
      filters.dateFrom = toDate;
      filters.dateTo = fromDate;
    }
  }
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

/**
 * Generate NO-FILTER baseline queries (fastest possible)
 * Just basic pagination with newest sort
 */
function generateBaselineFilters(context, events, done) {
  const filters = {};
  
  // Fastest possible configuration
  filters.sortBy = "newest";
  filters.page = getRandomInt(1, 10);
  filters.perPage = getRandomElement(PER_PAGE_OPTIONS);
  
  // No additional filters - pure baseline performance
  
  // Set all filter values in context
  Object.keys(filters).forEach(key => {
    context.vars[key] = filters[key];
  });
  
  return done();
}

// Export functions for Artillery to use
module.exports = {
  generateFastQueryFilters,
  generateSlowQueryFilters,
  generateTagQueryFilters,
  generateMixedQueryFilters,
  generateBaselineFilters
};
