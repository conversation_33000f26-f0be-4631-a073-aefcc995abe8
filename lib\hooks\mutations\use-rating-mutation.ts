import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import type { 
  RatingSubmissionRequest, 
  RatingSubmissionResponse, 
  RatingStatistics, 
  UserRatingData 
} from '@/lib/types/database'

/**
 * Rating mutation hook with optimistic updates for instant UI feedback
 * Handles authentication, optimistic updates with local calculation, and error rollback
 * Uses TWO-CACHE UPDATE STRATEGY: updates both detail cache AND grid caches
 * 
 * @param mugshotId - The ID of the mugshot to rate
 * @returns TanStack Query mutation for rating submission
 */
export function useRatingMutation(mugshotId: string) {
  const queryClient = useQueryClient()
  const { isAuthenticated, user } = useAuthStore()
  
  return useMutation({
    mutationFn: async (rating: number): Promise<RatingSubmissionResponse> => {
      // Immediate authentication check
      if (!isAuthenticated || !user) {
        throw new Error('Authentication required')
      }
      
      // Validate rating range to match database constraint
      if (!Number.isInteger(rating) || rating < 1 || rating > 10) {
        throw new Error('Rating must be an integer between 1 and 10')
      }

      // Validate mugshotId
      const numericMugshotId = parseInt(mugshotId, 10)
      if (isNaN(numericMugshotId) || numericMugshotId <= 0) {
        throw new Error('Invalid mugshot ID')
      }
      
      const request: RatingSubmissionRequest = {
        mugshotId: numericMugshotId,
        rating
      }
      
      const response = await fetch('/api/ratings/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      })
      
      const result: RatingSubmissionResponse = await response.json()
      
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to submit rating')
      }
      
      return result
    },
    
    onMutate: async (newRating: number) => {
      // Only proceed with optimistic updates for authenticated users
      if (!isAuthenticated || !user) {
        return
      }
      
      // ✅ STEP 1: Cancel outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: ['mugshot', mugshotId, 'rating-statistics'] })
      await queryClient.cancelQueries({ queryKey: ['user', 'mugshot', mugshotId, 'rating'] })
      await queryClient.cancelQueries({ queryKey: ['mugshots'] }) // Cancel all grid queries
      
      // ✅ STEP 2: Snapshot previous data for rollback
      const previousStatistics = queryClient.getQueryData<RatingStatistics>(['mugshot', mugshotId, 'rating-statistics'])
      const previousUserRating = queryClient.getQueryData<UserRatingData>(['user', 'mugshot', mugshotId, 'rating'])
      
      // ✅ STEP 3: Update detail cache (already working)
      const newUserRatingData: UserRatingData = { userRating: newRating }
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], newUserRatingData)
      
      // Calculate new statistics locally (same pattern as tags)
      let newStatistics: RatingStatistics | undefined
      if (previousStatistics) {
        const currentTotal = previousStatistics.totalRatings || 0
        const currentAvg = previousStatistics.averageRating || 0
        const previousRating = previousUserRating?.userRating
        
        let newTotal = currentTotal
        let newSum = currentAvg * currentTotal
        
        if (previousRating !== null && previousRating !== undefined) {
          // User is updating their rating - replace old rating with new
          newSum = newSum - previousRating + newRating
          // Total stays the same
        } else {
          // User is rating for the first time - add to total
          newSum = newSum + newRating
          newTotal = currentTotal + 1
        }
        
        const newAvg = newTotal > 0 ? newSum / newTotal : 0
        
        newStatistics = {
          averageRating: Math.round(newAvg * 100) / 100, // Round to 2 decimal places
          totalRatings: newTotal
        }
        
        queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], newStatistics)
      }
      
      // ✅ STEP 4: Update ALL grid caches - THE MAGIC HAPPENS HERE!
      // Get all ['mugshots', ...] query cache entries and update the specific mugshot
      const mugshotCaches = queryClient.getQueriesData({ queryKey: ['mugshots'] })
      const previousGridCaches: Array<{ queryKey: readonly unknown[], data: unknown }> = []
      
      mugshotCaches.forEach(([queryKey, data]) => {
        if (!data || typeof data !== 'object') return
        
        // Save original data for rollback
        previousGridCaches.push({ queryKey, data })
        
        const gridData = data as { mugshots?: Array<{ id: number, [key: string]: unknown }> }
        if (!gridData.mugshots || !Array.isArray(gridData.mugshots)) return
        
                 // Find and update the specific mugshot in this grid cache
         const updatedMugshots = gridData.mugshots.map(mugshot => {
           if (mugshot.id === parseInt(mugshotId, 10)) {
             // Update this mugshot's rating data in the grid
             const newAvgRating = newStatistics?.averageRating ?? mugshot.average_rating
             const newTotalRatings = newStatistics?.totalRatings ?? mugshot.total_ratings
             
             return {
               ...mugshot,
               // Update both database format (snake_case) AND UI format (camelCase)
               average_rating: newAvgRating,
               total_ratings: newTotalRatings,
               averageRating: newAvgRating,  // UI expects camelCase
               totalRatings: newTotalRatings, // UI expects camelCase
               rating: newAvgRating,         // Legacy property some components might use
               votes: newTotalRatings,       // Legacy property some components might use
               // Keep all other mugshot properties unchanged
             }
           }
           return mugshot // Leave other mugshots unchanged
         })
        
        // Update the cache with the modified grid data
        queryClient.setQueryData(queryKey, {
          ...gridData,
          mugshots: updatedMugshots
        })
      })
      
      return { 
        previousStatistics, 
        previousUserRating, 
        previousGridCaches 
      }
    },
    
    onError: (error, _newRating, context) => {
      console.error('Rating mutation error:', error)
      
      // ✅ STEP 5: Rollback ALL optimistic updates
      if (context?.previousStatistics) {
        queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], context.previousStatistics)
      }
      if (context?.previousUserRating) {
        queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], context.previousUserRating)
      }
      
      // Rollback all grid cache updates
      if (context?.previousGridCaches) {
        context.previousGridCaches.forEach(({ queryKey, data }) => {
          queryClient.setQueryData(queryKey, data)
        })
      }
    },
    
    onSuccess: (result, _newRating) => {
      // Rating submitted successfully - just log success
      console.log('Rating submitted successfully:', result)
      
      // Don't invalidate mugshots queries here - it causes page re-render and closes popover
      // The optimistic updates already handle the UI changes instantly
    },

    onSettled: () => {
      // Note: We don't invalidate rating-statistics queries since they're disabled
      // The optimistic updates in onMutate already handle the UI updates
      
      // Invalidate other related queries for consistency
      queryClient.invalidateQueries({ queryKey: ['user', 'mugshot', mugshotId, 'rating'] })
      queryClient.invalidateQueries({ queryKey: ['mugshot', mugshotId, 'detail'] })
      queryClient.invalidateQueries({ queryKey: ['user', 'mugshot', mugshotId, 'data'] })
    }
  })
}

/**
 * Utility to check if user can rate a mugshot
 * Returns whether the rating mutation should be enabled
 */
export function useCanRate(mugshotId: string): boolean {
  const { isAuthenticated, user } = useAuthStore()
  
  // Validate mugshotId is provided and valid
  if (!mugshotId || mugshotId.trim() === '') {
    return false
  }
  
  const numericMugshotId = parseInt(mugshotId, 10)
  if (isNaN(numericMugshotId) || numericMugshotId <= 0) {
    return false
  }
  
  return isAuthenticated && !!user
}

/**
 * Utility to handle unauthenticated rating attempts
 * Redirects to login with current URL preserved for post-login redirect
 */
export function handleUnauthenticatedRating(): void {
  const currentUrl = window.location.href
  window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
} 