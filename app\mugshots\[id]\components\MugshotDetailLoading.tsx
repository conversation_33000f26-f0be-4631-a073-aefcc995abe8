import { ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function MugshotDetailLoading() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header with Back Navigation */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="sm" className="border-cyan-500/30 text-white" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Mugshots
          </Button>
          
          <div className="flex-1">
            <Skeleton className="h-10 w-64 mb-2 bg-gray-800" />
            <Skeleton className="h-6 w-48 bg-gray-800" />
          </div>

          <Skeleton className="h-10 w-24 bg-gray-800" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Image and Quick Info Skeleton */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-900/90 border-cyan-500/30">
              <CardContent className="p-6">
                <Skeleton className="aspect-[4/5] mb-4 bg-gray-800 rounded-lg" />
                
                <div className="flex justify-center mb-4">
                  <Skeleton className="h-6 w-16 bg-gray-800 rounded-full" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-4 w-4 bg-gray-800" />
                    <Skeleton className="h-4 w-32 bg-gray-800" />
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-4 w-4 bg-gray-800" />
                    <Skeleton className="h-4 w-40 bg-gray-800" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Details Skeleton */}
          <div className="lg:col-span-2 space-y-6">
            {/* Arrest Information */}
            <Card className="bg-gray-900/90 border-cyan-500/30">
              <CardHeader>
                <Skeleton className="h-6 w-48 bg-gray-800" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Skeleton className="h-4 w-24 mb-2 bg-gray-800" />
                  <Skeleton className="h-6 w-64 bg-gray-800" />
                </div>
                
                <Skeleton className="h-[1px] w-full bg-gray-700" />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-32 mb-2 bg-gray-800" />
                    <Skeleton className="h-5 w-40 bg-gray-800" />
                  </div>
                  
                  <div>
                    <Skeleton className="h-4 w-20 mb-2 bg-gray-800" />
                    <Skeleton className="h-5 w-36 bg-gray-800" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Charges Skeleton */}
            <Card className="bg-gray-900/90 border-cyan-500/30">
              <CardHeader>
                <Skeleton className="h-6 w-48 bg-gray-800" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-12 w-full bg-gray-800 rounded-lg" />
                  <Skeleton className="h-12 w-full bg-gray-800 rounded-lg" />
                  <Skeleton className="h-12 w-3/4 bg-gray-800 rounded-lg" />
                </div>
              </CardContent>
            </Card>

            {/* Rating Section Skeleton */}
            <Card className="bg-gray-900/90 border-cyan-500/30">
              <CardHeader>
                <Skeleton className="h-6 w-48 bg-gray-800" />
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 space-y-4">
                  <Skeleton className="h-4 w-80 mx-auto bg-gray-800" />
                  <Skeleton className="h-10 w-40 mx-auto bg-gray-800 rounded" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 