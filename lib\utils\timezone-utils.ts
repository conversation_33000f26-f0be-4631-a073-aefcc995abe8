// Timezone utilities for mugshot rating time gate functionality

interface TimezoneResult {
  timezone: string
  currentTime: Date
  isValid: boolean
}

export interface CountdownResult {
  hours: number
  minutes: number
  seconds: number
  totalMilliseconds: number
  isExpired: boolean
}

// Map of US states to their primary timezones (unused but kept for future reference)
const _STATE_TIMEZONES: Record<string, string> = {
  'AL': 'America/Chicago',     // Alabama - Central Time
  'AK': 'America/Anchorage',   // Alaska
  'AZ': 'America/Phoenix',     // Arizona - Mountain Standard Time (no DST)
  'AR': 'America/Chicago',     // Arkansas - Central Time
  'CA': 'America/Los_Angeles', // California - Pacific Time
  'CO': 'America/Denver',      // Colorado - Mountain Time
  'CT': 'America/New_York',    // Connecticut - Eastern Time
  'DE': 'America/New_York',    // Delaware - Eastern Time
  'FL': 'America/New_York',    // Florida - Eastern Time (most of state)
  'GA': 'America/New_York',    // Georgia - Eastern Time
  'HI': 'Pacific/Honolulu',    // Hawaii
  'ID': 'America/Denver',      // Idaho - Mountain Time (most of state)
  'IL': 'America/Chicago',     // Illinois - Central Time
  'IN': 'America/New_York',    // Indiana - Eastern Time (most of state)
  'IA': 'America/Chicago',     // Iowa - Central Time
  'KS': 'America/Chicago',     // Kansas - Central Time (most of state)
  'KY': 'America/New_York',    // Kentucky - Eastern Time (most of state)
  'LA': 'America/Chicago',     // Louisiana - Central Time
  'ME': 'America/New_York',    // Maine - Eastern Time
  'MD': 'America/New_York',    // Maryland - Eastern Time
  'MA': 'America/New_York',    // Massachusetts - Eastern Time
  'MI': 'America/New_York',    // Michigan - Eastern Time (most of state)
  'MN': 'America/Chicago',     // Minnesota - Central Time
  'MS': 'America/Chicago',     // Mississippi - Central Time
  'MO': 'America/Chicago',     // Missouri - Central Time
  'MT': 'America/Denver',      // Montana - Mountain Time
  'NE': 'America/Chicago',     // Nebraska - Central Time (most of state)
  'NV': 'America/Los_Angeles', // Nevada - Pacific Time (most of state)
  'NH': 'America/New_York',    // New Hampshire - Eastern Time
  'NJ': 'America/New_York',    // New Jersey - Eastern Time
  'NM': 'America/Denver',      // New Mexico - Mountain Time
  'NY': 'America/New_York',    // New York - Eastern Time
  'NC': 'America/New_York',    // North Carolina - Eastern Time
  'ND': 'America/Chicago',     // North Dakota - Central Time (most of state)
  'OH': 'America/New_York',    // Ohio - Eastern Time
  'OK': 'America/Chicago',     // Oklahoma - Central Time
  'OR': 'America/Los_Angeles', // Oregon - Pacific Time (most of state)
  'PA': 'America/New_York',    // Pennsylvania - Eastern Time
  'RI': 'America/New_York',    // Rhode Island - Eastern Time
  'SC': 'America/New_York',    // South Carolina - Eastern Time
  'SD': 'America/Chicago',     // South Dakota - Central Time (most of state)
  'TN': 'America/Chicago',     // Tennessee - Central Time (most of state)
  'TX': 'America/Chicago',     // Texas - Central Time (most of state)
  'UT': 'America/Denver',      // Utah - Mountain Time
  'VT': 'America/New_York',    // Vermont - Eastern Time
  'VA': 'America/New_York',    // Virginia - Eastern Time
  'WA': 'America/Los_Angeles', // Washington - Pacific Time
  'WV': 'America/New_York',    // West Virginia - Eastern Time
  'WI': 'America/Chicago',     // Wisconsin - Central Time
  'WY': 'America/Denver',      // Wyoming - Mountain Time
}

/**
 * Get timezone for a given state (and optionally county)
 * Always returns America/Chicago timezone as per business requirements
 */
export function getTimezoneForLocation(_state?: string | null, _county?: string | null): string {
  // Always use America/Chicago timezone for all time gate comparisons
  return 'America/Chicago'
}

/**
 * Get current time in a specific timezone
 */
export function getCurrentTimeInTimezone(timezone: string): TimezoneResult {
  try {
    const now = new Date()
    
    // Create a date formatter for the specific timezone
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
    
    const parts = formatter.formatToParts(now)
    const year = parseInt(parts.find(p => p.type === 'year')?.value || '0')
    const month = parseInt(parts.find(p => p.type === 'month')?.value || '0') - 1 // Month is 0-indexed
    const day = parseInt(parts.find(p => p.type === 'day')?.value || '0')
    const hour = parseInt(parts.find(p => p.type === 'hour')?.value || '0')
    const minute = parseInt(parts.find(p => p.type === 'minute')?.value || '0')
    const second = parseInt(parts.find(p => p.type === 'second')?.value || '0')
    
    const localTime = new Date(year, month, day, hour, minute, second)
    
    return {
      timezone,
      currentTime: localTime,
      isValid: true
    }
  } catch (error) {
    console.error(`Error getting time for timezone ${timezone}:`, error)
    return {
      timezone: 'UTC',
      currentTime: new Date(),
      isValid: false
    }
  }
}

/**
 * Check if rating is enabled based on dateOfBooking and location timezone
 * Rating is enabled at midnight the day after dateOfBooking
 */
export function isRatingEnabled(
  dateOfBooking: string | null,
  state?: string | null,
  county?: string | null
): boolean {
  if (!dateOfBooking) {
    return false // No booking date, rating not enabled
  }
  
  try {
    const timezone = getTimezoneForLocation(state, county)
    const { currentTime } = getCurrentTimeInTimezone(timezone)
    
    // Parse booking date
    const bookingDate = new Date(dateOfBooking)
    
    // Calculate the day after booking date
    const dayAfterBooking = new Date(bookingDate)
    dayAfterBooking.setDate(bookingDate.getDate() + 1)
    
    // Get current date in local timezone (without time)
    const currentDateOnly = new Date(currentTime.getFullYear(), currentTime.getMonth(), currentTime.getDate())
    const enabledDateOnly = new Date(dayAfterBooking.getFullYear(), dayAfterBooking.getMonth(), dayAfterBooking.getDate())
    
    // Rating is enabled if current date >= day after booking date
    return currentDateOnly >= enabledDateOnly
  } catch (error) {
    console.error('Error checking rating enabled status:', error)
    return false
  }
}

/**
 * Calculate countdown until rating is enabled
 * Returns countdown to midnight of the day after dateOfBooking
 */
export function getCountdownToRatingEnabled(
  dateOfBooking: string | null,
  state?: string | null,
  county?: string | null
): CountdownResult {
  if (!dateOfBooking) {
    return {
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalMilliseconds: 0,
      isExpired: true
    }
  }
  
  try {
    const timezone = getTimezoneForLocation(state, county)
    const { currentTime } = getCurrentTimeInTimezone(timezone)
    
    // Parse booking date
    const bookingDate = new Date(dateOfBooking)
    
    // Calculate midnight of the day after booking date
    const enabledTime = new Date(bookingDate)
    enabledTime.setDate(bookingDate.getDate() + 1)
    enabledTime.setHours(0, 0, 0, 0)
    
    // Calculate time difference
    const timeDiff = enabledTime.getTime() - currentTime.getTime()
    
    if (timeDiff <= 0) {
      return {
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalMilliseconds: 0,
        isExpired: true
      }
    }
    
    // Convert to hours, minutes, seconds
    const hours = Math.floor(timeDiff / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)
    
    return {
      hours,
      minutes,
      seconds,
      totalMilliseconds: timeDiff,
      isExpired: false
    }
  } catch (error) {
    console.error('Error calculating countdown:', error)
    return {
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalMilliseconds: 0,
      isExpired: true
    }
  }
}

/**
 * Format countdown for display
 */
export function formatCountdown(countdown: CountdownResult): string {
  if (countdown.isExpired) {
    return '00:00:00'
  }
  
  const hours = countdown.hours.toString().padStart(2, '0')
  const minutes = countdown.minutes.toString().padStart(2, '0')
  const seconds = countdown.seconds.toString().padStart(2, '0')
  
  return `${hours}:${minutes}:${seconds}`
} 