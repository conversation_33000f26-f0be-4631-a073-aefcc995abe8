import { DatabaseMugshot } from '@/lib/services/mugshots-service-server'

// Smart additional details parser utility
export function parseAdditionalDetails(details: string | null | undefined): Record<string, string> | null {
  if (!details || typeof details !== 'string') {
    return null;
  }
  
  const detailsStr = details.toString().trim();
  
  // Try to parse as <PERSON><PERSON><PERSON> first
  try {
    const parsed = JSON.parse(detailsStr);
    if (typeof parsed === 'object' && parsed !== null) {
      return parsed;
    }
  } catch {
    // If JSON parsing fails, try to parse as key-value pairs
    const kvPairs: Record<string, string> = {};
    
    // Match patterns like "Name: WILLIAMS,BISHOP DOB: 11/30/2006 Age: 18"
    // Also handle patterns with newlines or multiple formats
    const patterns = [
      // Pattern 1: "Key: Value Key2: Value2" (space-separated)
      /(\w+(?:\s+\w+)*)\s*:\s*([^:]+?)(?=\s+\w+\s*:|$)/g,
      // Pattern 2: "Key=Value Key2=Value2" (equals-separated)
      /(\w+(?:\s+\w+)*)\s*=\s*([^=]+?)(?=\s+\w+\s*=|$)/g,
      // Pattern 3: "Key|Value Key2|Value2" (pipe-separated)
      /(\w+(?:\s+\w+)*)\s*\|\s*([^|]+?)(?=\s+\w+\s*\||$)/g
    ];
    
    for (const pattern of patterns) {
      const matches = [...detailsStr.matchAll(pattern)];
      if (matches.length > 0) {
        matches.forEach(match => {
          const key = match[1].trim();
          const value = match[2].trim();
          if (key && value) {
            kvPairs[key] = value;
          }
        });
        if (Object.keys(kvPairs).length > 0) {
          return kvPairs;
        }
      }
    }
    
    // If no patterns match, try line-by-line parsing
    const lines = detailsStr.split(/\r?\n/);
    for (const line of lines) {
      const colonMatch = line.match(/^([^:]+):(.+)$/);
      const equalsMatch = line.match(/^([^=]+)=(.+)$/);
      
      if (colonMatch) {
        const key = colonMatch[1].trim();
        const value = colonMatch[2].trim();
        if (key && value) kvPairs[key] = value;
      } else if (equalsMatch) {
        const key = equalsMatch[1].trim();
        const value = equalsMatch[2].trim();
        if (key && value) kvPairs[key] = value;
      }
    }
    
    return Object.keys(kvPairs).length > 0 ? kvPairs : null;
  }
  
  return null;
}

// Smart offense parser utility
function toTitleCase(str: string): string {
  return str
    .toLowerCase()
    .replace(/\b\w/g, char => char.toUpperCase())
}

export function smartParseOffenses(rawText: string | null): string[] {
  if (!rawText) return []

  // Step 1: INTELLIGENT NEWLINE HANDLING - Fix broken monetary values and other split text
  const processedText = rawText
  
  // Check if text contains newlines (database format)
  if (rawText.includes('\n')) {
    // Split by newlines and intelligently reassemble broken parts
    const lines = rawText.split(/\n+/).map(line => line.trim()).filter(line => line.length > 0)
    
    const reassembled = []
    for (let i = 0; i < lines.length; i++) {
      const current = lines[i]
      const next = lines[i + 1]
      
      // Pattern 1: Reassemble broken monetary values (e.g., "$1" + "000" = "$1000")
      const incompleteMoneyPattern = /\$\d+$/
      const isNumericContinuation = /^\d{3,}$/ // 3+ digits (like "000")
      
      if (next && incompleteMoneyPattern.test(current) && isNumericContinuation.test(next)) {
        reassembled.push(current + next)
        i++ // Skip the next line since we consumed it
        continue
      }
      
      // Pattern 2: Reassemble other broken text (very short line followed by longer one)
      if (next && current.length < 15 && next.length > 15 && !current.includes('$')) {
        // Check if combining makes sense (reasonable total length)
        const combined = current + ' ' + next
        if (combined.length < 120) { // Reasonable offense length
          reassembled.push(combined)
          i++ // Skip next
          continue
        }
      }
      
      // Pattern 3: Normal line - keep as is
      reassembled.push(current)
    }
    
    // For newline-separated offenses, return immediately after cleaning
    return reassembled
      .map(text => toTitleCase(text.replace(/\(.*?\)/g, "").replace(/\s+/g, " ").trim()))
      .filter(offense => {
        // Enhanced filtering to remove meaningless entries
        return offense.length > 2 && 
               !/^\d+$/.test(offense) && // No pure numbers like "000"
               !/^[A-Z]?$/.test(offense) && // No single letters
               offense !== 'And' && offense !== 'Or' && offense !== 'Of' // No common connecting words
      })
  }

  // Step 2: For non-newline text, use existing parsing logic (semicolon splitting only)
  
  // Step 2: For non-newline text, use existing parsing logic
  // Remove parentheses content (like bond info) and normalize whitespace
  const cleaned = processedText.replace(/\(.*?\)/g, "").replace(/\s+/g, " ").trim()
  
  // Step 3: Split by hyphen followed by offense codes (6+ digits + optional alphanumeric)
  const offenseBlocks = cleaned.split(/\s*-\s*(?=\d{6,}[A-Z0-9]*)/g)

  const offenses = offenseBlocks
    .flatMap(block => {
      // Remove leading offense codes
      const clean = block.replace(/^\d+[A-Z0-9]*/i, "").trim()
      
      // Split by semicolons only (removed comma splitting to avoid monetary value issues)
      const parts = clean.split(/[;](?![^()]*\))/).map(part => part.trim())
      
      // Then split each part by " - " or " / " patterns (new patterns for multiple offenses)
      return parts.flatMap(part => {
        // First split by " / " which often separates related offenses
        const slashParts = part.split(/\s\/\s/).map(slashPart => slashPart.trim())
        
        return slashParts.flatMap(slashPart => {
          // Then split by " - " when it separates distinct offenses
          // Look for pattern where " - " is followed by capital letter (new offense start)
          return slashPart.split(/\s-\s(?=[A-Z])/).map(dashPart => dashPart.trim())
        })
      })
    })
    .map(text => toTitleCase(text.replace(/^[-,;\/\n]+|[-,;\/\n]+$/g, "").trim()))
    .filter(offense => {
      // Enhanced filtering to remove meaningless entries
      return offense.length > 2 && 
             !/^\d+$/.test(offense) && // No pure numbers like "000"
             !/^[A-Z]?$/.test(offense) && // No single letters
             offense !== 'And' && offense !== 'Or' && offense !== 'Of' // No common connecting words
    })

  return offenses
}

// Types matching the existing UI component expectations
export interface UIMugshot {
  id: number
  name: string
  location: string
  state?: string
  county?: string
  rating: number
  image: string
  category: string
  votes: number
  views?: number
  arrestDate?: string
  birthDate?: string
  offenses?: string[]
  // Rating and tag data from DatabaseMugshot
  averageRating?: number
  totalRatings?: number
  wildCount?: number
  funnyCount?: number
  spookyCount?: number
  userRating?: number | null
  userTags?: string[]
  // Additional details parsed from database
  additionalDetails?: Record<string, string>
}

// Utility functions to transform database data to UI format
export function transformDBMugshotToUI(dbMugshot: DatabaseMugshot): UIMugshot {
  const fullName = `${dbMugshot.firstName || ''} ${dbMugshot.lastName || ''}`.trim()
  const location = dbMugshot.countyOfBooking && dbMugshot.stateOfBooking
    ? `${dbMugshot.countyOfBooking}, ${dbMugshot.stateOfBooking}`
    : dbMugshot.stateOfBooking || dbMugshot.countyOfBooking || 'Unknown'

  // Parse offenses using smart offense parser
  const offenses = smartParseOffenses(dbMugshot.offenseDescription)

  // Use actual rating/tag data if available, otherwise fallback to mock data
  const actualRating = dbMugshot.average_rating || generateMockRating()
  const actualVotes = dbMugshot.total_ratings || generateMockVotes()

  return {
    id: dbMugshot.id,
    name: fullName || 'Unknown',
    location,
    state: dbMugshot.stateOfBooking || undefined,
    county: dbMugshot.countyOfBooking || undefined,
    rating: actualRating,
    image: dbMugshot.imagePath || '/images/mugshot-placeholder.png',
    category: assignMugshotCategory(dbMugshot.offenseDescription),
    votes: actualVotes,
    views: generateMockViews(), // Mock data for now
    arrestDate: dbMugshot.dateOfBooking || undefined,
    birthDate: undefined, // Not in current schema
    offenses: offenses.length > 0 ? offenses : undefined,
    // Include actual rating and tag data from database
    averageRating: dbMugshot.average_rating,
    totalRatings: dbMugshot.total_ratings,
    wildCount: dbMugshot.wild_count,
    funnyCount: dbMugshot.funny_count,
    spookyCount: dbMugshot.spooky_count,
    userRating: dbMugshot.user_rating,
    userTags: dbMugshot.user_tags,
    // Parse additional details intelligently
    additionalDetails: parseAdditionalDetails(dbMugshot.additionalDetails) || undefined
  }
}

export function transformDBMugshotsToUI(dbMugshots: DatabaseMugshot[]): UIMugshot[] {
  return dbMugshots.map(transformDBMugshotToUI)
}

// Mock data generators (for features not yet implemented)
function generateMockRating(): number {
  return Math.floor(Math.random() * 10) + 1
}



function generateMockVotes(): number {
  return Math.floor(Math.random() * 1000) + 10
}

function generateMockViews(): number {
  return Math.floor(Math.random() * 5000) + 100
}

// Pagination utility
export interface PaginationParams {
  page: number
  perPage: number
}

export interface PaginationResult {
  totalPages: number
  offset: number
  hasNext: boolean
  hasPrevious: boolean
}

export function calculatePagination(totalCount: number, params: PaginationParams): PaginationResult {
  const totalPages = Math.ceil(totalCount / params.perPage)
  const offset = (params.page - 1) * params.perPage
  
  return {
    totalPages,
    offset,
    hasNext: params.page < totalPages,
    hasPrevious: params.page > 1
  }
}

// Category assignment utility using parsed offenses for better accuracy
export function assignMugshotCategory(offenseDescription: string | null): string {
  if (!offenseDescription) return 'Hot'
  
  const parsedOffenses = smartParseOffenses(offenseDescription)
  const allOffensesText = parsedOffenses.join(' ').toLowerCase()
  
  // Enhanced keyword-based categorization using parsed offenses
  if (
    allOffensesText.includes('dui') || 
    allOffensesText.includes('drunk') || 
    allOffensesText.includes('intoxication') ||
    allOffensesText.includes('under the influence') ||
    allOffensesText.includes('driving while intoxicated')
  ) {
    return 'Wild'
  } else if (
    allOffensesText.includes('assault') || 
    allOffensesText.includes('battery') || 
    allOffensesText.includes('violence') ||
    allOffensesText.includes('domestic') ||
    allOffensesText.includes('weapon') ||
    allOffensesText.includes('firearm')
  ) {
    return 'Scary'
  } else if (
    allOffensesText.includes('disturbing') || 
    allOffensesText.includes('disorderly') || 
    allOffensesText.includes('public') ||
    allOffensesText.includes('lewdness') ||
    allOffensesText.includes('indecent') ||
    allOffensesText.includes('prostitution')
  ) {
    return 'Funny'
  } else {
    return 'Hot'
  }
}

// Image path utility
export function getMugshotImageUrl(imagePath: string | null): string {
  if (!imagePath) return '/images/mugshot-placeholder.png'
  
  // If it's already a full URL, return as-is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // If it's a relative path, ensure it starts with /
  if (!imagePath.startsWith('/')) {
    return `/${imagePath}`
  }
  
  return imagePath
}

// Enhanced transform function with category assignment
export function transformDBMugshotToUIEnhanced(dbMugshot: DatabaseMugshot): UIMugshot {
  const basicTransform = transformDBMugshotToUI(dbMugshot)
  
  return {
    ...basicTransform,
    category: assignMugshotCategory(dbMugshot.offenseDescription),
    image: getMugshotImageUrl(dbMugshot.imagePath),
    offenses: smartParseOffenses(dbMugshot.offenseDescription)
    }
}

// =====================================================
// SEO URL GENERATION UTILITIES
// =====================================================

/**
 * Generate SEO-friendly slug for mugshot URLs
 * Format: first-name-last-name-county-state-id
 * Example: john-doe-miami-dade-fl-12345
 */
export function generateMugshotSlug(mugshot: UIMugshot | DatabaseMugshot): string {
  // Handle both UI and Database mugshot formats
  const firstName = 'firstName' in mugshot ? mugshot.firstName : mugshot.name?.split(' ')[0] || ''
  const lastName = 'lastName' in mugshot ? mugshot.lastName : mugshot.name?.split(' ').slice(1).join(' ') || ''
  const county = 'countyOfBooking' in mugshot ? mugshot.countyOfBooking : mugshot.location?.split(',')[0]?.trim() || ''
  const state = 'stateOfBooking' in mugshot ? mugshot.stateOfBooking : mugshot.location?.split(',')[1]?.trim() || ''
  const id = mugshot.id

  // Sanitize name parts
  const safeName = `${firstName}-${lastName}`.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')

  // Sanitize location parts with null checks
  const safeCounty = (county || '').toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')

  const safeState = (state || '').toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')

  // Combine parts (handle empty parts gracefully)
  const parts = [safeName, safeCounty, safeState, id.toString()].filter(part => part && part !== '-')
  
  return parts.join('-')
}

/**
 * Extract mugshot ID from SEO-friendly slug
 * Handles various slug formats and always returns the last numeric part
 */
export function extractIdFromSlug(slug: string): string {
  if (!slug) return ''
  
  // Split by hyphens and find the last numeric part
  const parts = slug.split('-')
  
  // Look for the last part that's purely numeric
  for (let i = parts.length - 1; i >= 0; i--) {
    const part = parts[i]
    if (/^\d+$/.test(part)) {
      return part
    }
  }
  
  // Fallback: if slug is just a number, return it
  if (/^\d+$/.test(slug)) {
    return slug
  }
  
  return ''
}

/**
 * Validate and sanitize slug format
 * Ensures slug follows expected pattern and handles edge cases
 */
export function validateMugshotSlug(slug: string): { isValid: boolean; mugshotId?: string; error?: string } {
  if (!slug) {
    return { isValid: false, error: 'Slug is empty' }
  }

  const mugshotId = extractIdFromSlug(slug)
  
  if (!mugshotId) {
    return { isValid: false, error: 'No valid ID found in slug' }
  }

  // Additional validation - ensure ID is reasonable
  const idNumber = parseInt(mugshotId, 10)
  if (isNaN(idNumber) || idNumber < 1) {
    return { isValid: false, error: 'Invalid mugshot ID' }
  }

  return { isValid: true, mugshotId }
}

/**
 * Generate canonical URL for mugshot
 * Used for meta tags and social sharing
 */
export function generateCanonicalUrl(mugshot: UIMugshot | DatabaseMugshot, baseUrl?: string): string {
  const slug = generateMugshotSlug(mugshot)
  const domain = baseUrl || process.env.NEXT_PUBLIC_SITE_URL || 'https://americastopmugs.com'
  
  return `${domain}/mugshots/${slug}`
}

/**
 * Create redirect mapping for old URL format to new SEO format
 * Helps with migration from /mugshots/[id] to /mugshots/[slug]
 */
export function createRedirectUrl(_mugshotId: string, mugshot: UIMugshot | DatabaseMugshot): string {
  const slug = generateMugshotSlug(mugshot)
  return `/mugshots/${slug}`
}

// =====================================================
// STRUCTURED DATA GENERATION
// =====================================================

/**
 * Generate JSON-LD structured data for mugshot pages
 * Implements Schema.org Person schema with aggregateRating
 */
export function generateMugshotStructuredData(
  mugshot: UIMugshot | DatabaseMugshot, 
  options: {
    includeRating?: boolean
    includeAddress?: boolean
    baseUrl?: string
  } = {}
) {
  const { includeRating = true, includeAddress = true, baseUrl } = options
  
  // Handle both UI and Database mugshot formats with proper type checks
  const firstName = 'firstName' in mugshot ? mugshot.firstName : mugshot.name?.split(' ')[0] || ''
  const lastName = 'lastName' in mugshot ? mugshot.lastName : mugshot.name?.split(' ').slice(1).join(' ') || ''
  const fullName = `${firstName} ${lastName}`.trim() || 
    ('firstName' in mugshot ? `${mugshot.firstName} ${mugshot.lastName}` : mugshot.name) || 'Unknown'
  const county = 'countyOfBooking' in mugshot ? mugshot.countyOfBooking : mugshot.location?.split(',')[0]?.trim() || ''
  const state = 'stateOfBooking' in mugshot ? mugshot.stateOfBooking : mugshot.location?.split(',')[1]?.trim() || ''
  const arrestDate = 'dateOfBooking' in mugshot ? mugshot.dateOfBooking : mugshot.arrestDate
  const imageUrl = 'imagePath' in mugshot ? mugshot.imagePath : mugshot.image
  
  // Get rating data with proper type checks
  const averageRating = 'average_rating' in mugshot ? mugshot.average_rating : 
    ('averageRating' in mugshot ? mugshot.averageRating : ('rating' in mugshot ? mugshot.rating : undefined))
  const totalRatings = 'total_ratings' in mugshot ? mugshot.total_ratings : 
    ('totalRatings' in mugshot ? mugshot.totalRatings : ('votes' in mugshot ? mugshot.votes : undefined))
  
  // Base structured data with proper typing
  const structuredData: {
    [key: string]: unknown
    "@context": string
    "@type": string
    name: string
    identifier: string
    url: string
    image?: {
      "@type": string
      url: string
      contentUrl: string
      width: string
      height: string
      caption: string
      description: string
    }
    address?: {
      "@type": string
      addressCountry: string
      addressLocality?: string
      addressRegion?: string
    }
    aggregateRating?: {
      "@type": string
      ratingValue: string
      ratingCount: string
      bestRating: string
      worstRating: string
      description: string
    }
    additionalProperty?: Array<{
      "@type": string
      name: string
      value: string
    }>
  } = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": fullName,
    "identifier": mugshot.id.toString(),
    "url": generateCanonicalUrl(mugshot, baseUrl),
  }
  
  // Add image if available
  if (imageUrl) {
    const absoluteImageUrl = imageUrl.startsWith('http') 
      ? imageUrl 
      : `${baseUrl || process.env.NEXT_PUBLIC_SITE_URL || 'https://americastopmugs.com'}${imageUrl}`
      
    structuredData.image = {
      "@type": "ImageObject",
      "url": absoluteImageUrl,
      "contentUrl": absoluteImageUrl,
      "width": "400",
      "height": "600",
      "caption": `${fullName} mugshot from ${county}, ${state}`,
      "description": `Booking photo of ${fullName}`,
    }
  }
  
  // Add address information with proper typing
  if (includeAddress && (county || state)) {
    structuredData.address = {
      "@type": "PostalAddress",
      "addressCountry": "US"
    }
    
    if (county) structuredData.address.addressLocality = county
    if (state) structuredData.address.addressRegion = state
  }
  
  // Add aggregate rating if available and requested
  if (includeRating && averageRating && totalRatings) {
    structuredData.aggregateRating = {
      "@type": "AggregateRating",
      "ratingValue": averageRating.toString(),
      "ratingCount": totalRatings.toString(),
      "bestRating": "10",
      "worstRating": "1",
      "description": `Community rating for ${fullName}'s mugshot`
    }
  }
  
  // Add additional context
  if (arrestDate) {
    structuredData.additionalProperty = [
      {
        "@type": "PropertyValue",
        "name": "Date of Booking",
        "value": arrestDate
      }
    ]
  }
  
  // Add offenses if available
  const offenses = 'offenseDescription' in mugshot 
    ? smartParseOffenses(mugshot.offenseDescription) 
    : mugshot.offenses || []
    
  if (offenses.length > 0) {
    if (!structuredData.additionalProperty) structuredData.additionalProperty = []
    structuredData.additionalProperty.push({
      "@type": "PropertyValue",
      "name": "Charges",
      "value": offenses.slice(0, 3).join(', ') // Limit to first 3 for brevity
    })
  }
  
  return structuredData
}

/**
 * Generate breadcrumb structured data for mugshot pages
 */
export function generateBreadcrumbStructuredData(mugshot: UIMugshot | DatabaseMugshot, baseUrl?: string) {
  const domain = baseUrl || process.env.NEXT_PUBLIC_SITE_URL || 'https://americastopmugs.com'
  const fullName = 'firstName' in mugshot ? `${mugshot.firstName} ${mugshot.lastName}` : mugshot.name
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": domain
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Mugshots",
        "item": `${domain}/mugshots`
      },
      {
        "@type": "ListItem", 
        "position": 3,
        "name": fullName,
        "item": generateCanonicalUrl(mugshot, baseUrl)
      }
    ]
  }
}

/**
 * Generate organization structured data for the site
 */
export function generateOrganizationStructuredData(baseUrl?: string) {
  const domain = baseUrl || process.env.NEXT_PUBLIC_SITE_URL || 'https://americastopmugs.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "America's Top Mugshots",
    "url": domain,
    "logo": {
      "@type": "ImageObject",
      "url": `${domain}/images/logo.png`,
      "width": "200",
      "height": "200"
    },
    "description": "Browse and rate mugshots from across America. Discover arrest records and booking photos from local law enforcement.",
    "sameAs": [
      "https://twitter.com/AmericasTopMugs",
      "https://facebook.com/AmericasTopMugshots"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    }
  }
}

// Example usage and test cases (for development reference)
export const offenseParserExamples = {
  single: {
    input: "DOMESTIC ASSAULT AND BATTERY",
    expected: ["Domestic Assault And Battery"]
  },
  semicolonSeparated: {
    input: "ENGAGING IN PROSTITUTION; LEWDNESS OR ASSIGNATION", 
    expected: ["Engaging In Prostitution", "Lewdness Or Assignation"]
  },
  semicolonWithHyphens: {
    input: "INTERFERING WITH OFFICIAL PROCESS - OBSTRUCTING OFFICER; destroying property - private or public; FAILURE TO APPEAR IN JD COURT; LARCENY OF MERCHANDISE",
    expected: [
      "Interfering With Official Process - Obstructing Officer",
      "Destroying Property - Private Or Public", 
      "Failure To Appear In Jd Court",
      "Larceny Of Merchandise"
    ]
  },
  withCodes: {
    input: "- 4700110201 FAILURE TO STOP AT STOP SIGN ( Bond: 0.00 ) - 63002402A1 POSSESSION OF CONTROLLED SUBSTANCE ( Bond: 0.00 ) - 630002405B POSSESSION OF PARAPHERNALIA ( Bond: 0.00 )",
    expected: [
      "Failure To Stop At Stop Sign",
      "Possession Of Controlled Substance", 
      "Possession Of Paraphernalia"
    ]
  },
  dashAndSlashSeparated: {
    input: "Public Disorderly Conduct - Trespassing / Entering Premises After Warning Or Refusing To Leave On Request - A / Open Container Of Beer Or Wine In Motor Vehicle",
    expected: [
      "Public Disorderly Conduct",
      "Trespassing",
      "Entering Premises After Warning Or Refusing To Leave On Request",
      "Open Container Of Beer Or Wine In Motor Vehicle"
    ]
  },
  complexMixedSeparators: {
    input: "DRIVING UNDER THE INFLUENCE - DUI / RECKLESS DRIVING - SPEEDING / FAILURE TO MAINTAIN LANE",
    expected: [
      "Driving Under The Influence",
      "Dui",
      "Reckless Driving",
      "Speeding",
      "Failure To Maintain Lane"
    ]
  },
  newlineWithBrokenMoney: {
    input: "Carry Or Possess Firearm By Convicted Felon\nMalicious Injury Or Destruction Of Property Less Than $1\n000\nDestroying Property - Private Or Public\nProtective Order Violation",
    expected: [
      "Carry Or Possess Firearm By Convicted Felon",
      "Malicious Injury Or Destruction Of Property Less Than $1000", 
      "Destroying Property - Private Or Public",
      "Protective Order Violation"
    ]
  },
  semicolonWithMonetaryValue: {
    input: "carry or possess firearm by convicted felon; malicious injury or destruction of property less than $1,000; destroying property - private or public; PROTECTIVE ORDER VIOLATION",
    expected: [
      "Carry Or Possess Firearm By Convicted Felon",
      "Malicious Injury Or Destruction Of Property Less Than $1,000",
      "Destroying Property - Private Or Public", 
      "Protective Order Violation"
    ]
  }
} 