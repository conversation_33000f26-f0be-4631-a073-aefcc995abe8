import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createClient } from '@/lib/supabase/server'
import { mugshotsServiceServer } from '@/lib/services/mugshots-service-server'

describe('Unified Mugshots Service', () => {
  let testMugshotIds: number[] = []
  let testUserIds: string[] = []

  beforeEach(async () => {
    // Clean up before each test
    await cleanup()
  })

  afterEach(async () => {
    // Clean up after each test
    await cleanup()
  })

  async function cleanup() {
    const supabase = await createClient()
    
    // Delete test ratings
    if (testMugshotIds.length > 0) {
      await supabase.from('ratings').delete().in('mugshot_id', testMugshotIds)
    }
    
    // Delete test mugshots
    if (testMugshotIds.length > 0) {
      await supabase.from('mugshots').delete().in('id', testMugshotIds)
    }
    
    testMugshotIds = []
    testUserIds = []
  }

  async function createTestMugshot(data: {
    firstName: string
    lastName: string
    stateOfBooking?: string
    countyOfBooking?: string
    dateOfBooking?: string
  }) {
    const supabase = await createClient()
    const { data: mugshot, error } = await supabase
      .from('mugshots')
      .insert({
        firstName: data.firstName,
        lastName: data.lastName,
        stateOfBooking: data.stateOfBooking || 'CA',
        countyOfBooking: data.countyOfBooking || 'Los Angeles',
        dateOfBooking: data.dateOfBooking || '2024-01-01',
        offenseDescription: 'Test offense',
        imagePath: '/test/image.jpg'
      })
      .select('id')
      .single()

    if (error) throw error
    testMugshotIds.push(mugshot.id)
    return mugshot.id
  }

  async function addRating(mugshotId: number, userId: string, rating: number) {
    const supabase = await createClient()
    const { error } = await supabase.from('ratings').insert({
      mugshot_id: mugshotId,
      user_id: userId,
      rating
    })
    if (error) throw error
  }

  function generateUserId(): string {
    const userId = crypto.randomUUID()
    testUserIds.push(userId)
    return userId
  }

  it('should return empty array when no rated mugshots exist', async () => {
    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    expect(results).toEqual([])
  })

  it('should return mugshots sorted by average rating descending', async () => {
    // Create test mugshots
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })
    const mugshot3 = await createTestMugshot({ firstName: 'Bob', lastName: 'Johnson' })

    // Add ratings - mugshot2 should have highest average
    await addRating(mugshot1, generateUserId(), 6) // avg: 6.0
    await addRating(mugshot1, generateUserId(), 4) // avg: 5.0

    await addRating(mugshot2, generateUserId(), 9) // avg: 9.0
    await addRating(mugshot2, generateUserId(), 8) // avg: 8.5
    await addRating(mugshot2, generateUserId(), 8) // avg: 8.33

    await addRating(mugshot3, generateUserId(), 7) // avg: 7.0

    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(3)
    expect(results[0].id).toBe(mugshot2) // Highest average rating
    expect(results[1].id).toBe(mugshot3) // Second highest
    expect(results[2].id).toBe(mugshot1) // Lowest average rating
    
    // Verify rating data is included
    expect(results[0].average_rating).toBeCloseTo(8.33, 1)
    expect(results[0].total_ratings).toBe(3)
    expect(results[1].average_rating).toBe(7)
    expect(results[1].total_ratings).toBe(1)
    expect(results[2].average_rating).toBe(5)
    expect(results[2].total_ratings).toBe(2)
  })

  it('should break ties by total ratings count', async () => {
    // Create mugshots with same average rating but different counts
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })

    // Both will have average rating of 8.0, but different counts
    await addRating(mugshot1, generateUserId(), 8) // 1 rating
    
    await addRating(mugshot2, generateUserId(), 8) // 3 ratings
    await addRating(mugshot2, generateUserId(), 8)
    await addRating(mugshot2, generateUserId(), 8)

    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(2)
    expect(results[0].id).toBe(mugshot2) // More ratings
    expect(results[1].id).toBe(mugshot1) // Fewer ratings
  })

  it('should apply search filters correctly', async () => {
    // Create mugshots with different names
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const mugshot2 = await createTestMugshot({ firstName: 'Jane', lastName: 'Smith' })

    // Add ratings to both
    await addRating(mugshot1, generateUserId(), 8)
    await addRating(mugshot2, generateUserId(), 9)

    // Search for "John" should only return mugshot1
    const results = await mugshotsServiceServer.getMugshots(
      { searchTerm: 'John' },
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(1)
    expect(results[0].id).toBe(mugshot1)
    expect(results[0].firstName).toBe('John')
  })

  it('should apply location filters correctly', async () => {
    // Create mugshots in different states
    const mugshot1 = await createTestMugshot({ 
      firstName: 'John', 
      lastName: 'Doe',
      stateOfBooking: 'CA'
    })
    const mugshot2 = await createTestMugshot({ 
      firstName: 'Jane', 
      lastName: 'Smith',
      stateOfBooking: 'NY'
    })

    // Add ratings to both
    await addRating(mugshot1, generateUserId(), 8)
    await addRating(mugshot2, generateUserId(), 9)

    // Filter by CA should only return mugshot1
    const results = await mugshotsServiceServer.getMugshots(
      { state: 'CA' },
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    expect(results).toHaveLength(1)
    expect(results[0].id).toBe(mugshot1)
    expect(results[0].stateOfBooking).toBe('CA')
  })

  it('should handle pagination correctly', async () => {
    // Create multiple rated mugshots
    const mugshots = []
    for (let i = 0; i < 5; i++) {
      const id = await createTestMugshot({ 
        firstName: `Person${i}`, 
        lastName: 'Test' 
      })
      mugshots.push(id)
      await addRating(id, generateUserId(), 10 - i) // Descending ratings
    }

    // Get first page (2 items)
    const page1 = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 2 }
    )

    expect(page1).toHaveLength(2)
    expect(page1[0].average_rating).toBe(10)
    expect(page1[1].average_rating).toBe(9)

    // Get second page (2 items)
    const page2 = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 2, perPage: 2 }
    )

    expect(page2).toHaveLength(2)
    expect(page2[0].average_rating).toBe(8)
    expect(page2[1].average_rating).toBe(7)
  })

  it('should return correct count for top-rated mugshots', async () => {
    // Create some mugshots, only some with ratings
    const ratedMugshot1 = await createTestMugshot({ firstName: 'Rated1', lastName: 'Test' })
    const ratedMugshot2 = await createTestMugshot({ firstName: 'Rated2', lastName: 'Test' })
    const unratedMugshot = await createTestMugshot({ firstName: 'Unrated', lastName: 'Test' })

    // Add ratings only to some
    await addRating(ratedMugshot1, generateUserId(), 8)
    await addRating(ratedMugshot2, generateUserId(), 9)
    // unratedMugshot gets no ratings

    const count = await mugshotsServiceServer.getMugshotCount({}, 'top-rated')

    // Should only count mugshots that have ratings
    expect(count).toBe(2)
  })

  it('should include user-specific rating data when userId provided', async () => {
    const mugshot1 = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    const userId1 = generateUserId()
    const userId2 = generateUserId()

    // Add ratings from different users
    await addRating(mugshot1, userId1, 8)
    await addRating(mugshot1, userId2, 6)

    // Get results for userId1
    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 },
      userId1
    )

    expect(results).toHaveLength(1)
    expect(results[0].user_rating).toBe(8) // Should show userId1's rating
    expect(results[0].average_rating).toBe(7) // Average of both ratings
    expect(results[0].total_ratings).toBe(2)
  })

  it('should exclude mugshots with no ratings', async () => {
    // Create mugshots - some with ratings, some without
    const ratedMugshot = await createTestMugshot({ firstName: 'Rated', lastName: 'Test' })
    const unratedMugshot1 = await createTestMugshot({ firstName: 'Unrated1', lastName: 'Test' })
    const unratedMugshot2 = await createTestMugshot({ firstName: 'Unrated2', lastName: 'Test' })

    // Only rate one mugshot
    await addRating(ratedMugshot, generateUserId(), 8)

    const results = await mugshotsServiceServer.getMugshots(
      {},
      { sortBy: 'top-rated' },
      { page: 1, perPage: 12 }
    )

    // Should only return the rated mugshot
    expect(results).toHaveLength(1)
    expect(results[0].id).toBe(ratedMugshot)
  })

  it('should fallback gracefully when database function fails', async () => {
    // Create a rated mugshot
    const mugshot = await createTestMugshot({ firstName: 'John', lastName: 'Doe' })
    await addRating(mugshot, generateUserId(), 8)

    // Mock the database function to fail
    const originalConsoleWarn = console.warn
    console.warn = () => {} // Suppress warning logs

    try {
      // This should trigger fallback
      const results = await mugshotsServiceServer.getMugshots(
        {},
        { sortBy: 'top-rated' },
        { page: 1, perPage: 12 }
      )

      // Should still get results via fallback
      expect(results).toHaveLength(1)
      expect(results[0].id).toBe(mugshot)
    } finally {
      console.warn = originalConsoleWarn
    }
  })
}) 