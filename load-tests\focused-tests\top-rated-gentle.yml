# Gentle Top-Rated Test - Conservative Load for Complex Queries
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 30
      arrivalRate: 1
      name: "Warm-up: Very gentle"
    - duration: 120
      arrivalRate: 3
      name: "Steady: Conservative top-rated"
    - duration: 30
      arrivalRate: 1
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 2000   # Allow higher for complex queries
    - http.response_time.median: 800 # Based on optimized query
    - http.codes.200: 90             # High success rate expected
    - http.codes.5xx: 5              # Allow some errors

  http:
    timeout: 20
    pool: 8  # Smaller pool for complex queries
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Top-Rated - Gentle Load"
    weight: 100
    flow:
      - function: "generateTopRatedFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "fast"
            sortBy: "top-rated"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
          name: "GET /api/mugshots-fast - Top-Rated (Gentle)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 3  # Longer think time for complex queries

processor: "./load-tests/focused-tests/data-generators-focused.js"
