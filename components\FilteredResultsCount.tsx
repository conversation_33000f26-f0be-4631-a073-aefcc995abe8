"use client"

import { useAuthStore } from "@/lib/stores/auth-store"
import { MapPin, Search, Calendar, Tag } from "lucide-react"

interface FilteredResultsCountProps {
  currentCount: number
  totalCount: number
  searchTerm?: string
  selectedState?: string
  selectedCounty?: string
  dateFrom?: string
  dateTo?: string
  categories?: string[]
}

export default function FilteredResultsCount({
  currentCount,
  totalCount,
  searchTerm,
  selectedState,
  selectedCounty,
  dateFrom,
  dateTo,
  categories = []
}: FilteredResultsCountProps) {
  const { hasHomeLocation, getHomeLocation } = useAuthStore()
  const { state: homeState, county: homeCounty } = getHomeLocation()
  
  // Build description based on filters
  const buildDescription = () => {
    const parts = []
    
    // Location information
    if (selectedState && selectedState !== 'all-states') {
      if (selectedCounty && selectedCounty !== 'all-counties') {
        const isHome = hasHomeLocation() && selectedState === homeState && selectedCounty === homeCounty
        parts.push(
          <span key="location" className="flex items-center gap-1">
            <MapPin className="w-4 h-4 text-cyan-400" />
            <span className={isHome ? "text-pink-400 font-medium" : "text-cyan-400"}>
              {selectedCounty}, {selectedState}
              {isHome && <span className="text-pink-300 text-xs ml-1">(Home)</span>}
            </span>
          </span>
        )
      } else {
        parts.push(
          <span key="location" className="flex items-center gap-1">
            <MapPin className="w-4 h-4 text-cyan-400" />
            <span className="text-cyan-400">{selectedState}</span>
          </span>
        )
      }
    } else if (selectedState === 'all-states') {
      // User explicitly selected "All States" - show this choice
      parts.push(
        <span key="location" className="flex items-center gap-1">
          <MapPin className="w-4 h-4 text-cyan-400" />
          <span className="text-cyan-400">All States</span>
        </span>
      )
    } else if (hasHomeLocation() && !selectedState) {
      // No location filters applied at all but user is authenticated - show default home location
      parts.push(
        <span key="location" className="flex items-center gap-1">
          <MapPin className="w-4 h-4 text-pink-400" />
          <span className="text-pink-400 font-medium">
            {homeCounty}, {homeState}
            <span className="text-pink-300 text-xs ml-1">(Home)</span>
          </span>
        </span>
      )
    }
    
    // Search term
    if (searchTerm) {
      parts.push(
        <span key="search" className="flex items-center gap-1">
          <Search className="w-4 h-4 text-green-400" />
          <span className="text-green-400">&quot;{searchTerm}&quot;</span>
        </span>
      )
    }
    
    // Date range
    if (dateFrom || dateTo) {
      let dateText = ""
      if (dateFrom && dateTo) {
        dateText = `${dateFrom} to ${dateTo}`
      } else if (dateFrom) {
        dateText = `from ${dateFrom}`
      } else if (dateTo) {
        dateText = `until ${dateTo}`
      }
      
      parts.push(
        <span key="date" className="flex items-center gap-1">
          <Calendar className="w-4 h-4 text-orange-400" />
          <span className="text-orange-400">{dateText}</span>
        </span>
      )
    }
    
    // Categories
    if (categories.length > 0) {
      parts.push(
        <span key="categories" className="flex items-center gap-1">
          <Tag className="w-4 h-4 text-purple-400" />
          <span className="text-purple-400">
            {categories.length === 1 ? categories[0] : `${categories.length} categories`}
          </span>
        </span>
      )
    }
    
    return parts
  }
  
  const filterParts = buildDescription()
  const hasFilters = filterParts.length > 0
  
  return (
    <div className="mb-6 text-center px-4">
      <div className="space-y-2">
        {/* Main count */}
        <p className="text-gray-400 text-base sm:text-lg">
          Showing <span className="font-bold text-white">{currentCount.toLocaleString()}</span> of{" "}
          <span className="font-bold text-white">{totalCount.toLocaleString()}</span> mugshots
        </p>
        
        {/* Filter details */}
        {hasFilters && (
          <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-3 text-xs sm:text-sm">
            <span className="text-gray-500 font-medium">Filtered by:</span>
            {filterParts.map((part, index) => (
              <span key={index} className="flex items-center">
                {part}
              </span>
            ))}
          </div>
        )}
        
        {/* Default location message for authenticated users with no filters */}
        {!hasFilters && hasHomeLocation() && (
          <p className="text-gray-500 text-xs sm:text-sm">
            Showing mugshots from your area • <span className="text-pink-400">{homeCounty}, {homeState}</span>
          </p>
        )}
        
        {/* Unfiltered message for unauthenticated users */}
        {!hasFilters && !hasHomeLocation() && (
          <p className="text-gray-500 text-xs sm:text-sm">
            Showing all mugshots nationwide
          </p>
        )}
      </div>
    </div>
  )
} 