import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { mugshotsOptimizedService } from '@/lib/services/mugshots-optimized-service'
import { transformDBMugshotsToUI } from '@/lib/utils/mugshot-transforms'
import { convertUITagArrayToDB, type TagType } from '@/lib/utils/tag-utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract pagination parameters
    const page = parseInt(searchParams.get('page') || '1', 10)
    const perPage = parseInt(searchParams.get('perPage') || '12', 10)
    
    // Validate pagination
    if (page < 1 || perPage < 1 || perPage > 100) {
      return NextResponse.json(
        { success: false, message: 'Invalid pagination parameters', error: 'INVALID_PAGINATION' },
        { status: 400 }
      )
    }
    
    // Extract and convert tag parameters from UI format to database format
    const rawTags = searchParams.get('tags')?.split(',').filter(Boolean) || []
    const validTags: TagType[] = convertUITagArrayToDB(rawTags)
    
    // Debug logging
    console.log('API Route - Raw tags from UI:', rawTags)
    console.log('API Route - Converted tags for DB:', validTags)
    
    // Extract filter parameters
    const filters = {
      searchTerm: searchParams.get('search') || undefined,
      state: searchParams.get('state') || undefined,
      county: searchParams.get('county') || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      category: searchParams.get('categories')?.split(',').filter(Boolean) || undefined,
      tags: validTags.length > 0 ? validTags : undefined
    }
    
    // Extract sort parameters
    const sortBy = searchParams.get('sortBy') || 'newest'
    if (!['newest', 'top-rated', 'most-viewed'].includes(sortBy)) {
      return NextResponse.json(
        { success: false, message: 'Invalid sort parameter', error: 'INVALID_SORT' },
        { status: 400 }
      )
    }
    
    // Get current user for personalized data (optional)
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    
    // Fetch mugshots with optimized service (fast path for simple queries)
    const dbMugshots = await mugshotsOptimizedService.getMugshots(
      filters,
      { sortBy: sortBy as 'newest' | 'top-rated' | 'most-viewed' },
      { page, perPage },
      userId // Include user ID for personalized rating/tag data
    )
    
    // Transform database mugshots to UI format on server side
    const mugshots = transformDBMugshotsToUI(dbMugshots)
    
    // Calculate total count for pagination (if needed for frontend)
    // Note: This is optional and can be expensive for large datasets
    const includeTotal = searchParams.get('includeTotal') === 'true'
    let totalCount: number | undefined
    
    if (includeTotal) {
      try {
        totalCount = await mugshotsServiceServer.getMugshotCount(filters)
      } catch (error) {
        console.warn('Failed to get total count:', error)
        // Don't fail the request if count fails
      }
    }
    
    // Transform response
    const response = {
      success: true,
      data: {
        mugshots,
        pagination: {
          page,
          perPage,
          totalCount,
          hasNextPage: mugshots.length === perPage,
          hasPreviousPage: page > 1
        },
        filters: {
          applied: Object.fromEntries(
            Object.entries(filters).filter(([, value]) => value !== undefined)
          ),
          sortBy
        },
        meta: {
          totalResults: mugshots.length,
          includedUserData: !!userId,
          timestamp: new Date().toISOString()
        }
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('Mugshots API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch mugshots',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
} 