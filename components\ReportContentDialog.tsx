'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alog<PERSON><PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { Flag, AlertTriangle, Shield } from 'lucide-react'

interface ReportContentDialogProps {
  contentType: 'mugshot' | 'user_tag' | 'user_comment' | 'user_profile'
  contentId: string
  trigger?: React.ReactNode
  onReportSubmitted?: () => void
}

const reportReasons = [
  { value: 'inappropriate_content', label: 'Inappropriate Content', description: 'Content that violates community guidelines' },
  { value: 'spam', label: 'Spam', description: 'Unwanted or repetitive content' },
  { value: 'harassment', label: 'Harassment', description: 'Bullying, threats, or targeting individuals' },
  { value: 'copyright', label: 'Copyright Violation', description: 'Unauthorized use of copyrighted material' },
  { value: 'violence', label: 'Violence', description: 'Graphic or threatening violent content' },
  { value: 'hate_speech', label: 'Hate Speech', description: 'Content promoting hatred based on identity' },
  { value: 'fake_content', label: 'False Information', description: 'Deliberately misleading or false content' },
  { value: 'privacy_violation', label: 'Privacy Violation', description: 'Sharing private information without consent' },
  { value: 'other', label: 'Other', description: 'Other community guideline violations' }
]

const contentTypeLabels = {
  mugshot: 'Mugshot',
  user_tag: 'Tag',
  user_comment: 'Comment',
  user_profile: 'Profile'
}

export default function ReportContentDialog({ 
  contentType, 
  contentId, 
  trigger,
  onReportSubmitted 
}: ReportContentDialogProps) {
  const [open, setOpen] = useState(false)
  const [selectedReason, setSelectedReason] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = async () => {
    if (!selectedReason) return

    try {
      setIsSubmitting(true)
      
      // In a real implementation, this would call your API
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content_type: contentType,
          content_id: contentId,
          reason: selectedReason,
          description: description.trim() || undefined
        })
      })

      if (response.ok) {
        setSubmitted(true)
        onReportSubmitted?.()
        toast.success('Report submitted successfully')
        // Auto-close after 2 seconds
        setTimeout(() => {
          setOpen(false)
          setSubmitted(false)
          setSelectedReason('')
          setDescription('')
        }, 2000)
      } else {
        throw new Error('Failed to submit report')
      }
    } catch (error) {
      console.error('Error submitting report:', error)
      toast.error('Failed to submit report')
    } finally {
      setIsSubmitting(false)
    }
  }

  const DefaultTrigger = () => (
    <Button variant="outline" size="sm" className="text-red-400 hover:text-red-300">
      <Flag className="h-4 w-4 mr-1" />
      Report
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || <DefaultTrigger />}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white">
            <Flag className="h-5 w-5 text-red-400" />
            Report {contentTypeLabels[contentType]}
          </DialogTitle>
        </DialogHeader>

        {submitted ? (
          <Card className="bg-green-900/20 border-green-500/30">
            <CardContent className="pt-6 text-center">
              <Shield className="h-12 w-12 text-green-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-green-400 mb-2">Report Submitted</h3>
              <p className="text-green-300 text-sm">
                Thank you for helping keep our community safe. We&apos;ll review your report and take appropriate action.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-300">
                  <p className="font-medium mb-1">Report Responsibly</p>
                  <p>Only report content that genuinely violates our community guidelines. False reports may result in restrictions on your account.</p>
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">
                What&apos;s the issue with this {contentTypeLabels[contentType].toLowerCase()}?
              </label>
              <Select value={selectedReason} onValueChange={setSelectedReason}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a reason for reporting" />
                </SelectTrigger>
                <SelectContent>
                  {reportReasons.map((reason) => (
                    <SelectItem key={reason.value} value={reason.value}>
                      <div>
                        <div className="font-medium">{reason.label}</div>
                        <div className="text-xs text-gray-400">{reason.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">
                Additional Details (Optional)
              </label>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Provide any additional context that might help our moderation team..."
                className="min-h-[80px]"
                maxLength={500}
              />
              <div className="text-xs text-gray-400 mt-1 text-right">
                {description.length}/500 characters
              </div>
            </div>

            <div className="text-xs text-gray-400">
              <p>By submitting this report, you agree that:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>You believe this content violates our community guidelines</li>
                <li>You&apos;re providing accurate information to the best of your knowledge</li>
                <li>False reports may result in account restrictions</li>
              </ul>
            </div>

            <div className="flex justify-end gap-3">
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={!selectedReason || isSubmitting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Report'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
} 