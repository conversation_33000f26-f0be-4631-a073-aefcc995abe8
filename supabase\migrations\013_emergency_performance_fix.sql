-- =====================================================
-- MIGRATION 013: Emergency Performance Fix for Tag Filtering
-- =====================================================
-- Purpose: Fix the statement timeout issues with tag filtering
-- This migration addresses the N+1 query problem in the search function

-- =====================================================
-- EMERGENCY: Add the most critical index for tag filtering
-- =====================================================

-- This index is absolutely critical for the EXISTS clause in tag filtering
-- Without it, the query scans the entire tags table for every mugshot
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emergency_tags_lookup 
ON public.tags (mugshot_id, tag_type);

-- Emergency index for ratings aggregation (prevents full table scan)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emergency_ratings_agg 
ON public.ratings (mugshot_id, rating);

-- =====================================================
-- ANALYZE TABLES TO UPDATE STATISTICS
-- =====================================================

-- Update table statistics so the query planner can make better decisions
ANALYZE public.tags;
ANALYZE public.ratings;
ANALYZE public.mugshots;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON INDEX idx_emergency_tags_lookup IS 
'Emergency index to fix tag filtering timeout - prevents full table scan in EXISTS clause';

COMMENT ON INDEX idx_emergency_ratings_agg IS 
'Emergency index for ratings aggregation - prevents N+1 queries for rating statistics'; 