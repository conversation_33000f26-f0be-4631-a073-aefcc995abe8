/**
 * Centralized query key factory for TanStack Query
 * Provides consistent query key patterns across the application
 */

import type { QueryClient } from '@tanstack/react-query'
import type { 
  MugshotFilters, 
  MugshotsSortOptions, 
  MugshotsPaginationOptions 
} from '@/lib/services/api-client'

export const queryKeys = {
  // Mugshots queries
  mugshots: (
    filters: MugshotFilters = {}, 
    sortOptions: MugshotsSortOptions = {}, 
    pagination: MugshotsPaginationOptions = {}
  ) => ['mugshots', filters, sortOptions, pagination] as const,
  
  // Individual mugshot detail
  mugshot: (mugshotId: string) => ['mugshot', mugshotId] as const,
  mugshotDetail: (mugshotId: string) => ['mugshot', mugshotId, 'detail'] as const,
  
  // User-specific data
  user: (userId: string) => ['user', userId] as const,
  userMugshotData: (mugshotId: string) => ['user', 'mugshot', mugshotId, 'data'] as const,
  userProfile: (userId: string) => ['user', userId, 'profile'] as const,
  
  // Ratings
  ratings: (mugshotId: string) => ['mugshot', mugshotId, 'ratings'] as const,
  ratingStatistics: (mugshotId: string) => ['ratings', mugshotId, 'statistics'] as const,
  
  // Tags
  tags: (mugshotId: string) => ['mugshot', mugshotId, 'tags'] as const,
  tagStatistics: (mugshotId?: string) => mugshotId 
    ? ['tags', mugshotId, 'statistics'] as const
    : ['tags', 'statistics'] as const,
  
  // Competition data (for future use)
  competitions: {
    weekly: () => ['competitions', 'weekly'] as const,
    monthly: () => ['competitions', 'monthly'] as const,
    quarterly: () => ['competitions', 'quarterly'] as const,
  },
} as const

/**
 * Helper functions for query invalidation patterns
 */
export const invalidationHelpers = {
  // Invalidate all mugshot-related data for a specific mugshot
  invalidateMugshotData: (queryClient: QueryClient, mugshotId: string) => {
    queryClient.invalidateQueries({ queryKey: ['mugshot', mugshotId] })
    queryClient.invalidateQueries({ queryKey: ['user', 'mugshot', mugshotId] })
  },
  
  // Invalidate all user-specific data
  invalidateUserData: (queryClient: QueryClient, userId?: string) => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: ['user', userId] })
    } else {
      queryClient.invalidateQueries({ queryKey: ['user'] })
    }
  },
  
  // Invalidate mugshots listing (when data affects listings)
  invalidateMugshotsListing: (queryClient: QueryClient) => {
    queryClient.invalidateQueries({ queryKey: ['mugshots'] })
  },
  
  // Invalidate all rating-related data
  invalidateRatings: (queryClient: QueryClient, mugshotId?: string) => {
    if (mugshotId) {
      queryClient.invalidateQueries({ queryKey: ['ratings', mugshotId] })
      queryClient.invalidateQueries({ queryKey: ['mugshot', mugshotId, 'ratings'] })
    } else {
      queryClient.invalidateQueries({ queryKey: ['ratings'] })
    }
  },
  
  // Invalidate all tag-related data
  invalidateTags: (queryClient: QueryClient, mugshotId?: string) => {
    if (mugshotId) {
      queryClient.invalidateQueries({ queryKey: ['tags', mugshotId] })
      queryClient.invalidateQueries({ queryKey: ['mugshot', mugshotId, 'tags'] })
    } else {
      queryClient.invalidateQueries({ queryKey: ['tags'] })
    }
  },
  
  // Bulk invalidation for complex operations
  invalidateAllMugshotRelated: (queryClient: QueryClient, mugshotId: string) => {
    invalidationHelpers.invalidateMugshotData(queryClient, mugshotId)
    invalidationHelpers.invalidateRatings(queryClient, mugshotId)
    invalidationHelpers.invalidateTags(queryClient, mugshotId)
  }
}

/**
 * Type-safe query key selectors
 * Useful for type inference and autocompletion
 */
export type QueryKey = 
  | ReturnType<typeof queryKeys.mugshots>
  | ReturnType<typeof queryKeys.mugshot>
  | ReturnType<typeof queryKeys.mugshotDetail>
  | ReturnType<typeof queryKeys.userMugshotData>
  | ReturnType<typeof queryKeys.userProfile>
  | ReturnType<typeof queryKeys.ratings>
  | ReturnType<typeof queryKeys.ratingStatistics>
  | ReturnType<typeof queryKeys.tags>
  | ReturnType<typeof queryKeys.tagStatistics>
  | ReturnType<typeof queryKeys.competitions.weekly>
  | ReturnType<typeof queryKeys.competitions.monthly>
  | ReturnType<typeof queryKeys.competitions.quarterly>

/**
 * Default cache configurations for different data types
 */
export const defaultCacheConfig = {
  mugshots: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  },
  mugshotDetail: {
    staleTime: 1 * 60 * 1000, // 1 minute (fresher for popups)
    gcTime: 10 * 60 * 1000, // 10 minutes
  },
  userData: {
    staleTime: 30 * 1000, // 30 seconds (frequently changing)
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
  statistics: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  },
} as const 