"use client"

import { useState, useEffect, useCallback } from "react"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Bell, Mail, MapPin, Clock, CheckCircle, AlertCircle } from "lucide-react"
import { toast } from "sonner"

// Mock types and functions for notification preferences
type NotificationPreferencesType = {
  email_enabled: boolean
  weekly_voting_alerts: boolean
  daily_winners: boolean
  monthly_championships: boolean
  quarterly_legends: boolean
  local_state_updates: boolean
  county_specific_alerts: boolean
  email_frequency: 'immediate' | 'daily' | 'weekly'
}

// Mock functions for display purposes
const mockGetNotificationPreferences = async (_userId: string): Promise<NotificationPreferencesType> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  return {
    email_enabled: true,
    weekly_voting_alerts: true,
    daily_winners: true,
    monthly_championships: true,
    quarterly_legends: true,
    local_state_updates: true,
    county_specific_alerts: true,
    email_frequency: 'immediate'
  }
}

const mockUpdateNotificationPreferences = async (
  _userId: string, 
  _preferences: Partial<NotificationPreferencesType>
): Promise<{ success: boolean }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return { success: true }
}

interface NotificationPreferencesProps {
  userId: string
}

export default function NotificationPreferences({ userId }: NotificationPreferencesProps) {
  const [preferences, setPreferences] = useState<Partial<NotificationPreferencesType>>({
    email_enabled: true,
    weekly_voting_alerts: true,
    daily_winners: true,
    monthly_championships: true,
    quarterly_legends: true,
    local_state_updates: true,
    county_specific_alerts: true,
    email_frequency: 'immediate'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const loadPreferences = useCallback(async () => {
    setIsLoading(true)
    try {
      const data = await mockGetNotificationPreferences(userId)
      
      if (data) {
        setPreferences(data)
      }
    } catch (error) {
      console.error('Error loading preferences:', error)
      toast.error('Failed to load notification preferences')
    } finally {
      setIsLoading(false)
    }
  }, [userId])

  useEffect(() => {
    loadPreferences()
  }, [loadPreferences])

  const savePreferences = async (updatedPreferences: Partial<NotificationPreferencesType>) => {
    setIsSaving(true)
    try {
      const result = await mockUpdateNotificationPreferences(userId, updatedPreferences)
      
      if (result.success) {
        setPreferences(updatedPreferences)
        setHasChanges(false)
        toast.success('Notification preferences saved!')
      } else {
        throw new Error('Failed to save preferences')
      }
    } catch (error) {
      console.error('Error saving preferences:', error)
      toast.error('Failed to save preferences')
    } finally {
      setIsSaving(false)
    }
  }

  const handleToggle = (key: keyof NotificationPreferencesType, value: boolean) => {
    const updatedPreferences = { ...preferences, [key]: value }
    setPreferences(updatedPreferences)
    setHasChanges(true)
    
    // Auto-save after a short delay
    setTimeout(() => {
      savePreferences(updatedPreferences)
    }, 500)
  }

  const handleFrequencyChange = (frequency: string) => {
    const updatedPreferences = { 
      ...preferences, 
      email_frequency: frequency as 'immediate' | 'daily' | 'weekly'
    }
    setPreferences(updatedPreferences)
    setHasChanges(true)
    
    // Auto-save after a short delay
    setTimeout(() => {
      savePreferences(updatedPreferences)
    }, 500)
  }

  if (isLoading) {
    return (
      <Card className="card-neon">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
            <span className="ml-3 text-white">Loading preferences...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="card-neon">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Bell className="h-5 w-5 text-cyan-400" />
            Email Notification Preferences
            {isSaving && (
              <div className="flex items-center gap-2 text-sm text-cyan-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                Saving...
              </div>
            )}
            {!isSaving && !hasChanges && (
              <CheckCircle className="h-4 w-4 text-green-400" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Master email toggle */}
          <div className="flex items-center justify-between p-4 rounded-lg border border-cyan-500/30 bg-gray-800/50">
            <div>
              <Label className="text-white font-medium text-base">Email Notifications</Label>
              <p className="text-sm text-gray-400 mt-1">Enable or disable all email notifications</p>
            </div>
            <Switch
              checked={preferences.email_enabled}
              onCheckedChange={(checked) => handleToggle('email_enabled', checked)}
              disabled={isSaving}
            />
          </div>

          {preferences.email_enabled && (
            <>
              {/* Email frequency */}
              <div className="space-y-3">
                <Label className="text-white font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4 text-cyan-400" />
                  Email Frequency
                </Label>
                <Select 
                  value={preferences.email_frequency} 
                  onValueChange={handleFrequencyChange}
                  disabled={isSaving}
                >
                  <SelectTrigger className="input-neon">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800/90 border-cyan-500/30">
                    <SelectItem value="immediate">Immediate</SelectItem>
                    <SelectItem value="daily">Daily Digest</SelectItem>
                    <SelectItem value="weekly">Weekly Digest</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-400">
                  {preferences.email_frequency === 'immediate' && 'Receive emails as events happen'}
                  {preferences.email_frequency === 'daily' && 'Receive a daily summary email'}
                  {preferences.email_frequency === 'weekly' && 'Receive a weekly summary email'}
                </p>
              </div>

              {/* Event notifications */}
              <div className="space-y-4">
                <Label className="text-white font-medium flex items-center gap-2">
                  <Mail className="h-4 w-4 text-pink-400" />
                  Event Notifications
                </Label>
                
                <div className="space-y-3 pl-6">
                  {[
                    { 
                      key: 'weekly_voting_alerts', 
                      label: 'Weekly Voting Alerts', 
                      desc: 'When weekly voting opens and ends',
                      icon: '🗳️'
                    },
                    { 
                      key: 'daily_winners', 
                      label: 'Daily Winners', 
                      desc: 'Daily winner announcements',
                      icon: '🏆'
                    },
                    { 
                      key: 'monthly_championships', 
                      label: 'Monthly Championships', 
                      desc: 'Monthly competition results and announcements',
                      icon: '🏅'
                    },
                    { 
                      key: 'quarterly_legends', 
                      label: 'Quarterly Legends', 
                      desc: 'Quarterly legend announcements and ceremonies',
                      icon: '👑'
                    }
                  ].map(({ key, label, desc, icon }) => (
                    <div key={key} className="flex items-center justify-between p-3 rounded-lg border border-gray-600/30 bg-gray-800/30">
                      <div className="flex items-start gap-3">
                        <span className="text-lg">{icon}</span>
                        <div>
                          <Label className="text-white text-sm font-medium">{label}</Label>
                          <p className="text-xs text-gray-400 mt-1">{desc}</p>
                        </div>
                      </div>
                      <Switch
                        checked={preferences[key as keyof NotificationPreferencesType] as boolean}
                        onCheckedChange={(checked) => handleToggle(key as keyof NotificationPreferencesType, checked)}
                        disabled={isSaving}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Location notifications */}
              <div className="space-y-4">
                <Label className="text-white font-medium flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-purple-400" />
                  Location-Based Notifications
                </Label>
                
                <div className="space-y-3 pl-6">
                  {[
                    { 
                      key: 'local_state_updates', 
                      label: 'State Updates', 
                      desc: 'Notifications about winners from your state',
                      icon: '🗺️'
                    },
                    { 
                      key: 'county_specific_alerts', 
                      label: 'County Alerts', 
                      desc: 'Notifications about winners from your county',
                      icon: '📍'
                    }
                  ].map(({ key, label, desc, icon }) => (
                    <div key={key} className="flex items-center justify-between p-3 rounded-lg border border-gray-600/30 bg-gray-800/30">
                      <div className="flex items-start gap-3">
                        <span className="text-lg">{icon}</span>
                        <div>
                          <Label className="text-white text-sm font-medium">{label}</Label>
                          <p className="text-xs text-gray-400 mt-1">{desc}</p>
                        </div>
                      </div>
                      <Switch
                        checked={preferences[key as keyof NotificationPreferencesType] as boolean}
                        onCheckedChange={(checked) => handleToggle(key as keyof NotificationPreferencesType, checked)}
                        disabled={isSaving}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Status info */}
              <div className="p-4 rounded-lg border border-blue-500/30 bg-blue-900/20">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div>
                    <p className="text-sm text-blue-300 font-medium">
                      Notification Status
                    </p>
                    <p className="text-xs text-blue-200 mt-1">
                      Changes are saved automatically. You can update these preferences at any time.
                      {preferences.email_frequency === 'daily' && ' Daily digest emails are sent at 6 AM.'}
                      {preferences.email_frequency === 'weekly' && ' Weekly digest emails are sent on Monday mornings.'}
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 