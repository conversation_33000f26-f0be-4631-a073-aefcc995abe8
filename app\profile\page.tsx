"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { useAuthStore, type AuthUser } from "@/lib/stores/auth-store"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import LocationDropdown from "@/components/LocationDropdown"
import PasswordChangeForm from "@/components/PasswordChangeForm"
import NotificationPreferences from "@/components/NotificationPreferences"
import { User, MapPin, Lock, AlertCircle, CheckCircle, Save, Bell } from "lucide-react"

interface Profile {
  id: string
  user_id: string
  email: string | null
  full_name: string
  state: string | null
  county: string | null
  role: string
  avatar_url: string | null
  created_at: string
  updated_at: string
}

// Type guard to check if user is properly authenticated
function isAuthenticatedUser(user: AuthUser | null): user is AuthUser {
  return user !== null && typeof user.id === 'string' && user.id.length > 0
}

export default function ProfilePage() {
  const router = useRouter()
  const { user, profile: storeProfile, isAuthenticated, isLoading: authLoading, loadUserProfile } = useAuthStore()
  
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [hasInitialized, setHasInitialized] = useState(false)
  
  // Form states
  const [fullName, setFullName] = useState("")
  const [selectedState, setSelectedState] = useState("")
  const [selectedCounty, setSelectedCounty] = useState("")

  // Memoized auth check function to prevent infinite loops
  const checkAuthAndLoadProfile = useCallback(async () => {
    if (hasInitialized) return
    
    try {
      // Early return if still loading auth
      if (authLoading) {
        console.log('Auth still loading, waiting...')
        return
      }

      // If we have a store profile, use it
      if (storeProfile) {
        console.log('Using store profile')
        setProfile(storeProfile as Profile)
        setFullName(storeProfile.full_name || "")
        setSelectedState(storeProfile.state || "")
        setSelectedCounty(storeProfile.county || "")
        setHasInitialized(true)
        setIsLoading(false)
        return
      }

      // If not authenticated, redirect to login
      if (!authLoading && !isAuthenticated) {
        console.log('Not authenticated, redirecting to login')
        router.push('/login')
        return
      }

      // If we have user but no profile, try to load from auth store
      if (isAuthenticatedUser(user) && !storeProfile) {
        console.log('Loading profile from auth store')
        await loadUserProfile()
        return // This will trigger a re-render with updated store data
      }

      // If auth store doesn't have profile, fetch directly from database
      if (isAuthenticatedUser(user) && !storeProfile) {
        console.log('Fetching profile directly from database')
        const supabase = createClient()
        
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (profileError) {
          if (profileError.code === 'PGRST116') {
            // Profile doesn't exist yet
            setError("Profile not found. Please complete your registration.")
            router.push('/auth/location-setup')
            return
          }
          throw profileError
        }

        if (profileData) {
          setProfile(profileData as Profile)
          setFullName(profileData.full_name || "")
          setSelectedState(profileData.state || "")
          setSelectedCounty(profileData.county || "")
        }
      }

      setHasInitialized(true)
    } catch (err) {
      console.error('Error in checkAuthAndLoadProfile:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load profile'
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [authLoading, isAuthenticated, user, storeProfile, loadUserProfile, router, hasInitialized])

  useEffect(() => {
    checkAuthAndLoadProfile()
  }, [checkAuthAndLoadProfile])

  const handleSaveProfile = async () => {
    // Validate user authentication with type guard
    if (!isAuthenticatedUser(user)) {
      setError("You must be logged in to update your profile")
      return
    }

    if (!fullName.trim()) {
      setError("Full name is required")
      return
    }

    if (!selectedState || !selectedCounty) {
      setError("Please select both state and county")
      return
    }

    setIsUpdating(true)
    setError("")
    setSuccess("")

    try {
      const supabase = createClient()
      
      const { data: updatedProfile, error } = await supabase
        .from('profiles')
        .update({
          full_name: fullName,
          state: selectedState,
          county: selectedCounty,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id) // Safe to use user.id now due to type guard
        .select()
        .single()

      if (error) throw error

      // Update local state
      setProfile(updatedProfile as Profile)
      
      // Reload auth store to update global state
      await loadUserProfile()
      
      setSuccess("Profile updated successfully!")
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000)

    } catch (err) {
      console.error('Error updating profile:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile'
      setError(errorMessage)
    } finally {
      setIsUpdating(false)
    }
  }

  // Loading state
  if (isLoading || authLoading) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  // Not authenticated state
  if (!isAuthenticated || !isAuthenticatedUser(user)) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
              <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
              <p className="text-muted-foreground mb-4">Please log in to access your profile.</p>
              <Button onClick={() => router.push('/login')}>
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Profile not found state
  if (!profile) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h2 className="text-xl font-semibold mb-2">Profile Not Found</h2>
              <p className="text-muted-foreground mb-4">
                {error || "We couldn't find your profile. Please complete your registration."}
              </p>
              <Button onClick={() => router.push('/auth/location-setup')}>
                Complete Registration
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Profile Settings</h1>
        <p className="text-muted-foreground mt-2">Manage your account information and preferences</p>
      </div>

      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="mb-6 border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-700">{success}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="location" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Location
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={user.email || profile.email || ""}
                    disabled
                    className="bg-muted text-muted-foreground cursor-not-allowed"
                  />
                  <p className="text-sm text-muted-foreground">Email cannot be changed here</p>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Account Role</Label>
                <div>
                  <Badge variant={profile.role === 'admin' ? 'destructive' : 'secondary'}>
                    {profile.role === 'admin' ? 'Administrator' : 'User'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="location" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Home Location</CardTitle>
              <p className="text-sm text-muted-foreground">
                Set your primary location to see local mugshots and get personalized content
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
                             <LocationDropdown
                 selectedState={selectedState}
                 selectedCounty={selectedCounty}
                 setSelectedState={(state: string) => setSelectedState(state)}
                 setSelectedCounty={(county: string) => setSelectedCounty(county)}
               />
              <Button 
                onClick={handleSaveProfile} 
                disabled={isUpdating}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isUpdating ? "Saving..." : "Save Location"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <PasswordChangeForm />
        </TabsContent>

                 <TabsContent value="notifications" className="space-y-6">
           <NotificationPreferences userId={user.id} />
         </TabsContent>
      </Tabs>
    </div>
  )
}
