{"aggregate": {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 2760, "vusers.created": 2760, "errors.Undefined function \"generateSlowQueryFilters\"": 2760, "http.requests": 2760, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 2686, "errors.ETIMEDOUT": 2686, "vusers.failed": 2686, "vusers.completed": 74, "http.codes.200": 74, "http.responses": 74, "http.downloaded_bytes": 620580, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).codes.200": 74}, "rates": {"http.request_rate": 4}, "firstCounterAt": 1753661605193, "firstHistogramAt": 1753662025179, "lastCounterAt": 1753662031268, "lastHistogramAt": 1753662031268, "firstMetricAt": 1753661605193, "lastMetricAt": 1753662031268, "period": 1753662030000, "summaries": {"vusers.session_length": {"min": 6748.2, "max": 27024, "count": 74, "mean": 16865.4, "p50": 16819.2, "median": 16819.2, "p75": 21813.5, "p90": 24107.7, "p95": 26115.6, "p99": 26643.2, "p999": 26643.2}, "http.response_time": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "http.response_time.2xx": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Top-Rated (Complex)": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}}, "histograms": {"vusers.session_length": {"min": 6748.2, "max": 27024, "count": 74, "mean": 16865.4, "p50": 16819.2, "median": 16819.2, "p75": 21813.5, "p90": 24107.7, "p95": 26115.6, "p99": 26643.2, "p999": 26643.2}, "http.response_time": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "http.response_time.2xx": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Top-Rated (Complex)": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}}}, "intermediate": [{"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 15, "vusers.created": 15, "errors.Undefined function \"generateSlowQueryFilters\"": 15, "http.requests": 15}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661605193, "lastCounterAt": 1753661609304, "firstMetricAt": 1753661605193, "lastMetricAt": 1753661609304, "period": "1753661600000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661610190, "lastCounterAt": 1753661619307, "firstMetricAt": 1753661610190, "lastMetricAt": 1753661619307, "period": "1753661610000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661620190, "lastCounterAt": 1753661629302, "firstMetricAt": 1753661620190, "lastMetricAt": 1753661629302, "period": "1753661620000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661630190, "lastCounterAt": 1753661639317, "firstMetricAt": 1753661630190, "lastMetricAt": 1753661639317, "period": "1753661630000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661640190, "lastCounterAt": 1753661649310, "firstMetricAt": 1753661640190, "lastMetricAt": 1753661649310, "period": "1753661640000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661650190, "lastCounterAt": 1753661659305, "firstMetricAt": 1753661650190, "lastMetricAt": 1753661659305, "period": "1753661650000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 55, "vusers.created": 55, "errors.Undefined function \"generateSlowQueryFilters\"": 55, "http.requests": 55, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661660190, "lastCounterAt": 1753661669598, "firstMetricAt": 1753661660190, "lastMetricAt": 1753661669598, "period": "1753661660000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30, "vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661670192, "lastCounterAt": 1753661679598, "firstMetricAt": 1753661670192, "lastMetricAt": 1753661679598, "period": "1753661670000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661680190, "lastCounterAt": 1753661689598, "firstMetricAt": 1753661680190, "lastMetricAt": 1753661689598, "period": "1753661680000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661690190, "lastCounterAt": 1753661699612, "firstMetricAt": 1753661690190, "lastMetricAt": 1753661699612, "period": "1753661690000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661700190, "lastCounterAt": 1753661709598, "firstMetricAt": 1753661700190, "lastMetricAt": 1753661709598, "period": "1753661700000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661710190, "lastCounterAt": 1753661719600, "firstMetricAt": 1753661710190, "lastMetricAt": 1753661719600, "period": "1753661710000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661720191, "lastCounterAt": 1753661729608, "firstMetricAt": 1753661720191, "lastMetricAt": 1753661729608, "period": "1753661720000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661730191, "lastCounterAt": 1753661739599, "firstMetricAt": 1753661730191, "lastMetricAt": 1753661739599, "period": "1753661730000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661740191, "lastCounterAt": 1753661749598, "firstMetricAt": 1753661740191, "lastMetricAt": 1753661749598, "period": "1753661740000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661750191, "lastCounterAt": 1753661759598, "firstMetricAt": 1753661750191, "lastMetricAt": 1753661759598, "period": "1753661750000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661760191, "lastCounterAt": 1753661769599, "firstMetricAt": 1753661760191, "lastMetricAt": 1753661769599, "period": "1753661760000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661770191, "lastCounterAt": 1753661779598, "firstMetricAt": 1753661770191, "lastMetricAt": 1753661779598, "period": "1753661770000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661780191, "lastCounterAt": 1753661789601, "firstMetricAt": 1753661780191, "lastMetricAt": 1753661789601, "period": "1753661780000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661790191, "lastCounterAt": 1753661799598, "firstMetricAt": 1753661790191, "lastMetricAt": 1753661799598, "period": "1753661790000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661800191, "lastCounterAt": 1753661809598, "firstMetricAt": 1753661800191, "lastMetricAt": 1753661809598, "period": "1753661800000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661810191, "lastCounterAt": 1753661819599, "firstMetricAt": 1753661810191, "lastMetricAt": 1753661819599, "period": "1753661810000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661820191, "lastCounterAt": 1753661829598, "firstMetricAt": 1753661820191, "lastMetricAt": 1753661829598, "period": "1753661820000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661830191, "lastCounterAt": 1753661839599, "firstMetricAt": 1753661830191, "lastMetricAt": 1753661839599, "period": "1753661830000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661840190, "lastCounterAt": 1753661849603, "firstMetricAt": 1753661840190, "lastMetricAt": 1753661849603, "period": "1753661840000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661850190, "lastCounterAt": 1753661859601, "firstMetricAt": 1753661850190, "lastMetricAt": 1753661859601, "period": "1753661850000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661860191, "lastCounterAt": 1753661869601, "firstMetricAt": 1753661860191, "lastMetricAt": 1753661869601, "period": "1753661860000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661870190, "lastCounterAt": 1753661879601, "firstMetricAt": 1753661870190, "lastMetricAt": 1753661879601, "period": "1753661870000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661880191, "lastCounterAt": 1753661889599, "firstMetricAt": 1753661880191, "lastMetricAt": 1753661889599, "period": "1753661880000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661890190, "lastCounterAt": 1753661899599, "firstMetricAt": 1753661890190, "lastMetricAt": 1753661899599, "period": "1753661890000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661900191, "lastCounterAt": 1753661909602, "firstMetricAt": 1753661900191, "lastMetricAt": 1753661909602, "period": "1753661900000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661910191, "lastCounterAt": 1753661919598, "firstMetricAt": 1753661910191, "lastMetricAt": 1753661919598, "period": "1753661910000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661920191, "lastCounterAt": 1753661929599, "firstMetricAt": 1753661920191, "lastMetricAt": 1753661929599, "period": "1753661920000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661930191, "lastCounterAt": 1753661939598, "firstMetricAt": 1753661930191, "lastMetricAt": 1753661939598, "period": "1753661930000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661940191, "lastCounterAt": 1753661949598, "firstMetricAt": 1753661940191, "lastMetricAt": 1753661949598, "period": "1753661940000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 80, "vusers.created": 80, "errors.Undefined function \"generateSlowQueryFilters\"": 80, "http.requests": 80, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661950191, "lastCounterAt": 1753661959598, "firstMetricAt": 1753661950191, "lastMetricAt": 1753661959598, "period": "1753661950000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 55, "vusers.created": 55, "errors.Undefined function \"generateSlowQueryFilters\"": 55, "http.requests": 55, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 8}, "http.request_rate": null, "firstCounterAt": 1753661960190, "lastCounterAt": 1753661969603, "firstMetricAt": 1753661960190, "lastMetricAt": 1753661969603, "period": "1753661960000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661970191, "lastCounterAt": 1753661979605, "firstMetricAt": 1753661970191, "lastMetricAt": 1753661979605, "period": "1753661970000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 80, "vusers.failed": 80}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661980191, "lastCounterAt": 1753661989606, "firstMetricAt": 1753661980191, "lastMetricAt": 1753661989606, "period": "1753661980000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753661990191, "lastCounterAt": 1753661999303, "firstMetricAt": 1753661990191, "lastMetricAt": 1753661999303, "period": "1753661990000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753662000191, "lastCounterAt": 1753662009303, "firstMetricAt": 1753662000191, "lastMetricAt": 1753662009303, "period": "1753662000000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 30, "vusers.created": 30, "errors.Undefined function \"generateSlowQueryFilters\"": 30, "http.requests": 30, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 30, "errors.ETIMEDOUT": 30, "vusers.failed": 30}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753662010191, "lastCounterAt": 1753662019303, "firstMetricAt": 1753662010191, "lastMetricAt": 1753662019303, "period": "1753662010000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Top-Rated Queries - Complex Calculations": 15, "vusers.created": 15, "errors.Undefined function \"generateSlowQueryFilters\"": 15, "http.requests": 15, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).errors.ETIMEDOUT": 16, "errors.ETIMEDOUT": 16, "vusers.failed": 16, "http.codes.200": 74, "http.responses": 74, "http.downloaded_bytes": 620580, "plugins.metrics-by-endpoint.GET /api/mugshots - Top-Rated (Complex).codes.200": 74, "vusers.completed": 44}, "rates": {"http.request_rate": 3}, "http.request_rate": null, "firstCounterAt": 1753662020191, "firstHistogramAt": 1753662025179, "lastCounterAt": 1753662029903, "lastHistogramAt": 1753662029903, "firstMetricAt": 1753662020191, "lastMetricAt": 1753662029903, "period": "1753662020000", "summaries": {"http.response_time": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "http.response_time.2xx": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Top-Rated (Complex)": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "vusers.session_length": {"min": 15533.8, "max": 27024, "count": 44, "mean": 20875.3, "p50": 20958.1, "median": 20958.1, "p75": 23630.3, "p90": 25091.6, "p95": 26115.6, "p99": 26643.2, "p999": 26643.2}}, "histograms": {"http.response_time": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "http.response_time.2xx": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots - Top-Rated (Complex)": {"min": 2470, "max": 15000, "count": 74, "mean": 6456.4, "p50": 4867, "median": 4867, "p75": 9801.2, "p90": 12213.1, "p95": 13770.3, "p99": 14917.2, "p999": 14917.2}, "vusers.session_length": {"min": 15533.8, "max": 27024, "count": 44, "mean": 20875.3, "p50": 20958.1, "median": 20958.1, "p75": 23630.3, "p90": 25091.6, "p95": 26115.6, "p99": 26643.2, "p999": 26643.2}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 30}, "rates": {}, "firstCounterAt": 1753662030132, "firstHistogramAt": 1753662030132, "lastCounterAt": 1753662031268, "lastHistogramAt": 1753662031268, "firstMetricAt": 1753662030132, "lastMetricAt": 1753662031268, "period": "1753662030000", "summaries": {"vusers.session_length": {"min": 6748.2, "max": 15073.7, "count": 30, "mean": 10984.2, "p50": 11050.8, "median": 11050.8, "p75": 13230.3, "p90": 14332.3, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}}, "histograms": {"vusers.session_length": {"min": 6748.2, "max": 15073.7, "count": 30, "mean": 10984.2, "p50": 11050.8, "median": 11050.8, "p75": 13230.3, "p90": 14332.3, "p95": 14917.2, "p99": 14917.2, "p999": 14917.2}}}]}