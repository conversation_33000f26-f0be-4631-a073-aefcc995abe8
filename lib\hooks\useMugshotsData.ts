/**
 * DEPRECATED: This hook has been replaced by useMugshotsQuery
 *
 * This file exists only for backward compatibility with tests.
 * All new code should use useMugshotsQuery from lib/hooks/queries/use-mugshots-query.ts
 * which properly uses our API layer instead of direct Supabase calls.
 */

import { useState, useCallback, useEffect } from 'react'
import { getMugshots } from '@/lib/services/api-client'
import type { UIMugshot } from '@/lib/utils/mugshot-transforms'
import type { TagType } from '@/lib/constants'

// Legacy interfaces for backward compatibility
export interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  categories?: string[]
  tags?: string[]
}

export interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

export interface PaginationOptions {
  page: number
  perPage: number
}

interface UseMugshotsDataState {
  mugshots: UIMugshot[]
  totalCount: number
  loading: boolean
  error: string | null
  isHealthy: boolean
}

interface UseMugshotsDataReturn extends UseMugshotsDataState {
  fetchMugshots: (
    filters?: MugshotFilters,
    sort?: SortOptions,
    pagination?: PaginationOptions
  ) => Promise<void>
  clearError: () => void
  refresh: () => Promise<void>
}

/**
 * @deprecated Use useMugshotsQuery instead
 * This hook is maintained only for test compatibility
 */
export function useMugshotsData(): UseMugshotsDataReturn {
  const [state, setState] = useState<UseMugshotsDataState>({
    mugshots: [],
    totalCount: 0,
    loading: true,
    error: null,
    isHealthy: false
  })

  const [currentParams, setCurrentParams] = useState<{
    filters: MugshotFilters
    sort: SortOptions
    pagination: PaginationOptions
  }>({
    filters: {},
    sort: { sortBy: 'newest' },
    pagination: { page: 1, perPage: 12 }
  })

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  const fetchMugshots = useCallback(async (
    filters: MugshotFilters = {},
    sort: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 }
  ) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      setCurrentParams({ filters, sort, pagination })

      // Use API client instead of direct Supabase calls
      const response = await getMugshots(
        {
          search: filters.searchTerm,
          state: filters.state,
          county: filters.county,
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          categories: filters.categories,
          tags: filters.tags as TagType[] | undefined
        },
        { sortBy: sort.sortBy },
        {
          page: pagination.page,
          perPage: pagination.perPage,
          includeTotal: true
        }
      )

      if (response.success && response.data) {
        const data = response.data
        setState(prev => ({
          ...prev,
          mugshots: data.mugshots,
          totalCount: data.pagination?.totalCount || 0,
          loading: false,
          isHealthy: true
        }))
      } else {
        throw new Error(response.message || 'Failed to fetch mugshots')
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error('❌ Error fetching mugshots:', error)

      setState(prev => ({
        ...prev,
        mugshots: [],
        totalCount: 0,
        loading: false,
        error: errorMessage,
        isHealthy: false
      }))
    }
  }, [])

  const refresh = useCallback(async () => {
    await fetchMugshots(currentParams.filters, currentParams.sort, currentParams.pagination)
  }, [fetchMugshots, currentParams])

  // Initial load
  useEffect(() => {
    fetchMugshots()
  }, [fetchMugshots])

  return {
    ...state,
    fetchMugshots,
    clearError,
    refresh
  }
}

// Legacy interface for tests
interface UseMugshotsPageState extends UseMugshotsDataState {
  filters: MugshotFilters
  sort: SortOptions
  pagination: PaginationOptions
  isEmpty: boolean
  totalPages: number
}

interface UseMugshotsPageReturn extends UseMugshotsPageState {
  updateFilters: (newFilters: Partial<MugshotFilters>) => void
  updateSort: (newSort: SortOptions) => void
  goToPage: (page: number) => void
  nextPage: () => void
  prevPage: () => void
  refresh: () => Promise<void>
  clearError: () => void
}

/**
 * @deprecated Use useMugshotsQuery instead
 * This hook is maintained only for test compatibility
 */
export function useMugshotsPage(): UseMugshotsPageReturn {
  const [filters, setFilters] = useState<MugshotFilters>({})
  const [sort, setSort] = useState<SortOptions>({ sortBy: 'newest' })
  const [pagination, setPagination] = useState<PaginationOptions>({ page: 1, perPage: 12 })

  const {
    mugshots,
    totalCount,
    loading,
    error,
    isHealthy,
    fetchMugshots,
    clearError: baseClearError
  } = useMugshotsData()

  const isEmpty = mugshots.length === 0 && !loading
  const totalPages = Math.ceil(totalCount / pagination.perPage)

  const updateFilters = useCallback((newFilters: Partial<MugshotFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
    fetchMugshots(updatedFilters, sort, { ...pagination, page: 1 })
  }, [filters, sort, pagination, fetchMugshots])

  const updateSort = useCallback((newSort: SortOptions) => {
    setSort(newSort)
    fetchMugshots(filters, newSort, pagination)
  }, [filters, pagination, fetchMugshots])

  const goToPage = useCallback((page: number) => {
    const newPagination = { ...pagination, page }
    setPagination(newPagination)
    fetchMugshots(filters, sort, newPagination)
  }, [filters, sort, pagination, fetchMugshots])

  const nextPage = useCallback(() => {
    if (pagination.page < totalPages) {
      goToPage(pagination.page + 1)
    }
  }, [pagination.page, totalPages, goToPage])

  const prevPage = useCallback(() => {
    if (pagination.page > 1) {
      goToPage(pagination.page - 1)
    }
  }, [pagination.page, goToPage])

  const refresh = useCallback(async () => {
    await fetchMugshots(filters, sort, pagination)
  }, [filters, sort, pagination, fetchMugshots])

  return {
    mugshots,
    totalCount,
    loading,
    error,
    isHealthy,
    filters,
    sort,
    pagination,
    isEmpty,
    totalPages,
    updateFilters,
    updateSort,
    goToPage,
    nextPage,
    prevPage,
    refresh,
    clearError: baseClearError
  }
}