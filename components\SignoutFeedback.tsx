'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Loader2, LogOut } from 'lucide-react'

interface SignoutFeedbackProps {
  isOpen: boolean
}

export default function SignoutFeedback({ isOpen }: SignoutFeedbackProps) {
  return (
    <Dialog open={isOpen}>
      <DialogContent 
        className="sm:max-w-md w-full max-w-[calc(100vw-2rem)] mx-auto bg-background/95 backdrop-blur border border-border shadow-xl fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        showCloseButton={false}
      >
        <div className="flex flex-col items-center justify-center p-4 sm:p-6 text-center">
          {/* Animated Icons */}
          <div className="relative mb-3 sm:mb-4">
            <div className="absolute inset-0 animate-ping">
              <LogOut className="h-6 w-6 sm:h-8 sm:w-8 text-pink-500/20" />
            </div>
            <LogOut className="relative h-6 w-6 sm:h-8 sm:w-8 text-pink-500" />
          </div>
          
          {/* Spinner */}
          <Loader2 className="h-5 w-5 sm:h-6 sm:w-6 animate-spin text-pink-500 mb-3 sm:mb-4" />
          
          {/* Text */}
          <div className="space-y-1 sm:space-y-2">
            <h3 className="text-base sm:text-lg font-semibold text-foreground">
              Signing Out
            </h3>
            <p className="text-xs sm:text-sm text-muted-foreground px-2">
              Please wait while we securely sign you out...
            </p>
          </div>
          
          {/* Progress indicator */}
          <div className="w-full bg-muted rounded-full h-1 mt-3 sm:mt-4 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-pink-500 to-pink-600 rounded-full animate-pulse transition-all duration-1000" 
                 style={{ width: '100%' }} />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 