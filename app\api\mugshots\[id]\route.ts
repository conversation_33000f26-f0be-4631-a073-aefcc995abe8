import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { transformDBMugshotToUI } from '@/lib/utils/mugshot-transforms'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(_: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    
    // Validate mugshot ID
    const mugshotId = parseInt(id, 10)
    if (isNaN(mugshotId) || mugshotId <= 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid mugshot ID', error: 'INVALID_MUGSHOT_ID' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    
    // Get mugshot details from database with rating/tag data
    const { data: rawMugshot, error: mugshotError } = await supabase
      .from('mugshots')
      .select('*')
      .eq('id', mugshotId)
      .single()

    if (mugshotError || !rawMugshot) {
      return NextResponse.json(
        { success: false, message: 'Mugshot not found', error: 'MUGSHOT_NOT_FOUND' },
        { status: 404 }
      )
    }

    // Get rating statistics using aggregation
    const { data: ratingStats, error: ratingError } = await supabase
      .from('ratings')
      .select('rating')
      .eq('mugshot_id', mugshotId)

    if (ratingError) {
      console.error('Error fetching ratings:', ratingError)
      return NextResponse.json(
        { success: false, message: 'Failed to fetch rating data', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }

    // Calculate rating statistics
    const ratings = ratingStats || []
    const totalRatings = ratings.length
    const averageRating = totalRatings > 0 
      ? Math.round((ratings.reduce((sum, r) => sum + r.rating, 0) / totalRatings) * 100) / 100
      : 0

    // Get tag statistics using aggregation  
    const { data: tagStats, error: tagError } = await supabase
      .from('tags')
      .select('tag_type')
      .eq('mugshot_id', mugshotId)

    if (tagError) {
      console.error('Error fetching tags:', tagError)
      return NextResponse.json(
        { success: false, message: 'Failed to fetch tag data', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }

    // Calculate tag statistics
    const tags = tagStats || []
    const tagCounts = {
      wild: tags.filter(t => t.tag_type === 'wild').length,
      funny: tags.filter(t => t.tag_type === 'funny').length,
      spooky: tags.filter(t => t.tag_type === 'spooky').length
    }

    // Add computed rating/tag data to the database mugshot
    const enrichedMugshot = {
      ...rawMugshot,
      average_rating: averageRating,
      total_ratings: totalRatings,
      wild_count: tagCounts.wild,
      funny_count: tagCounts.funny,
      spooky_count: tagCounts.spooky,
      user_rating: null, // TODO: Add user-specific rating when user is authenticated
      user_tags: [] // TODO: Add user-specific tags when user is authenticated
    }

    // Transform to UI format using the standard function
    const mugshot = transformDBMugshotToUI(enrichedMugshot)

    // Build comprehensive response
    const response = {
      success: true,
      data: {
        mugshot,
        ratings: {
          averageRating,
          totalRatings,
          allRatings: ratings.map(r => r.rating),
          ratingDistribution: {
            1: ratings.filter(r => r.rating === 1).length,
            2: ratings.filter(r => r.rating === 2).length,
            3: ratings.filter(r => r.rating === 3).length,
            4: ratings.filter(r => r.rating === 4).length,
            5: ratings.filter(r => r.rating === 5).length,
            6: ratings.filter(r => r.rating === 6).length,
            7: ratings.filter(r => r.rating === 7).length,
            8: ratings.filter(r => r.rating === 8).length,
            9: ratings.filter(r => r.rating === 9).length,
            10: ratings.filter(r => r.rating === 10).length
          }
        },
        tags: {
          totalTags: tags.length,
          tagCounts,
          allTags: tags.map(t => t.tag_type),
          popularTags: Object.entries(tagCounts)
            .filter(([, count]) => count > 0)
            .sort(([, a], [, b]) => b - a)
            .map(([tag]) => tag)
        },
        meta: {
          fetchedAt: new Date().toISOString(),
          mugshotId,
          hasRatings: totalRatings > 0,
          hasTags: tags.length > 0,
          dataFresh: true
        }
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('Mugshot API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch mugshot details',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
} 