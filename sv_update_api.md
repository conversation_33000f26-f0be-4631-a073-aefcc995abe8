# Updated Mugshots API Documentation

## Overview
Updated `/api/mugshots/[id]` endpoint to return comprehensive mugshot details with all ratings and tags data using proper database joins.

## Endpoint
```
GET /api/mugshots/[id]
```

## Parameters
- `id` (path parameter): Mugshot ID (integer)

## Database Tables Used
- `mugshots` - All mugshot details
- `ratings` - All ratings for the mugshot  
- `tags` - All tags for the mugshot

## Database Schema Reference

### Mugshots Table
```sql
create table public.mugshots (
  id bigserial not null,
  created_at timestamp with time zone null default now(),
  "firstName" text null default ''::text,
  "lastName" text null default ''::text,
  "dateOfBooking" date null,
  "stateOfBooking" text null,
  "countyOfBooking" text null,
  "offenseDescription" text null,
  "additionalDetails" text null,
  "imagePath" text null,
  fb_status text null,
  "adsText" text null,
  jb_post_link text null,
  jb_fb_post boolean not null default false,
  constraint mugshots_pkey primary key (id)
)
```

### Ratings Table
```sql
create table public.ratings (
  id uuid not null default extensions.uuid_generate_v4 (),
  mugshot_id bigint not null,
  user_id uuid not null,
  rating integer not null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  created_at_date date null,
  constraint ratings_rating_check check (
    (rating >= 1) and (rating <= 10)
  )
)
```

### Tags Table
```sql
create table public.tags (
  id uuid not null default extensions.uuid_generate_v4 (),
  mugshot_id bigint not null,
  user_id uuid not null,
  tag_type text not null,
  created_at timestamp with time zone null default now(),
  constraint tags_tag_type_check check (
    tag_type = any (array['wild'::text, 'funny'::text, 'spooky'::text])
  )
)
```

## Response Format

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "mugshot": {
      "id": 123,
      "firstName": "John",
      "lastName": "Doe", 
      "name": "John Doe",
      "dateOfBooking": "2024-01-15",
      "stateOfBooking": "California",
      "countyOfBooking": "Los Angeles",
      "location": "Los Angeles, California",
      "offenseDescription": "Theft",
      "additionalDetails": null,
      "imagePath": "/images/mugshot123.jpg",
      "image": "/images/mugshot123.jpg",
      "fbStatus": "posted",
      "adsText": "Sample ad text",
      "jbPostLink": "https://example.com/post",
      "jbFbPost": true,
      "createdAt": "2024-01-15T10:30:00Z"
    },
    "ratings": {
      "averageRating": 7.85,
      "totalRatings": 47,
      "allRatings": [8, 7, 9, 6, 8, 7, 10, 5, 8, 9],
      "ratingDistribution": {
        "1": 2,
        "2": 1,
        "3": 3,
        "4": 4,
        "5": 5,
        "6": 6,
        "7": 8,
        "8": 12,
        "9": 4,
        "10": 2
      }
    },
    "tags": {
      "totalTags": 23,
      "tagCounts": {
        "wild": 8,
        "funny": 12,
        "spooky": 3
      },
      "allTags": ["funny", "wild", "funny", "spooky", "wild"],
      "popularTags": ["funny", "wild", "spooky"]
    },
    "meta": {
      "fetchedAt": "2024-01-15T15:45:30Z",
      "mugshotId": 123,
      "hasRatings": true,
      "hasTags": true,
      "dataFresh": true
    }
  }
}
```

### Error Responses

#### Invalid Mugshot ID (400)
```json
{
  "success": false,
  "message": "Invalid mugshot ID",
  "error": "INVALID_MUGSHOT_ID"
}
```

#### Mugshot Not Found (404)
```json
{
  "success": false,
  "message": "Mugshot not found", 
  "error": "MUGSHOT_NOT_FOUND"
}
```

#### Database Error (500)
```json
{
  "success": false,
  "message": "Failed to fetch rating data",
  "error": "DATABASE_ERROR"
}
```

#### Internal Server Error (500)
```json
{
  "success": false,
  "message": "Failed to fetch mugshot details",
  "error": "INTERNAL_ERROR"
}
```

## Key Features

### 1. Complete Mugshot Data
- All fields from mugshots table
- Computed fields (name, location, image)
- Facebook and ad integration fields

### 2. Comprehensive Rating Statistics
- Average rating (rounded to 2 decimals)
- Total rating count
- All individual ratings array
- Rating distribution (1-10 breakdown)

### 3. Complete Tag Information
- Total tag count
- Tag type counts (wild, funny, spooky)
- All individual tags array
- Popular tags (sorted by count)

### 4. User-Agnostic Design
- Works for authenticated and non-authenticated users
- No user-specific data included
- Pure statistical/aggregate data only

### 5. Performance Optimized
- Uses proper database indexes
- Single API call for all data
- Efficient aggregation queries

## Database Queries Used

### 1. Mugshot Details
```sql
SELECT * FROM mugshots WHERE id = $1
```

### 2. Rating Statistics
```sql
SELECT rating FROM ratings WHERE mugshot_id = $1
```

### 3. Tag Statistics
```sql
SELECT tag_type FROM tags WHERE mugshot_id = $1
```

## Response Processing

### Rating Calculations
- Average: `SUM(ratings) / COUNT(ratings)` rounded to 2 decimals
- Distribution: Count of each rating value (1-10)
- All ratings: Raw array of rating values

### Tag Calculations
- Counts: Filter by tag_type and count occurrences
- Popular: Sort tag counts descending, exclude zero counts
- All tags: Raw array of tag_type values

## Usage Examples

### Frontend Integration
```javascript
// Fetch mugshot details
const response = await fetch('/api/mugshots/123')
const { success, data } = await response.json()

if (success) {
  const { mugshot, ratings, tags } = data
  
  // Display mugshot info
  console.log(`${mugshot.name} from ${mugshot.location}`)
  
  // Display ratings
  console.log(`Average: ${ratings.averageRating}/10 (${ratings.totalRatings} ratings)`)
  
  // Display popular tags
  console.log(`Popular tags: ${tags.popularTags.join(', ')}`)
}
```

### Error Handling
```javascript
try {
  const response = await fetch('/api/mugshots/123')
  const result = await response.json()
  
  if (!result.success) {
    console.error(`Error: ${result.message}`)
    return
  }
  
  // Use result.data
} catch (error) {
  console.error('Network error:', error)
}
```

## Implementation Status
- ✅ **API Endpoint Created**: `/api/mugshots/[id]`
- ✅ **Database Queries Optimized**: Uses indexed queries for performance
- ✅ **Response Format Standardized**: Consistent JSON structure
- ✅ **Error Handling**: Comprehensive error responses
- ✅ **Store Service Created**: `MugshotStoreService` handles API calls + store updates
- ✅ **Frontend Integration**: Integrated with MugshotModal and individual mugshot page

## Store Integration

### MugshotStoreService
A new service (`lib/services/mugshot-store-service.ts`) handles:
- ✅ **API Call**: Calls `/api/mugshots/[id]` endpoint
- ✅ **Rating Store Update**: Updates rating statistics and user ratings
- ✅ **Tag Store Update**: Updates tag statistics and user tags
- ✅ **Automatic Cache Management**: Handles store cache invalidation

### Usage Locations
- ✅ **MugshotModal**: Calls service when modal opens
- ✅ **Individual Mugshot Page**: Calls service on component mount
- ✅ **Store Updates**: All rating/tag data automatically synced to global stores

## Testing
- Test with valid mugshot IDs
- Test with invalid/non-existent IDs
- Test with mugshots that have no ratings/tags
- Test response time and data accuracy 