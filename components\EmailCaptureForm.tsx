"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import LocationDropdown from "@/components/LocationDropdown"
import { Mail, Gavel, CheckCircle, AlertCircle } from "lucide-react"
import { toast } from "sonner"

interface EmailCaptureFormProps {
  title?: string
  subtitle?: string
  showStateSelection?: boolean
  variant?: "jury" | "weekly" | "events"
  source?: string
}

// Mock function to simulate email subscription (for display purposes only)
const mockAddEmailSubscriber = async (data: {
  email: string
  subscription_type: string
  state?: string
  county?: string
  source: string
}) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // Mock validation - simulate some basic email validation
  if (!data.email.includes('@')) {
    return { success: false, error: 'Please enter a valid email address' }
  }
  
  // Mock success response
  return { success: true }
}

export default function EmailCaptureForm({ 
  title = "Join the Jury",
  subtitle = "Get weekly updates on the hottest mugshots and vote on your favorites",
  showStateSelection = true,
  variant = "jury",
  source = "homepage"
}: EmailCaptureFormProps) {
  const [email, setEmail] = useState("")
  const [selectedState, setSelectedState] = useState("")
  const [selectedCounty, setSelectedCounty] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")
    
    try {
      const result = await mockAddEmailSubscriber({
        email,
        subscription_type: variant,
        state: selectedState || undefined,
        county: selectedCounty || undefined,
        source
      })

      if (result.success) {
        setIsSubmitted(true)
        toast.success(variant === "jury" ? "Welcome to the Jury!" : "Successfully subscribed!")
      } else {
        throw new Error(result.error || 'Failed to subscribe')
      }
    } catch (error) {
      console.error('Subscription error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  if (isSubmitted) {
    return (
      <div className="card-neon p-8 text-center">
        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h3 className="text-xl font-bold text-white mb-2">
          {variant === "jury" ? "Welcome to the Jury!" : "Successfully Subscribed!"}
        </h3>
        <p className="text-gray-400 mb-4">
          {variant === "jury" 
            ? `You'll receive weekly updates with the hottest mugshots from ${selectedState}${selectedCounty ? `, ${selectedCounty}` : ""}.`
            : variant === "weekly"
            ? "You'll receive weekly voting alerts and competition updates."
            : "You'll receive notifications about daily winners and special events."
          }
        </p>
        <div className="text-sm text-gray-500 space-y-1">
          <p>✅ Daily winner notifications</p>
          {variant === "jury" || variant === "weekly" ? <p>✅ Weekly voting alerts</p> : null}
          {selectedState && <p>✅ Local updates from {selectedState}</p>}
          <p className="mt-3 text-xs">
            Check your email for a welcome message and manage preferences anytime.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="card-neon p-8">
      <div className="text-center mb-6">
        {variant === "jury" ? (
          <Gavel className="h-12 w-12 text-pink-500 mx-auto mb-4" />
        ) : (
          <Mail className="h-12 w-12 text-cyan-500 mx-auto mb-4" />
        )}
        <h3 className="text-2xl font-bold text-white mb-2">{title}</h3>
        <p className="text-gray-400">{subtitle}</p>
      </div>

      {error && (
        <div className="mb-4 p-3 rounded-lg border border-red-500/30 bg-red-900/20">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <p className="text-sm text-red-300">{error}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="email" className="text-white mb-2 block">
            Email Address
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            className="input-neon"
            required
          />
        </div>

        {showStateSelection && (
          <div>
            <Label className="text-white mb-2 block">
              {variant === "jury" 
                ? "Which states do you want updates for?" 
                : "Select your location (optional)"
              }
            </Label>
            <LocationDropdown
              selectedState={selectedState}
              setSelectedState={setSelectedState}
              selectedCounty={selectedCounty}
              setSelectedCounty={setSelectedCounty}
              statePlaceholder="Select your state"
              countyPlaceholder="Select your county (optional)"
              className="w-full"
            />
            {selectedState && (
              <p className="text-xs text-gray-400 mt-2">
                {variant === "jury" 
                  ? `Get updates for ${selectedState}${selectedCounty ? `, ${selectedCounty}` : ""}`
                  : `Get local updates for ${selectedState}${selectedCounty ? `, ${selectedCounty}` : ""}`
                }
              </p>
            )}
          </div>
        )}

        {/* What you'll receive section */}
        <div className="p-3 rounded-lg border border-cyan-500/30 bg-gray-800/50">
          <p className="text-sm text-cyan-300 font-medium mb-2">You&apos;ll receive:</p>
          <div className="text-xs text-gray-300 space-y-1">
            <p>🏆 Daily winner announcements</p>
            {variant === "jury" || variant === "weekly" ? <p>🗳️ Weekly voting alerts</p> : null}
            {variant === "events" ? <p>🎉 Special event notifications</p> : null}
            {selectedState && <p>📍 Local updates from your area</p>}
            <p className="text-gray-400 mt-2">
              Email frequency: Immediate (you can change this in preferences)
            </p>
          </div>
        </div>

        <Button
          type="submit"
          disabled={isLoading || !email}
          className="w-full btn-neon"
        >
          {isLoading ? "Subscribing..." : variant === "jury" ? "Join the Jury" : "Subscribe"}
        </Button>

        <p className="text-xs text-gray-500 text-center">
          We respect your privacy. Unsubscribe at any time. No spam, just the good stuff.
        </p>
      </form>
    </div>
  )
} 