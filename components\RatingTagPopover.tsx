'use client'

import { useState, useEffect, useTransition } from 'react'
import { useAuthStore } from '@/lib/stores/auth-store'
import { useRatingMutation, useCanRate, handleUnauthenticatedRating } from '@/lib/hooks/mutations/use-rating-mutation'
import { useUserRatingQuery } from '@/lib/hooks/queries/use-user-rating-query'
import { useTagMutation } from '@/lib/hooks/mutations/use-tag-mutation'
import { useUserMugshotDataQuery } from '@/lib/hooks/queries/use-user-mugshot-data-query'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { X, Loader2 } from 'lucide-react'
import { toastMessages } from '@/lib/utils/toast-messages'
import { toast } from 'sonner'
import type { TagType } from '@/lib/constants'

interface RatingTagPopoverProps {
  mugshotId: string
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  trigger: React.ReactNode
  className?: string
}

const ratingToasts = toastMessages.rating
const tagToasts = toastMessages.tag

export default function RatingTagPopover({ 
  mugshotId, 
  isOpen, 
  onOpenChange,
  trigger,
  className = ''
}: RatingTagPopoverProps) {
  const { user } = useAuthStore()
  
  // TanStack Query hooks for rating and tag data
  const { data: userRatingData, isLoading: isLoadingUserRating } = useUserRatingQuery(mugshotId, isOpen)
  const { data: userMugshotData, isLoading: isLoadingUserData } = useUserMugshotDataQuery(mugshotId, isOpen)
  const ratingMutation = useRatingMutation(mugshotId)
  const tagMutation = useTagMutation(mugshotId)
  const canRate = useCanRate(mugshotId)

  // Local state
  const [hoveredRating, setHoveredRating] = useState<number>(0)
  const [isPending, _startTransition] = useTransition()

  // Get current data from TanStack Query
  const userRating = userRatingData?.userRating ?? null
  const currentUserTags = new Set<TagType>(userMugshotData?.userTags as TagType[] || [])
  
  // Calculate if any operations are in progress
  const isRatingSubmitting = ratingMutation.isPending
  const isTagSubmitting = tagMutation.isPending
  const isAnyDataLoading = isLoadingUserRating || isLoadingUserData
  const isAnyOperationInProgress = isRatingSubmitting || isTagSubmitting || isPending || isAnyDataLoading

  // Debug: Log rating state when popover is open
  useEffect(() => {
    if (isOpen) {
      console.log(`[RatingTagPopover-${mugshotId}] Current rating state:`, {
        userRating: userRating,
        isAuthenticated: !!user,
        canRate: canRate,
        isRatingSubmitting,
        isTagSubmitting,
        isAnyDataLoading,
        isAnyOperationInProgress
      })
    }
  }, [isOpen, userRating, mugshotId, user, canRate, isRatingSubmitting, isTagSubmitting, isAnyDataLoading, isAnyOperationInProgress])

  // Note: User tag data is automatically loaded by useUserMugshotDataQuery when popover opens

  // TanStack Query tag toggle with automatic optimistic updates
  const handleTagClick = async (tagType: TagType) => {
    if (!user) {
      tagToasts.loginRequired()
      return
    }

    // Prevent action if any operation is in progress
    if (isAnyOperationInProgress) {
      console.log('Tag action blocked - operation in progress')
      return
    }

    // Show immediate toast feedback
    const isRemoving = currentUserTags.has(tagType)
    const loadingToastId = isRemoving ? 
      tagToasts.removing(tagType) : 
      tagToasts.adding(tagType)

    try {
      // TanStack Query handles optimistic updates automatically
      await tagMutation.mutateAsync(tagType)
      
      // Show success toast
      toast.dismiss(loadingToastId)
      if (isRemoving) {
        tagToasts.removed(tagType)
      } else {
        tagToasts.added(tagType)
      }
    } catch (error) {
      // TanStack Query handles rollback automatically
      console.error('Tag operation error:', error)
      toast.dismiss(loadingToastId)
      tagToasts.failed(isRemoving ? 'remove' : 'add', tagType)
    }
  }

  // TanStack Query rating submission
  const handleRating = async (rating: number) => {
    if (!user || !canRate) {
      if (!user) {
        ratingToasts.loginRequired()
        handleUnauthenticatedRating()
      }
      return
    }

    // Prevent action if any operation is in progress
    if (isAnyOperationInProgress) {
      console.log('Rating action blocked - operation in progress')
      return
    }

    // Step 1: Show optimistic toast feedback
    const oldRating = userRating
    const loadingToastId = oldRating !== null ? 
      ratingToasts.saving(rating) : 
      ratingToasts.saving(rating)

    // Step 2: Execute mutation (with built-in optimistic updates)
    try {
      await ratingMutation.mutateAsync(rating)
      
      // Step 3: Show success toast
      toast.dismiss(loadingToastId)
      if (oldRating !== null) {
        ratingToasts.updated(oldRating, rating)
      } else {
        ratingToasts.saved(rating)
      }
      
      // DO NOT close popover - let user continue rating/tagging
    } catch (error) {
      // Step 4: Handle errors (TanStack Query handles rollback automatically)
      console.error('Rating submission error:', error)
      toast.dismiss(loadingToastId)
      ratingToasts.failed()
    }
  }

  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        {trigger}
      </PopoverTrigger>
      
      <PopoverContent className={`w-96 max-w-[calc(100vw-2rem)] p-0 bg-gray-900 border-gray-700 mx-4 ${className}`}>
        <div className="space-y-4">
          {/* Enhanced Header with Hot Face Emoji */}
          <div className="flex items-center justify-between p-4 pb-2 border-b border-gray-700">
            <div className="flex items-center gap-2">
              <span className="text-xl">🥵</span>
              <h4 className="font-semibold text-white text-lg">Rate Attractiveness</h4>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Subtitle */}
          <div className="px-4 -mt-2">
            <p className="text-sm text-gray-400">Click a number from 1-10</p>
            {isAnyOperationInProgress && (
              <p className="text-xs text-yellow-400 mt-1">Please wait while processing...</p>
            )}
          </div>

          {/* Content Area - Rating Grid */}
          <div className="px-4">
            {/* 2x5 Number Grid - FIXED RATING PRE-SELECTION */}
            <div className="flex flex-col items-center justify-center gap-3 w-full">
              {/* First row: 1-5 */}
              <div className="flex items-center justify-center gap-2 w-full px-2">
                {[1, 2, 3, 4, 5].map((rating) => {
                  const isSelected = userRating === rating
                  const isSubmitting = isRatingSubmitting && isSelected
                  
                  const isHovered = hoveredRating > 0 && rating <= hoveredRating
                  // Show all numbers up to user's rating (like hover behavior)
                  const isUserRatedUpTo = userRating && userRating >= rating && hoveredRating === 0
                  const shouldBeFilled = isHovered || isUserRatedUpTo
                  
                  const getNumberColor = () => {
                    const percentage = (rating - 1) / 9
                    
                    if (percentage <= 0.4) {
                      const progress = percentage / 0.4
                      return `hsl(${210 + progress * 20}, ${95 - progress * 25}%, ${45 + progress * 20}%)`
                    } else if (percentage <= 0.6) {
                      const progress = (percentage - 0.4) / 0.2
                      return `hsl(${190 - progress * 40}, ${60 - progress * 40}%, ${70 + progress * 15}%)`
                    } else {
                      const progress = (percentage - 0.6) / 0.4
                      return `hsl(${30 - progress * 10}, ${80 + progress * 20}%, ${70 - progress * 20}%)`
                    }
                  }
                  
                  return (
                    <button
                      key={rating}
                      onClick={() => user && !isAnyOperationInProgress && handleRating(rating)}
                      onMouseEnter={() => !isAnyOperationInProgress && setHoveredRating(rating)}
                      onMouseLeave={() => !isAnyOperationInProgress && setHoveredRating(0)}
                      disabled={!user || isAnyOperationInProgress}
                      className={`
                        relative w-16 h-16 flex items-center justify-center
                        font-black text-5xl outline-text transition-all duration-300 transform
                        ${user && !isAnyOperationInProgress ? 'hover:scale-110 cursor-pointer' : 'cursor-not-allowed opacity-50'}
                        ${isSubmitting ? 'animate-pulse' : ''}
                        ${isAnyOperationInProgress ? 'pointer-events-none opacity-60' : ''}
                      `}
                      style={{
                        WebkitTextStroke: shouldBeFilled ? 'none' : '1px #6b7280',
                        color: shouldBeFilled ? getNumberColor() : 'transparent',
                        textShadow: shouldBeFilled ? `0 0 25px ${getNumberColor()}60` : 'none'
                      }}
                    >
                      {rating}
                      {isSubmitting && (
                        <Loader2 className="absolute inset-0 h-4 w-4 m-auto animate-spin text-white" />
                      )}
                    </button>
                  )
                })}
              </div>

              {/* Second row: 6-10 */}
              <div className="flex items-center justify-center gap-1 w-full px-2">
                {[6, 7, 8, 9, 10].map((rating) => {
                  const isSelected = userRating === rating
                  const isSubmitting = isRatingSubmitting && isSelected
                  
                  const isHovered = hoveredRating > 0 && rating <= hoveredRating
                  // Show all numbers up to user's rating (like hover behavior)
                  const isUserRatedUpTo = userRating && userRating >= rating && hoveredRating === 0
                  const shouldBeFilled = isHovered || isUserRatedUpTo
                  
                  const getNumberColor = () => {
                    const percentage = (rating - 1) / 9
                    
                    if (percentage <= 0.4) {
                      const progress = percentage / 0.4
                      return `hsl(${210 + progress * 20}, ${95 - progress * 25}%, ${45 + progress * 20}%)`
                    } else if (percentage <= 0.6) {
                      const progress = (percentage - 0.4) / 0.2
                      return `hsl(${190 - progress * 40}, ${60 - progress * 40}%, ${70 + progress * 15}%)`
                    } else {
                      const progress = (percentage - 0.6) / 0.4
                      return `hsl(${30 - progress * 10}, ${80 + progress * 20}%, ${70 - progress * 20}%)`
                    }
                  }
                  
                  return (
                    <button
                      key={rating}
                      onClick={() => user && !isAnyOperationInProgress && handleRating(rating)}
                      onMouseEnter={() => !isAnyOperationInProgress && setHoveredRating(rating)}
                      onMouseLeave={() => !isAnyOperationInProgress && setHoveredRating(0)}
                      disabled={!user || isAnyOperationInProgress}
                      className={`
                        relative w-16 h-16 flex items-center justify-center
                        font-black text-5xl outline-text transition-all duration-300 transform
                        ${user && !isAnyOperationInProgress ? 'hover:scale-110 cursor-pointer' : 'cursor-not-allowed opacity-50'}
                        ${isSubmitting ? 'animate-pulse' : ''}
                        ${isAnyOperationInProgress ? 'pointer-events-none opacity-60' : ''}
                      `}
                      style={{
                        WebkitTextStroke: shouldBeFilled ? 'none' : '1px #6b7280',
                        color: shouldBeFilled ? getNumberColor() : 'transparent',
                        textShadow: shouldBeFilled ? `0 0 25px ${getNumberColor()}60` : 'none'
                      }}
                    >
                      {rating === 10 ? '10' : rating}
                      {isSubmitting && (
                        <Loader2 className="absolute inset-0 h-4 w-4 m-auto animate-spin text-white" />
                      )}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Tag Selection */}
            <div className="space-y-3 mt-6">
              <div className="text-center">
                <p className="text-sm text-gray-400">Add a tag</p>
              </div>
              <div className="flex flex-wrap gap-2 justify-center">
                {[
                  { type: 'funny' as const, emoji: '😂', label: 'Funny' },
                  { type: 'wild' as const, emoji: '🤪', label: 'Wild' },
                  { type: 'spooky' as const, emoji: '😱', label: 'Spooky' }
                ].map((tag) => {
                  const isSelected = currentUserTags.has(tag.type)
                  const isSubmittingThisTag = isTagSubmitting
                  
                  return (
                    <div key={tag.type} className="relative group">
                      <button
                        onClick={() => !isAnyOperationInProgress && handleTagClick(tag.type as TagType)}
                        disabled={!user || isAnyOperationInProgress}
                        className={`
                          flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300
                          ${!user || isAnyOperationInProgress
                            ? 'opacity-50 cursor-not-allowed bg-gray-800/90 border border-gray-600 text-gray-500' 
                            : isSelected
                            ? 'bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-lg shadow-pink-500/30 scale-105'
                            : 'bg-gray-800/90 border border-cyan-500/30 text-white hover:bg-gray-700/90 hover:border-cyan-500/50'
                          }
                          ${isSubmittingThisTag ? 'animate-pulse' : ''}
                          ${isAnyOperationInProgress ? 'pointer-events-none' : ''}
                        `}
                      >
                        <span className="text-lg">{tag.emoji}</span>
                        <span>{tag.label}</span>
                      </button>
                      
                      {/* X icon for selected tags */}
                      {isSelected && user && !isAnyOperationInProgress && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleTagClick(tag.type)
                          }}
                          disabled={isAnyOperationInProgress}
                          className="absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full bg-red-500 hover:bg-red-600 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center disabled:opacity-0"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                      
                      {/* Loading indicator */}
                      {isSubmittingThisTag && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                          <Loader2 className="h-3 w-3 animate-spin text-white" />
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Auth prompt */}
            {!user && (
              <div className="text-center p-3 bg-blue-900/20 rounded-lg border border-blue-700/30 mt-4">
                <p className="text-sm text-blue-300 font-medium">
                  <button
                    onClick={() => {
                      const currentUrl = window.location.href
                      window.location.href = `/login?returnUrl=${encodeURIComponent(currentUrl)}`
                    }}
                    className="underline hover:text-blue-200 transition-colors bg-transparent border-none cursor-pointer"
                  >
                    Login required to rate
                  </button>
                </p>
              </div>
            )}
          </div>

          {/* Footer - User's Current Rating */}
          <div className="px-4 py-3 border-t border-gray-700 bg-gray-800/30">
            <div className="text-center text-sm">
              {userRating ? (
                <span className="text-gray-400">
                  Your current rating: <span className="text-green-400 font-medium">{userRating}/10</span>
                </span>
              ) : (
                <span className="text-gray-500">No rating yet</span>
              )}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
} 