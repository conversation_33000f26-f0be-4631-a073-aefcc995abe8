#!/usr/bin/env node

/**
 * Artillery Results Analysis Script
 * 
 * This script analyzes Artillery load test results and provides
 * detailed performance insights and recommendations.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Performance thresholds (from artillery.yml)
const thresholds = {
  responseTime: {
    median: 500,
    p95: 2000,
    p99: 5000
  },
  successRate: 95,
  errorRate: {
    client: 3,
    server: 2
  },
  minThroughput: 100
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function formatNumber(num) {
  return typeof num === 'number' ? num.toFixed(2) : 'N/A';
}

function formatPercentage(num) {
  return typeof num === 'number' ? `${num.toFixed(2)}%` : 'N/A';
}

function getLatestResultFile() {
  const resultsDir = path.join(__dirname, 'results');

  if (!fs.existsSync(resultsDir)) {
    console.error(colorize('❌ Results directory not found. Run a load test first.', 'red'));
    process.exit(1);
  }

  const files = fs.readdirSync(resultsDir)
    .filter(file => file.endsWith('.json'))
    .map(file => ({
      name: file,
      path: path.join(resultsDir, file),
      mtime: fs.statSync(path.join(resultsDir, file)).mtime,
      size: fs.statSync(path.join(resultsDir, file)).size
    }))
    .sort((a, b) => b.mtime - a.mtime);

  if (files.length === 0) {
    console.error(colorize('❌ No result files found. Run a load test first.', 'red'));
    process.exit(1);
  }

  // Show available files for user reference
  console.log(colorize('\n📁 Available result files:', 'blue'));
  files.forEach((file, index) => {
    const isLatest = index === 0;
    const sizeKB = (file.size / 1024).toFixed(1);
    const timeStr = file.mtime.toLocaleString();
    const marker = isLatest ? colorize('← LATEST', 'green') : '';
    console.log(`  ${file.name} (${sizeKB}KB, ${timeStr}) ${marker}`);
  });

  return files[0].path;
}

function analyzeResults(resultFile) {
  console.log(colorize('\n🔍 Artillery Load Test Results Analysis', 'cyan'));
  console.log(colorize('=' .repeat(50), 'cyan'));
  
  let data;
  try {
    data = JSON.parse(fs.readFileSync(resultFile, 'utf8'));
  } catch (error) {
    console.error(colorize(`❌ Error reading result file: ${error.message}`, 'red'));
    process.exit(1);
  }
  
  const aggregate = data.aggregate;
  const counters = aggregate.counters || {};
  const summaries = aggregate.summaries || {};
  
  // Basic test information
  console.log(colorize('\n📊 Test Overview', 'blue'));
  console.log(`File: ${path.basename(resultFile)}`);
  console.log(`Duration: ${formatNumber(aggregate.duration / 1000)} seconds`);
  console.log(`Total Requests: ${counters['http.requests'] || 0}`);
  
  // Response time analysis
  console.log(colorize('\n⏱️  Response Time Analysis', 'blue'));
  const responseTime = summaries['http.response_time'] || {};
  
  const median = responseTime.median || 0;
  const p95 = responseTime.p95 || 0;
  const p99 = responseTime.p99 || 0;
  const mean = responseTime.mean || 0;
  
  console.log(`Mean: ${formatNumber(mean)}ms`);
  console.log(`Median: ${formatNumber(median)}ms ${getThresholdStatus(median, thresholds.responseTime.median)}`);
  console.log(`95th Percentile: ${formatNumber(p95)}ms ${getThresholdStatus(p95, thresholds.responseTime.p95)}`);
  console.log(`99th Percentile: ${formatNumber(p99)}ms ${getThresholdStatus(p99, thresholds.responseTime.p99)}`);
  console.log(`Min: ${formatNumber(responseTime.min)}ms`);
  console.log(`Max: ${formatNumber(responseTime.max)}ms`);
  
  // Throughput analysis
  console.log(colorize('\n🚀 Throughput Analysis', 'blue'));
  const duration = aggregate.duration / 1000;
  const totalRequests = counters['http.requests'] || 0;
  const avgThroughput = totalRequests / duration;
  
  console.log(`Average Throughput: ${formatNumber(avgThroughput)} requests/second ${getThresholdStatus(avgThroughput, thresholds.minThroughput, true)}`);
  console.log(`Peak Throughput: ${formatNumber(aggregate.rps?.max || 0)} requests/second`);
  
  // Success rate analysis
  console.log(colorize('\n✅ Success Rate Analysis', 'blue'));
  const successRequests = counters['http.codes.200'] || 0;
  const successRate = totalRequests > 0 ? (successRequests / totalRequests) * 100 : 0;
  
  console.log(`Success Rate: ${formatPercentage(successRate)} ${getThresholdStatus(successRate, thresholds.successRate, true)}`);
  console.log(`Successful Requests: ${successRequests}`);
  console.log(`Failed Requests: ${totalRequests - successRequests}`);
  
  // Error analysis
  console.log(colorize('\n❌ Error Analysis', 'blue'));
  const clientErrors = (counters['http.codes.4xx'] || 0);
  const serverErrors = (counters['http.codes.5xx'] || 0);
  const totalErrors = (counters['errors.total'] || 0);
  
  const clientErrorRate = totalRequests > 0 ? (clientErrors / totalRequests) * 100 : 0;
  const serverErrorRate = totalRequests > 0 ? (serverErrors / totalRequests) * 100 : 0;
  
  console.log(`Total Errors: ${totalErrors}`);
  console.log(`Client Errors (4xx): ${clientErrors} (${formatPercentage(clientErrorRate)}) ${getThresholdStatus(clientErrorRate, thresholds.errorRate.client, false, true)}`);
  console.log(`Server Errors (5xx): ${serverErrors} (${formatPercentage(serverErrorRate)}) ${getThresholdStatus(serverErrorRate, thresholds.errorRate.server, false, true)}`);
  
  // HTTP status code breakdown
  console.log(colorize('\n📈 HTTP Status Codes', 'blue'));
  Object.keys(counters)
    .filter(key => key.startsWith('http.codes.'))
    .sort()
    .forEach(key => {
      const code = key.replace('http.codes.', '');
      const count = counters[key];
      const percentage = totalRequests > 0 ? (count / totalRequests) * 100 : 0;
      console.log(`${code}: ${count} (${formatPercentage(percentage)})`);
    });
  
  // Performance recommendations
  console.log(colorize('\n💡 Performance Recommendations', 'magenta'));
  generateRecommendations(responseTime, successRate, avgThroughput, clientErrorRate, serverErrorRate);
  
  // Overall assessment
  console.log(colorize('\n🎯 Overall Assessment', 'bright'));
  const overallScore = calculateOverallScore(median, p95, successRate, avgThroughput, serverErrorRate);
  console.log(`Performance Score: ${overallScore}/100 ${getScoreColor(overallScore)}`);
  console.log(getOverallAssessment(overallScore));
}

function getThresholdStatus(value, threshold, higher = false, lower = false) {
  if (typeof value !== 'number' || typeof threshold !== 'number') {
    return '';
  }
  
  let passed;
  if (lower) {
    passed = value < threshold;
  } else if (higher) {
    passed = value > threshold;
  } else {
    passed = value < threshold;
  }
  
  return passed 
    ? colorize('✅ PASS', 'green')
    : colorize('❌ FAIL', 'red');
}

function getScoreColor(score) {
  if (score >= 90) return colorize('🟢 EXCELLENT', 'green');
  if (score >= 75) return colorize('🟡 GOOD', 'yellow');
  if (score >= 60) return colorize('🟠 FAIR', 'yellow');
  return colorize('🔴 POOR', 'red');
}

function calculateOverallScore(median, p95, successRate, throughput, serverErrorRate) {
  let score = 100;
  
  // Response time penalties
  if (median > thresholds.responseTime.median) score -= 15;
  if (p95 > thresholds.responseTime.p95) score -= 20;
  
  // Success rate penalties
  if (successRate < thresholds.successRate) score -= 25;
  
  // Throughput penalties
  if (throughput < thresholds.minThroughput) score -= 20;
  
  // Error rate penalties
  if (serverErrorRate > thresholds.errorRate.server) score -= 20;
  
  return Math.max(0, score);
}

function getOverallAssessment(score) {
  if (score >= 90) {
    return colorize('🎉 Excellent performance! Your API is handling load very well.', 'green');
  } else if (score >= 75) {
    return colorize('👍 Good performance with minor areas for improvement.', 'yellow');
  } else if (score >= 60) {
    return colorize('⚠️  Fair performance. Consider optimization efforts.', 'yellow');
  } else {
    return colorize('🚨 Poor performance. Immediate optimization required.', 'red');
  }
}

function generateRecommendations(responseTime, successRate, throughput, clientErrorRate, serverErrorRate) {
  const recommendations = [];
  
  if (responseTime.median > thresholds.responseTime.median) {
    recommendations.push('• Optimize database queries and add appropriate indexes');
    recommendations.push('• Consider implementing response caching');
  }
  
  if (responseTime.p95 > thresholds.responseTime.p95) {
    recommendations.push('• Investigate slow queries and optimize them');
    recommendations.push('• Consider database connection pooling');
  }
  
  if (successRate < thresholds.successRate) {
    recommendations.push('• Review server logs for error patterns');
    recommendations.push('• Check database connection limits');
  }
  
  if (throughput < thresholds.minThroughput) {
    recommendations.push('• Scale server resources (CPU/Memory)');
    recommendations.push('• Optimize API endpoint performance');
  }
  
  if (serverErrorRate > thresholds.errorRate.server) {
    recommendations.push('• Fix server-side errors immediately');
    recommendations.push('• Implement proper error handling and logging');
  }
  
  if (clientErrorRate > thresholds.errorRate.client) {
    recommendations.push('• Review API validation and error responses');
    recommendations.push('• Check test data generation for invalid requests');
  }
  
  if (recommendations.length === 0) {
    console.log(colorize('🎉 No major issues detected! Your API is performing well.', 'green'));
  } else {
    recommendations.forEach(rec => console.log(rec));
  }
}

function compareResults(file1, file2) {
  console.log(colorize('\n🔄 Comparing Load Test Results', 'cyan'));
  console.log(colorize('=' .repeat(50), 'cyan'));
  
  const data1 = JSON.parse(fs.readFileSync(file1, 'utf8'));
  const data2 = JSON.parse(fs.readFileSync(file2, 'utf8'));
  
  const agg1 = data1.aggregate;
  const agg2 = data2.aggregate;
  
  console.log(`\nFile 1: ${path.basename(file1)}`);
  console.log(`File 2: ${path.basename(file2)}`);
  
  // Compare key metrics
  const metrics = [
    { name: 'Median Response Time', key: 'http.response_time', prop: 'median', unit: 'ms' },
    { name: '95th Percentile', key: 'http.response_time', prop: 'p95', unit: 'ms' },
    { name: 'Total Requests', key: 'http.requests', unit: '' },
    { name: 'Success Rate', key: 'http.codes.200', unit: '%', isRate: true }
  ];
  
  console.log(colorize('\n📊 Comparison', 'blue'));
  metrics.forEach(metric => {
    let val1, val2;
    
    if (metric.key === 'http.requests') {
      val1 = agg1.counters?.[metric.key] || 0;
      val2 = agg2.counters?.[metric.key] || 0;
    } else if (metric.isRate) {
      const total1 = agg1.counters?.['http.requests'] || 1;
      const total2 = agg2.counters?.['http.requests'] || 1;
      val1 = ((agg1.counters?.[metric.key] || 0) / total1) * 100;
      val2 = ((agg2.counters?.[metric.key] || 0) / total2) * 100;
    } else {
      val1 = agg1.summaries?.[metric.key]?.[metric.prop] || 0;
      val2 = agg2.summaries?.[metric.key]?.[metric.prop] || 0;
    }
    
    const diff = val2 - val1;
    const diffPercent = val1 !== 0 ? (diff / val1) * 100 : 0;
    const diffColor = diff > 0 ? 'red' : 'green';
    const diffSymbol = diff > 0 ? '↑' : '↓';
    
    console.log(`${metric.name}:`);
    console.log(`  File 1: ${formatNumber(val1)}${metric.unit}`);
    console.log(`  File 2: ${formatNumber(val2)}${metric.unit}`);
    console.log(`  Change: ${colorize(`${diffSymbol} ${formatNumber(Math.abs(diff))}${metric.unit} (${formatNumber(Math.abs(diffPercent))}%)`, diffColor)}`);
    console.log('');
  });
}

function listResultFiles() {
  const resultsDir = path.join(__dirname, 'results');

  if (!fs.existsSync(resultsDir)) {
    console.error(colorize('❌ Results directory not found.', 'red'));
    return;
  }

  const files = fs.readdirSync(resultsDir)
    .filter(file => file.endsWith('.json'))
    .map(file => ({
      name: file,
      path: path.join(resultsDir, file),
      mtime: fs.statSync(path.join(resultsDir, file)).mtime,
      size: fs.statSync(path.join(resultsDir, file)).size
    }))
    .sort((a, b) => b.mtime - a.mtime);

  if (files.length === 0) {
    console.log(colorize('❌ No result files found.', 'red'));
    return;
  }

  console.log(colorize('\n📁 Available Load Test Results:', 'cyan'));
  console.log(colorize('=' .repeat(60), 'cyan'));

  files.forEach((file, index) => {
    const sizeKB = (file.size / 1024).toFixed(1);
    const timeStr = file.mtime.toLocaleString();
    const isLatest = index === 0;
    const marker = isLatest ? colorize(' ← LATEST', 'green') : '';

    console.log(`${index + 1}. ${colorize(file.name, 'bright')}`);
    console.log(`   Size: ${sizeKB}KB | Modified: ${timeStr}${marker}`);
    console.log(`   Path: ${file.path}`);
    console.log('');
  });
}

// Main execution
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    // Analyze latest result
    const latestFile = getLatestResultFile();
    console.log(colorize(`\n📁 Analyzing latest result: ${path.basename(latestFile)}`, 'cyan'));
    analyzeResults(latestFile);
  } else if (args.length === 1) {
    const arg = args[0];

    if (arg === 'list' || arg === '--list' || arg === '-l') {
      // List all available files
      listResultFiles();
      return;
    }

    // Analyze specific file
    const resultFile = arg;
    if (!fs.existsSync(resultFile)) {
      console.error(colorize(`❌ File not found: ${resultFile}`, 'red'));
      process.exit(1);
    }
    analyzeResults(resultFile);
  } else if (args.length === 2) {
    // Compare two files
    const [file1, file2] = args;
    if (!fs.existsSync(file1) || !fs.existsSync(file2)) {
      console.error(colorize('❌ One or both files not found', 'red'));
      process.exit(1);
    }
    compareResults(file1, file2);
  } else {
    console.log('Usage:');
    console.log('  node analyze-results.js                    # Analyze latest result');
    console.log('  node analyze-results.js list               # List all available results');
    console.log('  node analyze-results.js <file>             # Analyze specific file');
    console.log('  node analyze-results.js <file1> <file2>    # Compare two files');
  }
}

if (require.main === module) {
  main();
}
