"use client"

import { useState, useEffect, Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import MugshotModal from "@/components/MugshotModal"
import MugshotCard from "@/components/MugshotCard"
import LocationDropdown from "@/components/LocationDropdown"
import { TrendingUp, Search, Vote } from "lucide-react"
import EmailCaptureForm from "@/components/EmailCaptureForm"
import PageSkeleton from "@/components/PageSkeleton"
import { useSearchParams } from "next/navigation"

// Deterministic random generator
const seededRandom = (seed: number) => {
  const x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
}

// Mock popular mugshots data with deterministic generation
const popularMugshots = Array.from({ length: 20 }, (_, i) => {
  const id = i + 1
  const names = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
  ]
  const categories = ["Hot", "Funny", "Wild", "Scary"]
  const states = ["Texas", "Florida", "California", "New York", "Nevada"]
  const counties = ["Harris", "Miami-Dade", "Los Angeles", "Kings", "Clark"]
  const state = states[id % states.length]
  
  return {
    id,
    name: names[id % names.length],
    location: `${counties[id % counties.length]} County, ${state}`,
    state,
    rating: Number((3.5 + seededRandom(id * 2) * 1.5).toFixed(1)),
    image: "/images/mugshot-placeholder.png",
    category: categories[id % 4],
    votes: Math.floor(100 + seededRandom(id * 3) * 900),
    isLocal: id % 3 === 0,
    arrestDate: new Date(Date.now() - seededRandom(id * 4) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    birthDate: new Date(Date.now() - seededRandom(id * 5) * 365 * 24 * 60 * 60 * 1000 * 25).toISOString().split('T')[0],
    offenses: ["Public Intoxication", "Disorderly Conduct"]
  }
})

function PopularPageContent() {
  const [isLoading, setIsLoading] = useState(true)
  const [showVotePrompt, setShowVotePrompt] = useState(false)
  const [votedMugshots, setVotedMugshots] = useState<Set<number>>(new Set())
  const [userHasVoted, setUserHasVoted] = useState(false)
  const searchParams = useSearchParams()
  const [selectedState, setSelectedState] = useState(searchParams.get('state') || "all-states")
  const [selectedCounty, setSelectedCounty] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState<"popular" | "recent" | "rating">("popular")
  const [selectedMugshot, setSelectedMugshot] = useState<(typeof popularMugshots)[0] | null>(null)
  const [isMugshotModalOpen, setIsMugshotModalOpen] = useState(false)

  // Initial loading effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 300)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Show vote prompt for local content after 3 seconds
    const timer = setTimeout(() => {
      if (selectedState !== "all-states" && !userHasVoted) {
        setShowVotePrompt(true)
      }
    }, 3000)

    return () => clearTimeout(timer)
  }, [selectedState, userHasVoted])

  // Handle mugshotId URL parameter to open specific mugshot
  useEffect(() => {
    const mugshotId = searchParams.get('mugshotId')
    if (mugshotId) {
      const mugshot = popularMugshots.find(m => m.id === parseInt(mugshotId))
      if (mugshot) {
        setSelectedMugshot(mugshot)
        setIsMugshotModalOpen(true)
      }
    }
  }, [searchParams])

  const getFilteredMugshots = () => {
    let filtered = popularMugshots

    if (selectedState !== "all-states") {
      filtered = filtered.filter(m => m.state === selectedState)
    }

    if (searchTerm) {
      filtered = filtered.filter(m => 
        m.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        m.location.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "popular":
          return b.votes - a.votes
        case "recent":
          return new Date(b.arrestDate).getTime() - new Date(a.arrestDate).getTime()
        case "rating":
          return b.rating - a.rating
        default:
          return b.votes - a.votes
      }
    })
  }

  const handleVote = (mugshotId: number, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent card click
    
    if (votedMugshots.has(mugshotId)) return
    
    // Add vote animation class
    const button = event.target as HTMLElement
    const buttonElement = button.closest('button')
    if (buttonElement) {
      buttonElement.classList.add('vote-success')
      
      // Remove animation class after animation completes
      setTimeout(() => {
        buttonElement.classList.remove('vote-success')
        buttonElement.classList.add('vote-disabled')
      }, 800)
    }
    
    setVotedMugshots(prev => new Set(prev).add(mugshotId))
    setUserHasVoted(true)
    setShowVotePrompt(false)
    
    // In real app, would trigger signup flow if not logged in
    setTimeout(() => {
      // Create a simple toast notification
      const toast = document.createElement('div')
      toast.className = 'fixed bottom-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50'
      toast.textContent = "Vote recorded! Sign up to save your preferences."
      document.body.appendChild(toast)
      
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast)
        }
      }, 3000)
    }, 800)
  }

  const handleCardClick = (mugshot: (typeof popularMugshots)[0]) => {
    setSelectedMugshot(mugshot)
    setIsMugshotModalOpen(true)
  }


  const filteredMugshots = getFilteredMugshots()

  if (isLoading) {
    return <PageSkeleton type="popular" />
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Page Header - Centered */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold uppercase tracking-tight text-white mb-4">
            <TrendingUp className="inline h-8 w-8 text-pink-500 mr-2" />
            <span className="text-pink-500">POPULAR</span> MUGSHOTS
          </h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            {selectedState !== "all-states" 
              ? `Hottest mugshots from ${selectedState} - vote on your neighbors!`
              : "Discover the most popular mugshots from across America"
            }
          </p>
        </div>

        {/* Vote Prompt for Local Users */}
        {showVotePrompt && selectedState !== "all-states" && (
          <div className="card-neon p-6 mb-8 bg-gradient-to-r from-pink-500/10 to-purple-500/10 border-pink-500/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Vote className="h-8 w-8 text-pink-500" />
                <div>
                  <h3 className="text-lg font-bold text-white">Vote on Local Mugshots!</h3>
                  <p className="text-gray-300">Help rank the hottest arrests in {selectedState}</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button className="btn-glow-pink cursor-pointer" onClick={() => setShowVotePrompt(false)}>
                  Start Voting
                </Button>
                <Button variant="ghost" onClick={() => setShowVotePrompt(false)} className="text-gray-400 cursor-pointer">
                  Later
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Filters Section - Clean Card Layout */}
        <div className="card-neon mb-8">
          <div className="space-y-6">
            {/* Filter Controls */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Location Dropdown */}
              <div className="lg:col-span-2 space-y-2">
                <label className="text-sm font-medium text-gray-300">Location</label>
                <LocationDropdown
                  selectedState={selectedState}
                  setSelectedState={setSelectedState}
                  selectedCounty={selectedCounty}
                  setSelectedCounty={setSelectedCounty}
                  statePlaceholder="All States"
                  countyPlaceholder="All Counties"
                  allStatesOption={true}
                  allCountiesOption={true}
                />
              </div>

              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Name or location..."
                    className="input-neon pl-10 cursor-pointer"
                  />
                </div>
              </div>

              {/* Sort By */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Sort By</label>
                <Select value={sortBy} onValueChange={(value) => setSortBy(value as "popular" | "recent" | "rating")}>
                  <SelectTrigger className="input-neon cursor-pointer">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800/90 border-cyan-500/30">
                    <SelectItem value="popular" className="text-white hover:bg-gray-700 cursor-pointer">Most Popular</SelectItem>
                    <SelectItem value="recent" className="text-white hover:bg-gray-700 cursor-pointer">Most Recent</SelectItem>
                    <SelectItem value="rating" className="text-white hover:bg-gray-700 cursor-pointer">Highest Rated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Results Info */}
        {selectedState !== "all-states" && (
          <div className="mb-8 text-center">
            <p className="text-gray-400">
              Showing <span className="text-cyan-400 font-bold">{filteredMugshots.length}</span> popular mugshots from <span className="text-pink-400 font-bold">{selectedState}</span>
            </p>
          </div>
        )}

        {/* Mugshots Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {filteredMugshots.map((mugshot) => (
            <MugshotCard
              key={mugshot.id}
              mugshot={mugshot}
              onClick={(mugshot) => handleCardClick(mugshot as typeof popularMugshots[0])}
              cardSize="large"
              showLocalBadge={selectedState !== "all-states"}
              showVoteButton={true}
              onVote={handleVote}
              isVoted={votedMugshots.has(mugshot.id)}
            />
          ))}
        </div>

        {/* Email Capture */}
        {selectedState !== "all-states" && (
          <div className="mb-12">
            <EmailCaptureForm 
              variant="jury"
              title={`Get ${selectedState} Updates`}
              subtitle={`Never miss the hottest mugshots from ${selectedState}. Join local voters!`}
              showStateSelection={false}
            />
          </div>
        )}

        {/* Mugshot Modal */}
        {selectedMugshot && (
          <MugshotModal
            mugshot={selectedMugshot}
            isOpen={isMugshotModalOpen}
            onClose={() => setIsMugshotModalOpen(false)}
          />
        )}
      </div>
    </div>
  )
}

function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading popular mugshots...</p>
      </div>
    </div>
  )
}

export default function PopularPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <PopularPageContent />
    </Suspense>
  )
} 