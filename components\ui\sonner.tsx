"use client"

import { useTheme } from "next-themes"
import { Toaster as <PERSON><PERSON>, ToasterP<PERSON> } from "sonner"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      position="bottom-right"
      duration={3000} // 3 seconds for most toasts
      closeButton={true} // Manual close with X icon
      expand={true} // Allow stacking nicely
      visibleToasts={5} // Max 5 visible toasts stacked
      richColors={true} // Better color support
      style={
        {
          "--normal-bg": "rgb(17 24 39)", // Solid gray-900 background
          "--normal-text": "rgb(243 244 246)", // Gray-100 text
          "--normal-border": "rgb(55 65 81)", // Gray-700 border
          "--success-bg": "rgb(5 46 22)", // Solid green-900 background
          "--success-text": "rgb(187 247 208)", // Green-200 text
          "--success-border": "rgb(21 128 61)", // Green-700 border
          "--error-bg": "rgb(69 10 10)", // Solid red-900 background
          "--error-text": "rgb(254 202 202)", // Red-200 text
          "--error-border": "rgb(185 28 28)", // Red-700 border
          "--info-bg": "rgb(12 74 110)", // Solid blue-900 background
          "--info-text": "rgb(186 230 253)", // Blue-200 text
          "--info-border": "rgb(29 78 216)", // Blue-700 border
          "--warning-bg": "rgb(69 26 3)", // Solid orange-900 background
          "--warning-text": "rgb(254 215 170)", // Orange-200 text
          "--warning-border": "rgb(194 65 12)", // Orange-700 border
        } as React.CSSProperties
      }
      toastOptions={{
        style: {
          background: "var(--normal-bg)",
          color: "var(--normal-text)",
          border: "1px solid var(--normal-border)",
          borderRadius: "8px",
          backdropFilter: "none", // No blur effect - solid background
          boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)",
        },
        className: "sonner-toast",
      }}
      {...props}
    />
  )
}

export { Toaster }
