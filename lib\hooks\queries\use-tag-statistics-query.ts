import { useQuery } from '@tanstack/react-query'
import type { TagStatistics } from '@/lib/types/database'

/**
 * Tag statistics query hook for fetching tag counts for a specific mugshot
 * Fetches the number of each tag type (wild, funny, spooky) for a mugshot
 * 
 * @param mugshotId - The ID of the mugshot to fetch tag statistics for
 * @param enabled - Whether the query should run (default: true)
 * @returns TanStack Query result with tag statistics
 */
export function useTagStatisticsQuery(mugshotId: string, enabled = true) {
  return useQuery({
    queryKey: ['mugshot', mugshotId, 'tag-statistics'],
    queryFn: async (): Promise<TagStatistics> => {
      try {
        const response = await fetch(`/api/tags/statistics?mugshotId=${mugshotId}`)
        
        // Check if response is JSON before parsing
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          const text = await response.text()
          console.error('Non-JSON response received for tag statistics:', text.substring(0, 200))
          throw new Error('Server returned invalid response format')
        }
        
        const result = await response.json()
        
        if (!response.ok) {
          // Handle specific HTTP errors
          if (response.status === 404) {
            // No tags for this mugshot yet - return zero counts
            return { wild: 0, funny: 0, spooky: 0, totalTags: 0 }
          } else if (response.status >= 500) {
            throw new Error('Server error occurred')
          } else if (response.status === 429) {
            throw new Error('Too many requests - please wait a moment')
          } else {
            throw new Error(result.message || 'Failed to fetch tag statistics')
          }
        }
        
        if (!result.success) {
          throw new Error(result.message || 'Failed to fetch tag statistics')
        }
        
        // Validate the data structure to prevent runtime errors
        const data = result.data
        if (!data || typeof data !== 'object') {
          throw new Error('Invalid tag statistics format')
        }
        
        // Ensure all required fields exist with defaults
        return {
          wild: typeof data.wild === 'number' ? data.wild : 0,
          funny: typeof data.funny === 'number' ? data.funny : 0,
          spooky: typeof data.spooky === 'number' ? data.spooky : 0,
          totalTags: typeof data.totalTags === 'number' ? data.totalTags : 0
        }
      } catch (error) {
        // Log the full error for debugging but throw a user-friendly message
        console.error('Tag statistics query error:', error)
        
        if (error instanceof TypeError && error.message.includes('fetch')) {
          throw new Error('Network connection error')
        }
        
        if (error instanceof Error) {
          throw error // Re-throw our custom error messages
        }
        
        throw new Error('Unable to load tag statistics')
      }
    },
    enabled,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on client errors except timeouts
      if (error?.message?.includes('invalid') || error?.message?.includes('format')) {
        return false
      }
      return failureCount < 3
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
} 