"use client"

import { useState, useEffect, useRef, RefObject } from "react"
import { useRouter, usePathname } from "next/navigation"
import { format } from "date-fns"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandGroup, CommandItem, CommandList } from "@/components/ui/command"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import LocationDropdown from "@/components/LocationDropdown"
import MobileFiltersSheet from "./MobileFiltersSheet"
import { useFilterStore } from "@/lib/stores/filter-store"
import { Search, Calendar as CalendarIcon, Grid2X2, Grid3X3, Check, ChevronsUpDown, Loader2 } from "lucide-react"
import { categoryEmojis } from "@/lib/constants"
import { cn } from "@/lib/utils"

type FiltersProps = {
  // Current values from server
  searchTerm: string
  selectedState: string
  selectedCounty: string
  dateFrom: string
  dateTo: string
  categories: string[]
  tags: string[]
  sortBy: string
  perPage: number
  gridView: string
  currentPage: number
  // Ref for auto-scrolling to grid on mobile
  gridContainerRef?: RefObject<HTMLDivElement | null>
}

const sortOptions = [
  { value: "newest", label: "Newest" },
  { value: "top-rated", label: "Top Rated" },
  { value: "most-viewed", label: "Most Viewed" }
]

const perPageOptions = [12, 24, 48]

export default function MugshotsFiltersClient({
  searchTerm: initialSearchTerm,
  selectedState: initialSelectedState,
  selectedCounty: initialSelectedCounty,
  dateFrom: initialDateFrom,
  dateTo: initialDateTo,
  categories: initialCategories,
  tags: initialTags,
  sortBy: initialSortBy,
  perPage: initialPerPage,
  gridView: initialGridView,
  currentPage: initialCurrentPage,
  gridContainerRef
}: FiltersProps) {
  const router = useRouter()
  const pathname = usePathname()
  
  // Global stores - Use these as the source of truth
  const {
    searchTerm,
    selectedState,
    selectedCounty,
    dateFrom,
    dateTo,
    categories,
    tags,
    sortBy,
    perPage,
    gridView,
    currentPage,
    isLoading,
    loadingFields,
    setSearchTerm,
    setSelectedState,
    setSelectedCounty,
    setDateFrom,
    setDateTo,
    setCategories,
    setTags,
    addTag,
    removeTag,
    setSortBy,
    setPerPage,
    setGridView,
    setCurrentPage,
    clearAllFilters: clearAllFiltersStore,
    clearAllLoadingStates,
    setDataLoadingStates,
    toUrlParams: _toUrlParams
  } = useFilterStore()

  // Tag selection functions
  const toggleTag = (tag: string) => {
    if (tags.includes(tag)) {
      removeTag(tag)
    } else {
      addTag(tag)
    }
  }
  
  // No need for loadUserProfile since server provides location-aware data
  
  // Local state for date pickers - initialize from server props
  const [dateFromState, setDateFromState] = useState<Date | undefined>(
    initialDateFrom ? new Date(initialDateFrom) : undefined
  )
  const [dateToState, setDateToState] = useState<Date | undefined>(
    initialDateTo ? new Date(initialDateTo) : undefined
  )
  
  // Combobox states
  const [sortOpen, setSortOpen] = useState(false)
  const [perPageOpen, setPerPageOpen] = useState(false)
  const [fromDateOpen, setFromDateOpen] = useState(false)
  const [toDateOpen, setToDateOpen] = useState(false)
  
  // Ref to track initialization state to prevent infinite loops
  const isInitialized = useRef(false)
  const [isClient, setIsClient] = useState(false)
  
  // Hydration safety
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Initialize filters from server props on mount - no need to call loadUserProfile since server handled it
  useEffect(() => {
    // Initialize store with server-provided values (which include user location if applied server-side)
    setSelectedState(initialSelectedState === 'all-states' ? '' : initialSelectedState)
    setSelectedCounty(initialSelectedCounty === 'all-counties' ? '' : initialSelectedCounty)
    setSearchTerm(initialSearchTerm)
    setDateFrom(initialDateFrom)
    setDateTo(initialDateTo)
    setCategories(initialCategories)
    setTags(initialTags) // Initialize tags
    setSortBy(initialSortBy as 'newest' | 'top-rated' | 'most-viewed')
    setPerPage(initialPerPage)
    setGridView(initialGridView as 'large' | 'compact')
    setCurrentPage(initialCurrentPage)
    
    // Update local date picker states
    setDateFromState(initialDateFrom ? new Date(initialDateFrom) : undefined)
    setDateToState(initialDateTo ? new Date(initialDateTo) : undefined)
    
    // Note: No need to call loadUserProfile - server already provided location-aware data
  }, [
    initialSelectedState, initialSelectedCounty, initialSearchTerm,
    initialDateFrom, initialDateTo, initialCategories, initialTags, initialSortBy,
    initialPerPage, initialGridView, initialCurrentPage,
    // Store setters - these are stable references from Zustand but ESLint requires them
    setSelectedState, setSelectedCounty, setSearchTerm, setDateFrom, setDateTo,
    setCategories, setTags, setSortBy, setPerPage, setGridView, setCurrentPage
  ])
  
  // Sync store changes to URL with debouncing to prevent multiple API calls
  useEffect(() => {
    // Skip URL sync until initialization is complete or if we're in SSR
    if (!isInitialized.current || !isClient) {
      isInitialized.current = true
      return
    }
    
    // Debounce URL updates to prevent rapid-fire API calls
    const debounceTimer = setTimeout(() => {
      // Create URLSearchParams inline to avoid function dependency
      const params = new URLSearchParams()
      
      if (searchTerm) params.set('search', searchTerm)
      if (selectedState && selectedState !== 'all-states') {
        params.set('state', selectedState)
      }
      if (selectedCounty && selectedCounty !== 'all-counties') {
        params.set('county', selectedCounty)
      }
      if (dateFrom) params.set('dateFrom', dateFrom)
      if (dateTo) params.set('dateTo', dateTo)
      if (categories.length > 0) {
        params.set('categories', categories.join(','))
      }
      if (tags.length > 0) {
        params.set('tags', tags.join(','))
      }
      if (sortBy !== 'newest') params.set('sort', sortBy)
      if (perPage !== 12) params.set('perPage', perPage.toString())
      if (gridView !== 'large') params.set('gridView', gridView)
      if (currentPage !== 1) params.set('page', currentPage.toString())
      
      const newUrl = params.toString() ? `${pathname}?${params.toString()}` : pathname
      
      // Only update if URL is different (avoid infinite loops)
      const currentSearch = window.location.search
      const newSearch = params.toString() ? `?${params.toString()}` : ''
      
      // More precise comparison to prevent infinite loops
      if (currentSearch !== newSearch && window.location.pathname === pathname) {
        router.push(newUrl, { scroll: false })
      }
    }, 300) // 300ms debounce
    
    return () => clearTimeout(debounceTimer)
  }, [searchTerm, selectedState, selectedCounty, dateFrom, dateTo, categories, tags, sortBy, perPage, gridView, currentPage, router, pathname, isClient]) // CRITICAL FIX: Removed toUrlParams function dependency
  
  // Note: Debug logs removed for production performance

  // Auto-clear loading states when URL actually changes (server re-renders)
  useEffect(() => {
    // Reset all loading states when the page re-renders with new data
    // Use the new clearAllLoadingStates function for consistency
    clearAllLoadingStates()
  }, [initialSearchTerm, initialSelectedState, initialSelectedCounty, initialDateFrom, initialDateTo, initialCategories, initialSortBy, initialPerPage, initialGridView, initialCurrentPage, clearAllLoadingStates])
  // clearAllLoadingStates is a stable function reference from Zustand store



  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    // NEW: Use coordinated loading state management
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const handleStateChange = (state: string) => {
    setSelectedState(state === 'all-states' ? '' : state)
    
    // NEW: If selecting all-states, also clear county
    if (state === 'all-states') {
      setSelectedCounty('')
    }
    
    // NEW: Use coordinated loading state management
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const handleCountyChange = (county: string) => {
    setSelectedCounty(county === 'all-counties' ? '' : county)
    // NEW: Use coordinated loading state management
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const handleDateChange = (type: 'from' | 'to', date: Date | undefined) => {
    if (type === 'from') {
      setDateFromState(date)
      setDateFrom(date ? format(date, 'yyyy-MM-dd') : '')
      
      // If setting from date and to date is not set, default to same date as from date
      if (date && !dateToState) {
        setDateToState(date)
        setDateTo(format(date, 'yyyy-MM-dd'))
      }
      
      // If from date is after to date, adjust to date to from date
      if (date && dateToState && date > dateToState) {
        setDateToState(date)
        setDateTo(format(date, 'yyyy-MM-dd'))
      }
    } else {
      // Validate to date is not before from date
      if (dateFromState && date && date < dateFromState) {
        // Silently adjust to_date to from_date if invalid
        setDateToState(dateFromState)
        setDateTo(format(dateFromState, 'yyyy-MM-dd'))
      } else {
        setDateToState(date)
        setDateTo(date ? format(date, 'yyyy-MM-dd') : '')
      }
    }
    // NEW: Use coordinated loading state management
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const handleSortChange = (newSort: string) => {
    setSortBy(newSort as 'newest' | 'top-rated' | 'most-viewed')
    // NEW: Use coordinated loading state management for immediate skeleton display
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const handlePerPageChange = (newPerPage: number) => {
    setPerPage(newPerPage)
    // NEW: Use coordinated loading state management for immediate skeleton display
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const handleGridViewChange = (view: string) => {
    setGridView(view as 'large' | 'compact')
    // NEW: Use coordinated loading state management for immediate skeleton display
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

  const clearAllFilters = () => {
    clearAllFiltersStore()
    // NEW: Use coordinated loading state management
    setDataLoadingStates(true)
    // Loading states will be auto-cleared when URL changes and page re-renders
  }

    // Don't show anything special during initialization - filters load instantly

  return (
    <>
      {/* Mobile Layout: Sorting Tabs + Search + Filter Button */}
      <div className="md:hidden mb-8">
        <div className="card-neon">
          <div className="space-y-4">
            {/* Sorting Tabs - Centered above search */}
            <div className="flex justify-center">
              <Tabs 
                value={sortBy} 
                onValueChange={(value) => handleSortChange(value)}
                className="w-fit"
              >
                <TabsList className="bg-gray-800/50 border border-cyan-500/30">
                  <TabsTrigger 
                    value="newest" 
                    className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-pink-600 data-[state=active]:text-white text-cyan-400 hover:bg-cyan-500/20"
                  >
                    Newest
                  </TabsTrigger>
                  <TabsTrigger 
                    value="top-rated" 
                    className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-pink-600 data-[state=active]:text-white text-cyan-400 hover:bg-cyan-500/20"
                  >
                    Top Rated
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Search Input + Filter Button Row */}
            <div className="flex gap-4 items-end">
              {/* Search Input */}
              <div className="flex-1 space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-300 block">Search by Name</label>
                  {loadingFields.search && (
                    <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
                  )}
                </div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Enter name..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="input-neon pl-10 bg-background"
                  />
                </div>
              </div>

              {/* Mobile Filter Sheet */}
              <div className="flex-shrink-0">
                <MobileFiltersSheet
                  searchTerm={searchTerm}
                  selectedState={selectedState}
                  selectedCounty={selectedCounty}
                  dateFrom={dateFrom}
                  dateTo={dateTo}
                  categories={categories}
                  tags={tags}
                  sortBy={sortBy}
                  perPage={perPage}
                  gridView={gridView}
                  loadingFields={loadingFields}
                  handleSearchChange={handleSearchChange}
                  handleStateChange={handleStateChange}
                  handleCountyChange={handleCountyChange}
                  handleDateChange={handleDateChange}
                  handleSortChange={handleSortChange}
                  handlePerPageChange={handlePerPageChange}
                  handleGridViewChange={handleGridViewChange}
                  toggleCategory={toggleTag}
                  clearAllFilters={clearAllFilters}
                  dateFromState={dateFromState}
                  dateToState={dateToState}
                  setDateFromState={setDateFromState}
                  setDateToState={setDateToState}
                  gridContainerRef={gridContainerRef}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout: Full Filters */}
      <div className="hidden md:block card-neon mb-8">
        <div className="space-y-6">
          {/* First Row - Search, Location, and Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 lg:gap-6">
          {/* Search Input */}
          <div className="lg:col-span-2 space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium mb-2 text-gray-300 block">Search by Name</label>
              {loadingFields.search && (
                <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
              )}
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Enter name..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="input-neon pl-10 bg-background"
              />
            </div>
          </div>

          {/* Location Dropdown */}
          <div className="md:col-span-2 lg:col-span-2">
            <LocationDropdown
              selectedState={selectedState || 'all-states'}
              setSelectedState={handleStateChange}
              selectedCounty={selectedCounty || 'all-counties'}
              setSelectedCounty={handleCountyChange}
              allStatesOption={true}
              allCountiesOption={true}
              className="w-full"
              isLoading={loadingFields.location}
            />
          </div>

          {/* Date Range */}
          <div className="md:col-span-2 lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium mb-2 text-gray-300 block">From Date</label>
                {loadingFields.date && (
                  <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
                )}
              </div>
              <Popover open={fromDateOpen} onOpenChange={setFromDateOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "input-neon w-full justify-start text-left font-normal",
                      !dateFromState && "text-gray-400"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFromState ? format(dateFromState, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl" align="start">
                  <Calendar
                    mode="single"
                    selected={dateFromState}
                    onSelect={(date) => {
                      handleDateChange('from', date)
                      setFromDateOpen(false)
                    }}
                    disabled={(date) => date > new Date()} // Disable future dates
                    initialFocus
                    className="border-none"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium mb-2 text-gray-300 block">To Date</label>
                {loadingFields.date && (
                  <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
                )}
              </div>
              <Popover open={toDateOpen} onOpenChange={setToDateOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    disabled={!dateFromState} // Disable if no from date selected
                    className={cn(
                      "input-neon w-full justify-start text-left font-normal",
                      (!dateToState || !dateFromState) && "text-gray-400",
                      !dateFromState && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateToState ? format(dateToState, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl" align="start">
                  <Calendar
                    mode="single"
                    selected={dateToState}
                    onSelect={(date) => {
                      handleDateChange('to', date)
                      setToDateOpen(false)
                    }}
                    disabled={(date) => {
                      // Disable future dates and dates before from date
                      const today = new Date()
                      if (date > today) return true
                      if (dateFromState && date < dateFromState) return true
                      return false
                    }}
                    initialFocus
                    className="border-none"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Second Row - Sort & Per Page, Categories, Grid View */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 lg:gap-6 items-end">
          {/* Sort By and Per Page */}
          <div className="lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Sort By Combobox */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium mb-2 text-gray-300 block">Sort by</label>
                {loadingFields.sort && (
                  <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
                )}
              </div>
              <Popover open={sortOpen} onOpenChange={setSortOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={sortOpen}
                    className="input-neon w-full justify-between"
                  >
                    {sortOptions.find(option => option.value === sortBy)?.label}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl" align="start">
                  <Command className="bg-transparent">
                    <CommandList className="bg-transparent">
                      <CommandGroup className="bg-transparent">
                        {sortOptions.map((option) => (
                          <CommandItem
                            key={option.value}
                            onSelect={() => {
                              handleSortChange(option.value)
                              setSortOpen(false)
                            }}
                            className="text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer"
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4 text-cyan-400",
                                sortBy === option.value ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {option.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Per Page Combobox */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium mb-2 text-gray-300 block">Per Page</label>
                {loadingFields.perPage && (
                  <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
                )}
              </div>
              <Popover open={perPageOpen} onOpenChange={setPerPageOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={perPageOpen}
                    className="input-neon w-full justify-between"
                  >
                    {perPage}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0 bg-gray-800/90 border-cyan-500/30 shadow-xl" align="start">
                  <Command className="bg-transparent">
                    <CommandList className="bg-transparent">
                      <CommandGroup className="bg-transparent">
                        {perPageOptions.map((option) => (
                          <CommandItem
                            key={option}
                            onSelect={() => {
                              handlePerPageChange(option)
                              setPerPageOpen(false)
                            }}
                            className="text-white hover:bg-cyan-500/20 data-[selected=true]:bg-cyan-500/30 cursor-pointer"
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4 text-cyan-400",
                                perPage === option ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {option}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Category Filter */}
          <div className="md:col-span-2 lg:col-span-3 space-y-2">
            <div className="flex items-center justify-center gap-4">
              <label className="text-sm font-medium mb-2 text-gray-300 block">Tags</label>
              {loadingFields.categories && (
                <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
              )}
              {tags.length > 0 && (
                <button
                  onClick={() => {
                    setTags([])
                    // NEW: Use coordinated loading state management
                    setDataLoadingStates(true)
                    // Loading states will be auto-cleared when URL changes and page re-renders
                  }}
                  className="text-xs text-cyan-400 hover:text-cyan-300 transition-colors flex items-center gap-1"
                  disabled={loadingFields.categories}
                >
                  {loadingFields.categories && <Loader2 className="h-3 w-3 animate-spin" />}
                  Clear all categories
                </button>
              )}
            </div>
            <div className="flex flex-wrap gap-2 justify-center">
              {Object.entries(categoryEmojis).map(([category, emoji]) => (
                <button
                  key={category}
                  onClick={() => toggleTag(category)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300 ${
                    tags.includes(category)
                      ? "bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-lg shadow-pink-500/30 scale-105"
                      : "bg-gray-800/90 border border-cyan-500/30 text-white hover:bg-gray-700/90 hover:border-cyan-500/50 hover:shadow-lg hover:shadow-cyan-500/20"
                  }`}
                >
                  <span className="text-lg">{emoji}</span>
                  <span>{category}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Grid View */}
          <div className="lg:col-span-1 space-y-2">
            <div className="flex items-center justify-center lg:justify-end gap-4">
              <label className="text-sm font-medium mb-2 text-gray-300 block">Grid View</label>
              {loadingFields.gridView && (
                <Loader2 className="h-3 w-3 text-cyan-400 animate-spin" />
              )}
            </div>
            <div className="flex gap-2 justify-center lg:justify-end">
              <Button
                variant={gridView === "large" ? "default" : "outline"}
                size="icon"
                onClick={() => handleGridViewChange("large")}
                className={`${
                  gridView === "large"
                    ? "bg-pink-600 hover:bg-pink-700 text-white border-pink-600"
                    : "border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/20 hover:border-cyan-400"
                }`}
              >
                <Grid2X2 className="h-4 w-4" />
              </Button>
              <Button
                variant={gridView === "compact" ? "default" : "outline"}
                size="icon"
                onClick={() => handleGridViewChange("compact")}
                className={`${
                                      gridView === "compact"
                    ? "bg-pink-600 hover:bg-pink-700 text-white border-pink-600"
                    : "border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/20 hover:border-cyan-400"
                }`}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Clear All Filters Button */}
        <div className="flex justify-center pt-4">
          <Button
            variant="outline"
            onClick={clearAllFilters}
            className="border-red-500/30 text-red-400 hover:bg-red-500/20 hover:border-red-400"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : null}
            Clear All Filters
          </Button>
        </div>
        </div>
      </div>
    </>
  )
} 