config:
  target: 'http://localhost:3000'
  phases:
    # Aggressive load test specifically for mugshots API
    # Simulating 10,000 concurrent users over 10 minutes
    - duration: 60
      arrivalRate: 50
      name: "Initial ramp-up"
    - duration: 120
      arrivalRate: 100
      rampTo: 300
      name: "Heavy ramp-up to peak load"
    - duration: 300
      arrivalRate: 300
      name: "Peak load - 10k concurrent users"
    - duration: 120
      arrivalRate: 300
      rampTo: 50
      name: "Gradual cool-down"
  
  # Strict performance requirements for API benchmarking
  ensure:
    # Response time SLAs
    - http.response_time.p95: 1500   # 95% under 1.5 seconds
    - http.response_time.p99: 3000   # 99% under 3 seconds
    - http.response_time.median: 300 # Median under 300ms
    
    # Error rate requirements
    - http.codes.200: 97  # At least 97% success rate
    - http.codes.4xx: 2   # Less than 2% client errors
    - http.codes.5xx: 1   # Less than 1% server errors
    
    # Throughput requirements
    - http.request_rate: 200  # Minimum 200 requests per second

  http:
    timeout: 20
    pool: 100
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  # Primary scenario: Heavy load on mugshots API with realistic filters
  - name: "Mugshots API Heavy Load Test"
    weight: 100
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
            sortBy: "{{ sortBy }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
            includeTotal: "true"
          name: "GET /api/mugshots - Heavy Load"
          capture:
            - json: "$.success"
              as: "requestSuccess"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
            - json: "$.data.mugshots.length"
              as: "resultCount"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
      
      # Minimal think time for maximum load
      - think: 0.5

processor: "./load-tests/scenarios/data-generators.js"
