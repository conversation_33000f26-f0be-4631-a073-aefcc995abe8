import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, RenderOptions } from '@testing-library/react'
import { ReactElement, ReactNode } from 'react'

// Create a clean query client for each test
function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false, // Disable retry in tests
        gcTime: 0, // No cache in tests (updated from deprecated cacheTime)
        staleTime: 0, // Always consider data stale in tests
      },
      mutations: {
        retry: false,
      },
    },
  })
}

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

// Custom render function that includes QueryClient provider
export function renderWithQuery(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient = createTestQueryClient(), ...renderOptions } = options

  function Wrapper({ children }: { children: ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}

// Mock successful API responses for testing
export const mockApiResponses = {
  mugshots: {
    success: true,
    data: {
      mugshots: [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          imagePath: '/test-image.jpg',
          dateOfBooking: '2024-01-01',
          countyOfBooking: 'Test County',
          stateOfBooking: 'TS',
          charges: 'Test Charge',
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 5,
        totalCount: 50,
        perPage: 12,
      }
    }
  },
  mugshotDetail: {
    success: true,
    data: {
      mugshot: {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        imagePath: '/test-image.jpg',
        dateOfBooking: '2024-01-01',
        countyOfBooking: 'Test County',
        stateOfBooking: 'TS',
        charges: 'Test Charge',
      },
      ratings: {
        averageRating: 7.5,
        totalRatings: 10,
      },
      tags: {
        tagCounts: {
          'funny': 5,
          'crazy': 3,
        }
      }
    }
  },
  userMugshotData: {
    success: true,
    data: {
      userRating: 8,
      userTags: ['funny'],
    }
  },
  ratingSubmit: {
    success: true,
    data: {
      averageRating: 7.6,
      totalRatings: 11,
    }
  },
  tagToggle: {
    success: true,
    data: {
      tagCounts: {
        'funny': 6,
        'crazy': 3,
      }
    }
  }
}

// Mock error responses for testing
export const mockApiErrors = {
  networkError: {
    success: false,
    message: 'Network error occurred',
    error: 'NETWORK_ERROR'
  },
  authError: {
    success: false,
    message: 'Authentication required',
    error: 'UNAUTHENTICATED'
  },
  notFound: {
    success: false,
    message: 'Resource not found',
    error: 'NOT_FOUND'
  }
}

// Helper to create MSW handlers for API endpoints
export function createMockHandlers() {
  // This will be expanded when we integrate MSW for more comprehensive testing
  return []
}

// Test utilities for query assertions
export const queryTestUtils = {
  // Wait for query to finish loading
  waitForQueryToFinish: async (queryClient: QueryClient, queryKey: unknown[]) => {
    await queryClient.getQueryCache().find({ queryKey })?.promise
  },
  
  // Get query data from cache
  getQueryData: (queryClient: QueryClient, queryKey: unknown[]) => {
    return queryClient.getQueryData(queryKey)
  },
  
  // Set query data in cache (for mocking)
  setQueryData: (queryClient: QueryClient, queryKey: unknown[], data: unknown) => {
    queryClient.setQueryData(queryKey, data)
  },
  
  // Clear all queries
  clearQueries: (queryClient: QueryClient) => {
    queryClient.clear()
  },
} 