"use client"

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, X, Link, Home } from 'lucide-react'

export default function AccountLinkedNotification() {
  const [isVisible, setIsVisible] = useState(false)
  const searchParams = useSearchParams()

  useEffect(() => {
    // Check if user just completed account linking
    const linked = searchParams.get('account_linked')
    
    if (linked === 'true') {
      setIsVisible(true)
      
      // Auto-hide after 10 seconds
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, 10000)
      
      return () => clearTimeout(timer)
    }
  }, [searchParams])

  const handleDismiss = () => {
    setIsVisible(false)
    
    // Clean up URL parameters
    const url = new URL(window.location.href)
    url.searchParams.delete('account_linked')
    url.searchParams.delete('link_message')
    window.history.replaceState({}, '', url.toString())
  }

  if (!isVisible) return null

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <Card className="bg-green-900/90 border-green-500/30 shadow-2xl backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0 mt-0.5" />
            
            <div className="flex-1">
              <h4 className="font-semibold text-green-100 mb-1">
                Accounts Successfully Linked!
              </h4>
              
              <p className="text-sm text-green-200 mb-3">
                Your Google account has been linked to your existing America&apos;s Top Mugshots account. 
                All your previous data, including your home location and preferences, has been preserved.
              </p>
              
              <div className="flex items-center gap-2 text-xs text-green-300">
                <Link className="w-4 h-4" />
                <span>You can now sign in with either email/password or Google</span>
              </div>
              
              <div className="flex items-center gap-2 text-xs text-green-300 mt-1">
                <Home className="w-4 h-4" />
                <span>Your home location filters are still active</span>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="text-green-300 hover:text-green-100 hover:bg-green-800/50 p-1"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="mt-3 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDismiss}
              className="text-green-300 border-green-500/30 hover:bg-green-800/50"
            >
              Got it!
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 