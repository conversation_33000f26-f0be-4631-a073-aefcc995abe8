import { describe, it, expect, vi, beforeEach, MockedFunction } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { createClient } from '@supabase/supabase-js'
import { mugshotsService } from '@/lib/services/mugshots-service'
import { transformDBMugshotsToUI } from '@/lib/utils/mugshot-transforms'

// Mock Next.js components
vi.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: vi.fn().mockReturnValue(null)
  }),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn()
  })
}))

vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />
}))

// Mock Supabase client
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn()
}))

// Mock the service
vi.mock('@/lib/services/mugshots-service', () => ({
  mugshotsService: {
    getMugshots: vi.fn(),
    getMugshotCount: vi.fn(),
    healthCheck: vi.fn()
  }
}))

// Sample database data that would come from Supabase
const mockDBMugshots = [
  {
    id: 1,
    created_at: '2024-01-01T00:00:00Z',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBooking: '2024-01-01',
    stateOfBooking: 'California',
    countyOfBooking: 'Los Angeles',
    offenseDescription: 'Public intoxication, Disorderly conduct',
    additionalDetails: 'Additional info here',
    imagePath: '/images/mugshot-1.jpg'
  },
  {
    id: 2,
    created_at: '2024-01-02T00:00:00Z',
    firstName: 'Jane',
    lastName: 'Smith',
    dateOfBooking: '2024-01-02',
    stateOfBooking: 'Texas',
    countyOfBooking: 'Harris',
    offenseDescription: 'DUI',
    additionalDetails: null,
    imagePath: '/images/mugshot-2.jpg'
  }
]

// Create a test version of the mugshots page component
const TestMugshotsPageContent = () => {
  const [mugshots, setMugshots] = React.useState([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState(null)

  React.useEffect(() => {
    const loadMugshots = async () => {
      try {
        setLoading(true)
        const dbMugshots = await mugshotsService.getMugshots()
        const uiMugshots = transformDBMugshotsToUI(dbMugshots)
        setMugshots(uiMugshots)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    loadMugshots()
  }, [])

  if (loading) return <div data-testid="loading">Loading...</div>
  if (error) return <div data-testid="error">Error: {error}</div>
  if (mugshots.length === 0) return <div data-testid="empty">No mugshots found</div>

  return (
    <div data-testid="mugshots-grid">
      {mugshots.map((mugshot: any) => (
        <div key={mugshot.id} data-testid={`mugshot-${mugshot.id}`}>
          <h3>{mugshot.name}</h3>
          <p>{mugshot.location}</p>
          <p>{mugshot.arrestDate}</p>
          <p>{mugshot.offenses.join(', ')}</p>
        </div>
      ))}
    </div>
  )
}

// Mock React hooks
const React = {
  useState: vi.fn(),
  useEffect: vi.fn()
}

describe('Mugshots Page Integration with Real Data', () => {
  let mockSetMugshots: MockedFunction<any>
  let mockSetLoading: MockedFunction<any>
  let mockSetError: MockedFunction<any>

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock React hooks
    mockSetMugshots = vi.fn()
    mockSetLoading = vi.fn()
    mockSetError = vi.fn()

    // Mock useState returns
    React.useState
      .mockReturnValueOnce([[], mockSetMugshots]) // mugshots state
      .mockReturnValueOnce([true, mockSetLoading]) // loading state
      .mockReturnValueOnce([null, mockSetError]) // error state

    // Mock useEffect to call the callback immediately
    React.useEffect.mockImplementation((callback) => callback())
  })

  it('should load and transform real mugshots data successfully', async () => {
    // Mock successful data fetch
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    const component = TestMugshotsPageContent()

    // Verify the service was called
    expect(mugshotsService.getMugshots).toHaveBeenCalledWith()

    // Verify loading state was set
    expect(mockSetLoading).toHaveBeenCalledWith(true)

    // Verify data transformation and state update
    await waitFor(() => {
      expect(mockSetMugshots).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            id: 1,
            name: 'John Doe',
            location: 'Los Angeles, California',
            arrestDate: '2024-01-01',
            offenses: ['Public intoxication', 'Disorderly conduct']
          }),
          expect.objectContaining({
            id: 2,
            name: 'Jane Smith',
            location: 'Harris, Texas',
            arrestDate: '2024-01-02',
            offenses: ['DUI']
          })
        ])
      )
    })

    // Verify loading was turned off
    expect(mockSetLoading).toHaveBeenCalledWith(false)
  })

  it('should handle service errors gracefully', async () => {
    const errorMessage = 'Database connection failed'
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockRejectedValue(new Error(errorMessage))

    TestMugshotsPageContent()

    await waitFor(() => {
      expect(mockSetError).toHaveBeenCalledWith(errorMessage)
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })
  })

  it('should handle empty results', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue([])

    TestMugshotsPageContent()

    await waitFor(() => {
      expect(mockSetMugshots).toHaveBeenCalledWith([])
      expect(mockSetLoading).toHaveBeenCalledWith(false)
    })
  })

  it('should handle service with filters and pagination', async () => {
    const filters = {
      searchTerm: 'john',
      state: 'California'
    }
    const sort = { sortBy: 'newest' as const }
    const pagination = { page: 1, perPage: 12 }

    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    // Test service call with parameters
    await mugshotsService.getMugshots(filters, sort, pagination)

    expect(mugshotsService.getMugshots).toHaveBeenCalledWith(filters, sort, pagination)
  })

  it('should verify data transformation preserves all required fields', () => {
    const transformed = transformDBMugshotsToUI(mockDBMugshots)

    expect(transformed).toHaveLength(2)
    
    // Verify first mugshot transformation
    expect(transformed[0]).toEqual(expect.objectContaining({
      id: 1,
      name: 'John Doe',
      location: 'Los Angeles, California',
      state: 'California',
      county: 'Los Angeles',
      arrestDate: '2024-01-01',
      image: '/images/mugshot-1.jpg',
      offenses: ['Public intoxication', 'Disorderly conduct'],
      category: expect.any(String),
      rating: 0,
      votes: 0,
      views: 0
    }))

    // Verify second mugshot transformation
    expect(transformed[1]).toEqual(expect.objectContaining({
      id: 2,
      name: 'Jane Smith',
      location: 'Harris, Texas',
      state: 'Texas',
      county: 'Harris',
      arrestDate: '2024-01-02',
      image: '/images/mugshot-2.jpg',
      offenses: ['DUI'],
      category: expect.any(String),
      rating: 0,
      votes: 0,
      views: 0
    }))
  })
})

describe('Service Integration with Filters', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should handle search term filtering', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    const filters = { searchTerm: 'john' }
    await mugshotsService.getMugshots(filters)

    expect(mugshotsService.getMugshots).toHaveBeenCalledWith(
      expect.objectContaining({ searchTerm: 'john' })
    )
  })

  it('should handle location filtering', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    const filters = { 
      state: 'California',
      county: 'Los Angeles'
    }
    await mugshotsService.getMugshots(filters)

    expect(mugshotsService.getMugshots).toHaveBeenCalledWith(
      expect.objectContaining({ 
        state: 'California',
        county: 'Los Angeles'
      })
    )
  })

  it('should handle date range filtering', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    const filters = {
      dateFrom: '2024-01-01',
      dateTo: '2024-01-31'
    }
    await mugshotsService.getMugshots(filters)

    expect(mugshotsService.getMugshots).toHaveBeenCalledWith(
      expect.objectContaining({
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31'
      })
    )
  })

  it('should handle sorting options', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    const sortOptions = [
      { sortBy: 'newest' as const },
      { sortBy: 'top-rated' as const },
      { sortBy: 'most-viewed' as const }
    ]

    for (const sort of sortOptions) {
      await mugshotsService.getMugshots({}, sort)
      expect(mugshotsService.getMugshots).toHaveBeenCalledWith(
        {},
        expect.objectContaining({ sortBy: sort.sortBy })
      )
    }
  })

  it('should handle pagination correctly', async () => {
    ;(mugshotsService.getMugshots as MockedFunction<any>).mockResolvedValue(mockDBMugshots)

    const paginationOptions = [
      { page: 1, perPage: 12 },
      { page: 2, perPage: 24 },
      { page: 3, perPage: 48 }
    ]

    for (const pagination of paginationOptions) {
      await mugshotsService.getMugshots({}, { sortBy: 'newest' }, pagination)
      expect(mugshotsService.getMugshots).toHaveBeenCalledWith(
        {},
        { sortBy: 'newest' },
        expect.objectContaining(pagination)
      )
    }
  })
}) 