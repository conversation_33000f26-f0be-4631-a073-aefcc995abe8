import { createClient } from '@/lib/supabase/server'
import type { TagType } from '@/lib/constants'

export interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  category?: string[] // Legacy field, will be deprecated
  tags?: TagType[]
}

export interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

export interface PaginationOptions {
  page: number
  perPage: number
}

export interface DatabaseMugshot {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
  average_rating?: number
  total_ratings?: number
  wild_count?: number
  funny_count?: number
  spooky_count?: number
  user_rating?: number | null
  user_tags?: string[]
}

class MugshotsNativeService {
  async getMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      const supabase = await createClient()
      const offset = (pagination.page - 1) * pagination.perPage
      let filteredIds: number[] | null = null

      if (sortOptions.sortBy === 'top-rated') {
        // Get all ratings and calculate averages in TypeScript for better type safety
        const { data: allRatings, error } = await supabase
          .from('ratings')
          .select('mugshot_id, rating')

        if (error || !allRatings) return []

        // Calculate averages and get top-rated mugshots
        const ratingMap: Record<number, { sum: number; count: number }> = {}

        allRatings.forEach((rating: { mugshot_id: number; rating: number }) => {
          if (!ratingMap[rating.mugshot_id]) {
            ratingMap[rating.mugshot_id] = { sum: 0, count: 0 }
          }
          ratingMap[rating.mugshot_id].sum += rating.rating
          ratingMap[rating.mugshot_id].count += 1
        })

        // Calculate averages and sort by rating
        const averages = Object.keys(ratingMap).map(mugshotIdStr => {
          const mugshotId = parseInt(mugshotIdStr, 10)
          const { sum, count } = ratingMap[mugshotId]
          return {
            mugshot_id: mugshotId,
            avg: sum / count
          }
        })

        // Sort by average rating descending and take top 1000
        averages.sort((a, b) => b.avg - a.avg)
        filteredIds = averages.slice(0, 1000).map(r => r.mugshot_id)
      }

      if (filters.tags && filters.tags.length > 0) {
        const { data: tagged, error } = await supabase
          .from('tags')
          .select('mugshot_id')
          .in('tag_type', filters.tags)

        if (error || !tagged) return []

        const tagIds = tagged.map((t: { mugshot_id: number }) => t.mugshot_id)
        filteredIds = filteredIds ? filteredIds.filter(id => tagIds.includes(id)) : tagIds

        if (filteredIds.length === 0) return []
      }

      let query = supabase
        .from('mugshots')
        .select(`
          id, created_at, firstName, lastName, dateOfBooking,
          stateOfBooking, countyOfBooking, offenseDescription,
          additionalDetails, imagePath, fb_status, adsText,
          jb_post_link, jb_fb_post
        `)
        .order('dateOfBooking', { ascending: false })
        .order('created_at', { ascending: false })

      if (filteredIds?.length) query = query.in('id', filteredIds)
      if (filters.searchTerm?.trim()) {
        const term = filters.searchTerm.trim()
        query = query.or(`firstName.ilike.%${term}%,lastName.ilike.%${term}%`)
      }
      if (filters.state && filters.state !== 'all-states') query = query.eq('stateOfBooking', filters.state)
      if (filters.county && filters.county !== 'all-counties') query = query.eq('countyOfBooking', filters.county)
      if (filters.dateFrom) query = query.gte('dateOfBooking', filters.dateFrom)
      if (filters.dateTo) query = query.lte('dateOfBooking', filters.dateTo)

      const { data: mugshots, error } = await query.range(offset, offset + pagination.perPage - 1)
      if (error || !mugshots?.length) return []

      const ids = mugshots.map(m => m.id)

      const [ratings, tags, userRatings, userTags] = await Promise.all([
        supabase.from('ratings').select('mugshot_id, rating').in('mugshot_id', ids),
        supabase.from('tags').select('mugshot_id, tag_type').in('mugshot_id', ids),
        userId ? supabase.from('ratings').select('mugshot_id, rating').in('mugshot_id', ids).eq('user_id', userId) : Promise.resolve({ data: [] }),
        userId ? supabase.from('tags').select('mugshot_id, tag_type').in('mugshot_id', ids).eq('user_id', userId) : Promise.resolve({ data: [] })
      ])

      const ratingMap = Object.fromEntries(
        ids.map(id => {
          const r = ratings.data?.filter((x: { mugshot_id: number; rating: number }) => x.mugshot_id === id) || []
          const avg = r.reduce((sum: number, x: { rating: number }) => sum + x.rating, 0) / (r.length || 1)
          return [id, { average_rating: +avg.toFixed(2), total_ratings: r.length }]
        })
      )

      const tagMap = Object.fromEntries(
        ids.map(id => {
          const t = tags.data?.filter((x: { mugshot_id: number; tag_type: string }) => x.mugshot_id === id) || []
          return [id, t.reduce((acc: Record<string, number>, x: { tag_type: string }) => {
            acc[x.tag_type] = (acc[x.tag_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)]
        })
      )

      const userRatingMap = Object.fromEntries(userRatings.data?.map((r: { mugshot_id: number; rating: number }) => [r.mugshot_id, r.rating]) || [])
      const userTagMap = userTags.data?.reduce((acc: Record<number, string[]>, t: { mugshot_id: number; tag_type: string }) => {
        if (!acc[t.mugshot_id]) acc[t.mugshot_id] = []
        acc[t.mugshot_id].push(t.tag_type)
        return acc
      }, {} as Record<number, string[]>) || {}

      return mugshots.map(m => ({
        ...m,
        ...ratingMap[m.id],
        wild_count: tagMap[m.id]?.wild || 0,
        funny_count: tagMap[m.id]?.funny || 0,
        spooky_count: tagMap[m.id]?.spooky || 0,
        user_rating: userRatingMap[m.id] || null,
        user_tags: userTagMap[m.id] || []
      }))
    } catch (err) {
      console.error('[getMugshots] Error:', err)
      return []
    }
  }

  async getMugshotCount(filters: MugshotFilters = {}): Promise<number> {
    try {
      const supabase = await createClient()
      let filteredIds: number[] | null = null

      if (filters.tags?.length) {
        const { data: tagged, error } = await supabase
          .from('tags')
          .select('mugshot_id')
          .in('tag_type', filters.tags)

        if (error || !tagged) return 0
        filteredIds = tagged.map((t: { mugshot_id: number }) => t.mugshot_id)
        if (filteredIds.length === 0) return 0
      }

      let query = supabase.from('mugshots').select('id', { count: 'exact', head: true })

      if (filteredIds) query = query.in('id', filteredIds)
      if (filters.searchTerm?.trim()) {
        const term = filters.searchTerm.trim()
        query = query.or(`firstName.ilike.%${term}%,lastName.ilike.%${term}%`)
      }
      if (filters.state && filters.state !== 'all-states') query = query.eq('stateOfBooking', filters.state)
      if (filters.county && filters.county !== 'all-counties') query = query.eq('countyOfBooking', filters.county)
      if (filters.dateFrom) query = query.gte('dateOfBooking', filters.dateFrom)
      if (filters.dateTo) query = query.lte('dateOfBooking', filters.dateTo)

      const { count, error } = await query
      if (error) return 0
      return count || 0
    } catch (err) {
      console.error('[getMugshotCount] Error:', err)
      return 0
    }
  }

  async getMugshotById(id: number, userId?: string): Promise<DatabaseMugshot | null> {
    try {
      const supabase = await createClient()

      const [{ data: mugshot }, { data: ratings }, { data: tags }, { data: userRatings }, { data: userTags }] = await Promise.all([
        supabase.from('mugshots').select('*').eq('id', id).single(),
        supabase.from('ratings').select('mugshot_id, rating').eq('mugshot_id', id),
        supabase.from('tags').select('mugshot_id, tag_type').eq('mugshot_id', id),
        userId ? supabase.from('ratings').select('mugshot_id, rating').eq('mugshot_id', id).eq('user_id', userId) : Promise.resolve({ data: [] }),
        userId ? supabase.from('tags').select('mugshot_id, tag_type').eq('mugshot_id', id).eq('user_id', userId) : Promise.resolve({ data: [] })
      ])

      if (!mugshot) return null

      const avgRating = ratings && ratings.length > 0
        ? ratings.reduce((sum: number, r: { rating: number }) => sum + r.rating, 0) / ratings.length
        : 0
      const tagCount = tags?.reduce((acc: Record<string, number>, t: { tag_type: string }) => {
        acc[t.tag_type] = (acc[t.tag_type] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}
      const userRating = userRatings?.[0]?.rating || null
      const userTagList = userTags?.map((t: { tag_type: string }) => t.tag_type) || []

      return {
        ...mugshot,
        average_rating: +avgRating.toFixed(2),
        total_ratings: ratings?.length || 0,
        wild_count: tagCount.wild || 0,
        funny_count: tagCount.funny || 0,
        spooky_count: tagCount.spooky || 0,
        user_rating: userRating,
        user_tags: userTagList
      }
    } catch (err) {
      console.error('[getMugshotById] Error:', err)
      return null
    }
  }
}

export const mugshotsNativeService = new MugshotsNativeService()