// Database types based on supabase migrations
// Generated from migrations: 003_create_ratings_and_tags_system.sql, 011_filter_functions.sql, 012_fix_tag_constraints.sql

import type { TagType } from '@/lib/constants'

// Re-export TagType from constants to maintain consistency
export type { TagType } from '@/lib/constants'

// Rating statistics interface for consistent typing across components
export interface RatingStatistics {
  averageRating: number
  totalRatings: number
}

// Tag statistics interface for consistent typing across components
export interface TagStatistics {
  wild: number
  funny: number
  spooky: number
  totalTags: number
}

export interface DatabaseMugshot {
  id: number // BIGSERIAL from production schema
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
}

export interface DatabaseRating {
  id: string // UUID
  mugshot_id: number // BIGINT
  user_id: string // UUID
  rating: number // 1-10
  created_at: string
  updated_at: string
}

export interface DatabaseTag {
  id: string // UUID
  mugshot_id: number // BIGINT
  user_id: string // UUID
  tag_type: TagType
  created_at: string
}

export interface DatabaseProfile {
  id: string // UUID
  user_id: string // UUID
  email: string | null
  full_name: string
  state: string | null
  county: string | null
  role: string
  avatar_url: string | null
  created_at: string
  updated_at: string
}

// Result type from search_filtered_mugshots function
export interface SearchFilteredMugshotsResult {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
  average_rating: number
  total_ratings: number
  tag_counts: Record<string, number> | Record<string, never> // JSONB from SQL function - can be empty object
}

// Function parameters for database calls
export interface SearchFilteredMugshotsParams {
  search_term?: string | null
  state_filter?: string | null
  county_filter?: string | null
  date_from?: string | null
  date_to?: string | null
  tags_filter?: TagType[] | null
  sort_by: 'newest' | 'top-rated'
  limit_count: number
  offset_count: number
}

export interface CountFilteredMugshotsParams {
  search_term?: string | null
  state_filter?: string | null
  county_filter?: string | null
  date_from?: string | null
  date_to?: string | null
  tags_filter?: TagType[] | null
}

// Enhanced mugshot with rating and tag data
export interface EnhancedDatabaseMugshot extends DatabaseMugshot {
  // Rating statistics
  average_rating: number
  total_ratings: number
  // Tag counts
  wild_count: number
  funny_count: number
  spooky_count: number
  // User-specific data (when user is authenticated)
  user_rating?: number | null
  user_tags?: TagType[]
}

// API request types
export interface RatingSubmissionRequest {
  mugshotId: number
  rating: number
}

// User-specific data types
export interface UserRatingData {
  userRating: number | null
}

// API response types
export interface TagToggleResponse {
  success: boolean
  action?: 'added' | 'removed'
  tag_type?: TagType
  error?: string
}

export interface RatingSubmissionResponse {
  success: boolean
  rating?: {
    mugshot_id: number
    rating: number
    user_id: string
  }
  error?: string
  message?: string
} 