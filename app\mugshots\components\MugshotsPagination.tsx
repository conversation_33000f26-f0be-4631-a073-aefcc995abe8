"use client"

import { useFilterStore } from "@/lib/stores/filter-store"
import {
  <PERSON><PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { cn } from "@/lib/utils"

type PaginationProps = {
  currentPage: number
  totalPages: number
  totalCount: number
}

export default function MugshotsPagination({
  currentPage,
  totalPages
}: PaginationProps) {
  const { setCurrentPage, setDataLoadingStates } = useFilterStore()

  const handlePageChange = (page: number) => {
    // Update global filter store first
    setCurrentPage(page)
    
    // NEW: Use coordinated loading state management for pagination
    setDataLoadingStates(true)
    
    // The filter store's URL sync effect will handle the URL update
    // Loading state will be auto-cleared when page re-renders with new data
  }

  // Smart pagination with ellipsis
  const getVisiblePages = () => {
    if (totalPages <= 1) return []
    
    const delta = 2 // Number of pages to show around current page
    const rangeWithDots = []
    
    // For small number of pages, show all
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        rangeWithDots.push(i)
      }
      return rangeWithDots
    }
    
    // Always show first page
    rangeWithDots.push(1)
    
    // Calculate range around current page
    const start = Math.max(2, currentPage - delta)
    const end = Math.min(totalPages - 1, currentPage + delta)
    
    // Add ellipsis after 1 if needed
    if (start > 2) {
      rangeWithDots.push('...')
    }
    
    // Add range around current page (excluding first and last)
    for (let i = start; i <= end; i++) {
      rangeWithDots.push(i)
    }
    
    // Add ellipsis before last if needed
    if (end < totalPages - 1) {
      rangeWithDots.push('...')
    }
    
    // Always show last page (if different from first)
    if (totalPages > 1) {
      rangeWithDots.push(totalPages)
    }
    
    return rangeWithDots
  }

  return (
    <div className="flex flex-col items-center gap-4 mb-12">
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => handlePageChange(currentPage - 1)}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
          
          {getVisiblePages().map((page, index) => (
            <PaginationItem key={`${page}-${index}`}>
              {page === '...' ? (
                <PaginationEllipsis />
              ) : (
                <PaginationLink
                  onClick={() => handlePageChange(page as number)}
                  isActive={currentPage === page}
                  className={cn(
                    "cursor-pointer",
                    currentPage === page && "bg-primary text-primary-foreground"
                  )}
                >
                  {page}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => handlePageChange(currentPage + 1)}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
      
      {/* Page Info */}
      <div className="text-gray-400 text-sm">
        Page {currentPage} of {totalPages}
      </div>
    </div>
  )
} 