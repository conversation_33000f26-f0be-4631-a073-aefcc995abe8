import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(_: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    
    // Validate mugshot ID
    const mugshotId = parseInt(id, 10)
    if (isNaN(mugshotId) || mugshotId <= 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid mugshot ID', error: 'INVALID_MUGSHOT_ID' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, message: 'Authentication required', error: 'UNAUTHENTICATED' },
        { status: 401 }
      )
    }
    
    // Get user's rating for this mugshot
    const { data: userRating, error: ratingError } = await supabase
      .from('ratings')
      .select('rating')
      .eq('mugshot_id', mugshotId)
      .eq('user_id', user.id)
      .maybeSingle()
    
    if (ratingError) {
      console.error('Error fetching user rating:', ratingError)
      return NextResponse.json(
        { success: false, message: 'Failed to fetch user rating', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: {
        userRating: userRating?.rating || null
      }
    })
    
  } catch (error) {
    console.error('User rating API error:', error)
    return NextResponse.json(
      { success: false, message: 'An unexpected error occurred', error: 'UNEXPECTED_ERROR' },
      { status: 500 }
    )
  }
} 