# Test 4: 100 Concurrent Users - High Load Test (Likely Breaking Point)
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 15
      name: "Warm-up: 15 users/sec"
    - duration: 300
      arrivalRate: 50
      name: "Steady: 50 users/sec (100 concurrent)"
    - duration: 60
      arrivalRate: 15
      name: "Cool-down: 15 users/sec"
  
  ensure:
    - http.response_time.p95: 3000   # 95% under 3 seconds
    - http.response_time.median: 800 # Median under 800ms
    - http.codes.200: 80             # 80% success rate (expect failures)
    - http.codes.5xx: 10             # Less than 10% server errors

  http:
    timeout: 30
    pool: 120
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "100 Users - Mugshots API"
    weight: 65
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - 100 users"
      - think: 1

  - name: "100 Users - Details API"
    weight: 35
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 100 users"
      - think: 1.5

processor: "../scenarios/data-generators.js"
