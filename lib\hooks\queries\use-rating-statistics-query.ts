import { useQuery, type QueryClient } from '@tanstack/react-query'
import type { RatingStatistics } from '@/lib/types/database'

/**
 * Rating statistics query hook - now only works with preloaded data
 * No API calls since /api/mugshots/[id]/rating-statistics doesn't exist
 * 
 * @param mugshotId - The ID of the mugshot
 * @param enabled - Not used anymore, kept for compatibility
 * @param initialData - Required preloaded data
 * @returns TanStack Query result with rating statistics
 */
export function useRatingStatisticsQuery(mugshotId: string, _enabled = true, initialData?: RatingStatistics) {
  return useQuery({
    queryKey: ['mugshot', mugshotId, 'rating-statistics'],
    queryFn: async (): Promise<RatingStatistics> => {
      // This should never run since we always provide initialData
      // If it does run, return default values instead of making API call
      console.warn(`[useRatingStatisticsQuery] No preloaded data for mugshot ${mugshotId}, using defaults`)
      return {
        averageRating: 0,
        totalRatings: 0
      }
    },
    enabled: false, // Never fetch from API
    initialData: initialData || { averageRating: 0, totalRatings: 0 }, // Always provide data
    staleTime: Infinity, // Never refetch
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })
}

/**
 * Prefetch utility for rating statistics - DEPRECATED
 * No longer needed since we don't make API calls
 */
export function prefetchRatingStatistics(
  queryClient: QueryClient,
  mugshotId: string,
  initialData?: RatingStatistics
) {
  // Just set the data directly in the cache
  if (initialData && queryClient.setQueryData) {
    queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], initialData)
  }
  return Promise.resolve()
}

/**
 * Hook to get cached rating statistics without triggering a fetch
 * Useful for components that only need cached data
 */
export function useCachedRatingStatistics(mugshotId: string): RatingStatistics | undefined {
  const { data } = useQuery<RatingStatistics>({
    queryKey: ['mugshot', mugshotId, 'rating-statistics'],
    enabled: false, // Don't fetch, only return cached data
  })
  
  return data
} 