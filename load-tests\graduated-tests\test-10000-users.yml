# Test 8: 10,000 Concurrent Users - Ultimate Stress Test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 200
      name: "Warm-up: 200 users/sec"
    - duration: 600
      arrivalRate: 1000
      name: "Ultimate Stress: 1000 users/sec (10,000 concurrent)"
    - duration: 60
      arrivalRate: 200
      name: "Cool-down: 200 users/sec"
  
  ensure:
    - http.response_time.p95: 30000  # 95% under 30 seconds
    - http.response_time.median: 10000 # Median under 10 seconds
    - http.codes.200: 5              # 5% success rate (expect total failure)
    - http.codes.5xx: 70             # Less than 70% server errors

  http:
    timeout: 120
    pool: 12000
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "10000 Users - Mugshots API"
    weight: 45
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - 10000 users"
      - think: 0.05

  - name: "10000 Users - Details API"
    weight: 55
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 10000 users"
      - think: 0.1

processor: "../scenarios/data-generators.js"
