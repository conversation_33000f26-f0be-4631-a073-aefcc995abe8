"use client"

import { useState } from "react"
// import Link from "next/link" // Unused import
import { Button } from "@/components/ui/button"
import TopMugshotsCarousel from "@/components/TopMugshotsCarousel"
import LocalMugshotsSection from "@/components/LocalMugshotsSection"
import EmailCaptureForm from "@/components/EmailCaptureForm"
import AnimatedNotoEmoji from "@/components/AnimatedNotoEmoji"
import Image from "next/image"
import UniversalNavLink from "@/components/UniversalNavLink"

type TimeFrame = "day" | "week" | "month" | "year"

export default function HomePage() {
  const [timeFrame, setTimeFrame] = useState<TimeFrame>("week")
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[600px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/images/hero-home.jpg"
            alt="America&apos;s Top Mugshots Hero"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/60" />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            <span className="text-white dark:text-white light:text-gray-900">AMERICA&apos;S</span>
            <br />
            <span className="text-glow-pink text-pink-500">TOP MUGSHOTS</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 dark:text-gray-300 light:text-gray-700 mb-8 max-w-2xl mx-auto">
            Vote, Rate & Discover the Most Notorious Faces Across America
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <UniversalNavLink href="/mugshots">
              <Button className="btn-glow-pink text-lg px-8 py-3">
                Browse All Mugshots
              </Button>
            </UniversalNavLink>
            <UniversalNavLink href="/popular">
              <Button variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500/10 text-lg px-8 py-3">
                Popular This Week
              </Button>
            </UniversalNavLink>
          </div>
        </div>
      </section>

      {/* Top Mugshots with Tabs and Carousel */}
      <section className="py-12 border-b border-gray-800">
        <div className="container mx-auto px-4 max-w-7xl">
          <TopMugshotsCarousel 
            timeFrame={timeFrame}
            onTimeFrameChange={setTimeFrame}
          />
        </div>
      </section>

      {/* Category Stats */}
      <section className="py-12 border-y border-gray-800">
        <div className="container mx-auto px-4 max-w-7xl">
          <h2 className="text-3xl font-bold text-center mb-12">
            <span className="text-foreground">Browse by</span> <span className="text-cyan-500">Category</span>
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { name: "Hot", count: "2,847", color: "text-red-500", icon: "🥵" },
              { name: "Funny", count: "1,923", color: "text-yellow-500", icon: "😂" },
              { name: "Wild", count: "3,156", color: "text-purple-500", icon: "🤪" },
              { name: "Scary", count: "1,634", color: "text-green-500", icon: "😱" }
            ].map((category) => (
              <UniversalNavLink key={category.name} href={`/mugshots?category=${category.name.toLowerCase()}`}>
                <div 
                  className="card-neon text-center hover:border-pink-500/50 transition-all duration-300"
                  onMouseEnter={() => setHoveredCategory(category.name)}
                  onMouseLeave={() => setHoveredCategory(null)}
                >
                  <div className="text-4xl mb-3 flex justify-center">
                    <AnimatedNotoEmoji 
                      emoji={category.icon} 
                      size={64}
                      isHovered={hoveredCategory === category.name}
                    />
                  </div>
                  <h3 className={`text-xl font-bold ${category.color} mb-2`}>{category.name}</h3>
                  <p className="text-2xl font-bold text-foreground">{category.count}</p>
                  <p className="text-muted-foreground text-sm">mugshots</p>
                </div>
              </UniversalNavLink>
            ))}
          </div>
        </div>
      </section>

      {/* Local Mugshots Discovery */}
      <section className="py-12">
        <div className="container mx-auto px-4 max-w-7xl">
          <LocalMugshotsSection />
        </div>
      </section>

      {/* Email Capture Forms */}
      <section className="py-12 border-t border-gray-800">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Join the Jury */}
            <EmailCaptureForm 
              variant="jury"
              title="Join the Jury"
              subtitle="Get notified about new mugshots in your state and help decide America&apos;s favorites"
              showStateSelection={true}
            />

            {/* Weekly Winners */}
            <EmailCaptureForm 
              variant="weekly"
              title="Weekly Winners"
              subtitle="Subscribe to get the top-rated mugshots delivered to your inbox every week"
              showStateSelection={true}
            />
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-16 text-center">
        <div className="container mx-auto px-4 max-w-7xl">
          <h2 className="text-4xl font-bold text-foreground mb-4">
            Ready to <span className="text-pink-500">Rate & Vote</span>?
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of users discovering and rating America&apos;s most interesting mugshots
          </p>
                      <UniversalNavLink href="/mugshots">
              <Button className="btn-glow-cyan text-lg px-12 py-4">
                Start Browsing Now
              </Button>
            </UniversalNavLink>
        </div>
      </section>
    </div>
  )
}
