# Gentle Baseline Test - Conservative Load for Connection Limits
# This tests your optimized queries with reasonable concurrency
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 30
      arrivalRate: 2
      name: "Warm-up: Gentle start"
    - duration: 120
      arrivalRate: 5
      name: "Steady: Conservative load"
    - duration: 30
      arrivalRate: 2
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 1500   # More realistic for Pro plan
    - http.response_time.median: 500 # Based on your 277ms query time
    - http.codes.200: 95             # High success rate expected
    - http.codes.5xx: 2              # Minimal server errors

  http:
    timeout: 15
    pool: 10  # Reduced connection pool
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Baseline - Gentle Load"
    weight: 100
    flow:
      - function: "generateBaselineFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "baseline"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
          name: "GET /api/mugshots-fast - Baseline (Gentle)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 2  # Longer think time

processor: "./load-tests/focused-tests/data-generators-focused.js"
