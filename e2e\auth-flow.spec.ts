import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display login page correctly', async ({ page }) => {
    await page.goto('/login')
    
    // Check page title and main elements
    await expect(page.locator('h1, h2, h3')).toContainText('Join the Jury')
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
  })

  test('should validate form fields', async ({ page }) => {
    await page.goto('/login')
    
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    // Should show validation errors
    await expect(page.locator('text=Email is required')).toBeVisible()
    await expect(page.locator('text=Password is required')).toBeVisible()
  })

  test('should show validation errors for invalid email', async ({ page }) => {
    await page.goto('/login')
    
    // Enter invalid email
    await page.fill('input[type="email"]', 'invalid-email')
    await page.fill('input[type="password"]', 'shortpw')
    
    // Try to submit
    await page.click('button[type="submit"]')
    
    // Should show validation errors
    await expect(page.locator('text=Invalid email')).toBeVisible()
    await expect(page.locator('text=Password must')).toBeVisible()
  })

  test('should toggle between login and signup modes', async ({ page }) => {
    await page.goto('/login')
    
    // Should start in login mode by default
    await expect(page.locator('input[name="confirmPassword"]')).not.toBeVisible()
    
    // Switch to signup mode (look for tab or toggle)
    const signupTab = page.locator('text=Sign Up').first()
    if (await signupTab.isVisible()) {
      await signupTab.click()
      
      // Should show additional signup fields
      await expect(page.locator('input[name="fullName"]')).toBeVisible()
      await expect(page.locator('input[name="confirmPassword"]')).toBeVisible()
    }
  })

  test('should require location selection for signup', async ({ page }) => {
    await page.goto('/login')
    
    // Switch to signup mode if needed
    const signupTab = page.locator('text=Sign Up').first()
    if (await signupTab.isVisible()) {
      await signupTab.click()
    }
    
    // Fill in required fields but leave location empty
    await page.fill('input[name="fullName"]', 'John Doe')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'Password123!')
    await page.fill('input[name="confirmPassword"]', 'Password123!')
    
    // Try to submit without location
    await page.click('button[type="submit"]')
    
    // Should show location validation error
    await expect(page.locator('text=Please select your state')).toBeVisible()
  })

  test('should redirect to return URL after login', async ({ page }) => {
    // Try to access protected route
    await page.goto('/mugshots')
    
    // Should redirect to login with return URL
    await expect(page).toHaveURL(/.*login.*returnUrl.*mugshots/)
    
    // Login form should be visible
    await expect(page.locator('input[type="email"]')).toBeVisible()
  })

  test('should handle logout correctly', async ({ page }) => {
    // First, assume we have a way to get to logout (header, menu, etc.)
    await page.goto('/mugshots')
    
    // Should redirect to login if not authenticated
    await expect(page).toHaveURL(/.*login/)
  })

  test('should show appropriate error page', async ({ page }) => {
    await page.goto('/error')
    
    // Should display error page with helpful information
    await expect(page.locator('text=Authentication Error')).toBeVisible()
    await expect(page.locator('text=Try Again')).toBeVisible()
    await expect(page.locator('text=Return Home')).toBeVisible()
  })

  test('should handle protected routes correctly', async ({ page }) => {
    // Test public routes - should NOT redirect to login
    const publicRoutes = ['/mugshots', '/popular', '/weekly-best']
    
    for (const route of publicRoutes) {
      await page.goto(route)
      
      // Should NOT redirect to login - pages should be publicly accessible
      await expect(page).not.toHaveURL(/.*login/)
    }

    // Test protected routes - should redirect to login
    const protectedRoutes = ['/profile']
    
    for (const route of protectedRoutes) {
      await page.goto(route)
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*login/)
      
      // Should have return URL parameter
      const url = page.url()
      expect(url).toContain('returnUrl')
    }
  })
})

test.describe('Form Validation', () => {
  test('should validate password requirements', async ({ page }) => {
    await page.goto('/login')
    
    // Switch to signup if needed
    const signupTab = page.locator('text=Sign Up').first()
    if (await signupTab.isVisible()) {
      await signupTab.click()
    }
    
    // Test weak password
    await page.fill('input[name="password"]', 'weak')
    await page.fill('input[name="confirmPassword"]', 'weak')
    await page.click('button[type="submit"]')
    
    // Should show password strength error
    await expect(page.locator('text=Password must')).toBeVisible()
  })

  test('should validate password confirmation match', async ({ page }) => {
    await page.goto('/login')
    
    // Switch to signup if needed  
    const signupTab = page.locator('text=Sign Up').first()
    if (await signupTab.isVisible()) {
      await signupTab.click()
    }
    
    // Enter mismatched passwords
    await page.fill('input[name="password"]', 'Password123!')
    await page.fill('input[name="confirmPassword"]', 'DifferentPassword123!')
    await page.click('button[type="submit"]')
    
    // Should show password mismatch error
    await expect(page.locator('text=Passwords do not match')).toBeVisible()
  })
}) 