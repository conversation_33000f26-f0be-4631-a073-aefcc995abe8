'use client'

import { ReactNode } from 'react'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

interface QueryDevtoolsProps {
  children: ReactNode
}

export function QueryDevtools({ children }: QueryDevtoolsProps) {
  return (
    <>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          buttonPosition="bottom-right"
        />
      )}
    </>
  )
} 