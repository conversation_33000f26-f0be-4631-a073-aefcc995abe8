# Realistic Mixed Queries Test - Real User Behavior
# Simulates actual user behavior with mixed filter combinations
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm-up: Mixed queries"
    - duration: 300
      arrivalRate: 12
      name: "Steady: Realistic mixed usage"
    - duration: 60
      arrivalRate: 5
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 2000   # Mixed performance
    - http.response_time.median: 500 # Mixed performance
    - http.codes.200: 90             # Good success rate
    - http.codes.5xx: 3              # Some server errors expected

  http:
    timeout: 20
    pool: 18
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Realistic Mixed Usage"
    weight: 100
    flow:
      - function: "generateMixedQueryFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
          name: "GET /api/mugshots - Realistic Mixed"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
      - think: 2

processor: "./load-tests/focused-tests/data-generators-focused.js"
