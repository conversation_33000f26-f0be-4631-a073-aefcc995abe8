-- Migration 006: Create Notification System
-- Description: Email notification preferences, tracking, and management system

-- Notification preferences for authenticated users
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email_enabled BOOLEAN DEFAULT true,
    
    -- Event-based notifications
    weekly_voting_alerts BOOLEAN DEFAULT true,
    daily_winners BOOLEAN DEFAULT true,
    monthly_championships BOOLEAN DEFAULT true,
    quarterly_legends BOOLEAN DEFAULT true,
    
    -- Location-based notifications
    local_state_updates BOOLEAN DEFAULT true,
    county_specific_alerts BO<PERSON>EAN DEFAULT true,
    
    -- Email frequency preferences
    email_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (email_frequency IN ('immediate', 'daily_digest', 'weekly_digest')),
    
    -- Location preferences for targeted content
    preferred_states TEXT[], -- Array of state names
    preferred_counties TEXT[], -- Array of county names
    
    -- Unsubscribe tokens for email management
    unsubscribe_token VARCHAR(100) UNIQUE DEFAULT gen_random_uuid()::text,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Email subscribers table (extending existing EmailCaptureForm functionality)
CREATE TABLE email_subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    subscription_type VARCHAR(50) NOT NULL, -- 'jury', 'weekly', 'events'
    state VARCHAR(100),
    county VARCHAR(100),
    source VARCHAR(50), -- 'homepage', 'popular_page', etc.
    is_active BOOLEAN DEFAULT true,
    confirmed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- Auto-confirm for now
    unsubscribe_token VARCHAR(100) UNIQUE DEFAULT gen_random_uuid()::text,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(email, subscription_type)
);

-- Email delivery tracking
CREATE TABLE email_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_email VARCHAR(255) NOT NULL,
    subject_line TEXT NOT NULL,
    email_type VARCHAR(50) NOT NULL, -- 'welcome', 'notification', 'winner_announcement'
    template_used VARCHAR(100),
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    external_message_id VARCHAR(255), -- For tracking with email service provider
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification queue for batching and scheduling
CREATE TABLE notification_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_type VARCHAR(50) NOT NULL, -- 'daily_winner', 'weekly_voting', 'monthly_champion'
    recipient_type VARCHAR(20) NOT NULL CHECK (recipient_type IN ('user', 'subscriber', 'admin')),
    recipient_id UUID, -- references auth.users(id) or email_subscribers(id)
    recipient_email VARCHAR(255),
    subject_line TEXT NOT NULL,
    message_content TEXT NOT NULL,
    template_data JSONB, -- Dynamic data for email templates
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10), -- 1 = highest priority
    processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'sent', 'failed', 'cancelled')),
    attempts INTEGER DEFAULT 0,
    last_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Enable Row Level Security
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_queue ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Notification preferences: users can only see/edit their own
CREATE POLICY "Users can manage own notification preferences" ON notification_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Email subscribers: public read for admin, users can unsubscribe themselves
CREATE POLICY "Public read access to email subscribers" ON email_subscribers
    FOR SELECT USING (true);

CREATE POLICY "Users can update own email subscription" ON email_subscribers
    FOR UPDATE USING (true); -- Allow unsubscribe by token

-- Email deliveries: admin only
CREATE POLICY "Admin access to email deliveries" ON email_deliveries
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Notification queue: admin only
CREATE POLICY "Admin access to notification queue" ON notification_queue
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Indexes for performance
CREATE INDEX idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX idx_email_subscribers_email ON email_subscribers(email);
CREATE INDEX idx_email_subscribers_subscription_type ON email_subscribers(subscription_type);
CREATE INDEX idx_email_subscribers_location ON email_subscribers(state, county);
CREATE INDEX idx_email_subscribers_active ON email_subscribers(is_active);
CREATE INDEX idx_email_deliveries_recipient ON email_deliveries(recipient_email);
CREATE INDEX idx_email_deliveries_type ON email_deliveries(email_type);
CREATE INDEX idx_email_deliveries_status ON email_deliveries(delivery_status);
CREATE INDEX idx_email_deliveries_sent_at ON email_deliveries(sent_at);
CREATE INDEX idx_notification_queue_scheduled ON notification_queue(scheduled_for);
CREATE INDEX idx_notification_queue_status ON notification_queue(processing_status);
CREATE INDEX idx_notification_queue_priority ON notification_queue(priority, scheduled_for);

-- Helper functions

-- Function to get user notification preferences with defaults
CREATE OR REPLACE FUNCTION get_user_notification_preferences(user_uuid UUID)
RETURNS TABLE (
    email_enabled BOOLEAN,
    weekly_voting_alerts BOOLEAN,
    daily_winners BOOLEAN,
    monthly_championships BOOLEAN,
    quarterly_legends BOOLEAN,
    local_state_updates BOOLEAN,
    county_specific_alerts BOOLEAN,
    email_frequency VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(np.email_enabled, true) as email_enabled,
        COALESCE(np.weekly_voting_alerts, true) as weekly_voting_alerts,
        COALESCE(np.daily_winners, true) as daily_winners,
        COALESCE(np.monthly_championships, true) as monthly_championships,
        COALESCE(np.quarterly_legends, true) as quarterly_legends,
        COALESCE(np.local_state_updates, true) as local_state_updates,
        COALESCE(np.county_specific_alerts, true) as county_specific_alerts,
        COALESCE(np.email_frequency, 'immediate'::VARCHAR) as email_frequency
    FROM notification_preferences np
    RIGHT JOIN auth.users u ON np.user_id = u.id
    WHERE u.id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to queue notification
CREATE OR REPLACE FUNCTION queue_notification(
    p_notification_type VARCHAR,
    p_recipient_type VARCHAR,
    p_recipient_id UUID,
    p_recipient_email VARCHAR,
    p_subject_line TEXT,
    p_message_content TEXT,
    p_template_data JSONB DEFAULT NULL,
    p_scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    p_priority INTEGER DEFAULT 5
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO notification_queue (
        notification_type,
        recipient_type,
        recipient_id,
        recipient_email,
        subject_line,
        message_content,
        template_data,
        scheduled_for,
        priority
    ) VALUES (
        p_notification_type,
        p_recipient_type,
        p_recipient_id,
        p_recipient_email,
        p_subject_line,
        p_message_content,
        p_template_data,
        p_scheduled_for,
        p_priority
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending notifications for processing
CREATE OR REPLACE FUNCTION get_pending_notifications(batch_size INTEGER DEFAULT 50)
RETURNS TABLE (
    id UUID,
    notification_type VARCHAR,
    recipient_email VARCHAR,
    subject_line TEXT,
    message_content TEXT,
    template_data JSONB,
    priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    UPDATE notification_queue nq
    SET processing_status = 'processing'
    WHERE nq.id IN (
        SELECT nq2.id
        FROM notification_queue nq2
        WHERE nq2.processing_status = 'pending'
        AND nq2.scheduled_for <= NOW()
        ORDER BY nq2.priority ASC, nq2.scheduled_for ASC
        LIMIT batch_size
        FOR UPDATE SKIP LOCKED
    )
    RETURNING 
        nq.id,
        nq.notification_type,
        nq.recipient_email,
        nq.subject_line,
        nq.message_content,
        nq.template_data,
        nq.priority;
END;
$$ LANGUAGE plpgsql;

-- Function to mark notification as sent
CREATE OR REPLACE FUNCTION mark_notification_sent(
    notification_id UUID,
    external_message_id VARCHAR DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE notification_queue
    SET 
        processing_status = 'sent',
        processed_at = NOW()
    WHERE id = notification_id;
    
    -- Also record in email_deliveries if external_message_id provided
    IF external_message_id IS NOT NULL THEN
        INSERT INTO email_deliveries (
            recipient_email,
            subject_line,
            email_type,
            delivery_status,
            external_message_id,
            sent_at
        )
        SELECT 
            recipient_email,
            subject_line,
            notification_type,
            'sent',
            external_message_id,
            NOW()
        FROM notification_queue
        WHERE id = notification_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as failed
CREATE OR REPLACE FUNCTION mark_notification_failed(
    notification_id UUID,
    error_message TEXT
)
RETURNS VOID AS $$
BEGIN
    UPDATE notification_queue
    SET 
        processing_status = 'failed',
        last_error = error_message,
        attempts = attempts + 1,
        processed_at = NOW()
    WHERE id = notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle updated_at timestamp
CREATE OR REPLACE FUNCTION handle_notification_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER notification_preferences_updated_at
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION handle_notification_updated_at();

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON notification_preferences TO authenticated;
GRANT SELECT, UPDATE ON email_subscribers TO anon, authenticated;
GRANT INSERT ON email_subscribers TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_user_notification_preferences(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION queue_notification(VARCHAR, VARCHAR, UUID, VARCHAR, TEXT, TEXT, JSONB, TIMESTAMP WITH TIME ZONE, INTEGER) TO authenticated; 