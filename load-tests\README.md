# Artillery Load Testing for Mugshots API

This directory contains comprehensive load testing setup for the Americas Top Mugshots API endpoints using Artillery.io. The tests are designed to simulate **10,000 concurrent users** and benchmark API performance under high load.

## 🎯 Objective

Benchmark the performance of our mugshots API endpoints under high concurrent load to:
- Measure response times (average, p95, p99)
- Assess throughput (requests per second)
- Monitor error rates
- Identify performance bottlenecks
- Establish baseline metrics before optimizations

## 📁 Directory Structure

```
load-tests/
├── artillery.yml                    # Main comprehensive test configuration
├── scenarios/
│   ├── data-generators.js          # Random data generation functions
│   ├── mugshots-api-load-test.yml  # Focused mugshots API test
│   └── mugshot-details-load-test.yml # Focused details API test
├── results/                        # Test results and reports (auto-generated)
├── run-load-tests.sh              # Execution script with multiple options
└── README.md                       # This documentation
```

## 🚀 Quick Start

### Prerequisites

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Ensure your API is accessible:**
   ```bash
   curl http://localhost:3000/api/mugshots?page=1&perPage=1
   ```

### Running Tests

#### Option 1: Using npm scripts (Recommended)
```bash
# Quick smoke test (2 minutes, 100 requests)
npm run load-test:quick

# Full comprehensive test (15-20 minutes, 10k concurrent users)
npm run load-test

# Generate detailed HTML report
npm run load-test:report
```

#### Option 2: Using the shell script
```bash
# Quick test
./load-tests/run-load-tests.sh quick

# Test only mugshots API
./load-tests/run-load-tests.sh mugshots

# Test only details API
./load-tests/run-load-tests.sh details

# Full comprehensive test suite
./load-tests/run-load-tests.sh full
```

#### Option 3: Direct Artillery commands
```bash
# Run specific test configuration
artillery run load-tests/artillery.yml

# Run with custom output
artillery run load-tests/artillery.yml --output results/my-test.json

# Generate HTML report from results
artillery report results/my-test.json --output results/my-report.html
```

## 📊 Test Scenarios

### 1. Comprehensive Test (`artillery.yml`)
- **Duration:** ~15-20 minutes
- **Peak Load:** 10,000 concurrent users (200 arrivals/second)
- **Phases:** Warm-up → Ramp-up → Peak → Sustained → Cool-down
- **Scenarios:**
  - 80% Mugshots API with random filters
  - 15% Mugshot details API
  - 5% Realistic user journeys

### 2. Mugshots API Focused Test
- **Duration:** ~10 minutes
- **Peak Load:** 300 arrivals/second
- **Focus:** `/api/mugshots` endpoint with various filter combinations
- **Filters Tested:**
  - State and county filtering
  - Search terms (names)
  - Tag filtering (wild, funny, spooky)
  - Date range filtering
  - Sorting options (newest, top-rated, most-viewed)
  - Pagination

### 3. Details API Focused Test
- **Duration:** ~8 minutes
- **Peak Load:** 150 arrivals/second
- **Focus:** `/api/mugshots/[id]` endpoint
- **Behavior:** Random ID generation with realistic distribution

## 🎲 Test Data Generation

The tests use realistic random data generation:

### States & Counties
- All 50 US states
- Major counties for each state
- 60% chance of state filter, 30% chance of county filter

### Search Terms
- Common surnames (Smith, Johnson, Williams, etc.)
- 25% chance of search term inclusion

### Tags
- Database-accurate tag types: `wild`, `funny`, `spooky`
- 40% chance of tag filtering
- 1-2 tags per request

### Date Ranges
- Random dates within the past year
- 20% chance of date range filtering
- Proper date ordering (from < to)

### Pagination
- Pages 1-10 (realistic user behavior)
- Per-page options: 12, 24, 48
- Sort options: newest, top-rated, most-viewed

## 📈 Performance Thresholds

### Response Time SLAs
- **Median:** < 500ms
- **95th Percentile:** < 2000ms
- **99th Percentile:** < 5000ms

### Error Rate Limits
- **Success Rate:** > 95%
- **Client Errors (4xx):** < 3%
- **Server Errors (5xx):** < 2%

### Throughput Requirements
- **Minimum RPS:** 100 requests/second
- **Peak Target:** 200+ requests/second

## 📋 Interpreting Results

### Key Metrics to Monitor

1. **Response Times**
   - `http.response_time.median` - Half of requests faster than this
   - `http.response_time.p95` - 95% of requests faster than this
   - `http.response_time.p99` - 99% of requests faster than this

2. **Throughput**
   - `http.request_rate` - Requests per second
   - `http.requests` - Total requests made

3. **Success Rates**
   - `http.codes.200` - Successful responses
   - `http.codes.4xx` - Client errors (bad requests)
   - `http.codes.5xx` - Server errors

4. **Errors**
   - `errors.total` - Total errors encountered
   - `errors.ECONNRESET` - Connection reset errors
   - `errors.ETIMEDOUT` - Timeout errors

### Sample Good Results
```
✓ http.response_time.p95: 1200ms (threshold: 2000ms)
✓ http.response_time.median: 350ms (threshold: 500ms)
✓ http.codes.200: 98.5% (threshold: 95%)
✓ http.request_rate: 185 rps (threshold: 100 rps)
✓ errors.total: 12 (< 1% of total requests)
```

### Red Flags to Watch For
- Response times consistently above thresholds
- Error rates above 5%
- Declining throughput during sustained load
- Memory leaks (increasing response times over time)
- Database connection pool exhaustion

## 🔧 Troubleshooting

### Common Issues

1. **Server Not Running**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3000
   ```
   **Solution:** Start your dev server with `npm run dev`

2. **High Error Rates**
   - Check database connection limits
   - Monitor server resource usage
   - Review API error logs

3. **Slow Response Times**
   - Check database query performance
   - Monitor server CPU/memory usage
   - Consider database indexing

4. **Connection Timeouts**
   - Increase `http.timeout` in config
   - Check network connectivity
   - Monitor server connection limits

### Performance Optimization Tips

1. **Database Optimization**
   - Add indexes on frequently filtered columns
   - Optimize complex queries
   - Consider query result caching

2. **API Optimization**
   - Implement response caching
   - Use connection pooling
   - Optimize JSON serialization

3. **Infrastructure**
   - Monitor server resources during tests
   - Consider horizontal scaling
   - Implement load balancing

## 📝 Test Results Analysis

Results are automatically saved in `load-tests/results/` with timestamps:
- `*.json` - Raw Artillery results data
- `*.html` - Formatted HTML reports with charts

### Comparing Results
Use the JSON files to compare performance across different test runs:
```bash
# Compare two test runs
node -e "
const run1 = require('./results/test1.json');
const run2 = require('./results/test2.json');
console.log('Run 1 P95:', run1.aggregate.summaries['http.response_time'].p95);
console.log('Run 2 P95:', run2.aggregate.summaries['http.response_time'].p95);
"
```

## 🎯 Next Steps

After running the load tests:

1. **Analyze Results** - Review response times, error rates, and throughput
2. **Identify Bottlenecks** - Look for slow queries, resource constraints
3. **Optimize Code** - Implement performance improvements
4. **Re-test** - Run tests again to measure improvements
5. **Set Monitoring** - Implement ongoing performance monitoring

## 🔍 Additional Tools

### Results Analysis Script
```bash
# Analyze the latest test results
node load-tests/analyze-results.js

# Compare two specific test runs
node load-tests/analyze-results.js results/test1.json results/test2.json
```

### Continuous Testing
For ongoing performance monitoring, consider setting up:
- Scheduled load tests in CI/CD pipeline
- Performance regression detection
- Automated alerting on threshold breaches

## 📞 Support

For questions about load testing or interpreting results:
- Review the Artillery.io documentation: https://artillery.io/docs/
- Check server logs during test execution
- Monitor database performance metrics
- Consider profiling tools for detailed analysis


# 1. Test baseline (newest sort, no filters)
time curl "http://localhost:3000/api/mugshots-fast?mode=baseline&page=1&perPage=12"

# 2. Test fast queries with state filter
time curl "http://localhost:3000/api/mugshots-fast?mode=fast&state=California&sortBy=newest&page=1&perPage=12"

# 3. Test top-rated queries
time curl "http://localhost:3000/api/mugshots-fast?mode=fast&sortBy=top-rated&page=1&perPage=12"

# 4. Test tag filtering
time curl "http://localhost:3000/api/mugshots-fast?mode=fast&tags=wild,funny&sortBy=newest&page=1&perPage=12"

# 5. Test complex combination
time curl "http://localhost:3000/api/mugshots-fast?mode=fast&state=California&tags=wild&sortBy=top-rated&search=Smith&page=1&perPage=12"


# Test optimized baseline
npm run load-test:baseline-fast

# Test optimized fast queries
npm run load-test:fast-optimized

# Test top-rated performance
npm run load-test:top-rated

# Test tag queries performance
npm run load-test:tags

# Analyze results
node load-tests/analyze-results.js

xpected Performance with Pro Plan + Optimizations
Single Query Performance:
Baseline (newest): 100-300ms
State filtering: 150-400ms
Top-rated: 500-1000ms
Tag filtering: 300-600ms
Complex combinations: 400-800ms
Load Test Results:
Success Rate: 95%+ for all query types
Concurrent Users: 50-100+
Response Times: 200-800ms median depending on complexity
Index Usage:
Newest queries: idx_mugshots_newest_perfect
State + newest: idx_mugshots_state_newest
Date range: idx_mugshots_daterange_newest
Name search: idx_mugshots_fullname_search
Top-rated: idx_ratings_mugshot_avg_calc
Tag filtering: idx_tags_type_mugshot_fast