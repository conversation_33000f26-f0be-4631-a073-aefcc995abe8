import { redirect } from 'next/navigation'
import { requireAdmin } from '@/lib/auth-context'
import { Button } from '@/components/ui/button'
// import Link from 'next/link' // Unused import
import { LayoutDashboard, Users, Flag, Settings, BarChart3 } from 'lucide-react'
import UniversalNavLink from '@/components/UniversalNavLink'

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  try {
    // Server-side admin check - will throw if not admin
    await requireAdmin()
  } catch {
    // Redirect non-admin users to mugshots page
    redirect('/mugshots')
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="flex">
        {/* Admin Sidebar */}
        <aside className="w-64 bg-gray-800/50 border-r border-pink-500/30 min-h-screen">
          <div className="p-6">
            <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
              <LayoutDashboard className="h-5 w-5 text-pink-500" />
              Admin Panel
            </h2>
            
            <nav className="space-y-2">
              <UniversalNavLink href="/admin">
                <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </UniversalNavLink>
              
              <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700" disabled>
                <Users className="h-4 w-4 mr-2" />
                User Management
                <span className="ml-auto text-xs text-gray-500">Soon</span>
              </Button>
              
              <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700" disabled>
                <Flag className="h-4 w-4 mr-2" />
                Content Moderation
                <span className="ml-auto text-xs text-gray-500">Soon</span>
              </Button>
              
              <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700" disabled>
                <Settings className="h-4 w-4 mr-2" />
                Settings
                <span className="ml-auto text-xs text-gray-500">Soon</span>
              </Button>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  )
} 