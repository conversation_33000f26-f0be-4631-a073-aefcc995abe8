import { Skeleton } from "@/components/ui/skeleton"

interface PageSkeletonProps {
  type?: "mugshots" | "popular" | "weekly" | "monthly" | "quarterly" | "home"
}

export default function PageSkeleton({ type = "mugshots" }: PageSkeletonProps) {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header Skeleton */}
        <div className="text-center mb-12">
          <Skeleton className="h-8 sm:h-12 w-60 sm:w-80 mx-auto mb-4" />
          <Skeleton className="h-4 sm:h-6 w-72 sm:w-96 mx-auto" />
        </div>

        {/* Current Competition Section (for monthly/quarterly) */}
        {(type === "monthly" || type === "quarterly") && (
          <div className="mb-12">
            <div className="bg-gray-800/30 border border-purple-500/30 rounded-xl p-8 mb-8">
              <div className="flex items-center gap-3 mb-6">
                <Skeleton className="h-6 w-6" />
                <Skeleton className="h-6 w-64" />
                <Skeleton className="h-6 w-24" />
              </div>
              
              {/* Voting notice skeleton */}
              <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4 mb-6">
                <Skeleton className="h-5 w-48 mb-2" />
                <Skeleton className="h-4 w-full" />
              </div>
              
              {/* Candidates grid skeleton */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <CompetitionCandidateSkeleton key={i} />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Filters Section Skeleton (for mugshots page) */}
        {type === "mugshots" && (
          <div className="bg-gray-800/30 border border-pink-500/30 rounded-xl p-6 mb-8">
            <div className="space-y-6">
              {/* First Row */}
              <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
                <div className="lg:col-span-2 space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              
              {/* Second Row */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
              
              {/* Third Row */}
              <div className="flex flex-wrap gap-4 justify-between items-center">
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                </div>
                <div className="flex gap-4">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Competition Filters Skeleton (for competition pages) */}
        {(type === "weekly" || type === "monthly" || type === "quarterly") && (
          <div className={`bg-gray-800/30 border ${
            type === 'monthly' ? 'border-purple-500/30' : 
            type === 'quarterly' ? 'border-amber-500/30' : 
            'border-pink-500/30'
          } rounded-xl p-6 mb-8`}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards Skeleton (for popular/weekly/monthly/quarterly pages) */}
        {(type === "popular" || type === "weekly" || type === "monthly" || type === "quarterly") && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className={`bg-gray-800/30 border ${
                type === 'monthly' ? 'border-purple-500/30' : 
                type === 'quarterly' ? 'border-amber-500/30' : 
                'border-pink-500/30'
              } rounded-xl p-6`}>
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Carousel Skeleton (for home page) */}
        {type === "home" && (
          <div className="mb-12">
            <div className="flex justify-center mb-6">
              <div className="flex gap-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-20" />
                ))}
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <MugshotCardSkeleton key={i} />
              ))}
            </div>
          </div>
        )}

        {/* Mugshot Grid Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {Array.from({ length: 12 }).map((_, i) => (
            <MugshotCardSkeleton key={i} />
          ))}
        </div>

        {/* Hall of Fame Footer (for quarterly) */}
        {type === "quarterly" && (
          <div className="bg-gray-800/30 border border-amber-500/30 rounded-xl p-8 text-center">
            <Skeleton className="h-12 w-12 mx-auto mb-4" />
            <Skeleton className="h-6 w-32 mx-auto mb-4" />
            <Skeleton className="h-4 w-96 mx-auto mb-2" />
            <Skeleton className="h-4 w-80 mx-auto" />
          </div>
        )}

        {/* Pagination Skeleton */}
        <div className="flex justify-center gap-2 mb-12">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-8 w-8" />
          ))}
        </div>
      </div>
    </div>
  )
}

function MugshotCardSkeleton() {
  return (
    <div className="bg-gray-800/30 border border-pink-500/30 rounded-xl p-3 sm:p-4 space-y-3 sm:space-y-4">
      {/* Image skeleton */}
      <Skeleton className="w-full h-48 sm:h-64 rounded-lg" />
      
      {/* Content skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 sm:h-5 w-3/4" />
        <Skeleton className="h-3 sm:h-4 w-1/2" />
        <div className="flex justify-between items-center">
          <Skeleton className="h-3 sm:h-4 w-12 sm:w-16" />
          <Skeleton className="h-3 sm:h-4 w-8 sm:w-12" />
        </div>
      </div>
    </div>
  )
}

function CompetitionCandidateSkeleton() {
  return (
    <div className="bg-gray-800/30 border border-purple-500/30 rounded-xl p-4 space-y-3">
      {/* Image skeleton */}
      <Skeleton className="w-full h-48 rounded-lg" />
      
      {/* Content skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
        <div className="space-y-1">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
        </div>
        <Skeleton className="h-3 w-20" />
      </div>
    </div>
  )
} 