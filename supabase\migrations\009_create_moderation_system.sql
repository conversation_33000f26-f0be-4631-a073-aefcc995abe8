-- Migration: Create Content Moderation System
-- Description: Content reporting, moderation actions, user discipline, and automated flagging

-- Content reports table for user-flagged content
CREATE TABLE content_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('mugshot', 'user_tag', 'user_comment', 'user_profile')),
    content_id VARCHAR(100) NOT NULL, -- Can reference different table IDs
    reported_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    reason VARCHAR(50) NOT NULL CHECK (reason IN (
        'inappropriate_content', 'spam', 'harassment', 'copyright', 
        'violence', 'hate_speech', 'fake_content', 'privacy_violation', 'other'
    )),
    description TEXT,
    evidence_urls TEXT[], -- Screenshots or additional proof
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'removed', 'dismissed')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('urgent', 'high', 'medium', 'low')),
    auto_flagged BOOLEAN DEFAULT false,
    confidence_score DECIMAL(3,2), -- For automated flagging (0.00-1.00)
    
    -- Moderation fields
    moderated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    moderated_at TIMESTAMP WITH TIME ZONE,
    moderation_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate reports from same user for same content
    UNIQUE(content_type, content_id, reported_by)
);

-- Moderation actions log for audit trail
CREATE TABLE moderation_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES auth.users(id) NOT NULL,
    action_type VARCHAR(30) NOT NULL CHECK (action_type IN (
        'approve_content', 'remove_content', 'dismiss_report', 'warn_user', 
        'suspend_user', 'ban_user', 'remove_tag', 'bulk_approve', 'bulk_remove'
    )),
    target_type VARCHAR(20) NOT NULL, -- 'mugshot', 'user', 'tag', 'report'
    target_id VARCHAR(100) NOT NULL,
    reason TEXT,
    previous_state JSONB, -- Store before state for reversibility
    new_state JSONB, -- Store after state
    bulk_action_id UUID, -- Link related bulk actions
    
    -- Additional context
    affected_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- User affected by the action
    notification_sent BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User moderation history for tracking user violations
CREATE TABLE user_moderation_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    action_type VARCHAR(20) NOT NULL CHECK (action_type IN ('warning', 'suspension', 'ban', 'account_restriction')),
    reason TEXT NOT NULL,
    duration_hours INTEGER, -- For temporary suspensions
    expires_at TIMESTAMP WITH TIME ZONE,
    issued_by UUID REFERENCES auth.users(id) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    
    -- Appeal process
    appealed_at TIMESTAMP WITH TIME ZONE,
    appeal_reason TEXT,
    appeal_status VARCHAR(20) CHECK (appeal_status IN ('pending', 'approved', 'denied')),
    appeal_reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    appeal_reviewed_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automated flagging rules configuration
CREATE TABLE auto_flagging_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_name VARCHAR(100) NOT NULL,
    content_type VARCHAR(20) NOT NULL,
    rule_type VARCHAR(20) NOT NULL CHECK (rule_type IN ('keyword', 'image_analysis', 'user_pattern', 'content_similarity')),
    rule_config JSONB NOT NULL, -- Rule-specific configuration
    priority VARCHAR(10) DEFAULT 'medium',
    is_active BOOLEAN DEFAULT true,
    
    -- Performance tracking
    total_flags INTEGER DEFAULT 0,
    accurate_flags INTEGER DEFAULT 0,
    accuracy_rate DECIMAL(5,2) DEFAULT 0.00,
    
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_content_reports_status ON content_reports(status);
CREATE INDEX idx_content_reports_priority ON content_reports(priority, created_at);
CREATE INDEX idx_content_reports_content ON content_reports(content_type, content_id);
CREATE INDEX idx_content_reports_reported_by ON content_reports(reported_by);
CREATE INDEX idx_content_reports_auto_flagged ON content_reports(auto_flagged, confidence_score);

CREATE INDEX idx_moderation_actions_admin ON moderation_actions(admin_id, created_at);
CREATE INDEX idx_moderation_actions_target ON moderation_actions(target_type, target_id);
CREATE INDEX idx_moderation_actions_bulk ON moderation_actions(bulk_action_id);
CREATE INDEX idx_moderation_actions_affected_user ON moderation_actions(affected_user_id);

CREATE INDEX idx_user_moderation_user_active ON user_moderation_history(user_id, is_active);
CREATE INDEX idx_user_moderation_expires ON user_moderation_history(expires_at, is_active);
CREATE INDEX idx_user_moderation_appeal ON user_moderation_history(appeal_status, appealed_at);

CREATE INDEX idx_auto_flagging_rules_active ON auto_flagging_rules(is_active, content_type);

-- Row Level Security policies
ALTER TABLE content_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE moderation_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_moderation_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE auto_flagging_rules ENABLE ROW LEVEL SECURITY;

-- Content Reports Policies
CREATE POLICY "Users can view their own reports" ON content_reports
    FOR SELECT USING (auth.uid() = reported_by);

CREATE POLICY "Users can create reports" ON content_reports
    FOR INSERT WITH CHECK (auth.uid() = reported_by);

CREATE POLICY "Admins can view all reports" ON content_reports
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can update reports" ON content_reports
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Moderation Actions Policies (Admin only)
CREATE POLICY "Admins can view moderation actions" ON moderation_actions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can create moderation actions" ON moderation_actions
    FOR INSERT WITH CHECK (
        auth.uid() = admin_id AND
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- User Moderation History Policies
CREATE POLICY "Users can view their own moderation history" ON user_moderation_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all user moderation history" ON user_moderation_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can create user moderation history" ON user_moderation_history
    FOR INSERT WITH CHECK (
        auth.uid() = issued_by AND
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can update user moderation history" ON user_moderation_history
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Auto Flagging Rules Policies (Admin only)
CREATE POLICY "Admins can manage auto flagging rules" ON auto_flagging_rules
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Function to get moderation queue with priority sorting
CREATE OR REPLACE FUNCTION get_moderation_queue(
    p_content_type VARCHAR(20) DEFAULT NULL,
    p_status VARCHAR(20) DEFAULT 'pending',
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    report_id UUID,
    content_type VARCHAR(20),
    content_id VARCHAR(100),
    content_data JSONB,
    reason VARCHAR(50),
    description TEXT,
    priority VARCHAR(10),
    reported_by_email VARCHAR(255),
    report_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    auto_flagged BOOLEAN,
    confidence_score DECIMAL(3,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cr.id as report_id,
        cr.content_type,
        cr.content_id,
        CASE 
            WHEN cr.content_type = 'mugshot' THEN 
                (SELECT row_to_json(m.*) FROM mugshots m WHERE m.id::text = cr.content_id)
            WHEN cr.content_type = 'user_tag' THEN
                (SELECT row_to_json(ut.*) FROM user_tags ut WHERE ut.id::text = cr.content_id)
            ELSE '{}'::jsonb
        END as content_data,
        cr.reason,
        cr.description,
        cr.priority,
        u.email as reported_by_email,
        (SELECT COUNT(*) FROM content_reports cr2 
         WHERE cr2.content_type = cr.content_type 
         AND cr2.content_id = cr.content_id 
         AND cr2.status = 'pending') as report_count,
        cr.created_at,
        cr.auto_flagged,
        cr.confidence_score
    FROM content_reports cr
    LEFT JOIN auth.users u ON cr.reported_by = u.id
    WHERE 
        (p_content_type IS NULL OR cr.content_type = p_content_type)
        AND cr.status = p_status
    ORDER BY 
        CASE cr.priority 
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2  
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        cr.created_at ASC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to perform moderation action with logging
CREATE OR REPLACE FUNCTION perform_moderation_action(
    p_admin_id UUID,
    p_action_type VARCHAR(30),
    p_target_type VARCHAR(20),
    p_target_id VARCHAR(100),
    p_reason TEXT,
    p_bulk_action_id UUID DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    v_previous_state JSONB;
    v_new_state JSONB;
    v_action_id UUID;
    v_affected_user_id UUID;
BEGIN
    -- Capture previous state based on target type
    CASE p_target_type
        WHEN 'mugshot' THEN
            SELECT row_to_json(m.*) INTO v_previous_state 
            FROM mugshots m WHERE m.id::text = p_target_id;
        WHEN 'user_tag' THEN
            SELECT row_to_json(ut.*) INTO v_previous_state 
            FROM user_tags ut WHERE ut.id::text = p_target_id;
        WHEN 'report' THEN
            SELECT row_to_json(cr.*) INTO v_previous_state 
            FROM content_reports cr WHERE cr.id::text = p_target_id;
    END CASE;

    -- Perform the action based on type
    CASE p_action_type
        WHEN 'approve_content' THEN
            UPDATE content_reports 
            SET 
                status = 'approved', 
                moderated_by = p_admin_id,
                moderated_at = NOW(),
                moderation_reason = p_reason,
                updated_at = NOW()
            WHERE content_id = p_target_id AND status = 'pending';
            
        WHEN 'remove_content' THEN
            -- Mark content as removed/hidden
            IF p_target_type = 'mugshot' THEN
                UPDATE mugshots 
                SET is_moderated = true, is_approved = false, moderated_at = NOW()
                WHERE id::text = p_target_id;
                
                -- Get the user who uploaded the mugshot for notification
                SELECT user_id INTO v_affected_user_id FROM mugshots WHERE id::text = p_target_id;
                
            ELSIF p_target_type = 'user_tag' THEN
                UPDATE user_tags 
                SET is_approved = false, moderated_at = NOW()
                WHERE id::text = p_target_id;
                
                -- Get the user who created the tag
                SELECT user_id INTO v_affected_user_id FROM user_tags WHERE id::text = p_target_id;
            END IF;
            
            -- Update related reports
            UPDATE content_reports 
            SET 
                status = 'removed', 
                moderated_by = p_admin_id,
                moderated_at = NOW(),
                moderation_reason = p_reason,
                updated_at = NOW()
            WHERE content_id = p_target_id AND status = 'pending';
            
        WHEN 'dismiss_report' THEN
            UPDATE content_reports 
            SET 
                status = 'dismissed', 
                moderated_by = p_admin_id,
                moderated_at = NOW(),
                moderation_reason = p_reason,
                updated_at = NOW()
            WHERE id::text = p_target_id;
    END CASE;

    -- Capture new state
    CASE p_target_type
        WHEN 'mugshot' THEN
            SELECT row_to_json(m.*) INTO v_new_state 
            FROM mugshots m WHERE m.id::text = p_target_id;
        WHEN 'user_tag' THEN
            SELECT row_to_json(ut.*) INTO v_new_state 
            FROM user_tags ut WHERE ut.id::text = p_target_id;
        WHEN 'report' THEN
            SELECT row_to_json(cr.*) INTO v_new_state 
            FROM content_reports cr WHERE cr.id::text = p_target_id;
    END CASE;

    -- Log the action
    INSERT INTO moderation_actions (
        admin_id, action_type, target_type, target_id, reason, 
        previous_state, new_state, bulk_action_id, affected_user_id
    )
    VALUES (
        p_admin_id, p_action_type, p_target_type, p_target_id, p_reason,
        v_previous_state, v_new_state, p_bulk_action_id, v_affected_user_id
    )
    RETURNING id INTO v_action_id;

    RETURN jsonb_build_object(
        'success', true,
        'action_id', v_action_id,
        'affected_user_id', v_affected_user_id,
        'previous_state', v_previous_state,
        'new_state', v_new_state
    );

EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get moderation statistics for dashboard
CREATE OR REPLACE FUNCTION get_moderation_stats(
    p_admin_id UUID DEFAULT NULL,
    p_date_range VARCHAR(10) DEFAULT 'week'
)
RETURNS JSONB AS $$
DECLARE
    v_date_filter TIMESTAMP WITH TIME ZONE;
    v_stats JSONB;
BEGIN
    -- Set date filter based on range
    CASE p_date_range
        WHEN 'today' THEN v_date_filter := CURRENT_DATE;
        WHEN 'week' THEN v_date_filter := CURRENT_DATE - INTERVAL '7 days';
        WHEN 'month' THEN v_date_filter := CURRENT_DATE - INTERVAL '30 days';
        ELSE v_date_filter := CURRENT_DATE - INTERVAL '7 days';
    END CASE;

    -- Build statistics object
    SELECT jsonb_build_object(
        'pending_reports', (
            SELECT COUNT(*) FROM content_reports 
            WHERE status = 'pending'
        ),
        'actions_taken', (
            SELECT COUNT(*) FROM moderation_actions 
            WHERE created_at >= v_date_filter
            AND (p_admin_id IS NULL OR admin_id = p_admin_id)
        ),
        'content_removed', (
            SELECT COUNT(*) FROM moderation_actions 
            WHERE action_type = 'remove_content' 
            AND created_at >= v_date_filter
            AND (p_admin_id IS NULL OR admin_id = p_admin_id)
        ),
        'users_moderated', (
            SELECT COUNT(*) FROM user_moderation_history 
            WHERE created_at >= v_date_filter
            AND (p_admin_id IS NULL OR issued_by = p_admin_id)
        ),
        'auto_flagged_accuracy', (
            SELECT COALESCE(ROUND(AVG(
                CASE 
                    WHEN cr.status = 'approved' THEN 1.0 - COALESCE(cr.confidence_score, 0.5)
                    WHEN cr.status = 'removed' THEN COALESCE(cr.confidence_score, 0.5)
                    ELSE 0.5
                END
            ), 2), 0.00)
            FROM content_reports cr
            WHERE cr.auto_flagged = true 
            AND cr.created_at >= v_date_filter
            AND cr.status IN ('approved', 'removed')
        ),
        'top_report_reasons', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'reason', reason,
                    'count', reason_count
                ) ORDER BY reason_count DESC
            )
            FROM (
                SELECT reason, COUNT(*) as reason_count
                FROM content_reports
                WHERE created_at >= v_date_filter
                GROUP BY reason
                ORDER BY COUNT(*) DESC
                LIMIT 5
            ) reasons
        )
    ) INTO v_stats;

    RETURN v_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to moderate user (warn, suspend, ban)
CREATE OR REPLACE FUNCTION moderate_user(
    p_admin_id UUID,
    p_user_id UUID,
    p_action_type VARCHAR(20),
    p_reason TEXT,
    p_duration_hours INTEGER DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    v_history_id UUID;
    v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Calculate expiration time for temporary actions
    IF p_duration_hours IS NOT NULL THEN
        v_expires_at := NOW() + (p_duration_hours || ' hours')::INTERVAL;
    END IF;

    -- Insert moderation history
    INSERT INTO user_moderation_history (
        user_id, action_type, reason, duration_hours, expires_at, issued_by
    )
    VALUES (
        p_user_id, p_action_type, p_reason, p_duration_hours, v_expires_at, p_admin_id
    )
    RETURNING id INTO v_history_id;

    -- Log the moderation action
    INSERT INTO moderation_actions (
        admin_id, action_type, target_type, target_id, reason, affected_user_id
    )
    VALUES (
        p_admin_id, p_action_type, 'user', p_user_id::text, p_reason, p_user_id
    );

    RETURN jsonb_build_object(
        'success', true,
        'history_id', v_history_id,
        'expires_at', v_expires_at
    );

EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is currently moderated
CREATE OR REPLACE FUNCTION is_user_moderated(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_active_moderation RECORD;
BEGIN
    SELECT * INTO v_active_moderation
    FROM user_moderation_history
    WHERE user_id = p_user_id 
    AND is_active = true
    AND (expires_at IS NULL OR expires_at > NOW())
    ORDER BY created_at DESC
    LIMIT 1;

    IF FOUND THEN
        RETURN jsonb_build_object(
            'is_moderated', true,
            'action_type', v_active_moderation.action_type,
            'reason', v_active_moderation.reason,
            'expires_at', v_active_moderation.expires_at,
            'issued_at', v_active_moderation.created_at
        );
    ELSE
        RETURN jsonb_build_object('is_moderated', false);
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically expire user moderations
CREATE OR REPLACE FUNCTION expire_user_moderations()
RETURNS void AS $$
BEGIN
    UPDATE user_moderation_history
    SET is_active = false, updated_at = NOW()
    WHERE is_active = true
    AND expires_at IS NOT NULL
    AND expires_at <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT ON content_reports TO authenticated;
GRANT SELECT ON moderation_actions TO authenticated;
GRANT SELECT ON user_moderation_history TO authenticated;
GRANT SELECT ON auto_flagging_rules TO authenticated;

GRANT EXECUTE ON FUNCTION get_moderation_queue TO authenticated;
GRANT EXECUTE ON FUNCTION perform_moderation_action TO authenticated;
GRANT EXECUTE ON FUNCTION get_moderation_stats TO authenticated;
GRANT EXECUTE ON FUNCTION moderate_user TO authenticated;
GRANT EXECUTE ON FUNCTION is_user_moderated TO authenticated;
GRANT EXECUTE ON FUNCTION expire_user_moderations TO authenticated;

-- Create a scheduled task to expire user moderations (if using pg_cron extension)
-- SELECT cron.schedule('expire-user-moderations', '*/5 * * * *', 'SELECT expire_user_moderations();'); 