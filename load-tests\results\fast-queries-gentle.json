{"aggregate": {"counters": {"vusers.created_by_name.Fast Queries - Gentle Load": 600, "vusers.created": 600, "errors.Undefined function \"generateFastFilters\"": 600, "http.requests": 600, "http.codes.200": 600, "http.responses": 600, "http.downloaded_bytes": 2511195, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 600, "vusers.failed": 0, "vusers.completed": 600}, "rates": {"http.request_rate": 2}, "firstCounterAt": 1753687643785, "firstHistogramAt": 1753687644737, "lastCounterAt": 1753687825186, "lastHistogramAt": 1753687825186, "firstMetricAt": 1753687643785, "lastMetricAt": 1753687825186, "period": 1753687820000, "summaries": {"http.response_time": {"min": 323, "max": 1431, "count": 600, "mean": 394.2, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 539.2, "p95": 772.9, "p99": 1043.3, "p999": 1408.4}, "http.response_time.2xx": {"min": 323, "max": 1431, "count": 600, "mean": 394.2, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 539.2, "p95": 772.9, "p99": 1043.3, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 323, "max": 1431, "count": 600, "mean": 394.2, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 539.2, "p95": 772.9, "p99": 1043.3, "p999": 1408.4}, "vusers.session_length": {"min": 2324.9, "max": 3446.8, "count": 600, "mean": 2403.3, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2566.3, "p95": 2780, "p99": 3072.4, "p999": 3395.5}}, "histograms": {"http.response_time": {"min": 323, "max": 1431, "count": 600, "mean": 394.2, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 539.2, "p95": 772.9, "p99": 1043.3, "p999": 1408.4}, "http.response_time.2xx": {"min": 323, "max": 1431, "count": 600, "mean": 394.2, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 539.2, "p95": 772.9, "p99": 1043.3, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 323, "max": 1431, "count": 600, "mean": 394.2, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 539.2, "p95": 772.9, "p99": 1043.3, "p999": 1408.4}, "vusers.session_length": {"min": 2324.9, "max": 3446.8, "count": 600, "mean": 2403.3, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2566.3, "p95": 2780, "p99": 3072.4, "p999": 3395.5}}}, "intermediate": [{"counters": {"vusers.created_by_name.Fast Queries - Gentle Load": 14, "vusers.created": 14, "errors.Undefined function \"generateFastFilters\"": 14, "http.requests": 14, "http.codes.200": 12, "http.responses": 12, "http.downloaded_bytes": 50331, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 12, "vusers.failed": 0, "vusers.completed": 8}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753687643785, "firstHistogramAt": 1753687644737, "lastCounterAt": 1753687649842, "lastHistogramAt": 1753687649191, "firstMetricAt": 1753687643785, "lastMetricAt": 1753687649842, "period": "1753687640000", "summaries": {"http.response_time": {"min": 325, "max": 929, "count": 12, "mean": 474.6, "p50": 340.4, "median": 340.4, "p75": 584.2, "p90": 620.3, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 325, "max": 929, "count": 12, "mean": 474.6, "p50": 340.4, "median": 340.4, "p75": 584.2, "p90": 620.3, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 929, "count": 12, "mean": 474.6, "p50": 340.4, "median": 340.4, "p75": 584.2, "p90": 620.3, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "vusers.session_length": {"min": 2337.2, "max": 2971.6, "count": 8, "mean": 2560.8, "p50": 2369, "median": 2369, "p75": 2618.1, "p90": 2893.5, "p95": 2893.5, "p99": 2893.5, "p999": 2893.5}}, "histograms": {"http.response_time": {"min": 325, "max": 929, "count": 12, "mean": 474.6, "p50": 340.4, "median": 340.4, "p75": 584.2, "p90": 620.3, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "http.response_time.2xx": {"min": 325, "max": 929, "count": 12, "mean": 474.6, "p50": 340.4, "median": 340.4, "p75": 584.2, "p90": 620.3, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 929, "count": 12, "mean": 474.6, "p50": 340.4, "median": 340.4, "p75": 584.2, "p90": 620.3, "p95": 871.5, "p99": 871.5, "p999": 871.5}, "vusers.session_length": {"min": 2337.2, "max": 2971.6, "count": 8, "mean": 2560.8, "p50": 2369, "median": 2369, "p75": 2618.1, "p90": 2893.5, "p95": 2893.5, "p99": 2893.5, "p999": 2893.5}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 20, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83739, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 20, "vusers.created_by_name.Fast Queries - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateFastFilters\"": 20, "http.requests": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753687650121, "firstHistogramAt": 1753687650121, "lastCounterAt": 1753687659841, "lastHistogramAt": 1753687659193, "firstMetricAt": 1753687650121, "lastMetricAt": 1753687659841, "period": "1753687650000", "summaries": {"vusers.session_length": {"min": 2334.1, "max": 2390.4, "count": 20, "mean": 2353.7, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}, "http.response_time": {"min": 327, "max": 376, "count": 20, "mean": 345.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 368.8, "p99": 368.8, "p999": 368.8}, "http.response_time.2xx": {"min": 327, "max": 376, "count": 20, "mean": 345.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 368.8, "p99": 368.8, "p999": 368.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 327, "max": 376, "count": 20, "mean": 345.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 368.8, "p99": 368.8, "p999": 368.8}}, "histograms": {"vusers.session_length": {"min": 2334.1, "max": 2390.4, "count": 20, "mean": 2353.7, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}, "http.response_time": {"min": 327, "max": 376, "count": 20, "mean": 345.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 368.8, "p99": 368.8, "p999": 368.8}, "http.response_time.2xx": {"min": 327, "max": 376, "count": 20, "mean": 345.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 368.8, "p99": 368.8, "p999": 368.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 327, "max": 376, "count": 20, "mean": 345.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 368.8, "p99": 368.8, "p999": 368.8}}}, {"counters": {"http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83833, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 20, "vusers.failed": 0, "vusers.completed": 20, "vusers.created_by_name.Fast Queries - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateFastFilters\"": 20, "http.requests": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753687660114, "firstHistogramAt": 1753687660114, "lastCounterAt": 1753687669840, "lastHistogramAt": 1753687669207, "firstMetricAt": 1753687660114, "lastMetricAt": 1753687669840, "period": "1753687660000", "summaries": {"http.response_time": {"min": 327, "max": 584, "count": 20, "mean": 366.6, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 368.8, "p95": 539.2, "p99": 539.2, "p999": 539.2}, "http.response_time.2xx": {"min": 327, "max": 584, "count": 20, "mean": 366.6, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 368.8, "p95": 539.2, "p99": 539.2, "p999": 539.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 327, "max": 584, "count": 20, "mean": 366.6, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 368.8, "p95": 539.2, "p99": 539.2, "p999": 539.2}, "vusers.session_length": {"min": 2333.9, "max": 2595.5, "count": 20, "mean": 2377.7, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2566.3, "p99": 2566.3, "p999": 2566.3}}, "histograms": {"http.response_time": {"min": 327, "max": 584, "count": 20, "mean": 366.6, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 368.8, "p95": 539.2, "p99": 539.2, "p999": 539.2}, "http.response_time.2xx": {"min": 327, "max": 584, "count": 20, "mean": 366.6, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 368.8, "p95": 539.2, "p99": 539.2, "p999": 539.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 327, "max": 584, "count": 20, "mean": 366.6, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 368.8, "p95": 539.2, "p99": 539.2, "p999": 539.2}, "vusers.session_length": {"min": 2333.9, "max": 2595.5, "count": 20, "mean": 2377.7, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2566.3, "p99": 2566.3, "p999": 2566.3}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 24, "http.codes.200": 32, "http.responses": 32, "http.downloaded_bytes": 133913, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 32, "vusers.created_by_name.Fast Queries - Gentle Load": 34, "vusers.created": 34, "errors.Undefined function \"generateFastFilters\"": 34, "http.requests": 34}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687670117, "firstHistogramAt": 1753687670117, "lastCounterAt": 1753687679968, "lastHistogramAt": 1753687679320, "firstMetricAt": 1753687670117, "lastMetricAt": 1753687679968, "period": "1753687670000", "summaries": {"vusers.session_length": {"min": 2333, "max": 2869.3, "count": 24, "mean": 2428.3, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2725, "p95": 2780, "p99": 2836.2, "p999": 2836.2}, "http.response_time": {"min": 331, "max": 1431, "count": 32, "mean": 604.6, "p50": 354.3, "median": 354.3, "p75": 820.7, "p90": 963.1, "p95": 1380.5, "p99": 1408.4, "p999": 1408.4}, "http.response_time.2xx": {"min": 331, "max": 1431, "count": 32, "mean": 604.6, "p50": 354.3, "median": 354.3, "p75": 820.7, "p90": 963.1, "p95": 1380.5, "p99": 1408.4, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 331, "max": 1431, "count": 32, "mean": 604.6, "p50": 354.3, "median": 354.3, "p75": 820.7, "p90": 963.1, "p95": 1380.5, "p99": 1408.4, "p999": 1408.4}}, "histograms": {"vusers.session_length": {"min": 2333, "max": 2869.3, "count": 24, "mean": 2428.3, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2725, "p95": 2780, "p99": 2836.2, "p999": 2836.2}, "http.response_time": {"min": 331, "max": 1431, "count": 32, "mean": 604.6, "p50": 354.3, "median": 354.3, "p75": 820.7, "p90": 963.1, "p95": 1380.5, "p99": 1408.4, "p999": 1408.4}, "http.response_time.2xx": {"min": 331, "max": 1431, "count": 32, "mean": 604.6, "p50": 354.3, "median": 354.3, "p75": 820.7, "p90": 963.1, "p95": 1380.5, "p99": 1408.4, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 331, "max": 1431, "count": 32, "mean": 604.6, "p50": 354.3, "median": 354.3, "p75": 820.7, "p90": 963.1, "p95": 1380.5, "p99": 1408.4, "p999": 1408.4}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167528, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 44, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687680116, "firstHistogramAt": 1753687680116, "lastCounterAt": 1753687689968, "lastHistogramAt": 1753687689451, "firstMetricAt": 1753687680116, "lastMetricAt": 1753687689968, "period": "1753687680000", "summaries": {"http.response_time": {"min": 329, "max": 483, "count": 40, "mean": 349.5, "p50": 340.4, "median": 340.4, "p75": 361.5, "p90": 368.8, "p95": 368.8, "p99": 376.2, "p999": 376.2}, "http.response_time.2xx": {"min": 329, "max": 483, "count": 40, "mean": 349.5, "p50": 340.4, "median": 340.4, "p75": 361.5, "p90": 368.8, "p95": 368.8, "p99": 376.2, "p999": 376.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 329, "max": 483, "count": 40, "mean": 349.5, "p50": 340.4, "median": 340.4, "p75": 361.5, "p90": 368.8, "p95": 368.8, "p99": 376.2, "p999": 376.2}, "vusers.session_length": {"min": 2333.1, "max": 3446.8, "count": 44, "mean": 2505.4, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2951.9, "p95": 3395.5, "p99": 3395.5, "p999": 3395.5}}, "histograms": {"http.response_time": {"min": 329, "max": 483, "count": 40, "mean": 349.5, "p50": 340.4, "median": 340.4, "p75": 361.5, "p90": 368.8, "p95": 368.8, "p99": 376.2, "p999": 376.2}, "http.response_time.2xx": {"min": 329, "max": 483, "count": 40, "mean": 349.5, "p50": 340.4, "median": 340.4, "p75": 361.5, "p90": 368.8, "p95": 368.8, "p99": 376.2, "p999": 376.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 329, "max": 483, "count": 40, "mean": 349.5, "p50": 340.4, "median": 340.4, "p75": 361.5, "p90": 368.8, "p95": 368.8, "p99": 376.2, "p999": 376.2}, "vusers.session_length": {"min": 2333.1, "max": 3446.8, "count": 44, "mean": 2505.4, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2951.9, "p95": 3395.5, "p99": 3395.5, "p999": 3395.5}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167452, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687690120, "firstHistogramAt": 1753687690120, "lastCounterAt": 1753687699968, "lastHistogramAt": 1753687699330, "firstMetricAt": 1753687690120, "lastMetricAt": 1753687699968, "period": "1753687690000", "summaries": {"http.response_time": {"min": 326, "max": 535, "count": 40, "mean": 355.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 497.8, "p99": 497.8, "p999": 497.8}, "http.response_time.2xx": {"min": 326, "max": 535, "count": 40, "mean": 355.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 497.8, "p99": 497.8, "p999": 497.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 535, "count": 40, "mean": 355.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 497.8, "p99": 497.8, "p999": 497.8}, "vusers.session_length": {"min": 2329.9, "max": 2537.3, "count": 40, "mean": 2367.8, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2465.6, "p95": 2515.5, "p99": 2515.5, "p999": 2515.5}}, "histograms": {"http.response_time": {"min": 326, "max": 535, "count": 40, "mean": 355.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 497.8, "p99": 497.8, "p999": 497.8}, "http.response_time.2xx": {"min": 326, "max": 535, "count": 40, "mean": 355.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 497.8, "p99": 497.8, "p999": 497.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 535, "count": 40, "mean": 355.2, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 361.5, "p95": 497.8, "p99": 497.8, "p999": 497.8}, "vusers.session_length": {"min": 2329.9, "max": 2537.3, "count": 40, "mean": 2367.8, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2465.6, "p95": 2515.5, "p99": 2515.5, "p999": 2515.5}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 40, "http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167495, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687700118, "firstHistogramAt": 1753687700118, "lastCounterAt": 1753687709968, "lastHistogramAt": 1753687709724, "firstMetricAt": 1753687700118, "lastMetricAt": 1753687709968, "period": "1753687700000", "summaries": {"vusers.session_length": {"min": 2328.6, "max": 2878.2, "count": 40, "mean": 2419.5, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2618.1, "p95": 2780, "p99": 2893.5, "p999": 2893.5}, "http.response_time": {"min": 326, "max": 866, "count": 40, "mean": 410.9, "p50": 340.4, "median": 340.4, "p75": 368.8, "p90": 632.8, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "http.response_time.2xx": {"min": 326, "max": 866, "count": 40, "mean": 410.9, "p50": 340.4, "median": 340.4, "p75": 368.8, "p90": 632.8, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 866, "count": 40, "mean": 410.9, "p50": 340.4, "median": 340.4, "p75": 368.8, "p90": 632.8, "p95": 788.5, "p99": 854.2, "p999": 854.2}}, "histograms": {"vusers.session_length": {"min": 2328.6, "max": 2878.2, "count": 40, "mean": 2419.5, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2618.1, "p95": 2780, "p99": 2893.5, "p999": 2893.5}, "http.response_time": {"min": 326, "max": 866, "count": 40, "mean": 410.9, "p50": 340.4, "median": 340.4, "p75": 368.8, "p90": 632.8, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "http.response_time.2xx": {"min": 326, "max": 866, "count": 40, "mean": 410.9, "p50": 340.4, "median": 340.4, "p75": 368.8, "p90": 632.8, "p95": 788.5, "p99": 854.2, "p999": 854.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 866, "count": 40, "mean": 410.9, "p50": 340.4, "median": 340.4, "p75": 368.8, "p90": 632.8, "p95": 788.5, "p99": 854.2, "p999": 854.2}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 40, "http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167316, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687710125, "firstHistogramAt": 1753687710125, "lastCounterAt": 1753687719968, "lastHistogramAt": 1753687719308, "firstMetricAt": 1753687710125, "lastMetricAt": 1753687719968, "period": "1753687710000", "summaries": {"vusers.session_length": {"min": 2324.9, "max": 2703.2, "count": 40, "mean": 2374.4, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2618.1, "p99": 2618.1, "p999": 2618.1}, "http.response_time": {"min": 325, "max": 693, "count": 40, "mean": 387.6, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 584.2, "p95": 596, "p99": 608, "p999": 608}, "http.response_time.2xx": {"min": 325, "max": 693, "count": 40, "mean": 387.6, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 584.2, "p95": 596, "p99": 608, "p999": 608}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 693, "count": 40, "mean": 387.6, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 584.2, "p95": 596, "p99": 608, "p999": 608}}, "histograms": {"vusers.session_length": {"min": 2324.9, "max": 2703.2, "count": 40, "mean": 2374.4, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2618.1, "p99": 2618.1, "p999": 2618.1}, "http.response_time": {"min": 325, "max": 693, "count": 40, "mean": 387.6, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 584.2, "p95": 596, "p99": 608, "p999": 608}, "http.response_time.2xx": {"min": 325, "max": 693, "count": 40, "mean": 387.6, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 584.2, "p95": 596, "p99": 608, "p999": 608}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 693, "count": 40, "mean": 387.6, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 584.2, "p95": 596, "p99": 608, "p999": 608}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167154, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687720112, "firstHistogramAt": 1753687720112, "lastCounterAt": 1753687729968, "lastHistogramAt": 1753687729312, "firstMetricAt": 1753687720112, "lastMetricAt": 1753687729968, "period": "1753687720000", "summaries": {"http.response_time": {"min": 325, "max": 669, "count": 40, "mean": 373.2, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 399.5, "p95": 632.8, "p99": 645.6, "p999": 645.6}, "http.response_time.2xx": {"min": 325, "max": 669, "count": 40, "mean": 373.2, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 399.5, "p95": 632.8, "p99": 645.6, "p999": 645.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 669, "count": 40, "mean": 373.2, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 399.5, "p95": 632.8, "p99": 645.6, "p999": 645.6}, "vusers.session_length": {"min": 2327.6, "max": 2685.3, "count": 40, "mean": 2404, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2618.1, "p95": 2671, "p99": 2671, "p999": 2671}}, "histograms": {"http.response_time": {"min": 325, "max": 669, "count": 40, "mean": 373.2, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 399.5, "p95": 632.8, "p99": 645.6, "p999": 645.6}, "http.response_time.2xx": {"min": 325, "max": 669, "count": 40, "mean": 373.2, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 399.5, "p95": 632.8, "p99": 645.6, "p999": 645.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 669, "count": 40, "mean": 373.2, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 399.5, "p95": 632.8, "p99": 645.6, "p999": 645.6}, "vusers.session_length": {"min": 2327.6, "max": 2685.3, "count": 40, "mean": 2404, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2618.1, "p95": 2671, "p99": 2671, "p999": 2671}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 40, "http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167293, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687730129, "firstHistogramAt": 1753687730129, "lastCounterAt": 1753687739969, "lastHistogramAt": 1753687739326, "firstMetricAt": 1753687730129, "lastMetricAt": 1753687739969, "period": "1753687730000", "summaries": {"vusers.session_length": {"min": 2332.6, "max": 2968.8, "count": 40, "mean": 2440.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2725, "p95": 2893.5, "p99": 2951.9, "p999": 2951.9}, "http.response_time": {"min": 326, "max": 963, "count": 40, "mean": 433.8, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 713.5, "p95": 907, "p99": 925.4, "p999": 925.4}, "http.response_time.2xx": {"min": 326, "max": 963, "count": 40, "mean": 433.8, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 713.5, "p95": 907, "p99": 925.4, "p999": 925.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 963, "count": 40, "mean": 433.8, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 713.5, "p95": 907, "p99": 925.4, "p999": 925.4}}, "histograms": {"vusers.session_length": {"min": 2332.6, "max": 2968.8, "count": 40, "mean": 2440.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2725, "p95": 2893.5, "p99": 2951.9, "p999": 2951.9}, "http.response_time": {"min": 326, "max": 963, "count": 40, "mean": 433.8, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 713.5, "p95": 907, "p99": 925.4, "p999": 925.4}, "http.response_time.2xx": {"min": 326, "max": 963, "count": 40, "mean": 433.8, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 713.5, "p95": 907, "p99": 925.4, "p999": 925.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 963, "count": 40, "mean": 433.8, "p50": 347.3, "median": 347.3, "p75": 361.5, "p90": 713.5, "p95": 907, "p99": 925.4, "p999": 925.4}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167443, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687740145, "firstHistogramAt": 1753687740145, "lastCounterAt": 1753687749968, "lastHistogramAt": 1753687749325, "firstMetricAt": 1753687740145, "lastMetricAt": 1753687749968, "period": "1753687740000", "summaries": {"http.response_time": {"min": 331, "max": 390, "count": 40, "mean": 353.5, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "http.response_time.2xx": {"min": 331, "max": 390, "count": 40, "mean": 353.5, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 331, "max": 390, "count": 40, "mean": 353.5, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "vusers.session_length": {"min": 2336, "max": 2395.9, "count": 40, "mean": 2363, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}, "histograms": {"http.response_time": {"min": 331, "max": 390, "count": 40, "mean": 353.5, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "http.response_time.2xx": {"min": 331, "max": 390, "count": 40, "mean": 353.5, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 331, "max": 390, "count": 40, "mean": 353.5, "p50": 354.3, "median": 354.3, "p75": 361.5, "p90": 368.8, "p95": 376.2, "p99": 376.2, "p999": 376.2}, "vusers.session_length": {"min": 2336, "max": 2395.9, "count": 40, "mean": 2363, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167408, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687750116, "firstHistogramAt": 1753687750116, "lastCounterAt": 1753687759968, "lastHistogramAt": 1753687759445, "firstMetricAt": 1753687750116, "lastMetricAt": 1753687759968, "period": "1753687750000", "summaries": {"http.response_time": {"min": 328, "max": 1084, "count": 40, "mean": 460.8, "p50": 347.3, "median": 347.3, "p75": 368.8, "p90": 854.2, "p95": 1043.3, "p99": 1064.4, "p999": 1064.4}, "http.response_time.2xx": {"min": 328, "max": 1084, "count": 40, "mean": 460.8, "p50": 347.3, "median": 347.3, "p75": 368.8, "p90": 854.2, "p95": 1043.3, "p99": 1064.4, "p999": 1064.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 1084, "count": 40, "mean": 460.8, "p50": 347.3, "median": 347.3, "p75": 368.8, "p90": 854.2, "p95": 1043.3, "p99": 1064.4, "p999": 1064.4}, "vusers.session_length": {"min": 2333.6, "max": 3097.4, "count": 40, "mean": 2450, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2893.5, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}}, "histograms": {"http.response_time": {"min": 328, "max": 1084, "count": 40, "mean": 460.8, "p50": 347.3, "median": 347.3, "p75": 368.8, "p90": 854.2, "p95": 1043.3, "p99": 1064.4, "p999": 1064.4}, "http.response_time.2xx": {"min": 328, "max": 1084, "count": 40, "mean": 460.8, "p50": 347.3, "median": 347.3, "p75": 368.8, "p90": 854.2, "p95": 1043.3, "p99": 1064.4, "p999": 1064.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 1084, "count": 40, "mean": 460.8, "p50": 347.3, "median": 347.3, "p75": 368.8, "p90": 854.2, "p95": 1043.3, "p99": 1064.4, "p999": 1064.4}, "vusers.session_length": {"min": 2333.6, "max": 3097.4, "count": 40, "mean": 2450, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2893.5, "p95": 3072.4, "p99": 3072.4, "p999": 3072.4}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167208, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687760132, "firstHistogramAt": 1753687760132, "lastCounterAt": 1753687769968, "lastHistogramAt": 1753687769321, "firstMetricAt": 1753687760132, "lastMetricAt": 1753687769968, "period": "1753687760000", "summaries": {"http.response_time": {"min": 324, "max": 386, "count": 40, "mean": 346.5, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 361.5, "p99": 368.8, "p999": 368.8}, "http.response_time.2xx": {"min": 324, "max": 386, "count": 40, "mean": 346.5, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 361.5, "p99": 368.8, "p999": 368.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 324, "max": 386, "count": 40, "mean": 346.5, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 361.5, "p99": 368.8, "p999": 368.8}, "vusers.session_length": {"min": 2327.7, "max": 2649.8, "count": 40, "mean": 2374.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2566.3, "p99": 2566.3, "p999": 2566.3}}, "histograms": {"http.response_time": {"min": 324, "max": 386, "count": 40, "mean": 346.5, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 361.5, "p99": 368.8, "p999": 368.8}, "http.response_time.2xx": {"min": 324, "max": 386, "count": 40, "mean": 346.5, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 361.5, "p99": 368.8, "p999": 368.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 324, "max": 386, "count": 40, "mean": 346.5, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 361.5, "p99": 368.8, "p999": 368.8}, "vusers.session_length": {"min": 2327.7, "max": 2649.8, "count": 40, "mean": 2374.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2566.3, "p99": 2566.3, "p999": 2566.3}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167445, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687770110, "firstHistogramAt": 1753687770110, "lastCounterAt": 1753687779968, "lastHistogramAt": 1753687779338, "firstMetricAt": 1753687770110, "lastMetricAt": 1753687779968, "period": "1753687770000", "summaries": {"http.response_time": {"min": 325, "max": 954, "count": 40, "mean": 414.4, "p50": 354.3, "median": 354.3, "p75": 376.2, "p90": 478.3, "p95": 907, "p99": 944, "p999": 944}, "http.response_time.2xx": {"min": 325, "max": 954, "count": 40, "mean": 414.4, "p50": 354.3, "median": 354.3, "p75": 376.2, "p90": 478.3, "p95": 907, "p99": 944, "p999": 944}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 954, "count": 40, "mean": 414.4, "p50": 354.3, "median": 354.3, "p75": 376.2, "p90": 478.3, "p95": 907, "p99": 944, "p999": 944}, "vusers.session_length": {"min": 2332.3, "max": 2961.1, "count": 40, "mean": 2424.2, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2515.5, "p95": 2893.5, "p99": 2951.9, "p999": 2951.9}}, "histograms": {"http.response_time": {"min": 325, "max": 954, "count": 40, "mean": 414.4, "p50": 354.3, "median": 354.3, "p75": 376.2, "p90": 478.3, "p95": 907, "p99": 944, "p999": 944}, "http.response_time.2xx": {"min": 325, "max": 954, "count": 40, "mean": 414.4, "p50": 354.3, "median": 354.3, "p75": 376.2, "p90": 478.3, "p95": 907, "p99": 944, "p999": 944}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 325, "max": 954, "count": 40, "mean": 414.4, "p50": 354.3, "median": 354.3, "p75": 376.2, "p90": 478.3, "p95": 907, "p99": 944, "p999": 944}, "vusers.session_length": {"min": 2332.3, "max": 2961.1, "count": 40, "mean": 2424.2, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2515.5, "p95": 2893.5, "p99": 2951.9, "p999": 2951.9}}}, {"counters": {"http.codes.200": 40, "http.responses": 40, "http.downloaded_bytes": 167436, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 40, "vusers.failed": 0, "vusers.completed": 40, "vusers.created_by_name.Fast Queries - Gentle Load": 40, "vusers.created": 40, "errors.Undefined function \"generateFastFilters\"": 40, "http.requests": 40}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687780116, "firstHistogramAt": 1753687780116, "lastCounterAt": 1753687789968, "lastHistogramAt": 1753687789319, "firstMetricAt": 1753687780116, "lastMetricAt": 1753687789968, "period": "1753687780000", "summaries": {"http.response_time": {"min": 328, "max": 620, "count": 40, "mean": 352.4, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 376.2, "p99": 383.8, "p999": 383.8}, "http.response_time.2xx": {"min": 328, "max": 620, "count": 40, "mean": 352.4, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 376.2, "p99": 383.8, "p999": 383.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 620, "count": 40, "mean": 352.4, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 376.2, "p99": 383.8, "p999": 383.8}, "vusers.session_length": {"min": 2332.9, "max": 2632, "count": 40, "mean": 2362.4, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}, "histograms": {"http.response_time": {"min": 328, "max": 620, "count": 40, "mean": 352.4, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 376.2, "p99": 383.8, "p999": 383.8}, "http.response_time.2xx": {"min": 328, "max": 620, "count": 40, "mean": 352.4, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 376.2, "p99": 383.8, "p999": 383.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 620, "count": 40, "mean": 352.4, "p50": 347.3, "median": 347.3, "p75": 354.3, "p90": 361.5, "p95": 376.2, "p99": 383.8, "p999": 383.8}, "vusers.session_length": {"min": 2332.9, "max": 2632, "count": 40, "mean": 2362.4, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}}, {"counters": {"http.codes.200": 28, "http.responses": 28, "http.downloaded_bytes": 117243, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 28, "vusers.failed": 0, "vusers.completed": 32, "vusers.created_by_name.Fast Queries - Gentle Load": 26, "vusers.created": 26, "errors.Undefined function \"generateFastFilters\"": 26, "http.requests": 26}, "rates": {"http.request_rate": 6}, "http.request_rate": null, "firstCounterAt": 1753687790113, "firstHistogramAt": 1753687790113, "lastCounterAt": 1753687799842, "lastHistogramAt": 1753687799189, "firstMetricAt": 1753687790113, "lastMetricAt": 1753687799842, "period": "1753687790000", "summaries": {"http.response_time": {"min": 328, "max": 369, "count": 28, "mean": 342.4, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 361.5, "p99": 361.5, "p999": 361.5}, "http.response_time.2xx": {"min": 328, "max": 369, "count": 28, "mean": 342.4, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 361.5, "p99": 361.5, "p999": 361.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 369, "count": 28, "mean": 342.4, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 361.5, "p99": 361.5, "p999": 361.5}, "vusers.session_length": {"min": 2330.3, "max": 2379.6, "count": 32, "mean": 2350.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}, "histograms": {"http.response_time": {"min": 328, "max": 369, "count": 28, "mean": 342.4, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 361.5, "p99": 361.5, "p999": 361.5}, "http.response_time.2xx": {"min": 328, "max": 369, "count": 28, "mean": 342.4, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 361.5, "p99": 361.5, "p999": 361.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 369, "count": 28, "mean": 342.4, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 361.5, "p99": 361.5, "p999": 361.5}, "vusers.session_length": {"min": 2330.3, "max": 2379.6, "count": 32, "mean": 2350.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}}, {"counters": {"vusers.failed": 0, "vusers.completed": 20, "http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83709, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 20, "vusers.created_by_name.Fast Queries - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateFastFilters\"": 20, "http.requests": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753687800127, "firstHistogramAt": 1753687800127, "lastCounterAt": 1753687809842, "lastHistogramAt": 1753687809200, "firstMetricAt": 1753687800127, "lastMetricAt": 1753687809842, "period": "1753687800000", "summaries": {"vusers.session_length": {"min": 2329.7, "max": 2389.3, "count": 20, "mean": 2352, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}, "http.response_time": {"min": 328, "max": 811, "count": 20, "mean": 387.9, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 376.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "http.response_time.2xx": {"min": 328, "max": 811, "count": 20, "mean": 387.9, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 376.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 811, "count": 20, "mean": 387.9, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 376.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}}, "histograms": {"vusers.session_length": {"min": 2329.7, "max": 2389.3, "count": 20, "mean": 2352, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}, "http.response_time": {"min": 328, "max": 811, "count": 20, "mean": 387.9, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 376.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "http.response_time.2xx": {"min": 328, "max": 811, "count": 20, "mean": 387.9, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 376.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 328, "max": 811, "count": 20, "mean": 387.9, "p50": 340.4, "median": 340.4, "p75": 354.3, "p90": 376.2, "p95": 772.9, "p99": 772.9, "p999": 772.9}}}, {"counters": {"http.codes.200": 20, "http.responses": 20, "http.downloaded_bytes": 83654, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 20, "vusers.failed": 0, "vusers.completed": 20, "vusers.created_by_name.Fast Queries - Gentle Load": 20, "vusers.created": 20, "errors.Undefined function \"generateFastFilters\"": 20, "http.requests": 20}, "rates": {"http.request_rate": 2}, "http.request_rate": null, "firstCounterAt": 1753687810138, "firstHistogramAt": 1753687810138, "lastCounterAt": 1753687819842, "lastHistogramAt": 1753687819193, "firstMetricAt": 1753687810138, "lastMetricAt": 1753687819842, "period": "1753687810000", "summaries": {"http.response_time": {"min": 323, "max": 813, "count": 20, "mean": 385.6, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "http.response_time.2xx": {"min": 323, "max": 813, "count": 20, "mean": 385.6, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 323, "max": 813, "count": 20, "mean": 385.6, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "vusers.session_length": {"min": 2329.9, "max": 2825.8, "count": 20, "mean": 2437.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2780, "p95": 2836.2, "p99": 2836.2, "p999": 2836.2}}, "histograms": {"http.response_time": {"min": 323, "max": 813, "count": 20, "mean": 385.6, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "http.response_time.2xx": {"min": 323, "max": 813, "count": 20, "mean": 385.6, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 323, "max": 813, "count": 20, "mean": 385.6, "p50": 340.4, "median": 340.4, "p75": 347.3, "p90": 354.3, "p95": 772.9, "p99": 772.9, "p999": 772.9}, "vusers.session_length": {"min": 2329.9, "max": 2825.8, "count": 20, "mean": 2437.1, "p50": 2369, "median": 2369, "p75": 2369, "p90": 2780, "p95": 2836.2, "p99": 2836.2, "p999": 2836.2}}}, {"counters": {"http.codes.200": 8, "http.responses": 8, "http.downloaded_bytes": 33595, "plugins.metrics-by-endpoint.GET /api/mugshots-fast - Fast Queries (Gentle).codes.200": 8, "vusers.failed": 0, "vusers.completed": 12, "vusers.created_by_name.Fast Queries - Gentle Load": 6, "vusers.created": 6, "errors.Undefined function \"generateFastFilters\"": 6, "http.requests": 6}, "rates": {"http.request_rate": 4}, "http.request_rate": null, "firstCounterAt": 1753687820116, "firstHistogramAt": 1753687820116, "lastCounterAt": 1753687825186, "lastHistogramAt": 1753687825186, "firstMetricAt": 1753687820116, "lastMetricAt": 1753687825186, "period": "1753687820000", "summaries": {"http.response_time": {"min": 326, "max": 348, "count": 8, "mean": 334.1, "p50": 333.7, "median": 333.7, "p75": 333.7, "p90": 333.7, "p95": 333.7, "p99": 333.7, "p999": 333.7}, "http.response_time.2xx": {"min": 326, "max": 348, "count": 8, "mean": 334.1, "p50": 333.7, "median": 333.7, "p75": 333.7, "p90": 333.7, "p95": 333.7, "p99": 333.7, "p999": 333.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 348, "count": 8, "mean": 334.1, "p50": 333.7, "median": 333.7, "p75": 333.7, "p90": 333.7, "p95": 333.7, "p99": 333.7, "p999": 333.7}, "vusers.session_length": {"min": 2336.4, "max": 2368, "count": 12, "mean": 2348.4, "p50": 2322.1, "median": 2322.1, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}, "histograms": {"http.response_time": {"min": 326, "max": 348, "count": 8, "mean": 334.1, "p50": 333.7, "median": 333.7, "p75": 333.7, "p90": 333.7, "p95": 333.7, "p99": 333.7, "p999": 333.7}, "http.response_time.2xx": {"min": 326, "max": 348, "count": 8, "mean": 334.1, "p50": 333.7, "median": 333.7, "p75": 333.7, "p90": 333.7, "p95": 333.7, "p99": 333.7, "p999": 333.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots-fast - Fast Queries (Gentle)": {"min": 326, "max": 348, "count": 8, "mean": 334.1, "p50": 333.7, "median": 333.7, "p75": 333.7, "p90": 333.7, "p95": 333.7, "p99": 333.7, "p999": 333.7}, "vusers.session_length": {"min": 2336.4, "max": 2368, "count": 12, "mean": 2348.4, "p50": 2322.1, "median": 2322.1, "p75": 2369, "p90": 2369, "p95": 2369, "p99": 2369, "p999": 2369}}}]}