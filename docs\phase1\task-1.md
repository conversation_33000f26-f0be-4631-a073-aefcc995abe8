# Task 1: Foundation Setup and Analysis + Query Client Configuration and Provider Setup

## Overview

This task establishes the foundational infrastructure for modern state management by installing TanStack Query, creating a centralized query client with performance-optimized settings, and setting up the provider structure needed for all subsequent implementation phases. This work is critical because it creates the backbone that will support server state management, caching, optimistic updates, and realtime features throughout the entire application.

**Core Deliverables:**
- TanStack Query installation and configuration
- Centralized QueryClient with performance-optimized defaults
- Provider setup in app layout for universal access
- Development tools integration for debugging
- Comprehensive documentation of existing API patterns
- Error boundary implementation for robust error handling

## Prerequisites

**Knowledge Requirements:**
- Understanding of React Query/TanStack Query concepts (queries, mutations, cache)
- Familiarity with React Context providers and app-level configuration
- Next.js App Router patterns and layout.tsx structure
- Error boundary implementation in React 18+

**Environment Setup:**
- Node.js environment with npm/yarn access
- Development server running
- Access to package.json for dependency management

**Dependencies:**
- Existing Next.js application with App Router
- React 18+ already installed
- Current Zustand stores documented and understood

## Detailed Implementation Checklist

### 1. Package Installation and Dependencies

- [ ] **1.1 Install TanStack Query core packages**
  ```bash
  npm install @tanstack/react-query @tanstack/react-query-devtools
  ```
  - Verify installation in package.json
  - Confirm version compatibility with React 18+
  - Document version numbers for consistency

- [ ] **1.2 Install optional performance packages**
  ```bash
  npm install @tanstack/query-sync-storage-persister @tanstack/react-query-persist-client
  ```
  - These enable cache persistence for improved UX
  - Required for offline capability future enhancement

- [ ] **1.3 Verify no dependency conflicts**
  - Run `npm audit` to check for vulnerabilities
  - Ensure no peer dependency warnings
  - Test that development server still starts correctly

### 2. Query Client Configuration

- [ ] **2.1 Create centralized query client configuration**
  - **File:** `lib/query/query-client.ts`
  - **Purpose:** Single source of truth for all TanStack Query configuration
  
  ```typescript
  import { QueryClient } from '@tanstack/react-query'
  
  export const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes before considering it stale
        staleTime: 5 * 60 * 1000,
        // Keep data in cache for 10 minutes after components unmount  
        cacheTime: 10 * 60 * 1000,
        // Retry failed queries 3 times with exponential backoff
        retry: 3,
        // Don't refetch when window regains focus (saves API calls)
        refetchOnWindowFocus: false,
        // Don't refetch when component remounts if data is fresh
        refetchOnMount: false,
        // Enable background refetching for fresh data
        refetchOnReconnect: true,
      },
      mutations: {
        // Retry failed mutations once
        retry: 1,
      },
    },
  })
  ```

- [ ] **2.2 Create query key factory for standardization**
  - **File:** `lib/query/query-keys.ts`
  - **Purpose:** Centralized query key management for cache invalidation
  
  ```typescript
  export const queryKeys = {
    // Mugshots listing with filters and pagination
    mugshots: (filters: MugshotFilters, pagination: PaginationOptions) => 
      ['mugshots', filters, pagination] as const,
    
    // Individual mugshot detail data
    mugshot: (id: string) => ['mugshot', id] as const,
    mugshotDetail: (id: string) => ['mugshot', id, 'detail'] as const,
    
    // User-specific data for authenticated users
    userMugshotData: (mugshotId: string) => ['user', 'mugshot', mugshotId, 'data'] as const,
    
    // Rating and tag related queries
    ratings: (mugshotId: string) => ['mugshot', mugshotId, 'ratings'] as const,
    tags: (mugshotId: string) => ['mugshot', mugshotId, 'tags'] as const,
    
    // User profile data
    userProfile: (userId: string) => ['user', userId, 'profile'] as const,
  } as const
  ```

- [ ] **2.3 Create development tools configuration**
  - **File:** `lib/query/devtools.ts`
  - **Purpose:** Development debugging and monitoring setup
  
  ```typescript
  import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
  import { ReactNode } from 'react'
  
  interface QueryDevtoolsProps {
    children: ReactNode
  }
  
  export function QueryDevtools({ children }: QueryDevtoolsProps) {
    return (
      <>
        {children}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools 
            initialIsOpen={false}
            position="bottom-right"
          />
        )}
      </>
    )
  }
  ```

### 3. Provider Setup in App Layout

- [ ] **3.1 Create QueryClientProvider wrapper component**
  - **File:** `lib/providers/query-provider.tsx`
  - **Purpose:** Reusable provider component with error boundary integration
  
  ```typescript
  'use client'
  
  import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
  import { ReactNode, useState } from 'react'
  import { QueryDevtools } from '@/lib/query/devtools'
  
  interface QueryProviderProps {
    children: ReactNode
  }
  
  export function QueryProvider({ children }: QueryProviderProps) {
    // Create query client instance per component tree
    const [queryClient] = useState(() => new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 5 * 60 * 1000, // 5 minutes
          cacheTime: 10 * 60 * 1000, // 10 minutes
          retry: 3,
          refetchOnWindowFocus: false,
          refetchOnMount: false,
          refetchOnReconnect: true,
        },
        mutations: {
          retry: 1,
        },
      },
    }))
  
    return (
      <QueryClientProvider client={queryClient}>
        <QueryDevtools>
          {children}
        </QueryDevtools>
      </QueryClientProvider>
    )
  }
  ```

- [ ] **3.2 Integrate provider into app layout**
  - **File:** `app/layout.tsx` (modify existing)
  - **Change:** Add QueryProvider wrapper while preserving all existing providers
  
  ```typescript
  // Add import at top
  import { QueryProvider } from '@/lib/providers/query-provider'
  
  // Wrap existing children with QueryProvider (innermost wrapper)
  export default function RootLayout({
    children,
  }: {
    children: React.ReactNode
  }) {
    return (
      <html lang="en">
        <body className={inter.className}>
          {/* Existing providers like ThemeProvider, AuthProvider */}
          <QueryProvider>
            {/* All existing layout content */}
            {children}
          </QueryProvider>
        </body>
      </html>
    )
  }
  ```

### 4. Error Boundary Implementation

- [ ] **4.1 Create comprehensive query error boundary**
  - **File:** `components/error-boundaries/QueryErrorBoundary.tsx`
  - **Purpose:** Graceful error handling for all server state operations
  
  ```typescript
  'use client'
  
  import React, { ErrorInfo, ReactNode } from 'react'
  import { Button } from '@/components/ui/button'
  import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
  
  interface QueryErrorBoundaryProps {
    children: ReactNode
    fallback?: ReactNode
  }
  
  interface QueryErrorBoundaryState {
    hasError: boolean
    error: Error | null
  }
  
  export class QueryErrorBoundary extends React.Component<
    QueryErrorBoundaryProps,
    QueryErrorBoundaryState
  > {
    constructor(props: QueryErrorBoundaryProps) {
      super(props)
      this.state = { hasError: false, error: null }
    }
  
    static getDerivedStateFromError(error: Error): QueryErrorBoundaryState {
      return { hasError: true, error }
    }
  
    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
      console.error('Query Error Boundary caught an error:', error, errorInfo)
      
      // Log to error tracking service in production
      if (process.env.NODE_ENV === 'production') {
        // TODO: Integrate with error tracking service
      }
    }
  
    handleReset = () => {
      this.setState({ hasError: false, error: null })
    }
  
    render() {
      if (this.state.hasError) {
        if (this.props.fallback) {
          return this.props.fallback
        }
  
        return (
          <Alert variant="destructive" className="m-4">
            <AlertTitle>Something went wrong</AlertTitle>
            <AlertDescription>
              We encountered an error while loading data. Please try again.
            </AlertDescription>
            <Button 
              onClick={this.handleReset}
              variant="outline"
              className="mt-4"
            >
              Try Again
            </Button>
          </Alert>
        )
      }
  
      return this.props.children
    }
  }
  ```

- [ ] **4.2 Create specific error fallback components**
  - **File:** `components/error-boundaries/ErrorFallbacks.tsx`
  - **Purpose:** Specific error UI for different data types
  
  ```typescript
  import { Button } from '@/components/ui/button'
  import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
  
  export function MugshotsErrorFallback({ 
    error, 
    resetErrorBoundary 
  }: { 
    error: Error
    resetErrorBoundary: () => void 
  }) {
    return (
      <Card className="m-4">
        <CardHeader>
          <CardTitle>Unable to load mugshots</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            We're having trouble loading the mugshot data. This might be a temporary issue.
          </p>
          <Button onClick={resetErrorBoundary}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  export function MugshotDetailErrorFallback({ 
    error, 
    resetErrorBoundary 
  }: { 
    error: Error
    resetErrorBoundary: () => void 
  }) {
    return (
      <div className="p-6 text-center">
        <h3 className="text-lg font-semibold mb-2">Unable to load mugshot details</h3>
        <p className="text-muted-foreground mb-4">
          We couldn't load the details for this mugshot.
        </p>
        <Button onClick={resetErrorBoundary} size="sm">
          Try Again
        </Button>
      </div>
    )
  }
  ```

### 5. API Endpoint Documentation and Analysis

- [ ] **5.1 Create comprehensive API endpoint documentation**
  - **File:** `docs/api-endpoints.md`
  - **Purpose:** Document all existing endpoints for migration reference
  
  ```markdown
  # Existing API Endpoints Analysis
  
  ## Core Data Endpoints
  
  ### GET /api/mugshots
  - **Purpose:** Paginated mugshots listing with filters
  - **Parameters:** page, perPage, state, county, searchTerm, includeTotal
  - **Response:** { success: boolean, data: { mugshots: Mugshot[], pagination: PaginationData } }
  - **Usage:** Main mugshots page, filtering, search
  - **Cache Strategy:** 5 minutes stale time, background refetch
  
  ### GET /api/mugshots/[id]
  - **Purpose:** Single mugshot with complete ratings/tags statistics
  - **Parameters:** id (mugshot ID)
  - **Response:** { success: boolean, data: { mugshot: Mugshot, ratings: RatingData, tags: TagData } }
  - **Usage:** Mugshot detail popup, fresh data display
  - **Cache Strategy:** 1 minute stale time (fresher for popups)
  
  ### GET /api/user/mugshot/[id]/data
  - **Purpose:** User-specific rating/tag data (requires auth)
  - **Parameters:** id (mugshot ID)
  - **Response:** { success: boolean, data: { userRating: number, userTags: string[] } }
  - **Usage:** Show user's previous ratings/tags
  - **Cache Strategy:** 30 seconds stale time (user data changes frequently)
  
  ## Interaction Endpoints
  
  ### POST /api/ratings/submit
  - **Purpose:** Submit user rating for mugshot
  - **Body:** { mugshotId: string, rating: number }
  - **Response:** { success: boolean, data: { averageRating: number, totalRatings: number } }
  - **Usage:** Rating interactions, optimistic updates
  
  ### POST /api/tags/toggle
  - **Purpose:** Toggle user tag for mugshot
  - **Body:** { mugshotId: string, tagType: string }
  - **Response:** { success: boolean, data: { tagCounts: Record<string, number> } }
  - **Usage:** Tag interactions, optimistic updates
  ```

- [ ] **5.2 Map existing Zustand store usage patterns**
  - **File:** `docs/zustand-store-analysis.md`
  - **Purpose:** Document current state management for migration planning
  
  ```markdown
  # Current Zustand Store Analysis
  
  ## auth-store.ts - User Authentication (✅ Keep as-is)
  - **Purpose:** User session, authentication state, profile data
  - **Migration:** Keep for client-side auth state, move server data to Query
  - **Usage:** Header, user nav, authentication guards
  
  ## filter-store.ts - Search Filters (🔄 Enhance with Query)
  - **Purpose:** Search filters, URL synchronization
  - **Migration:** Keep client state, add Query integration for server calls
  - **Usage:** Mugshots page filtering, URL parameter management
  
  ## rating-store.ts - Rating Data (🔄 Replace with Query)
  - **Purpose:** Rating data, optimistic updates
  - **Migration:** Replace with TanStack Query mutations
  - **Usage:** Rating interface, mugshot detail popup
  
  ## tag-store.ts - Tag Data (🔄 Replace with Query)
  - **Purpose:** Tag data, optimistic updates  
  - **Migration:** Replace with TanStack Query mutations
  - **Usage:** Tag interface, mugshot detail popup
  ```

### 6. Testing Infrastructure Setup

- [ ] **6.1 Create query testing utilities**
  - **File:** `lib/testing/query-test-utils.tsx`
  - **Purpose:** Helper functions for testing components with TanStack Query
  
  ```typescript
  import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
  import { render, RenderOptions } from '@testing-library/react'
  import { ReactElement, ReactNode } from 'react'
  
  // Create a clean query client for each test
  function createTestQueryClient() {
    return new QueryClient({
      defaultOptions: {
        queries: {
          retry: false, // Disable retry in tests
          cacheTime: 0, // No cache in tests
        },
        mutations: {
          retry: false,
        },
      },
    })
  }
  
  interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
    queryClient?: QueryClient
  }
  
  // Custom render function that includes QueryClient provider
  export function renderWithQuery(
    ui: ReactElement,
    options: CustomRenderOptions = {}
  ) {
    const { queryClient = createTestQueryClient(), ...renderOptions } = options
  
    function Wrapper({ children }: { children: ReactNode }) {
      return (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      )
    }
  
    return {
      ...render(ui, { wrapper: Wrapper, ...renderOptions }),
      queryClient,
    }
  }
  
  // Mock successful API responses for testing
  export const mockApiResponses = {
    mugshots: {
      success: true,
      data: {
        mugshots: [
          {
            id: '1',
            firstName: 'John',
            lastName: 'Doe',
            imagePath: '/test-image.jpg',
            // ... other mock data
          }
        ],
        pagination: {
          currentPage: 1,
          totalPages: 5,
          totalCount: 50,
        }
      }
    }
  }
  ```

- [ ] **6.2 Create basic integration tests**
  - **File:** `__tests__/query-setup.test.tsx`
  - **Purpose:** Verify query client setup and provider integration
  
  ```typescript
  import { renderWithQuery } from '@/lib/testing/query-test-utils'
  import { useQueryClient } from '@tanstack/react-query'
  
  function TestComponent() {
    const queryClient = useQueryClient()
    return <div data-testid="test">Query client available: {!!queryClient}</div>
  }
  
  describe('Query Setup', () => {
    test('QueryClient is available in component tree', () => {
      const { getByTestId } = renderWithQuery(<TestComponent />)
      expect(getByTestId('test')).toHaveTextContent('Query client available: true')
    })
  
    test('Query client has correct default options', () => {
      const { queryClient } = renderWithQuery(<TestComponent />)
      const defaultOptions = queryClient.getDefaultOptions()
      
      expect(defaultOptions.queries?.retry).toBe(false) // Test environment
      expect(defaultOptions.queries?.cacheTime).toBe(0) // Test environment
    })
  })
  ```

## Acceptance Criteria Verification

### Requirement 1.1-1.7 (Comprehensive Analysis Before Changes)
- [ ] ✅ All existing API endpoints documented with usage patterns
- [ ] ✅ All Zustand stores analyzed and migration paths defined  
- [ ] ✅ Component dependency mapping completed
- [ ] ✅ Impact analysis documented for each change

### Requirement 3.1-3.3 (Server State Management with TanStack Query)
- [ ] ✅ TanStack Query installed and configured
- [ ] ✅ Query client with automatic caching and background refetching
- [ ] ✅ Error handling with automatic retry mechanisms
- [ ] ✅ Foundation for optimistic updates established

### Requirement 7.1-7.2 (Error Handling and Recovery)
- [ ] ✅ User-friendly error boundaries implemented
- [ ] ✅ Error tracking and logging setup
- [ ] ✅ Recovery mechanisms in place

## Files to Create/Modify

### New Files Created:
- `lib/query/query-client.ts` - Centralized query client configuration
- `lib/query/query-keys.ts` - Standardized query key management
- `lib/query/devtools.ts` - Development tools setup
- `lib/providers/query-provider.tsx` - Reusable provider component
- `components/error-boundaries/QueryErrorBoundary.tsx` - Error boundary for queries
- `components/error-boundaries/ErrorFallbacks.tsx` - Specific error UI components
- `docs/api-endpoints.md` - API endpoint documentation
- `docs/zustand-store-analysis.md` - Current state management analysis
- `lib/testing/query-test-utils.tsx` - Testing utilities
- `__tests__/query-setup.test.tsx` - Integration tests

### Existing Files Modified:
- `app/layout.tsx` - Add QueryProvider wrapper
- `package.json` - Add TanStack Query dependencies

## Implementation Notes

### Configuration Rationale:
- **5-minute stale time:** Balances fresh data with API efficiency for mugshot listings
- **1-minute stale time for details:** Ensures popup data is fresher without excessive requests
- **30-second stale time for user data:** User-specific data changes more frequently
- **No refetch on window focus:** Prevents unnecessary API calls during browsing
- **3 retries with exponential backoff:** Handles temporary network issues gracefully

### Performance Considerations:
- Query client instance created per app mount to prevent memory leaks
- Development tools only loaded in development environment
- Error boundaries prevent entire app crashes from query failures
- Cache sizes automatically managed by TanStack Query's LRU algorithm

### Integration Patterns:
- Provider setup follows Next.js App Router patterns
- Error boundaries use existing shadcn UI components for consistency
- Query keys use TypeScript const assertions for type safety
- Testing utilities support existing Jest/React Testing Library setup

## Testing Checklist

### Manual Verification:
- [ ] Development server starts without errors after installation
- [ ] React Query DevTools appear in development mode (bottom-right corner)
- [ ] Error boundary displays correctly when test error is thrown
- [ ] No console errors or warnings in browser

### Automated Testing:
- [ ] All new test files pass: `npm test`
- [ ] No TypeScript compilation errors: `npm run type-check`
- [ ] No linting errors: `npm run lint`

### Performance Verification:
- [ ] Initial bundle size impact is minimal (<50KB gzipped)
- [ ] Development tools don't affect production builds
- [ ] Error boundaries don't impact normal operation performance

## Next Steps

This foundation enables all subsequent implementation phases:

1. **Task 2 (Core Query Hooks):** 
   - Can now implement useMugshotsQuery, useMugshotDetailQuery, useUserMugshotDataQuery
   - Query client and provider infrastructure is ready

2. **Task 3 (Optimistic Mutations):**
   - Error boundaries will handle mutation failures gracefully
   - Query client configured for optimal mutation retry behavior

3. **Task 4 (Realtime Integration):**
   - Query cache invalidation infrastructure ready for realtime updates
   - Error handling established for connection failures

4. **Task 5 (Component Migration):**
   - Provider setup allows any component to access TanStack Query
   - Testing utilities ready for migration validation

The foundation provides robust error handling, development debugging, performance optimization, and backward compatibility that will support the entire state management refactor while maintaining zero risk to existing functionality. 