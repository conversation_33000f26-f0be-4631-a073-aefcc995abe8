'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

/**
 * Navigation Progress Hook
 * Provides immediate click feedback and triggers skeleton loading
 * Uses custom events for proper timing coordination
 */
export function useNavigationProgress() {
  const [clickedElement, setClickedElement] = useState<string | null>(null)
  const router = useRouter()

  const navigateWithProgress = (href: string, elementId?: string) => {
    // Immediate visual feedback on the clicked element
    if (elementId) {
      setClickedElement(elementId)
      
      // Clear click state after brief delay
      setTimeout(() => setClickedElement(null), 120)
    }

    // Dispatch navigation started event for skeleton loading
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('navigation-started'))
    }

    // Navigate immediately
    router.push(href)
  }

  return {
    clickedElement,
    navigateWithProgress,
    isElementClicked: (elementId: string) => clickedElement === elementId
  }
}

/**
 * Enhanced Link Component Props
 * For components that need navigation with progress feedback
 */
export interface NavLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  id?: string
  onClick?: () => void
} 