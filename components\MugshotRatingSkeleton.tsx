import { Skeleton } from '@/components/ui/skeleton'

interface MugshotRatingSkeletonProps {
  className?: string
}

export default function MugshotRatingSkeleton({ className = '' }: MugshotRatingSkeletonProps) {
  return (
    <div className={`bg-gray-800/50 rounded-lg p-4 ${className}`}>
      {/* Rating Display Skeleton */}
      <div className="text-center mb-4">
        <div className="flex items-center justify-center gap-1">
          <Skeleton className="h-12 w-20 bg-gray-700/50" /> {/* Rating number */}
          <Skeleton className="h-4 w-16 bg-gray-700/30" /> {/* "out of 10" */}
          <Skeleton className="h-4 w-20 bg-gray-700/30 ml-1" /> {/* "(X ratings)" */}
        </div>
      </div>

      {/* Tags Section Skeleton */}
      <div className="mb-4">
        <div className="flex flex-wrap justify-center gap-2">
          {/* Show 2-3 tag skeletons */}
          <Skeleton className="h-8 w-20 bg-gray-700/50 rounded-lg" />
          <Skeleton className="h-8 w-24 bg-gray-700/50 rounded-lg" />
          <Skeleton className="h-8 w-22 bg-gray-700/50 rounded-lg" />
        </div>
      </div>

      {/* Click to Rate Button Skeleton */}
      <div className="text-center">
        <Skeleton className="h-10 w-24 bg-gray-700/50 rounded border-2 border-gray-600/50" />
      </div>
    </div>
  )
} 