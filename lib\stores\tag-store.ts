import { getUserMugshotData } from '@/lib/services/api-client'
import { useAuthStore } from '@/lib/stores/auth-store'
import { create } from 'zustand'

interface TagStatistics {
  wild: number
  funny: number 
  spooky: number
}

interface TagStore {
  // Track which tags the user has selected for each mugshot
  userTags: Record<string, Set<string>> // mugshotId -> Set of tag types the user has tagged
  
  // Track tag statistics for each mugshot  
  tagStatistics: Record<string, TagStatistics> // mugshotId -> tag counts
  
  // Loading states
  submittingTags: Record<string, Set<string>> // mugshotId -> Set of tag types being submitted
  loadingTagData: Record<string, boolean> // mugshotId -> is loading tag data
  
  // Optimistic updates tracking
  optimisticTagChanges: Record<string, Set<string>> // mugshotId -> pending tag changes
  
  // Actions
  getUserTags: (mugshotId: string) => Promise<string[]>
  setUserTags: (mugshotId: string, tagTypes: string[]) => void
  setTagStatistics: (mugshotId: string, stats: TagStatistics) => void
  setLoadingTagData: (mugshotId: string, isLoading: boolean) => void
  toggleTagOptimistic: (mugshotId: string, tagType: string) => void
  confirmTagChange: (mugshotId: string, tagType: string, newStats: TagStatistics) => void
  revertTagChange: (mugshotId: string, tagType: string) => void
  setSubmitting: (mugshotId: string, tagType: string, isSubmitting: boolean) => void
  clearCache: () => void
  clearMugshotCache: (mugshotId: string) => void
}

export const useTagStore = create<TagStore>((set, get) => ({
  userTags: {},
  tagStatistics: {},
  submittingTags: {},
  loadingTagData: {},
  optimisticTagChanges: {},

  getUserTags: async (mugshotId: string) => {
    const state = get()
    
    // Check if user is authenticated before making API call
    const authStore = useAuthStore.getState()
    if (!authStore.isAuthenticated || !authStore.user) {
      console.log('User not authenticated, skipping user tags load')
      // Set empty tags for unauthenticated users
      set(state => ({
        userTags: {
          ...state.userTags,
          [mugshotId]: new Set()
        },
        loadingTagData: {
          ...state.loadingTagData,
          [mugshotId]: false
        }
      }))
      return []
    }
    
    // Prevent duplicate requests
    if (state.loadingTagData[mugshotId]) {
      // Wait for the existing request to complete
      while (get().loadingTagData[mugshotId]) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      const userTags = get().userTags[mugshotId]
      return userTags ? Array.from(userTags) : []
    }
    
    // Mark as loading
    set(state => ({
      loadingTagData: {
        ...state.loadingTagData,
        [mugshotId]: true
      }
    }))
    
    try {
      const response = await getUserMugshotData(parseInt(mugshotId))
      
      if (response.success && response.data) {
        // Update user tags
        set(state => ({
          userTags: {
            ...state.userTags,
            [mugshotId]: new Set(response.data!.userTags)
          },
          loadingTagData: {
            ...state.loadingTagData,
            [mugshotId]: false
          }
        }))
        
        return response.data.userTags
      } else {
        throw new Error(response.message || 'Failed to get user tags')
      }
    } catch (error) {
      console.error('Error fetching user tags:', error)
      
      set(state => ({
        loadingTagData: {
          ...state.loadingTagData,
          [mugshotId]: false
        }
      }))
      
      return []
    }
  },

  setUserTags: (mugshotId: string, tagTypes: string[]) => {
    set(state => ({
      userTags: {
        ...state.userTags,
        [mugshotId]: new Set(tagTypes)
      }
    }))
  },

  setTagStatistics: (mugshotId: string, stats: TagStatistics) => {
    console.log(`[TagStore] setTagStatistics called:`, {
      mugshotId,
      stats,
      timestamp: new Date().toISOString(),
      stackTrace: new Error().stack?.split('\n')[2]?.trim()
    })
    
    set(state => ({
      tagStatistics: {
        ...state.tagStatistics,
        [mugshotId]: stats
      }
    }))
    
    console.log(`[TagStore] setTagStatistics completed for ${mugshotId}:`, stats)
  },

  setLoadingTagData: (mugshotId: string, isLoading: boolean) => {
    set(state => ({
      loadingTagData: {
        ...state.loadingTagData,
        [mugshotId]: isLoading
      }
    }))
  },

  toggleTagOptimistic: (mugshotId: string, tagType: string) => {
    set(state => {
      const currentTags = state.userTags[mugshotId] || new Set()
      const newTags = new Set(currentTags)
      const currentStats = state.tagStatistics[mugshotId] || { wild: 0, funny: 0, spooky: 0 }
      const newStats = { ...currentStats }
      
      // Track optimistic change
      const currentOptimistic = state.optimisticTagChanges[mugshotId] || new Set()
      const newOptimistic = new Set(currentOptimistic)
      
      if (newTags.has(tagType)) {
        // Removing tag
        newTags.delete(tagType)
        newStats[tagType as keyof TagStatistics] = Math.max(0, newStats[tagType as keyof TagStatistics] - 1)
        newOptimistic.add(`remove-${tagType}`)
        newOptimistic.delete(`add-${tagType}`)
      } else {
        // Adding tag
        newTags.add(tagType)
        newStats[tagType as keyof TagStatistics] = newStats[tagType as keyof TagStatistics] + 1
        newOptimistic.add(`add-${tagType}`)
        newOptimistic.delete(`remove-${tagType}`)
      }
      
      return {
        userTags: {
          ...state.userTags,
          [mugshotId]: newTags
        },
        tagStatistics: {
          ...state.tagStatistics,
          [mugshotId]: newStats
        },
        optimisticTagChanges: {
          ...state.optimisticTagChanges,
          [mugshotId]: newOptimistic
        }
      }
    })
  },

  confirmTagChange: (mugshotId: string, tagType: string, newStats: TagStatistics) => {
    set(state => {
      const currentOptimistic = state.optimisticTagChanges[mugshotId] || new Set()
      const newOptimistic = new Set(currentOptimistic)
      
      // Remove confirmed changes from optimistic tracking
      newOptimistic.delete(`add-${tagType}`)
      newOptimistic.delete(`remove-${tagType}`)
      
      return {
        tagStatistics: {
          ...state.tagStatistics,
          [mugshotId]: newStats
        },
        optimisticTagChanges: {
          ...state.optimisticTagChanges,
          [mugshotId]: newOptimistic
        }
      }
    })
  },

  revertTagChange: (mugshotId: string, tagType: string) => {
    set(state => {
      const currentTags = state.userTags[mugshotId] || new Set()
      const newTags = new Set(currentTags)
      const currentStats = state.tagStatistics[mugshotId] || { wild: 0, funny: 0, spooky: 0 }
      const newStats = { ...currentStats }
      const currentOptimistic = state.optimisticTagChanges[mugshotId] || new Set()
      const newOptimistic = new Set(currentOptimistic)
      
      // Revert optimistic change
      if (newOptimistic.has(`add-${tagType}`)) {
        // Revert added tag
        newTags.delete(tagType)
        newStats[tagType as keyof TagStatistics] = Math.max(0, newStats[tagType as keyof TagStatistics] - 1)
        newOptimistic.delete(`add-${tagType}`)
      } else if (newOptimistic.has(`remove-${tagType}`)) {
        // Revert removed tag
        newTags.add(tagType)
        newStats[tagType as keyof TagStatistics] = newStats[tagType as keyof TagStatistics] + 1
        newOptimistic.delete(`remove-${tagType}`)
      }
      
      return {
        userTags: {
          ...state.userTags,
          [mugshotId]: newTags
        },
        tagStatistics: {
          ...state.tagStatistics,
          [mugshotId]: newStats
        },
        optimisticTagChanges: {
          ...state.optimisticTagChanges,
          [mugshotId]: newOptimistic
        }
      }
    })
  },

  setSubmitting: (mugshotId: string, tagType: string, isSubmitting: boolean) => {
    set(state => {
      const currentSubmitting = state.submittingTags[mugshotId] || new Set()
      const newSubmitting = new Set(currentSubmitting)
      
      if (isSubmitting) {
        newSubmitting.add(tagType)
      } else {
        newSubmitting.delete(tagType)
      }
      
      return {
        submittingTags: {
          ...state.submittingTags,
          [mugshotId]: newSubmitting
        }
      }
    })
  },

  clearCache: () => {
    set({ 
      userTags: {}, 
      tagStatistics: {},
      submittingTags: {},
      loadingTagData: {},
      optimisticTagChanges: {}
    })
  },

  clearMugshotCache: (mugshotId: string) => {
    set(state => {
      const newUserTags = { ...state.userTags }
      const newTagStatistics = { ...state.tagStatistics }
      const newSubmittingTags = { ...state.submittingTags }
      const newLoadingTagData = { ...state.loadingTagData }
      const newOptimisticTagChanges = { ...state.optimisticTagChanges }
      
      delete newUserTags[mugshotId]
      delete newTagStatistics[mugshotId]
      delete newSubmittingTags[mugshotId]
      delete newLoadingTagData[mugshotId]
      delete newOptimisticTagChanges[mugshotId]
      
      return {
        userTags: newUserTags,
        tagStatistics: newTagStatistics,
        submittingTags: newSubmittingTags,
        loadingTagData: newLoadingTagData,
        optimisticTagChanges: newOptimisticTagChanges
      }
    })
  }
})) 