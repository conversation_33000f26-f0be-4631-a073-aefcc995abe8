// Client-side API service for making requests to our API endpoints

import type { UIMugshot } from '@/lib/utils/mugshot-transforms'
import type { TagType } from '@/lib/constants'

export interface ApiResponse<T = unknown> {
  success: boolean
  message?: string
  error?: string
  data?: T
}

// Rating API functions
export interface RatingStatistics {
  averageRating: number
  totalRatings: number
  userRating?: number | null
}

export interface RatingSubmissionData {
  mugshotId: number
  rating: number
}

export interface RatingSubmissionResult {
  success: boolean
  message: string
  rating?: {
    mugshotId: number
    rating: number
    userId: string
  }
  error?: string
}

export async function submitRating(data: RatingSubmissionData): Promise<RatingSubmissionResult> {
  try {
    const response = await fetch('/api/ratings/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Rating submission API error:', error)
    return {
      success: false,
      message: 'Network error occurred. Please try again.',
      error: 'NETWORK_ERROR'
    }
  }
}

// REMOVED: getRatingStatistics - use /api/mugshots/[id] instead

export async function getBulkRatingStatistics(mugshotIds: number[]): Promise<Record<number, RatingStatistics>> {
  try {
    const response = await fetch('/api/ratings/statistics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ mugshotIds }),
    })

    const data = await response.json()
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch bulk rating statistics')
    }
    
    return data.statistics
  } catch (error) {
    console.error('Bulk rating statistics API error:', error)
    return {}
  }
}

// Tag API functions
export interface TagStatistics {
  wild: number
  funny: number
  spooky: number
  totalTags: number
}

export interface TagToggleData {
  mugshotId: number
  tagType: 'wild' | 'funny' | 'spooky'
}

export interface TagToggleResult {
  success: boolean
  message: string
  tag?: {
    mugshotId: number
    tagType: string
    action: 'added' | 'removed'
    userId: string
  }
  error?: string
}

export async function toggleTag(data: TagToggleData): Promise<TagToggleResult> {
  try {
    const response = await fetch('/api/tags/toggle', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Tag toggle API error:', error)
    return {
      success: false,
      message: 'Network error occurred. Please try again.',
      error: 'NETWORK_ERROR'
    }
  }
}

export async function getTagStatistics(mugshotId: number): Promise<TagStatistics> {
  try {
    const response = await fetch(`/api/tags/statistics?mugshotId=${mugshotId}`)
    const data = await response.json()
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch tag statistics')
    }
    
    return data.statistics
  } catch (error) {
    console.error('Tag statistics API error:', error)
    return { wild: 0, funny: 0, spooky: 0, totalTags: 0 }
  }
}

export async function getBulkTagStatistics(mugshotIds: number[]): Promise<Record<number, TagStatistics>> {
  try {
    const response = await fetch('/api/tags/statistics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ mugshotIds }),
    })

    const data = await response.json()
    
    if (!response.ok || !data.success) {
      throw new Error(data.message || 'Failed to fetch bulk tag statistics')
    }
    
    return data.statistics
  } catch (error) {
    console.error('Bulk tag statistics API error:', error)
    return {}
  }
}

// New combined tag API function
export interface MugshotTagInfo {
  tag_type: string
  tag_count: number
  current_user_tagged: boolean
  id: string
}

export interface MugshotTagsResponse {
  success: boolean
  data?: {
    tags: MugshotTagInfo[]
    statistics: TagStatistics
    userTags: string[]
    meta: {
      mugshotId: number
      includedUserData: boolean
      timestamp: string
    }
  }
  message?: string
  error?: string
}

// NEW: Get user-specific data (rating + tags) for authenticated users
export interface UserMugshotData {
  userRating: number | null
  userTags: string[]
  meta: {
    mugshotId: number
    userId: string
    fetchedAt: string
  }
}

export interface UserMugshotResponse {
  success: boolean
  data?: UserMugshotData
  message?: string
  error?: string
}

export async function getUserMugshotData(mugshotId: number): Promise<UserMugshotResponse> {
  try {
    const response = await fetch(`/api/user/mugshot/${mugshotId}/data`)
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch user mugshot data')
    }
    
    return data
  } catch (error) {
    console.error('User mugshot data API error:', error)
    return {
      success: false,
      message: 'Network error occurred. Please try again.',
      error: 'NETWORK_ERROR'
    }
  }
}

// Auth API functions - Using existing endpoints
export interface LoginData {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  fullName: string
  selectedState: string
  selectedCounty: string
}

export interface AuthResult {
  success: boolean
  message?: string
  error?: string
  errors?: Record<string, string>
  redirectTo?: string
  needsLocationSetup?: boolean
  profile?: {
    id: string
    user_id: string
    email: string | null
    full_name: string
    state: string | null
    county: string | null
    role: string
    avatar_url: string | null
    created_at: string
    updated_at: string
  }
}

export async function loginUser(data: LoginData): Promise<AuthResult> {
  try {
    const response = await fetch('/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Login API error:', error)
    return {
      success: false,
      error: 'Network error occurred. Please try again.'
    }
  }
}

export async function signupUser(data: SignupData): Promise<AuthResult> {
  try {
    const response = await fetch('/api/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Signup API error:', error)
    return {
      success: false,
      error: 'Network error occurred. Please try again.'
    }
  }
}

export async function logoutUser(): Promise<AuthResult> {
  try {
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Logout API error:', error)
    return {
      success: false,
      error: 'Network error occurred. Please try again.'
    }
  }
}

// Mugshots API functions
export interface MugshotFilters {
  search?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  categories?: string[] // Legacy field for backward compatibility
  tags?: TagType[] // Updated to use proper TagType
}

export interface MugshotsPaginationOptions {
  page?: number
  perPage?: number
  includeTotal?: boolean
}

export interface MugshotsSortOptions {
  sortBy?: 'newest' | 'top-rated' | 'most-viewed'
}

export interface MugshotsResponse {
  success: boolean
  data?: {
    mugshots: UIMugshot[]
    pagination: {
      page: number
      perPage: number
      totalCount?: number
      hasNextPage: boolean
      hasPreviousPage: boolean
    }
    filters: {
      applied: Record<string, unknown>
      sortBy: string
    }
    meta: {
      totalResults: number
      includedUserData: boolean
      timestamp: string
    }
  }
  message?: string
  error?: string
}

export interface SingleMugshotResponse {
  success: boolean
  data?: {
    mugshot: UIMugshot
    ratings: {
      averageRating: number
      totalRatings: number
      allRatings: number[]
      ratingDistribution: Record<number, number>
    }
    tags: {
      totalTags: number
      tagCounts: {
        wild: number
        funny: number
        spooky: number
      }
      allTags: string[]
      popularTags: string[]
    }
    meta: {
      fetchedAt: string
      mugshotId: number
      hasRatings: boolean
      hasTags: boolean
      dataFresh: boolean
    }
  }
  message?: string
  error?: string
}

export async function getMugshots(
  filters: MugshotFilters = {},
  sortOptions: MugshotsSortOptions = {},
  paginationOptions: MugshotsPaginationOptions = {}
): Promise<MugshotsResponse> {
  try {
    const params = new URLSearchParams()
    
    // Add filter parameters
    if (filters.search) params.set('search', filters.search)
    if (filters.state) params.set('state', filters.state)
    if (filters.county) params.set('county', filters.county)
    if (filters.dateFrom) params.set('dateFrom', filters.dateFrom)
    if (filters.dateTo) params.set('dateTo', filters.dateTo)
    if (filters.categories?.length) params.set('categories', filters.categories.join(','))
    if (filters.tags?.length) params.set('tags', filters.tags.join(','))
    
    // Add sort parameters
    if (sortOptions.sortBy) params.set('sortBy', sortOptions.sortBy)
    
    // Add pagination parameters
    if (paginationOptions.page) params.set('page', paginationOptions.page.toString())
    if (paginationOptions.perPage) params.set('perPage', paginationOptions.perPage.toString())
    if (paginationOptions.includeTotal) params.set('includeTotal', 'true')
    
    const response = await fetch(`/api/mugshots?${params.toString()}`)
    const result = await response.json()
    
    if (!response.ok) {
      throw new Error(result.message || 'Failed to fetch mugshots')
    }
    
    return result
  } catch (error) {
    console.error('Get mugshots API error:', error)
    return {
      success: false,
      message: 'Failed to fetch mugshots',
      error: 'NETWORK_ERROR'
    }
  }
}

export async function getSingleMugshot(mugshotId: number): Promise<SingleMugshotResponse> {
  try {
    const response = await fetch(`/api/mugshots/${mugshotId}`)
    const result = await response.json()
    
    if (!response.ok) {
      throw new Error(result.message || 'Failed to fetch mugshot')
    }
    
    return result
  } catch (error) {
    console.error('Get single mugshot API error:', error)
    return {
      success: false,
      message: 'Failed to fetch mugshot details',
      error: 'NETWORK_ERROR'
    }
  }
} 