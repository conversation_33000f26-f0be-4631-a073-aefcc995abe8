import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createProfile, getProfile, updateProfile } from '@/lib/profile-utils'

// Mock Supabase
const mockSupabase = {
  from: vi.fn(() => ({
    insert: vi.fn(),
    select: vi.fn(),
    update: vi.fn(),
    eq: vi.fn(() => ({
      select: vi.fn(),
      single: vi.fn()
    }))
  }))
}

describe('Profile Management', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createProfile', () => {
    it('should create a new user profile with metadata', async () => {
      const mockInsert = vi.fn().mockResolvedValue({
        data: [{ 
          id: 'profile-123',
          user_id: 'user-123',
          full_name: '<PERSON>',
          state: 'CA',
          county: 'Los Angeles',
          role: 'user'
        }],
        error: null
      })

      mockSupabase.from.mockReturnValue({
        insert: mockInsert
      })

      const profileData = {
        userId: 'user-123',
        fullName: '<PERSON>',
        state: 'CA',
        county: 'Los Angeles'
      }

      const result = await createProfile(mockSupabase as any, profileData)

      expect(mockSupabase.from).toHaveBeenCalledWith('profiles')
      expect(mockInsert).toHaveBeenCalledWith({
        user_id: 'user-123',
        full_name: 'John Doe',
        state: 'CA',
        county: 'Los Angeles',
        role: 'user'
      })
      expect(result.success).toBe(true)
    })

    it('should handle profile creation errors', async () => {
      const mockInsert = vi.fn().mockResolvedValue({
        data: null,
        error: { message: 'Profile already exists' }
      })

      mockSupabase.from.mockReturnValue({
        insert: mockInsert
      })

      const profileData = {
        userId: 'user-123',
        fullName: 'John Doe',
        state: 'CA',
        county: 'Los Angeles'
      }

      const result = await createProfile(mockSupabase as any, profileData)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Profile already exists')
    })
  })

  describe('getProfile', () => {
    it('should retrieve user profile by user ID', async () => {
      const mockSingle = vi.fn().mockResolvedValue({
        data: {
          id: 'profile-123',
          user_id: 'user-123',
          full_name: 'John Doe',
          state: 'CA',
          county: 'Los Angeles',
          role: 'user'
        },
        error: null
      })

      const mockEq = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: mockSingle
        })
      })

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: mockEq
        })
      })

      const result = await getProfile(mockSupabase as any, 'user-123')

      expect(mockSupabase.from).toHaveBeenCalledWith('profiles')
      expect(mockEq).toHaveBeenCalledWith('user_id', 'user-123')
      expect(result.success).toBe(true)
      expect(result.profile?.role).toBe('user')
    })

    it('should handle profile not found', async () => {
      const mockSingle = vi.fn().mockResolvedValue({
        data: null,
        error: { message: 'Profile not found' }
      })

      const mockEq = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: mockSingle
        })
      })

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: mockEq
        })
      })

      const result = await getProfile(mockSupabase as any, 'nonexistent-user')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Profile not found')
    })
  })
}) 