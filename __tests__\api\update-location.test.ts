import { describe, it, expect, vi, beforeEach } from 'vitest'
import { POST } from '@/app/api/update-location/route'
import { NextRequest } from 'next/server'

// Mock Supabase with proper chaining
const mockSelect = vi.fn()
const mockEq = vi.fn()
const mockSingle = vi.fn()
const mockUpdate = vi.fn()
const mockInsert = vi.fn()

const mockSupabase = {
  auth: {
    getUser: vi.fn()
  },
  from: vi.fn(() => ({
    select: mockSelect.mockReturnValue({
      eq: mockEq.mockReturnValue({
        single: mockSingle
      })
    }),
    update: mockUpdate.mockReturnValue({
      eq: vi.fn().mockResolvedValue({ error: null })
    }),
    insert: mockInsert.mockResolvedValue({ error: null })
  }))
}

vi.mock('@/lib/supabase/server', () => ({
  createClient: vi.fn(() => mockSupabase)
}))

describe('Update Location API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should update location when profile exists', async () => {
    // Mock authenticated user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          user_metadata: { full_name: 'Test User' }
        }
      },
      error: null
    })

    // Mock existing profile found
    mockSingle.mockResolvedValue({
      data: {
        id: 'profile-123',
        user_id: 'user-123',
        full_name: 'Test User',
        state: 'CA',
        county: 'Los Angeles'
      },
      error: null
    })

    const request = new NextRequest('http://localhost/api/update-location', {
      method: 'POST',
      body: JSON.stringify({
        userId: 'user-123',
        state: 'NY',
        county: 'Manhattan'
      })
    })

    const response = await POST(request)
    const result = await response.json()

    expect(result.success).toBe(true)
    expect(mockUpdate).toHaveBeenCalledWith({
      state: 'NY',
      county: 'Manhattan',
      updated_at: expect.any(String)
    })
  })

  it('should create profile when profile does not exist', async () => {
    // Mock authenticated user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-456',
          email: '<EMAIL>',
          user_metadata: { full_name: 'New User' }
        }
      },
      error: null
    })

    // Mock profile not found
    mockSingle.mockResolvedValue({
      data: null,
      error: { code: 'PGRST116' } // Not found error
    })

    const request = new NextRequest('http://localhost/api/update-location', {
      method: 'POST',
      body: JSON.stringify({
        userId: 'user-456',
        state: 'TX',
        county: 'Harris'
      })
    })

    const response = await POST(request)
    const result = await response.json()

    expect(result.success).toBe(true)
    expect(mockInsert).toHaveBeenCalledWith({
      user_id: 'user-456',
      full_name: 'New User',
      email: '<EMAIL>',
      state: 'TX',
      county: 'Harris',
      role: 'user',
      created_at: expect.any(String),
      updated_at: expect.any(String)
    })
  })

  it('should create profile with fallback name when metadata missing', async () => {
    // Mock authenticated user with minimal metadata
    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-789',
          email: '<EMAIL>',
          user_metadata: {} // No full_name
        }
      },
      error: null
    })

    // Mock profile not found
    mockSingle.mockResolvedValue({
      data: null,
      error: { code: 'PGRST116' }
    })

    const request = new NextRequest('http://localhost/api/update-location', {
      method: 'POST',
      body: JSON.stringify({
        userId: 'user-789',
        state: 'FL',
        county: 'Miami-Dade'
      })
    })

    const response = await POST(request)
    const result = await response.json()

    expect(result.success).toBe(true)
    expect(mockInsert).toHaveBeenCalledWith({
      user_id: 'user-789',
      full_name: 'minimal', // Falls back to email prefix
      email: '<EMAIL>',
      state: 'FL',
      county: 'Miami-Dade',
      role: 'user',
      created_at: expect.any(String),
      updated_at: expect.any(String)
    })
  })

  it('should handle unauthorized access', async () => {
    // Mock unauthenticated user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    })

    const request = new NextRequest('http://localhost/api/update-location', {
      method: 'POST',
      body: JSON.stringify({
        userId: 'user-123',
        state: 'CA',
        county: 'Los Angeles'
      })
    })

    const response = await POST(request)
    const result = await response.json()

    expect(response.status).toBe(401)
    expect(result.success).toBe(false)
    expect(result.error).toBe('Unauthorized')
  })

  it('should prevent users from updating other users profiles', async () => {
    // Mock authenticated user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-123',
          email: '<EMAIL>'
        }
      },
      error: null
    })

    const request = new NextRequest('http://localhost/api/update-location', {
      method: 'POST',
      body: JSON.stringify({
        userId: 'different-user-456', // Different user ID
        state: 'CA',
        county: 'Los Angeles'
      })
    })

    const response = await POST(request)
    const result = await response.json()

    expect(response.status).toBe(403)
    expect(result.success).toBe(false)
    expect(result.error).toBe('Forbidden')
  })
}) 