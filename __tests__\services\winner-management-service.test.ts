import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  getWinners, 
  getEligibleMugshots, 
  assignDailyWinner, 
  assignWeeklyWinner,
  reverseAssignment,
  getAssignmentHistory,
  getWinnerStats,
  exportAssignmentData,
  type WinnerAssignment
} from '@/lib/services/winner-management-service'

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: vi.fn()
  },
  from: vi.fn(),
  rpc: vi.fn()
}

vi.mock('@/lib/supabase/server', () => ({
  createClient: () => mockSupabase
}))

vi.mock('next/cache', () => ({
  revalidatePath: vi.fn()
}))

vi.mock('next/headers', () => ({
  cookies: vi.fn()
}))

describe('WinnerManagementService', () => {
  const mockAdmin = {
    id: 'admin-123',
    email: '<EMAIL>'
  }

  const mockProfile = {
    role: 'admin'
  }

  const mockWinners = [
    {
      id: 'winner-1',
      date: '2024-01-15',
      selection_method: 'automatic',
      mugshot: {
        id: 'mugshot-1',
        first_name: '<PERSON>',
        last_name: 'Doe',
        image_path: '/images/john.jpg',
        state_of_booking: 'California',
        county_of_booking: 'Los Angeles'
      }
    },
    {
      id: 'winner-2',
      date: '2024-01-14',
      selection_method: 'manual',
      manual_override_reason: 'Tie breaker decision',
      mugshot: {
        id: 'mugshot-2',
        first_name: 'Jane',
        last_name: 'Smith',
        image_path: '/images/jane.jpg',
        state_of_booking: 'Texas',
        county_of_booking: 'Harris'
      }
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default auth mocks
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockAdmin }
    })
    
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: mockProfile
      }),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      insert: vi.fn().mockResolvedValue({ data: null, error: null })
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Authentication and Authorization', () => {
    it('should require authentication', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null }
      })

      await expect(getWinners('daily')).rejects.toThrow('Authentication required')
    })

    it('should require admin role', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { role: 'user' }
        })
      })

      await expect(getWinners('daily')).rejects.toThrow('Admin access required')
    })

    it('should allow admin access', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockProfile
        }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: mockWinners,
          error: null
        })
      })

      const result = await getWinners('daily')
      expect(result).toHaveLength(2)
    })
  })

  describe('getWinners', () => {
    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockProfile
        }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: mockWinners,
          error: null
        })
      })
    })

    it('should fetch winners successfully', async () => {
      const result = await getWinners('daily', 10)
      
      expect(result).toHaveLength(2)
      expect(result[0]).toMatchObject({
        id: 'winner-1',
        date: '2024-01-15',
        selectionMethod: 'automatic',
        mugshot: {
          id: 'mugshot-1',
          firstName: 'John',
          lastName: 'Doe'
        }
      })
    })

    it('should handle different competition types', async () => {
      await getWinners('weekly')
      
      expect(mockSupabase.from).toHaveBeenCalledWith('weekly_winners')
    })

    it('should apply limit correctly', async () => {
      const mockLimit = vi.fn().mockResolvedValue({ data: [], error: null })
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        limit: mockLimit
      })

      await getWinners('daily', 25)
      
      expect(mockLimit).toHaveBeenCalledWith(25)
    })

    it('should handle database errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: null,
          error: new Error('Database error')
        })
      })

      await expect(getWinners('daily')).rejects.toThrow('Failed to load daily winners')
    })
  })

  describe('getEligibleMugshots', () => {
    const mockMugshots = [
      {
        id: 'mugshot-1',
        first_name: 'John',
        last_name: 'Doe',
        image_path: '/images/john.jpg',
        state_of_booking: 'California',
        county_of_booking: 'Los Angeles',
        date_of_booking: '2024-01-15'
      }
    ]

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        gte: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: mockMugshots,
          error: null
        })
      })
    })

    it('should fetch eligible mugshots', async () => {
      const result = await getEligibleMugshots('daily', '2024-01-15', 50)
      
      expect(result).toEqual(mockMugshots)
    })

    it('should apply date filtering for different competition types', async () => {
      const mockGte = vi.fn().mockReturnThis()
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        gte: mockGte,
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({ data: mockMugshots, error: null })
      })

      await getEligibleMugshots('weekly')
      
      expect(mockGte).toHaveBeenCalledWith('date_of_booking', expect.any(String))
    })
  })

  describe('assignDailyWinner', () => {
    const mockAssignment: WinnerAssignment = {
      competitionType: 'daily',
      date: '2024-01-15',
      mugshotId: 'mugshot-123',
      reason: 'Test assignment',
      assignmentType: 'manual_selection'
    }

    const mockRpcResponse = {
      success: true,
      assignment_id: 'assignment-123',
      new_winner: 'mugshot-123'
    }

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        insert: vi.fn().mockResolvedValue({ data: null, error: null })
      })
      
      mockSupabase.rpc.mockResolvedValue({
        data: mockRpcResponse,
        error: null
      })
    })

    it('should assign daily winner successfully', async () => {
      const result = await assignDailyWinner(mockAssignment)
      
      expect(result).toEqual(mockRpcResponse)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('assign_manual_daily_winner', {
        p_date: '2024-01-15',
        p_mugshot_id: 'mugshot-123',
        p_admin_id: 'admin-123',
        p_reason: 'Test assignment',
        p_assignment_type: 'manual_selection'
      })
    })

    it('should require date for daily assignments', async () => {
      const invalidAssignment = { ...mockAssignment, date: undefined }
      
      await expect(assignDailyWinner(invalidAssignment)).rejects.toThrow(
        'Date is required for daily winner assignment'
      )
    })

    it('should log moderation action', async () => {
      const mockInsert = vi.fn().mockResolvedValue({ data: null, error: null })
      
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'moderation_actions') {
          return {
            insert: mockInsert
          }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      await assignDailyWinner(mockAssignment)
      
      expect(mockInsert).toHaveBeenCalledWith({
        admin_id: 'admin-123',
        action_type: 'manual_winner_assignment',
        target_type: 'daily',
        target_id: '2024-01-15',
        reason: 'Test assignment',
        new_state: {
          mugshot_id: 'mugshot-123',
          assignment_type: 'manual_selection'
        }
      })
    })

    it('should handle RPC errors', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: new Error('RPC error')
      })

      await expect(assignDailyWinner(mockAssignment)).rejects.toThrow(
        'Failed to assign daily winner'
      )
    })

    it('should handle unsuccessful RPC responses', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: { success: false, error: 'Custom error' },
        error: null
      })

      await expect(assignDailyWinner(mockAssignment)).rejects.toThrow('Custom error')
    })
  })

  describe('assignWeeklyWinner', () => {
    const mockAssignment: WinnerAssignment = {
      competitionType: 'weekly',
      competitionId: 'comp-123',
      mugshotId: 'mugshot-123',
      reason: 'Test weekly assignment',
      assignmentType: 'override'
    }

    const mockRpcResponse = {
      success: true,
      assignment_id: 'assignment-123',
      competition_id: 'comp-123',
      new_winner: 'mugshot-123'
    }

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        insert: vi.fn().mockResolvedValue({ data: null, error: null })
      })
      
      mockSupabase.rpc.mockResolvedValue({
        data: mockRpcResponse,
        error: null
      })
    })

    it('should assign weekly winner successfully', async () => {
      const result = await assignWeeklyWinner(mockAssignment)
      
      expect(result).toEqual(mockRpcResponse)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('assign_manual_weekly_winner', {
        p_competition_id: 'comp-123',
        p_candidate_id: 'mugshot-123',
        p_admin_id: 'admin-123',
        p_reason: 'Test weekly assignment',
        p_assignment_type: 'override'
      })
    })

    it('should require competition ID for weekly assignments', async () => {
      const invalidAssignment = { ...mockAssignment, competitionId: undefined }
      
      await expect(assignWeeklyWinner(invalidAssignment)).rejects.toThrow(
        'Competition ID is required for weekly winner assignment'
      )
    })
  })

  describe('reverseAssignment', () => {
    const mockRpcResponse = {
      success: true,
      assignment_id: 'assignment-123',
      reversal_reason: 'Test reversal'
    }

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        insert: vi.fn().mockResolvedValue({ data: null, error: null })
      })
      
      mockSupabase.rpc.mockResolvedValue({
        data: mockRpcResponse,
        error: null
      })
    })

    it('should reverse assignment successfully', async () => {
      const result = await reverseAssignment('assignment-123', 'Test reversal')
      
      expect(result).toEqual(mockRpcResponse)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('reverse_winner_assignment', {
        p_assignment_id: 'assignment-123',
        p_admin_id: 'admin-123',
        p_reversal_reason: 'Test reversal'
      })
    })

    it('should log reversal action', async () => {
      const mockInsert = vi.fn().mockResolvedValue({ data: null, error: null })
      
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'moderation_actions') {
          return { insert: mockInsert }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      await reverseAssignment('assignment-123', 'Test reversal')
      
      expect(mockInsert).toHaveBeenCalledWith({
        admin_id: 'admin-123',
        action_type: 'reverse_winner_assignment',
        target_type: 'assignment',
        target_id: 'assignment-123',
        reason: 'Test reversal',
        new_state: { reversed: true }
      })
    })
  })

  describe('getAssignmentHistory', () => {
    const mockAssignments = [
      {
        id: 'assignment-1',
        competition_type: 'daily',
        competition_id: '2024-01-15',
        assignment_reason: 'Test assignment',
        assignment_type: 'manual_selection',
        is_reversed: false,
        created_at: '2024-01-15T10:00:00Z'
      }
    ]

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: mockAssignments,
          error: null
        })
      })
    })

    it('should fetch assignment history', async () => {
      const result = await getAssignmentHistory()
      
      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        id: 'assignment-1',
        competitionType: 'daily',
        assignmentType: 'manual_selection'
      })
    })

    it('should filter by competition type', async () => {
      const mockEq = vi.fn().mockReturnThis()
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: mockEq,
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({ data: mockAssignments, error: null })
      })

      await getAssignmentHistory('daily')
      
      expect(mockEq).toHaveBeenCalledWith('competition_type', 'daily')
    })
  })

  describe('getWinnerStats', () => {
    beforeEach(() => {
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          gte: vi.fn().mockReturnThis(),
          count: 10
        }
      })
    })

    it('should calculate winner statistics', async () => {
      const result = await getWinnerStats('daily')
      
      expect(result).toMatchObject({
        totalWinners: 10,
        manualAssignments: 10,
        recentActivity: 10,
        automaticWinners: 0
      })
    })
  })

  describe('exportAssignmentData', () => {
    const mockAssignments = [
      {
        id: 'assignment-1',
        competition_type: 'daily',
        assignment_reason: 'Test',
        created_at: '2024-01-15T10:00:00Z',
        admin: { first_name: 'John', last_name: 'Admin' },
        new_winner: { first_name: 'Jane', last_name: 'Winner' },
        previous_winner: null
      }
    ]

    beforeEach(() => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockResolvedValue({
          data: mockAssignments,
          error: null
        })
      })
    })

    it('should generate CSV export data', async () => {
      const result = await exportAssignmentData()
      
      expect(result).toContain('Assignment ID,Competition Type')
      expect(result).toContain('assignment-1,daily')
      expect(result).toContain('Jane Winner')
    })

    it('should apply filters to export', async () => {
      const mockGte = vi.fn().mockReturnThis()
      const mockEq = vi.fn().mockReturnThis()
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: mockEq,
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        gte: mockGte,
        lte: vi.fn().mockResolvedValue({ data: mockAssignments, error: null })
      })

      await exportAssignmentData({
        competitionType: 'daily',
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      })
      
      expect(mockEq).toHaveBeenCalledWith('competition_type', 'daily')
      expect(mockGte).toHaveBeenCalledWith('created_at', '2024-01-01')
    })
  })
}) 