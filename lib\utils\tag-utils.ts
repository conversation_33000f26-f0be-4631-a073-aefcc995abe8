import { tagEmojis, TAG_TYPES, TAG_TYPE_DISPLAY, type TagType } from '@/lib/constants'

// Re-export TagType for convenience (keep using constants.ts as source of truth)
export type { TagType } from '@/lib/constants'

/**
 * Converts UI display format (title case) to database format (lowercase)
 * Now handles both title case and lowercase inputs for URL parameter compatibility
 */
export function convertUITagToDB(uiTag: string): TagType | null {
  // Normalize the input to handle both cases
  const normalizedTag = uiTag.toLowerCase()
  
  switch (normalizedTag) {
    case 'wild':
      return TAG_TYPES.WILD
    case 'funny':
      return TAG_TYPES.FUNNY
    case 'spooky':
      return TAG_TYPES.SPOOKY
    default:
      return null
  }
}

/**
 * Converts database format (lowercase) to UI display format (title case)
 */
export function convertDBTagToUI(dbTag: TagType): string {
  return TAG_TYPE_DISPLAY[dbTag]
}

/**
 * Converts array of UI tags to database format
 */
export function convertUITagArrayToDB(uiTags: string[]): TagType[] {
  return uiTags.map(convertUITagToDB).filter((tag): tag is TagType => tag !== null)
}

/**
 * Converts array of database tags to UI format
 */
export function convertDBTagArrayToUI(dbTags: TagType[]): string[] {
  return dbTags.map(convertDBTagToUI)
}

// Interface for tag statistics from database
export interface TagStatistics {
  wild: number
  funny: number
  spooky: number
  totalTags: number
}

// Interface for the tag badge to display
export interface TagBadge {
  type: TagType
  displayName: string
  emoji: string
  count: number
}

/**
 * Determines which tag badge to show on a mugshot card based on tag counts
 * Rules:
 * 1. Show tag with highest count
 * 2. If tied, show any one (deterministic: wild > funny > spooky)
 * 3. If no tags, return null
 */
export function getDisplayTagBadge(tagStats: TagStatistics): TagBadge | null {
  const { wild, funny, spooky } = tagStats
  
  // If no tags at all, return null
  if (wild === 0 && funny === 0 && spooky === 0) {
    return null
  }
  
  // Find the maximum count
  const maxCount = Math.max(wild, funny, spooky)
  
  // Determine which tag to show (priority order for ties: wild > funny > spooky)
  let selectedTagType: TagType
  
  if (wild === maxCount) {
    selectedTagType = TAG_TYPES.WILD
  } else if (funny === maxCount) {
    selectedTagType = TAG_TYPES.FUNNY
  } else {
    selectedTagType = TAG_TYPES.SPOOKY
  }
  
  return {
    type: selectedTagType,
    displayName: TAG_TYPE_DISPLAY[selectedTagType],
    emoji: tagEmojis[TAG_TYPE_DISPLAY[selectedTagType]],
    count: maxCount
  }
}

/**
 * Validates if a tag type is valid
 */
export function isValidTagType(tagType: string): tagType is TagType {
  return Object.values(TAG_TYPES).includes(tagType as TagType)
}

/**
 * Converts database tag type to display format
 */
export function getTagDisplayInfo(tagType: TagType) {
  const displayName = TAG_TYPE_DISPLAY[tagType]
  return {
    displayName,
    emoji: tagEmojis[displayName],
    type: tagType
  }
}

/**
 * Gets all tag types for iteration
 */
export function getAllTagTypes(): TagType[] {
  return Object.values(TAG_TYPES)
}

/**
 * Creates empty tag statistics object
 */
export function createEmptyTagStats(): TagStatistics {
  return {
    wild: 0,
    funny: 0,
    spooky: 0,
    totalTags: 0
  }
} 