config:
  target: 'http://localhost:3000'
  phases:
    # Warm-up phase - gradually increase load
    - duration: 60
      arrivalRate: 10
      name: "Warm-up phase"
    # Ramp-up phase - build to target load
    - duration: 120
      arrivalRate: 50
      rampTo: 200
      name: "Ramp-up phase"
    # Peak load phase - simulate 10,000 concurrent users
    - duration: 300
      arrivalRate: 200
      name: "Peak load phase (10k concurrent users)"
    # Sustained load phase - maintain high load
    - duration: 180
      arrivalRate: 150
      name: "Sustained load phase"
    # Cool-down phase - gradually reduce load
    - duration: 60
      arrivalRate: 150
      rampTo: 10
      name: "Cool-down phase"
  
  # Performance thresholds and SLA requirements
  ensure:
    # Response time thresholds
    - http.response_time.p95: 2000  # 95% of requests under 2 seconds
    - http.response_time.p99: 5000  # 99% of requests under 5 seconds
    - http.response_time.median: 500  # Median response time under 500ms
    
    # Error rate thresholds
    - http.codes.200: 95  # At least 95% success rate
    - http.codes.4xx: 3   # Less than 3% client errors
    - http.codes.5xx: 2   # Less than 2% server errors
    
    # Throughput requirements
    - http.request_rate: 100  # Minimum 100 requests per second

  # Load test configuration
  http:
    timeout: 30  # 30 second timeout for requests
    pool: 50     # Connection pool size
  
  # Metrics and reporting
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    
  # Environment variables for dynamic configuration
  variables:
    baseUrl: 'http://localhost:3000'
    apiPath: '/api'

# Test scenarios
scenarios:
  # Main mugshots API load test (80% of traffic)
  - name: "Mugshots API Load Test"
    weight: 80
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "{{ baseUrl }}{{ apiPath }}/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
            sortBy: "{{ sortBy }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
          name: "GET /api/mugshots"
          capture:
            - json: "$.data.mugshots[0].id"
              as: "firstMugshotId"
      - think: 1  # 1 second think time between requests

  # Mugshot details API load test (15% of traffic)
  - name: "Mugshot Details API Load Test"
    weight: 15
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "{{ baseUrl }}{{ apiPath }}/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id]"
      - think: 2  # 2 second think time for detail views

  # Mixed realistic user behavior (5% of traffic)
  - name: "Realistic User Journey"
    weight: 5
    flow:
      # First, browse mugshots
      - function: "generateRandomFilters"
      - get:
          url: "{{ baseUrl }}{{ apiPath }}/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            sortBy: "{{ sortBy }}"
          name: "Browse mugshots"
          capture:
            - json: "$.data.mugshots[0].id"
              as: "selectedMugshotId"
      - think: 3
      
      # Then view a specific mugshot
      - get:
          url: "{{ baseUrl }}{{ apiPath }}/mugshots/{{ selectedMugshotId }}"
          name: "View mugshot details"
      - think: 5
      
      # Browse more with different filters
      - function: "generateRandomFilters"
      - get:
          url: "{{ baseUrl }}{{ apiPath }}/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            tags: "{{ tags }}"
            sortBy: "top-rated"
          name: "Browse with filters"
      - think: 2

# Custom functions for generating realistic test data
processor: "./load-tests/scenarios/data-generators.js"
