# Scaling Test - Find Maximum Capacity
# Progressive load increase to find your system's limits
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 30
      arrivalRate: 5
      name: "Phase 1: Baseline (5/sec)"
    - duration: 60
      arrivalRate: 10
      name: "Phase 2: Double load (10/sec)"
    - duration: 60
      arrivalRate: 15
      name: "Phase 3: Triple load (15/sec)"
    - duration: 60
      arrivalRate: 20
      name: "Phase 4: Quad load (20/sec)"
    - duration: 30
      arrivalRate: 5
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 2000   # Allow higher for scaling test
    - http.response_time.median: 800 # Allow higher under load
    - http.codes.200: 90             # Still expect high success
    - http.codes.5xx: 5              # Allow some errors at high load

  http:
    timeout: 20
    pool: 25  # Increased for higher concurrency
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Baseline - Scaling Test"
    weight: 100
    flow:
      - function: "generateBaselineFilters"
      - get:
          url: "/api/mugshots-fast"
          qs:
            mode: "baseline"
            page: "{{ page }}"
            perPage: "{{ perPage }}"
          name: "GET /api/mugshots-fast - Scaling"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
            - hasProperty: "data.pagination"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.data.pagination.totalCount"
              as: "totalResults"
      - think: 1

processor: "./load-tests/focused-tests/data-generators-focused.js"
