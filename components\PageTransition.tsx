'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { Skeleton } from '@/components/ui/skeleton'

/**
 * Page Transition Component  
 * Uses pathname changes to provide accurate navigation loading states
 * Shows skeleton only when navigation is actually happening
 */
export default function PageTransition({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const [isNavigating, setIsNavigating] = useState(false)
  const [previousPathname, setPreviousPathname] = useState<string>('')

  useEffect(() => {
    // Set initial pathname on mount
    if (!previousPathname) {
      setPreviousPathname(pathname)
      return
    }

    // If pathname changed, we were navigating
    if (previousPathname !== pathname) {
      setIsNavigating(false) // Navigation completed
      setPreviousPathname(pathname)
    }
  }, [pathname, previousPathname])

  // Listen for navigation start events from our navigation hook
  useEffect(() => {
    const handleNavigationStart = () => {
      setIsNavigating(true)
    }

    // Listen for custom navigation events
    window.addEventListener('navigation-started', handleNavigationStart)
    
    return () => {
      window.removeEventListener('navigation-started', handleNavigationStart)
    }
  }, [])

  // Show skeleton when navigation is in progress
  if (isNavigating) {
    return <PageSkeleton />
  }

  return <>{children}</>
}

/**
 * Page Skeleton Component
 * Comprehensive skeleton layout matching the app structure
 * Provides immediate visual feedback during navigation
 * Note: Header is not included since it remains visible during navigation
 */
function PageSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      {/* Main content skeleton - no header since it stays visible */}
      <div className="container mx-auto px-4 py-8">
        {/* Page title skeleton */}
        <div className="text-center mb-8">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>

        {/* Grid skeleton for mugshot pages */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
          {Array.from({ length: 20 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="aspect-[3/4] w-full rounded-lg" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-3/4" />
              <div className="flex justify-between">
                <Skeleton className="h-3 w-12" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          ))}
        </div>

        {/* Pagination skeleton */}
        <div className="flex justify-center space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-12" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </div>
  )
} 