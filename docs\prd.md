# PRD: State Management Refactor

## Overview
This PRD defines the goals, constraints, and acceptance criteria for refactoring the state management layer of a live Next.js + Supabase application. The primary objective is to improve state handling by introducing TanStack Query for server state and refining Zustand usage for client state, without altering existing UI components or API behavior.

## Goals
- Clear separation of server vs client state
- Improved caching, background updates, optimistic updates
- Full SSR/SEO compatibility
- Realtime UX via Supabase
- Zero UI/API changes

## Functional Requirements
1. Analyze current Zustand usage, API usage, and components
2. Retain all UI and API logic exactly
3. Switch server state to TanStack Query
4. Restrict Zustand to client-only state
5. Use Supabase for realtime data sync
6. Add optimistic UI updates
7. Preserve SSR and SEO behavior

## Constraints
- No frontend component or styling changes
- No API endpoint or structure changes
- Must use Next.js App Router and Supabase

## Success Criteria
- No visual or behavioral differences
- Better UX performance under latency
- Pass all tests, add full regression coverage
