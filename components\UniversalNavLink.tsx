'use client'

import { <PERSON>actNode, cloneElement, isValidElement } from 'react'
import { useNavigationProgress } from '@/hooks/useNavigationProgress'

interface UniversalNavLinkProps {
  href: string
  children: ReactNode
  className?: string
  replace?: boolean
  scroll?: boolean
  prefetch?: boolean
  id?: string
  disabled?: boolean
}

/**
 * Universal Navigation Link Component
 * Can wrap ANY link or button to provide consistent navigation feedback
 * Use this everywhere for consistent UX across the entire app
 * 
 * Usage:
 * <UniversalNavLink href="/path">
 *   <Button>Click me</Button>
 * </UniversalNavLink>
 * 
 * Or as direct link:
 * <UniversalNavLink href="/path" className="text-blue-500">
 *   Navigate here
 * </UniversalNavLink>
 */
export default function UniversalNavLink({
  href,
  children,
  className = '',
  id,
  disabled = false
}: UniversalNavLinkProps) {
  const { navigateWithProgress, isElementClicked } = useNavigationProgress()
  
  // Generate unique ID for this navigation element
  const elementId = id || `nav-${href.replace(/[^a-zA-Z0-9]/g, '-')}-${Math.random().toString(36).substr(2, 9)}`
  const isCurrentlyClicked = isElementClicked(elementId)

  const handleClick = (e: React.MouseEvent) => {
    if (disabled) return
    
    e.preventDefault()
    navigateWithProgress(href, elementId)
  }

  // If children is a valid React element, clone it with our navigation logic
  if (isValidElement(children)) {
    // Type assertion for cloneable element with common props
    const element = children as React.ReactElement<{
      className?: string
      style?: React.CSSProperties
      onClick?: (event: React.MouseEvent) => void
      disabled?: boolean
    }>
    
    return cloneElement(element, {
      onClick: handleClick,
      className: `${element.props.className || ''} ${className} ${
        isCurrentlyClicked 
          ? 'scale-95 opacity-75 transition-all duration-100 ease-out' 
          : 'transition-all duration-100 ease-out hover:scale-[1.02] cursor-pointer'
      } ${disabled ? 'opacity-50 pointer-events-none' : ''}`.trim(),
      style: {
        ...element.props.style,
        ...(isCurrentlyClicked && {
          transform: 'scale(0.95)',
          opacity: 0.75
        })
      },
      disabled: disabled || isCurrentlyClicked
    })
  }

  // Fallback: render as a clickable element
  return (
    <span
      onClick={handleClick}
      className={`${className} ${
        isCurrentlyClicked 
          ? 'scale-95 opacity-75 transition-all duration-100 ease-out' 
          : 'transition-all duration-100 ease-out hover:scale-[1.02] cursor-pointer'
      } ${disabled ? 'opacity-50 pointer-events-none' : ''} inline-block`}
    >
      {children}
    </span>
  )
}

/**
 * Simple Navigation Button Component
 * Pre-styled button with navigation functionality
 */
interface NavButtonProps {
  href: string
  children: ReactNode
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  disabled?: boolean
  fullWidth?: boolean
}

export function NavButton({
  href,
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  fullWidth = false
}: NavButtonProps) {
  const { navigateWithProgress, isElementClicked } = useNavigationProgress()
  
  const elementId = `btn-${href.replace(/[^a-zA-Z0-9]/g, '-')}-${Math.random().toString(36).substr(2, 9)}`
  const isCurrentlyClicked = isElementClicked(elementId)

  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-150 ease-out focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 focus:ring-offset-background relative overflow-hidden group'
  
  const variantClasses = {
    primary: 'bg-pink-600 hover:bg-pink-700 text-white shadow-lg hover:shadow-xl',
    secondary: 'bg-secondary hover:bg-secondary/80 text-secondary-foreground border border-border',
    ghost: 'hover:bg-pink-500/10 text-pink-400 hover:text-pink-300',
    outline: 'border border-pink-500 text-pink-500 hover:bg-pink-500 hover:text-white'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-md gap-1.5',
    md: 'px-4 py-2 text-base rounded-lg gap-2',
    lg: 'px-6 py-3 text-lg rounded-xl gap-2.5'
  }

  const clickedClasses = isCurrentlyClicked 
    ? 'scale-95 opacity-80' 
    : 'hover:scale-[1.02]'

  return (
    <button
      onClick={() => navigateWithProgress(href, elementId)}
      disabled={disabled || isCurrentlyClicked}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${clickedClasses} ${fullWidth ? 'w-full' : ''} ${className}`}
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
      
      {/* Loading overlay */}
      {isCurrentlyClicked && (
        <div className="absolute inset-0 bg-pink-500/20 animate-pulse" />
      )}
      
      {/* Content */}
      <span className="relative z-10 flex items-center gap-inherit">
        {children}
        {isCurrentlyClicked && (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        )}
      </span>
    </button>
  )
}

/**
 * Logo Navigation Component
 * Special component for logo/brand navigation with appropriate styling
 */
interface LogoNavProps {
  href: string
  children: ReactNode
  className?: string
}

export function LogoNav({ href, children, className = '' }: LogoNavProps) {
  const { navigateWithProgress, isElementClicked } = useNavigationProgress()
  
  const elementId = `logo-${href.replace(/[^a-zA-Z0-9]/g, '-')}`
  const isCurrentlyClicked = isElementClicked(elementId)

  return (
    <button
      onClick={() => navigateWithProgress(href, elementId)}
      className={`${className} ${
        isCurrentlyClicked 
          ? 'scale-95 opacity-80' 
          : 'hover:scale-[1.02]'
      } transition-all duration-150 ease-out relative group`}
      disabled={isCurrentlyClicked}
    >
      {/* Subtle glow effect on hover */}
      <div className="absolute inset-0 bg-pink-500/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      
      {/* Loading overlay */}
      {isCurrentlyClicked && (
        <div className="absolute inset-0 bg-pink-500/10 rounded-lg animate-pulse" />
      )}
      
      <div className="relative z-10">
        {children}
      </div>
    </button>
  )
} 