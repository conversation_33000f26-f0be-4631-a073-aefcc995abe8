import { useState, useEffect, useCallback } from 'react'
import { getBulkRatingStatistics, type RatingStatistics } from '@/lib/services/rating-service'

interface UseBulkRatingStatisticsReturn {
  statsMap: Record<string, RatingStatistics>
  isLoading: boolean
  error: string | null
  refreshStats: () => Promise<void>
}

export function useBulkRatingStatistics(mugshotIds: string[]): UseBulkRatingStatisticsReturn {
  const [statsMap, setStatsMap] = useState<Record<string, RatingStatistics>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadStats = useCallback(async () => {
    if (mugshotIds.length === 0) {
      setStatsMap({})
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const stats = await getBulkRatingStatistics(mugshotIds)
      setStatsMap(stats)
    } catch (err) {
      console.error('Error loading bulk rating statistics:', err)
      setError('Failed to load rating statistics')
    } finally {
      setIsLoading(false)
    }
  }, [mugshotIds])

  // Load stats when mugshot IDs change
  useEffect(() => {
    loadStats()
  }, [loadStats])

  const refreshStats = async () => {
    await loadStats()
  }

  return {
    statsMap,
    isLoading,
    error,
    refreshStats
  }
} 