import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  selectDailyWinner, 
  assignManualDaily<PERSON>inner, 
  getDailyWinner,
  getRecentDailyWinners,
  getDailyWinnersInRange,
  getDailyWinnerCandidates,
  getDailyWinnerStats,
  getDailyWinnerAuditLogs,
  refreshDailyWinnersView
} from '@/lib/services/daily-winner-service'

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: vi.fn()
  },
  from: vi.fn(),
  rpc: vi.fn()
}

// Mock functions that chain
const mockSelect = vi.fn()
const mockEq = vi.fn()
const mockSingle = vi.fn()
const mockOrder = vi.fn()
const mockLimit = vi.fn()
const mockRange = vi.fn()
const mockGte = vi.fn()
const mockLte = vi.fn()
const mockUpsert = vi.fn()
const mockInsert = vi.fn()

// Setup method chaining
mockSelect.mockReturnThis()
mockEq.mockReturnThis()
mockOrder.mockReturnThis()
mockLimit.mockReturnThis()
mockRange.mockReturnThis()
mockGte.mockReturnThis()
mockLte.mockReturnThis()
mockUpsert.mockReturnThis()
mockInsert.mockReturnThis()

mockSupabase.from.mockReturnValue({
  select: mockSelect,
  eq: mockEq,
  single: mockSingle,
  order: mockOrder,
  limit: mockLimit,
  range: mockRange,
  gte: mockGte,
  lte: mockLte,
  upsert: mockUpsert,
  insert: mockInsert
})

// Mock revalidatePath
vi.mock('next/cache', () => ({
  revalidatePath: vi.fn()
}))

// Mock Supabase client
vi.mock('@/lib/supabase/server', () => ({
  createClient: vi.fn(() => mockSupabase)
}))

describe('Daily Winner Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset all mock return values
    mockSingle.mockResolvedValue({ data: null, error: null })
    mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null }, error: null })
    mockSupabase.rpc.mockResolvedValue({ data: null, error: null })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('selectDailyWinner', () => {
    it('should successfully select a daily winner with valid data', async () => {
      const mockUser = { id: 'user123' }
      const mockWinnerResult = {
        success: true,
        message: 'Daily winner selected successfully',
        winner: {
          mugshot_id: 123,
          average_rating: 4.5,
          total_ratings: 25,
          ranking: 1
        },
        candidates_considered: 5,
        execution_time_ms: 150,
        tie_breaker_reason: null
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: mockWinnerResult,
        error: null
      })

      const result = await selectDailyWinner('2024-01-15', false)

      expect(result.success).toBe(true)
      expect(result.message).toBe('Daily winner selected successfully')
      expect(result.winner).toEqual(mockWinnerResult.winner)
      expect(result.candidates_considered).toBe(5)
      expect(result.execution_time_ms).toBe(150)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('select_daily_winner', {
        target_date: '2024-01-15',
        force_selection: false,
        operator_id: mockUser.id
      })
    })

    it('should handle no winner case gracefully', async () => {
      const mockNoWinnerResult = {
        success: true,
        message: 'No winner selected - insufficient candidates',
        candidates_considered: 0,
        execution_time_ms: 100
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: mockNoWinnerResult,
        error: null
      })

      const result = await selectDailyWinner('2024-01-15')

      expect(result.success).toBe(true)
      expect(result.message).toBe('No winner selected - insufficient candidates')
      expect(result.winner).toBeUndefined()
      expect(result.candidates_considered).toBe(0)
    })

    it('should handle database errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' }
      })

      const result = await selectDailyWinner('2024-01-15')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Database error occurred during winner selection')
      expect(result.error).toBe('DATABASE_ERROR')
    })

    it('should use yesterday as default date when no date provided', async () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: { success: true, message: 'Winner selected' },
        error: null
      })

      await selectDailyWinner()

      expect(mockSupabase.rpc).toHaveBeenCalledWith('select_daily_winner', {
        target_date: yesterday,
        force_selection: false,
        operator_id: 'user123'
      })
    })
  })

  describe('assignManualDailyWinner', () => {
    it('should successfully assign manual winner as admin', async () => {
      const mockUser = { id: 'admin123' }
      const mockProfile = { role: 'admin' }
      const mockMugshot = { 
        id: 123, 
        firstName: 'John', 
        lastName: 'Doe' 
      }
      const mockRatingStats = {
        overall_rating: 4.2,
        total_ratings: 30
      }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValueOnce({
        data: mockProfile,
        error: null
      })

      mockSingle.mockResolvedValueOnce({
        data: mockMugshot,
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: mockRatingStats,
        error: null
      })

      mockUpsert.mockResolvedValue({ error: null })
      mockInsert.mockResolvedValue({ error: null })

      const result = await assignManualDailyWinner(
        '2024-01-15', 
        123, 
        'Special contest winner'
      )

      expect(result.success).toBe(true)
      expect(result.message).toContain('John Doe')
      expect(result.winner?.mugshot_id).toBe(123)
      expect(result.winner?.average_rating).toBe(4.2)
      expect(result.winner?.total_ratings).toBe(30)

      expect(mockSupabase.from).toHaveBeenCalledWith('profiles')
      expect(mockSupabase.from).toHaveBeenCalledWith('mugshots')
      expect(mockSupabase.from).toHaveBeenCalledWith('daily_winners')
      expect(mockSupabase.from).toHaveBeenCalledWith('daily_winner_audit_log')
    })

    it('should reject unauthorized users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const result = await assignManualDailyWinner('2024-01-15', 123, 'Test reason')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Authentication required for manual winner assignment')
      expect(result.error).toBe('UNAUTHENTICATED')
    })

    it('should reject non-admin users', async () => {
      const mockUser = { id: 'user123' }
      const mockProfile = { role: 'user' }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValue({
        data: mockProfile,
        error: null
      })

      const result = await assignManualDailyWinner('2024-01-15', 123, 'Test reason')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Admin access required for manual winner assignment')
      expect(result.error).toBe('UNAUTHORIZED')
    })

    it('should handle mugshot not found', async () => {
      const mockUser = { id: 'admin123' }
      const mockProfile = { role: 'admin' }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValueOnce({
        data: mockProfile,
        error: null
      })

      mockSingle.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' }
      })

      const result = await assignManualDailyWinner('2024-01-15', 999, 'Test reason')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Mugshot not found')
      expect(result.error).toBe('MUGSHOT_NOT_FOUND')
    })
  })

  describe('getDailyWinner', () => {
    it('should return daily winner details for valid date', async () => {
      const mockWinner = {
        id: 'winner123',
        date: '2024-01-15',
        mugshot_id: 123,
        average_rating: 4.5,
        total_ratings: 25,
        selection_method: 'automatic',
        mugshot_first_name: 'John',
        mugshot_last_name: 'Doe'
      }

      mockSupabase.rpc.mockResolvedValue({
        data: [mockWinner],
        error: null
      })

      const result = await getDailyWinner('2024-01-15')

      expect(result).toEqual(mockWinner)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_daily_winner_details', {
        target_date: '2024-01-15'
      })
    })

    it('should return null when no winner found', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null
      })

      const result = await getDailyWinner('2024-01-15')

      expect(result).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      const result = await getDailyWinner('2024-01-15')

      expect(result).toBeNull()
    })
  })

  describe('getRecentDailyWinners', () => {
    it('should return recent winners with default limit', async () => {
      const mockWinners = [
        { id: '1', date: '2024-01-15', mugshot_id: 123 },
        { id: '2', date: '2024-01-14', mugshot_id: 124 }
      ]

      mockLimit.mockResolvedValue({
        data: mockWinners,
        error: null
      })

      const result = await getRecentDailyWinners()

      expect(result).toEqual(mockWinners)
      expect(mockSupabase.from).toHaveBeenCalledWith('recent_daily_winners')
      expect(mockOrder).toHaveBeenCalledWith('date', { ascending: false })
      expect(mockLimit).toHaveBeenCalledWith(30)
    })

    it('should respect custom limit', async () => {
      mockLimit.mockResolvedValue({
        data: [],
        error: null
      })

      await getRecentDailyWinners(10)

      expect(mockLimit).toHaveBeenCalledWith(10)
    })

    it('should return empty array on database error', async () => {
      mockLimit.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      const result = await getRecentDailyWinners()

      expect(result).toEqual([])
    })
  })

  describe('getDailyWinnerCandidates', () => {
    it('should return eligible candidates with default criteria', async () => {
      const mockCandidates = [
        {
          mugshot_id: 123,
          average_rating: 4.5,
          total_ratings: 25,
          rating_rank: 1,
          vote_rank: 1,
          booking_date: '2024-01-14',
          mugshot_age_days: 1
        }
      ]

      mockSupabase.rpc.mockResolvedValue({
        data: mockCandidates,
        error: null
      })

      const result = await getDailyWinnerCandidates('2024-01-15')

      expect(result).toEqual(mockCandidates)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_daily_winner_candidates', {
        target_date: '2024-01-15',
        min_rating: 4.0,
        min_votes: 10
      })
    })

    it('should respect custom criteria', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null
      })

      await getDailyWinnerCandidates('2024-01-15', 4.5, 20)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_daily_winner_candidates', {
        target_date: '2024-01-15',
        min_rating: 4.5,
        min_votes: 20
      })
    })
  })

  describe('getDailyWinnerStats', () => {
    it('should calculate stats correctly with valid data', async () => {
      const mockWinners = [
        { 
          id: '1', 
          date: '2024-01-15', 
          execution_time_ms: 100, 
          candidates_considered: 5, 
          mugshot_id: 123 
        },
        { 
          id: '2', 
          date: '2024-01-14', 
          execution_time_ms: 200, 
          candidates_considered: 3, 
          mugshot_id: 124 
        },
        { 
          id: '3', 
          date: '2024-01-13', 
          execution_time_ms: null, 
          candidates_considered: 0, 
          mugshot_id: null 
        }
      ]

      mockSupabase.from.mockReturnValue({
        select: mockSelect.mockResolvedValue({
          data: mockWinners,
          error: null
        })
      })

      const result = await getDailyWinnerStats()

      expect(result.total_winners).toBe(3)
      expect(result.average_execution_time).toBe(150) // (100 + 200) / 2
      expect(result.average_candidates_per_selection).toBe(2.7) // (5 + 3 + 0) / 3 = 2.666... rounded to 1 decimal
      expect(result.no_winner_days).toBe(1)
    })

    it('should handle empty data gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: mockSelect.mockResolvedValue({
          data: [],
          error: null
        })
      })

      const result = await getDailyWinnerStats()

      expect(result.total_winners).toBe(0)
      expect(result.average_execution_time).toBe(0)
      expect(result.average_candidates_per_selection).toBe(0)
      expect(result.no_winner_days).toBe(0)
    })
  })

  describe('getDailyWinnerAuditLogs', () => {
    it('should return audit logs for admin users', async () => {
      const mockUser = { id: 'admin123' }
      const mockProfile = { role: 'admin' }
      const mockAuditLogs = [
        {
          id: 'log1',
          selection_date: '2024-01-15',
          execution_timestamp: '2024-01-15T12:00:00Z',
          selected_mugshot_id: 123,
          selection_reason: 'Automatic selection'
        }
      ]

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValue({
        data: mockProfile,
        error: null
      })

      mockRange.mockResolvedValue({
        data: mockAuditLogs,
        error: null
      })

      const result = await getDailyWinnerAuditLogs(10, 0)

      expect(result).toEqual(mockAuditLogs)
      expect(mockSupabase.from).toHaveBeenCalledWith('daily_winner_audit_log')
      expect(mockOrder).toHaveBeenCalledWith('execution_timestamp', { ascending: false })
      expect(mockRange).toHaveBeenCalledWith(0, 9)
    })

    it('should return empty array for non-admin users', async () => {
      const mockUser = { id: 'user123' }
      const mockProfile = { role: 'user' }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValue({
        data: mockProfile,
        error: null
      })

      const result = await getDailyWinnerAuditLogs()

      expect(result).toEqual([])
    })
  })

  describe('refreshDailyWinnersView', () => {
    it('should refresh materialized view for admin users', async () => {
      const mockUser = { id: 'admin123' }
      const mockProfile = { role: 'admin' }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValue({
        data: mockProfile,
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null
      })

      const result = await refreshDailyWinnersView()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Daily winners view refreshed successfully')
      expect(mockSupabase.rpc).toHaveBeenCalledWith('refresh_recent_daily_winners')
    })

    it('should reject non-admin users', async () => {
      const mockUser = { id: 'user123' }
      const mockProfile = { role: 'user' }

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      mockSingle.mockResolvedValue({
        data: mockProfile,
        error: null
      })

      const result = await refreshDailyWinnersView()

      expect(result.success).toBe(false)
      expect(result.message).toBe('Admin access required')
    })
  })
}) 