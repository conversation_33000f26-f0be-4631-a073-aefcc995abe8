import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import RatingInterface from '../../components/RatingInterface'

// Mock the rating service
vi.mock('../../lib/services/rating-service', () => ({
  submitRating: vi.fn(),
  getUserRatings: vi.fn(),
  checkTimeGate: vi.fn(),
}))

// Mock the auth store
vi.mock('../../lib/stores/auth-store', () => ({
  useAuthStore: vi.fn(),
}))

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

import { submitRating, getUserRatings, checkTimeGate } from '../../lib/services/rating-service'
import { useAuthStore } from '../../lib/stores/auth-store'
import { toast } from 'sonner'

const mockSubmitRating = submitRating as any
const mockGetUserRatings = getUserRatings as any
const mockCheckTimeGate = checkTimeGate as any
const mockUseAuthStore = useAuthStore as any

describe('RatingInterface', () => {
  const mockUser = { id: 'test-user', email: '<EMAIL>' }
  const defaultProps = {
    mugshotId: 1,
    bookingDate: '2024-01-01',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default auth store state
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      isLoading: false,
    })
    
    // Default service responses
    mockCheckTimeGate.mockResolvedValue({
      allowed: true,
      message: 'Rating is available',
    })
    
    mockGetUserRatings.mockResolvedValue([])
    
    mockSubmitRating.mockResolvedValue({
      success: true,
      message: 'Rating saved successfully!',
    })
  })

  it('should render rating interface for authenticated user', async () => {
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rate this mugshot')).toBeInTheDocument()
    })
    
    // Check all categories are present
    expect(screen.getByText('Hot')).toBeInTheDocument()
    expect(screen.getByText('Funny')).toBeInTheDocument()
    expect(screen.getByText('Wild')).toBeInTheDocument()
    expect(screen.getByText('Scary')).toBeInTheDocument()
  })

  it('should show loading state initially', () => {
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      isLoading: true,
    })
    
    render(<RatingInterface {...defaultProps} />)
    
    expect(screen.getByText('Loading ratings...')).toBeInTheDocument()
  })

  it('should show login required for unauthenticated users', async () => {
    mockUseAuthStore.mockReturnValue({
      user: null,
      isLoading: false,
    })
    
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Login required to save ratings')).toBeInTheDocument()
    })
  })

  it('should show time gate message when rating not allowed', async () => {
    mockCheckTimeGate.mockResolvedValue({
      allowed: false,
      message: 'Rating will be available in 5 hours',
    })
    
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rating will be available in 5 hours')).toBeInTheDocument()
    })
  })

  it('should handle rating submission', async () => {
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rate this mugshot')).toBeInTheDocument()
    })
    
    // Find and click the first star in Hot category
    const stars = screen.getAllByRole('button')
    const firstStar = stars.find(star => star.className.includes('cursor-pointer'))
    
    if (firstStar) {
      fireEvent.click(firstStar)
      
      await waitFor(() => {
        expect(mockSubmitRating).toHaveBeenCalled()
        expect(toast.success).toHaveBeenCalledWith('Rating saved successfully!')
      })
    }
  })

  it('should display existing user ratings', async () => {
    mockGetUserRatings.mockResolvedValue([
      { category: 'Hot', rating: 4 },
      { category: 'Funny', rating: 3 },
    ])
    
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(mockGetUserRatings).toHaveBeenCalledWith(1)
    })
  })

  it('should handle rating submission errors', async () => {
    mockSubmitRating.mockResolvedValue({
      success: false,
      message: 'Failed to save rating',
      error: 'DATABASE_ERROR',
    })
    
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rate this mugshot')).toBeInTheDocument()
    })
    
    // Find and click a star
    const stars = screen.getAllByRole('button')
    const firstStar = stars.find(star => star.className.includes('cursor-pointer'))
    
    if (firstStar) {
      fireEvent.click(firstStar)
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to save rating')
      })
    }
  })

  it('should render in compact mode', async () => {
    render(<RatingInterface {...defaultProps} compact={true} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rate this mugshot')).toBeInTheDocument()
    })
    
    // In compact mode, the padding should be smaller
    const card = screen.getByText('Rate this mugshot').closest('.p-3')
    expect(card).toBeInTheDocument()
  })

  it('should handle hover effects on stars', async () => {
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rate this mugshot')).toBeInTheDocument()
    })
    
    // Test hover behavior
    const stars = screen.getAllByRole('button')
    const firstStar = stars.find(star => star.className.includes('cursor-pointer'))
    
    if (firstStar) {
      fireEvent.mouseEnter(firstStar)
      // Should show hover state
      expect(firstStar.className).toContain('fill-yellow-400')
      
      fireEvent.mouseLeave(firstStar)
      // Should remove hover state if no rating
    }
  })

  it('should prevent rating when time gate is active', async () => {
    mockCheckTimeGate.mockResolvedValue({
      allowed: false,
      message: 'Rating will be available in 2 hours',
    })
    
    render(<RatingInterface {...defaultProps} />)
    
    await waitFor(() => {
      expect(screen.getByText('Rating will be available in 2 hours')).toBeInTheDocument()
    })
    
    // Should not show rating interface
    expect(screen.queryByText('Rate this mugshot')).not.toBeInTheDocument()
  })
}) 