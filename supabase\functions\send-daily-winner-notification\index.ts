import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface DailyWinnerNotificationPayload {
  winnerId: string
  winDate: string
  mugshot: {
    id: string
    first_name: string
    last_name: string
    image_path: string
    state_of_booking: string
    county_of_booking: string
    offenses?: string[]
  }
  averageRating: number
  totalRatings: number
  method?: string
}

interface NotificationRecipient {
  user_id?: string
  email: string
  first_name?: string
  state?: string
  county?: string
  unsubscribe_token: string
}

serve(async (req: Request) => {
  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const payload: DailyWinnerNotificationPayload = await req.json()
    
    // Get notification recipients
    const { data: recipients, error: recipientsError } = await supabase
      .rpc('get_notification_recipients', {
        p_notification_type: 'daily_winner',
        p_state: payload.mugshot.state_of_booking,
        p_county: payload.mugshot.county_of_booking
      })

    if (recipientsError) {
      console.error('Error getting recipients:', recipientsError)
      throw recipientsError
    }

    if (!recipients || recipients.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        totalRecipients: 0,
        successCount: 0,
        errorCount: 0,
        message: 'No recipients found for daily winner notifications'
      }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      })
    }

    let successCount = 0
    let errorCount = 0

    // Send email to each recipient
    for (const recipient of recipients as NotificationRecipient[]) {
      try {
        const emailContent = generateDailyWinnerEmail(payload, recipient)
        
        // Send email using Resend
        const emailResponse = await sendEmail({
          to: recipient.email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        })

        // Log successful send
        await supabase.rpc('log_email_notification', {
          p_user_id: recipient.user_id,
          p_email: recipient.email,
          p_notification_type: 'daily_winner',
          p_template_name: 'daily-winner-announcement',
          p_subject: emailContent.subject,
          p_event_id: payload.winnerId,
          p_event_type: 'daily_winner',
          p_external_message_id: emailResponse.id
        })

        successCount++
      } catch (emailError) {
        console.error(`Failed to send email to ${recipient.email}:`, emailError)
        
        // Log failed send
        await supabase.from('email_notifications_log').insert({
          user_id: recipient.user_id,
          email: recipient.email,
          notification_type: 'daily_winner',
          template_name: 'daily-winner-announcement',
          subject: 'Daily Winner Announcement',
          event_id: payload.winnerId,
          event_type: 'daily_winner',
          delivery_status: 'failed',
          error_message: emailError instanceof Error ? emailError.message : 'Unknown error'
        })

        errorCount++
      }
    }

    return new Response(JSON.stringify({
      success: true,
      totalRecipients: recipients.length,
      successCount,
      errorCount,
      winnerId: payload.winnerId
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    })

  } catch (error) {
    console.error('Daily winner notification error:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500
    })
  }
})

function generateDailyWinnerEmail(payload: DailyWinnerNotificationPayload, recipient: NotificationRecipient) {
  const baseUrl = Deno.env.get('SITE_URL') || 'https://americas-top-mugshots.com'
  const mugshotUrl = `${baseUrl}/mugshots/${payload.mugshot.id}?utm_source=email&utm_campaign=daily_winner`
  const weeklyVotingUrl = `${baseUrl}/weekly-voting?utm_source=email&utm_campaign=daily_winner`
  const unsubscribeUrl = `${baseUrl}/unsubscribe?token=${recipient.unsubscribe_token}`
  const preferencesUrl = `${baseUrl}/preferences?token=${recipient.unsubscribe_token}`
  
  const subject = `🏆 Daily Winner: ${payload.mugshot.first_name} ${payload.mugshot.last_name} from ${payload.mugshot.state_of_booking}`
  
  const isLocal = recipient.state === payload.mugshot.state_of_booking || 
                 recipient.county === payload.mugshot.county_of_booking
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>Daily Winner Announcement</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
          margin: 0; 
          padding: 0; 
          background-color: #0a0a0a; 
          line-height: 1.6;
        }
        .container { 
          max-width: 600px; 
          margin: 0 auto; 
          background-color: #1a1a1a; 
        }
        .header { 
          background: linear-gradient(135deg, #ec4899, #8b5cf6); 
          padding: 30px 20px; 
          text-align: center; 
        }
        .logo { 
          color: white; 
          font-size: 28px; 
          font-weight: bold; 
          text-decoration: none; 
          margin-bottom: 10px;
          display: block;
        }
        .header h1 {
          color: white; 
          margin: 10px 0 0 0; 
          font-size: 24px;
          font-weight: bold;
        }
        .content { 
          padding: 30px 20px; 
          color: #ffffff; 
        }
        .greeting {
          font-size: 18px;
          margin-bottom: 20px;
        }
        .winner-card { 
          background-color: #262626; 
          border: 2px solid #ec4899; 
          border-radius: 12px; 
          padding: 25px; 
          margin: 25px 0; 
          text-align: center;
        }
        .winner-name {
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          margin: 0 0 10px 0;
        }
        .winner-location {
          color: #888;
          margin: 8px 0;
          font-size: 16px;
        }
        .winner-stats {
          background-color: #1a1a1a;
          border: 1px solid #06b6d4;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .winner-stats h3 {
          color: #06b6d4;
          margin: 0 0 15px 0;
          font-size: 18px;
        }
        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 10px 0;
          padding: 8px 0;
          border-bottom: 1px solid #333;
        }
        .stat-item:last-child {
          border-bottom: none;
        }
        .stat-label {
          color: #888;
        }
        .stat-value {
          color: #ffffff;
          font-weight: bold;
        }
        .local-badge {
          background: linear-gradient(135deg, #10b981, #06b6d4);
          color: white;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
          margin-left: 10px;
        }
        .cta-button { 
          display: inline-block; 
          background: linear-gradient(135deg, #ec4899, #8b5cf6); 
          color: white; 
          padding: 16px 32px; 
          text-decoration: none; 
          border-radius: 8px; 
          font-weight: bold; 
          font-size: 16px;
          margin: 15px 10px;
          text-align: center;
        }
        .cta-section {
          text-align: center;
          margin: 30px 0;
        }
        .next-competition {
          background-color: #262626;
          border: 1px solid #8b5cf6;
          border-radius: 8px;
          padding: 20px;
          margin: 25px 0;
          text-align: center;
        }
        .next-competition h3 {
          color: #8b5cf6;
          margin: 0 0 15px 0;
        }
        .footer { 
          background-color: #262626; 
          padding: 25px 20px; 
          text-align: center; 
          color: #888; 
          font-size: 12px; 
          line-height: 1.5;
        }
        .footer p {
          margin: 8px 0;
        }
        .unsubscribe { 
          color: #888; 
          text-decoration: none; 
        }
        .unsubscribe:hover {
          color: #ec4899;
        }
        @media (max-width: 600px) {
          .content { padding: 20px 15px; }
          .winner-card { padding: 20px; }
          .header { padding: 20px 15px; }
          .logo { font-size: 24px; }
          .header h1 { font-size: 20px; }
          .winner-name { font-size: 20px; }
          .cta-button { margin: 15px 5px; padding: 14px 24px; }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <a href="${baseUrl}" class="logo">America's Top Mugshots</a>
          <h1>🏆 We Have a Daily Winner!</h1>
        </div>
        
        <div class="content">
          <p class="greeting">Hey ${recipient.first_name || 'there'}!</p>
          
          <p>The votes are in and we have our daily champion! ${isLocal ? 'This winner is from your area! 🎉' : 'Check out today\'s highest-rated mugshot:'}</p>
          
          <div class="winner-card">
            <h2 class="winner-name">
              ${payload.mugshot.first_name} ${payload.mugshot.last_name}
              ${isLocal ? '<span class="local-badge">LOCAL</span>' : ''}
            </h2>
            <p class="winner-location">${payload.mugshot.state_of_booking}, ${payload.mugshot.county_of_booking}</p>
            
            <div class="winner-stats">
              <h3>🏆 Winning Stats</h3>
              <div class="stat-item">
                <span class="stat-label">⭐ Average Rating</span>
                <span class="stat-value">${payload.averageRating}/10</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">👥 Total Ratings</span>
                <span class="stat-value">${payload.totalRatings}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">📅 Winner Date</span>
                <span class="stat-value">${new Date(payload.winDate).toLocaleDateString()}</span>
              </div>
              ${payload.method ? `
                <div class="stat-item">
                  <span class="stat-label">🎯 Selection Method</span>
                  <span class="stat-value">${payload.method === 'automatic' ? 'Top Rated' : 'Manual Pick'}</span>
                </div>
              ` : ''}
            </div>
          </div>
          
          <div class="cta-section">
            <a href="${mugshotUrl}" class="cta-button">View Winner Details</a>
            <a href="${weeklyVotingUrl}" class="cta-button">Vote in Weekly Championship</a>
          </div>
          
          <div class="next-competition">
            <h3>🗳️ What's Next?</h3>
            <p>This daily winner will compete in this week's voting championship. Stay tuned for the weekly voting announcement where you can vote for the ultimate weekly champion!</p>
          </div>
          
          ${isLocal ? `
            <p style="background-color: #262626; border: 1px solid #10b981; border-radius: 8px; padding: 15px; text-align: center; margin: 20px 0;">
              <strong style="color: #10b981;">🎯 Local Winner Alert!</strong><br>
              This champion is from your area (${payload.mugshot.state_of_booking}). Local heroes represent!
            </p>
          ` : ''}
        </div>
        
        <div class="footer">
          <p>You're receiving this because you signed up for daily winner alerts.</p>
          <p>
            <a href="${preferencesUrl}" class="unsubscribe">Manage Preferences</a> | 
            <a href="${unsubscribeUrl}" class="unsubscribe">Unsubscribe</a>
          </p>
          <p>© 2024 America's Top Mugshots. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Daily Winner Announcement

Hey ${recipient.first_name || 'there'}!

The votes are in and we have our daily champion! ${isLocal ? 'This winner is from your area!' : ''}

🏆 ${payload.mugshot.first_name} ${payload.mugshot.last_name}
📍 ${payload.mugshot.state_of_booking}, ${payload.mugshot.county_of_booking}

Winning Stats:
⭐ Average Rating: ${payload.averageRating}/10
👥 Total Ratings: ${payload.totalRatings}
📅 Winner Date: ${new Date(payload.winDate).toLocaleDateString()}
${payload.method ? `🎯 Selection Method: ${payload.method === 'automatic' ? 'Top Rated' : 'Manual Pick'}` : ''}

View winner details: ${mugshotUrl}
Vote in weekly championship: ${weeklyVotingUrl}

What's Next?
This daily winner will compete in this week's voting championship. Stay tuned for the weekly voting announcement!

${isLocal ? `🎯 Local Winner Alert! This champion is from your area (${payload.mugshot.state_of_booking}).` : ''}

Manage your email preferences: ${preferencesUrl}
Unsubscribe: ${unsubscribeUrl}

© 2024 America's Top Mugshots. All rights reserved.
  `

  return { html, text, subject }
}

async function sendEmail(emailData: {
  to: string
  subject: string
  html: string
  text: string
}) {
  const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
  
  if (!RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY environment variable is not set')
  }

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      from: 'America\'s Top Mugshots <<EMAIL>>',
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text
    })
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`Email service error: ${response.status} ${response.statusText} - ${errorText}`)
  }

  return await response.json()
} 