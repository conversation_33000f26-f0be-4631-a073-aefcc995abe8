# Top-Rated Queries Test - Complex Rating Calculations
# Tests the most expensive query path: fetches ALL ratings, calculates averages in TypeScript
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 3
      name: "Warm-up: Top-rated queries"
    - duration: 300
      arrivalRate: 8
      name: "Steady: Top-rated performance test"
    - duration: 60
      arrivalRate: 3
      name: "Cool-down"
  
  ensure:
    - http.response_time.p95: 3000   # Expect slower responses
    - http.response_time.median: 800 # Expect slower responses
    - http.codes.200: 85             # Lower success rate expected
    - http.codes.5xx: 5              # May have more server errors

  http:
    timeout: 25
    pool: 15
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "Top-Rated Queries - Complex Calculations"
    weight: 100
    flow:
      - function: "generateSlowQueryFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            sortBy: "{{ sortBy }}"
            state: "{{ state }}"
            search: "{{ searchTerm }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
          name: "GET /api/mugshots - Top-Rated (Complex)"
          expect:
            - statusCode: 200
            - hasProperty: "data.mugshots"
          capture:
            - json: "$.data.mugshots.length"
              as: "resultCount"
            - json: "$.success"
              as: "requestSuccess"
      - think: 2

processor: "./load-tests/focused-tests/data-generators-focused.js"
