import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import AdminOnly from '@/components/AdminOnly'
import { createClient } from '@/lib/supabase/client'

// Mock the Supabase client
vi.mock('@/lib/supabase/client')

const mockCreateClient = vi.mocked(createClient)

describe('AdminOnly Component', () => {
  let mockSupabase: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockSupabase = {
      auth: {
        getUser: vi.fn()
      },
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn()
    }
    
    mockCreateClient.mockReturnValue(mockSupabase)
  })

  it('should render children for admin user', async () => {
    // Mock admin user and profile
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'admin-123' } }
    })

    mockSupabase.single.mockResolvedValue({
      data: { role: 'admin' }
    })

    render(
      <AdminOnly>
        <div>Admin Content</div>
      </AdminOnly>
    )

    await waitFor(() => {
      expect(screen.getByText('Admin Content')).toBeInTheDocument()
    })
  })

  it('should render fallback for regular user', async () => {
    // Mock regular user and profile
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'user-456' } }
    })

    mockSupabase.single.mockResolvedValue({
      data: { role: 'user' }
    })

    render(
      <AdminOnly fallback={<div>Access Denied</div>}>
        <div>Admin Content</div>
      </AdminOnly>
    )

    await waitFor(() => {
      expect(screen.getByText('Access Denied')).toBeInTheDocument()
      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
    })
  })

  it('should render fallback for unauthenticated user', async () => {
    // Mock no user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null }
    })

    render(
      <AdminOnly fallback={<div>Please Login</div>}>
        <div>Admin Content</div>
      </AdminOnly>
    )

    await waitFor(() => {
      expect(screen.getByText('Please Login')).toBeInTheDocument()
      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
    })
  })

  it('should render nothing when no fallback provided for non-admin', async () => {
    // Mock regular user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'user-789' } }
    })

    mockSupabase.single.mockResolvedValue({
      data: { role: 'user' }
    })

    const { container } = render(
      <AdminOnly>
        <div>Admin Content</div>
      </AdminOnly>
    )

    await waitFor(() => {
      // Check that admin content is not visible
      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
      // Container should be empty (React renders nothing)
      expect(container.innerHTML).toBe('')
    })
  })

  it('should handle profile fetch error gracefully', async () => {
    // Mock user but profile fetch error
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'user-error' } }
    })

    mockSupabase.single.mockRejectedValue(new Error('Profile fetch failed'))

    render(
      <AdminOnly fallback={<div>Error State</div>}>
        <div>Admin Content</div>
      </AdminOnly>
    )

    await waitFor(() => {
      expect(screen.getByText('Error State')).toBeInTheDocument()
      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
    })
  })

  it('should show fallback during loading state', () => {
    // Mock slow response
    mockSupabase.auth.getUser.mockImplementation(() => 
      new Promise(resolve => {
        setTimeout(() => resolve({ data: { user: { id: 'admin-123' } } }), 100)
      })
    )

    render(
      <AdminOnly fallback={<div>Loading...</div>}>
        <div>Admin Content</div>
      </AdminOnly>
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
  })
}) 