# Supabase Mugshot Service for Next.js API Routes

---

## ✅ FILE: lib/supabaseMugshotService.ts

```ts
// FILE: lib/supabaseMugshotService.ts

import { createClient } from '@/lib/supabase/server'

export async function getFilteredMugshots({
  searchTerm = null,
  state = null,
  county = null,
  dateFrom = null,
  dateTo = null,
  tags = null,
  sortBy = 'newest', // or 'top-rated'
  limit = 12,
  offset = 0
}) {
  const supabase = createClient()
  let initialIds: number[] | null = null

  // Step 1: Handle top-rated sorting
  if (sortBy === 'top-rated') {
    const { data: rated } = await supabase
      .from('ratings')
      .select('mugshot_id, avg:rating')
      .group('mugshot_id')
      .order('avg', { ascending: false })
      .limit(1000) // fetch top 1000 rated mugshots

    initialIds = rated?.map(r => r.mugshot_id) || []
  }

  // Step 2: Apply tag filters
  if (tags && tags.length > 0) {
    const { data: tagged } = await supabase
      .from('tags')
      .select('mugshot_id')
      .in('tag_type', tags)

    const tagFilteredIds = tagged?.map(t => t.mugshot_id) || []
    initialIds = initialIds
      ? initialIds.filter(id => tagFilteredIds.includes(id))
      : tagFilteredIds
  }

  // Step 3: Build mugshot query
  let query = supabase
    .from('mugshots')
    .select('*')
    .order('dateOfBooking', { ascending: false })
    .order('created_at', { ascending: false })

  if (initialIds?.length) {
    query = query.in('id', initialIds)
  }

  if (searchTerm) {
    query = query.or(
      `firstName.ilike.%${searchTerm}%,lastName.ilike.%${searchTerm}%`
    )
  }

  if (state) query = query.eq('stateOfBooking', state)
  if (county) query = query.eq('countyOfBooking', county)
  if (dateFrom) query = query.gte('dateOfBooking', dateFrom)
  if (dateTo) query = query.lte('dateOfBooking', dateTo)

  // Step 4: Get mugshots for this page
  const { data: mugshots, error } = await query.range(offset, offset + limit - 1)
  if (error) throw error
  if (!mugshots?.length) return []

  const ids = mugshots.map(m => m.id)

  // Step 5: Enrich with rating & tag data
  const [{ data: ratings }, { data: tagList }] = await Promise.all([
    supabase.from('ratings').select('mugshot_id, rating').in('mugshot_id', ids),
    supabase.from('tags').select('mugshot_id, tag_type').in('mugshot_id', ids)
  ])

  const ratingMap = Object.fromEntries(
    ids.map(id => {
      const rates = ratings?.filter(r => r.mugshot_id === id) || []
      const avg = rates.reduce((sum, r) => sum + r.rating, 0) / (rates.length || 1)
      return [id, { average_rating: +avg.toFixed(2), total_ratings: rates.length }]
    })
  )

  const tagMap = Object.fromEntries(
    ids.map(id => {
      const tags = tagList?.filter(t => t.mugshot_id === id) || []
      const grouped = tags.reduce((acc, t) => {
        acc[t.tag_type] = (acc[t.tag_type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      return [id, grouped]
    })
  )

  // Step 6: Final merge
  return mugshots.map(m => ({
    ...m,
    ...ratingMap[m.id],
    tag_counts: tagMap[m.id] || {}
  }))
}

## supabaseMugshotCountService.ts
// FILE: lib/supabaseMugshotCountService.ts

import { createClient } from '@/lib/supabase/server'

export async function getFilteredMugshotCount({
  searchTerm = null,
  state = null,
  county = null,
  dateFrom = null,
  dateTo = null,
  tags = null
}) {
  const supabase = createClient()
  let filterIds: number[] | null = null

  // Tags
  if (tags?.length > 0) {
    const { data: tagged } = await supabase
      .from('tags')
      .select('mugshot_id')
      .in('tag_type', tags)

    filterIds = tagged?.map(t => t.mugshot_id) || []
  }

  let query = supabase
    .from('mugshots')
    .select('*', { count: 'exact', head: true })

  if (filterIds?.length) query = query.in('id', filterIds)
  if (searchTerm) {
    query = query.or(
      `firstName.ilike.%${searchTerm}%,lastName.ilike.%${searchTerm}%`
    )
  }
  if (state) query = query.eq('stateOfBooking', state)
  if (county) query = query.eq('countyOfBooking', county)
  if (dateFrom) query = query.gte('dateOfBooking', dateFrom)
  if (dateTo) query = query.lte('dateOfBooking', dateTo)

  const { count, error } = await query
  if (error) throw error
  return count
}


## API Usage Example (Route Handler)
// FILE: app/api/mugshots/route.ts

import { getFilteredMugshots } from '@/lib/supabaseMugshotService'

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url)

  const searchTerm = searchParams.get('search') || null
  const state = searchParams.get('state') || null
  const county = searchParams.get('county') || null
  const tags = searchParams.get('tags')?.split(',') || null
  const sortBy = searchParams.get('sort_by') || 'newest'
  const limit = parseInt(searchParams.get('limit') || '12')
  const offset = parseInt(searchParams.get('offset') || '0')

  try {
    const results = await getFilteredMugshots({
      searchTerm, state, county, tags, sortBy, limit, offset
    })
    return Response.json({ success: true, data: results })
  } catch (err: any) {
    return Response.json({ success: false, message: err.message })
  }
}
