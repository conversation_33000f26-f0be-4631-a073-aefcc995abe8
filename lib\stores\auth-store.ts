import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export interface AuthUser {
  id: string
  email?: string
  user_metadata?: Record<string, unknown>
  app_metadata?: Record<string, unknown>
  created_at?: string
}

// Utility function to convert Supabase User to AuthUser
function convertSupabaseUser(supabaseUser: User): AuthUser {
  return {
    id: supabaseUser.id,
    email: supabaseUser.email,
    user_metadata: supabaseUser.user_metadata,
    app_metadata: supabaseUser.app_metadata,
    created_at: supabaseUser.created_at
  }
}

interface UserProfile {
  id: string
  user_id: string
  email: string | null
  full_name: string
  state: string | null
  county: string | null
  role: string
  avatar_url: string | null
  created_at: string
  updated_at: string
}

interface AuthState {
  // User and profile state
  user: AuthUser | null
  profile: UserProfile | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // User's home location (from profile)
  homeState: string | null
  homeCounty: string | null
  
  // Loading control
  lastLoadAttempt: number | null
  
  // Actions
  setUser: (user: AuthUser | User | null) => void
  setProfile: (profile: UserProfile | null) => void
  setError: (error: string | null) => void
  setLoading: (loading: boolean) => void
  clearAuth: () => void
  loadUserProfile: () => Promise<void>
  
  // Helper getters
  getHomeLocation: () => { state: string | null; county: string | null }
  hasHomeLocation: () => boolean
}

export const useAuthStore = create<AuthState>()(
  (set, get) => ({
    // Initial state
    user: null,
    profile: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    homeState: null,
    homeCounty: null,
    lastLoadAttempt: null,
    
    // Actions
    setUser: (user) => {
      // Convert Supabase User to AuthUser if needed
      const authUser = user && 'aud' in user ? convertSupabaseUser(user as User) : user as AuthUser | null
      set({ 
        user: authUser, 
        isAuthenticated: !!authUser,
        error: null // Clear any previous errors
      })
    },
    
    setProfile: (profile) => {
      set({ 
        profile,
        homeState: profile?.state || null,
        homeCounty: profile?.county || null,
        error: null // Clear any previous errors
      })
    },

    setError: (error) => {
      set({ error })
    },

    setLoading: (isLoading) => {
      set({ isLoading })
    },
    
    clearAuth: () => {
      set({
        user: null,
        profile: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        homeState: null,
        homeCounty: null,
        lastLoadAttempt: null,
      })
    },
    
    loadUserProfile: async () => {
      const state = get()
      const now = Date.now()
      
      // Prevent multiple concurrent loads (debounce with 1 second)
      if (state.isLoading || (state.lastLoadAttempt && (now - state.lastLoadAttempt) < 1000)) {
        console.log('Skipping profile load - already loading or too recent')
        return
      }

      set({ isLoading: true, error: null, lastLoadAttempt: now })
      
      try {
        const supabase = createClient()
        
        // Add timeout protection for auth check
        const timeoutPromise = new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Auth timeout')), 15000) // Increased from 5000 to 15000 (15 seconds)
        )
        
        // First check if there's a session using getSession() which doesn't throw errors
        const sessionPromise = supabase.auth.getSession()
        const { data: { session }, error: sessionError } = await Promise.race([
          sessionPromise,
          timeoutPromise
        ])
        
        if (sessionError || !session?.user) {
          console.log('No session found, clearing auth')
          get().clearAuth()
          return
        }
        
        // Only call getUser if we have a valid session
        const authPromise = supabase.auth.getUser()
        const { data: { user }, error: authError } = await Promise.race([
          authPromise,
          timeoutPromise
        ])
        
        if (authError) {
          console.error('Auth error:', authError)
          set({ error: 'Authentication failed', isLoading: false })
          return
        }
        
        if (!user) {
          console.log('No user found, clearing auth')
          get().clearAuth()
          return
        }
        
        // Update user in store
        get().setUser(user)
        
        // Load profile from database
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', user.id)
          .single()
        
        if (profileError) {
          console.error('Profile loading error:', profileError)
          if (profileError.code !== 'PGRST116') { // Not found is okay
            set({ error: 'Failed to load user profile', isLoading: false })
            return
          }
        }
        
        if (profile) {
          get().setProfile(profile)
        }
        
        set({ isLoading: false })
        
      } catch (error) {
        console.error('Profile loading failed:', error)
        set({ 
          error: error instanceof Error ? error.message : 'Failed to load profile',
          isLoading: false 
        })
      }
    },
    
    // Helper getters
    getHomeLocation: () => {
      const state = get()
      return { state: state.homeState, county: state.homeCounty }
    },
    
    hasHomeLocation: () => {
      const state = get()
      return !!(state.homeState && state.homeCounty)
    }
  })
) 