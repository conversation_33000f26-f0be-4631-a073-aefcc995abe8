"use client"

import type React from "react"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
// import Link from "next/link" // Unused import
import { <PERSON><PERSON> } from "@/components/ui/button"
import UniversalNavLink from "@/components/UniversalNavLink"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import LocationDropdown from "@/components/LocationDropdown"
import { Mail, Lock, User, Phone, Eye, EyeOff, ArrowLeft } from "lucide-react"

// Simple toast notification
const showToast = (message: string) => {
  const toast = document.createElement('div')
  toast.className = 'fixed bottom-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50'
  toast.textContent = message
  document.body.appendChild(toast)
  
  setTimeout(() => {
    if (document.body.contains(toast)) {
      document.body.removeChild(toast)
    }
  }, 3000)
}

function LoginPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  
  // Simple state management - avoid complex cascading updates
  const [isLogin, setIsLogin] = useState(true)
  const [authMethod, setAuthMethod] = useState<'email' | 'phone'>('email')
  const [showOTP, setShowOTP] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [returnUrl, setReturnUrl] = useState<string | null>(null)
  
  // Form data
  const [email, setEmail] = useState('')
  const [phone, setPhone] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [selectedState, setSelectedState] = useState('')
  const [selectedCounty, setSelectedCounty] = useState('')
  
  // OTP state
  const [otpCode, setOtpCode] = useState(['', '', '', '', '', ''])
  const [otpResendCooldown, setOtpResendCooldown] = useState(0)
  
  // Errors
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Check if user is already authenticated and redirect
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient()
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (!error && session?.user) {
          // User is already logged in, redirect to return URL or mugshots
          const returnUrlParam = searchParams.get('returnUrl')
          const redirectTo = returnUrlParam ? decodeURIComponent(returnUrlParam) : '/mugshots'
          
          // Use window.location for immediate redirect
          window.location.href = redirectTo
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        // If auth check fails, just continue to show login page
      }
    }
    
    checkAuth()

    // Also listen for auth state changes
    const supabase = createClient()
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        const returnUrlParam = searchParams.get('returnUrl')
        const redirectTo = returnUrlParam ? decodeURIComponent(returnUrlParam) : '/mugshots'
        window.location.href = redirectTo
      }
    })

    return () => subscription.unsubscribe()
  }, [searchParams, router])

  // Get return URL from search params
  useEffect(() => {
    const returnUrlParam = searchParams.get('returnUrl')
    if (returnUrlParam) {
      setReturnUrl(decodeURIComponent(returnUrlParam))
    }
  }, [searchParams])

  // OTP countdown timer
  useEffect(() => {
    if (otpResendCooldown > 0) {
      const timer = setTimeout(() => {
        setOtpResendCooldown(prev => prev - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [otpResendCooldown])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (isLogin && authMethod === 'phone' && !showOTP) {
      if (!phone.trim()) newErrors.phone = 'Phone number is required'
      else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(phone)) newErrors.phone = 'Invalid phone number'
    } else if (isLogin && authMethod === 'email') {
      if (!email.trim()) newErrors.email = 'Email is required'
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) newErrors.email = 'Invalid email'
      if (!password.trim()) newErrors.password = 'Password is required'
    } else if (!isLogin) {
      if (!fullName.trim()) newErrors.fullName = 'Full name is required'
      
      if (authMethod === 'email') {
        if (!email.trim()) newErrors.email = 'Email is required'
        else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) newErrors.email = 'Invalid email'
      } else {
        if (!phone.trim()) newErrors.phone = 'Phone number is required'
        else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(phone)) newErrors.phone = 'Invalid phone number'
      }
      
      if (!password.trim()) newErrors.password = 'Password is required'
      else if (password.length < 8) newErrors.password = 'Password must be at least 8 characters'
      
      if (!confirmPassword.trim()) newErrors.confirmPassword = 'Please confirm password'
      else if (password !== confirmPassword) newErrors.confirmPassword = 'Passwords do not match'
      
      if (!selectedState) newErrors.location = 'Please select your state'
      else if (!selectedCounty) newErrors.location = 'Please select your county'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleOTPChange = (index: number, value: string) => {
    if (value.length > 1) return
    
    const newOtp = [...otpCode]
    newOtp[index] = value
    setOtpCode(newOtp)

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`)
      nextInput?.focus()
    }
  }

  const handleOTPKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpCode[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`)
      prevInput?.focus()
    }
  }

  const handlePhoneLogin = async () => {
    if (!validateForm()) return

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      setShowOTP(true)
      showToast('Verification code sent!')
    } catch {
      setErrors({ general: 'Failed to send verification code' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOTPVerify = async () => {
    if (otpCode.some(digit => !digit)) {
      setErrors({ otp: 'Please enter all 6 digits' })
      return
    }

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Redirect to return URL or show completion
      if (returnUrl) {
        window.location.href = returnUrl
      } else {
        showToast('Successfully logged in!')
        // Default redirect for phone auth
        window.location.href = "/mugshots"
      }
    } catch {
      setErrors({ general: 'Invalid verification code' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    setIsLoading(true)
    try {
      if (isLogin) {
        // Sign in using API endpoint
        const response = await fetch('/api/auth/signin', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email,
            password
          })
        })

        const result = await response.json()
        
        if (result.success) {
          showToast('Successfully logged in!')
          
          // Ensure profile exists after successful signin
          try {
            const profileResponse = await fetch('/api/auth/create-profile', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              }
            })
            
            const profileResult = await profileResponse.json()
            console.log('Profile creation result:', profileResult)
          } catch (profileError) {
            console.warn('Profile creation failed:', profileError)
            // Continue with normal flow even if profile creation fails
          }
          
          // Check if user needs location setup
          if (result.needsLocationSetup) {
            window.location.href = '/auth/location-setup'
          } else {
            const redirectUrl = returnUrl || "/mugshots"
            window.location.href = redirectUrl
          }
        } else {
          setErrors({ general: result.error || 'Sign in failed' })
        }
      } else {
        // Sign up using API endpoint
        const response = await fetch('/api/auth/signup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email,
            password,
            fullName,
            selectedState,
            selectedCounty
          })
        })

        const result = await response.json()
        
        if (result.success) {
          showToast('Account created! Please check your email to confirm, then sign in.')
          
          // Clear form data
          setEmail('')
          setPassword('')
          setConfirmPassword('')
          setFullName('')
          setSelectedState('')
          setSelectedCounty('')
          setErrors({})
          
          // Switch to login mode
          setIsLogin(true)
          
          // Show additional instruction
          setTimeout(() => {
            showToast('Check your email inbox and click the confirmation link to activate your account.')
          }, 2000)
        } else {
          setErrors({ general: result.error || 'Sign up failed' })
        }
      }
    } catch (error) {
      console.error('Authentication error:', error)
      setErrors({ general: 'An unexpected error occurred' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleAuth = async () => {
    setIsLoading(true)
    try {
      const { handleGoogleAuth: initGoogleAuth } = await import('@/lib/auth-utils')
      
      const result = await initGoogleAuth(returnUrl || '/mugshots')
      
      if (result.error) {
        setErrors({ general: result.error.message || 'Google sign-in failed' })
        return
      }

      // Redirect to Google OAuth URL
      if (result.data?.url) {
        window.location.href = result.data.url
      } else {
        setErrors({ general: 'Failed to initialize Google sign-in' })
      }
    } catch (error) {
      console.error('Google OAuth error:', error)
      setErrors({ general: 'Google sign-in failed' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendOTP = async () => {
    if (otpResendCooldown > 0) return

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setOtpResendCooldown(60)
      showToast('Verification code resent!')
    } catch {
      setErrors({ general: 'Failed to resend code' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTabChange = (newIsLogin: boolean) => {
    setIsLogin(newIsLogin)
    setErrors({})
  }

  const handleMethodChange = (method: 'email' | 'phone') => {
    setAuthMethod(method)
    setErrors({})
  }

  const handleBackToPhone = () => {
    setShowOTP(false)
    setOtpCode(['', '', '', '', '', ''])
    setErrors({})
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="max-w-lg mx-auto">
          <Card className="bg-gray-900 border border-pink-500/30 text-white overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-purple-900 to-pink-900 p-4 relative">
              <CardTitle className="text-xl font-bold tracking-tight text-center">
                {isLogin && authMethod === 'phone' && showOTP 
                  ? "Enter Verification Code" 
                  : "Join the Jury"
                }
              </CardTitle>
              {isLogin && authMethod === 'phone' && showOTP && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={handleBackToPhone}
                  className="absolute top-4 left-4 text-white hover:bg-pink-800/50 h-8 w-8"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
            </CardHeader>

            <CardContent className="p-6">
              {/* OTP Verification Screen */}
              {isLogin && authMethod === 'phone' && showOTP ? (
                <div className="space-y-6">
                  <div className="text-center">
                    <p className="text-gray-400 text-sm mb-2">We sent a 6-digit code to</p>
                    <p className="text-white font-medium">{phone}</p>
                  </div>

                  <div className="space-y-4">
                    <Label className="text-gray-300 text-center block">Enter Verification Code</Label>
                    <div className="flex justify-center space-x-2">
                      {otpCode.map((digit, index) => (
                        <Input
                          key={index}
                          id={`otp-${index}`}
                          type="text"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          maxLength={1}
                          value={digit}
                          onChange={(e) => handleOTPChange(index, e.target.value)}
                          onKeyDown={(e) => handleOTPKeyDown(index, e)}
                          className={`w-12 h-12 text-center text-lg font-bold border-pink-500/30 bg-gray-800 text-white ${
                            errors.otp ? "border-red-500" : ""
                          }`}
                        />
                      ))}
                    </div>
                    {errors.otp && <p className="text-red-400 text-xs text-center">{errors.otp}</p>}
                  </div>

                  <Button
                    onClick={handleOTPVerify}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    disabled={isLoading}
                  >
                    {isLoading ? "Verifying..." : "Verify Code"}
                  </Button>

                  <div className="text-center">
                    <p className="text-gray-400 text-sm mb-2">Didn&apos;t receive the code?</p>
                    <Button
                      variant="ghost"
                      onClick={handleResendOTP}
                      disabled={otpResendCooldown > 0 || isLoading}
                      className="text-pink-400 hover:text-pink-300 p-0 h-auto"
                    >
                      {otpResendCooldown > 0 ? `Resend in ${otpResendCooldown}s` : "Resend Code"}
                    </Button>
                  </div>

                  {errors.general && (
                    <div className="bg-red-900/20 border border-red-500/30 rounded-md p-3">
                      <p className="text-red-400 text-sm text-center">{errors.general}</p>
                    </div>
                  )}
                </div>
              ) : (
                /* Main Auth Screen */
                <>
                  <p className="text-gray-400 text-sm mb-6 text-center">
                    {isLogin 
                      ? "Welcome back! Sign in to rate mugshots and join the community." 
                      : "Create your account to start rating mugshots and join the community!"
                    }
                  </p>

                  {/* Login/Signup Toggle */}
                  <div className="flex space-x-1 p-1 bg-gray-800 rounded-lg mb-6">
                    <button
                      type="button"
                      onClick={() => handleTabChange(true)}
                      className={`flex-1 py-2 text-sm font-medium rounded-md transition-colors ${
                        isLogin 
                          ? 'bg-pink-600 text-white' 
                          : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      Login
                    </button>
                    <button
                      type="button"
                      onClick={() => handleTabChange(false)}
                      className={`flex-1 py-2 text-sm font-medium rounded-md transition-colors ${
                        !isLogin 
                          ? 'bg-pink-600 text-white' 
                          : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      Sign Up
                    </button>
                  </div>

                  {/* Google Sign In */}
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full border-gray-600 bg-gray-800 text-white hover:bg-gray-700 mb-4"
                    onClick={handleGoogleAuth}
                    disabled={isLoading}
                  >
                    <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="currentColor"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="currentColor"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="currentColor"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                    Continue with Google
                  </Button>

                  <div className="relative mb-4">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-gray-600" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-gray-900 px-2 text-gray-400">Or continue with</span>
                    </div>
                  </div>

                  {/* Auth Method Selection */}
                  <div className="flex space-x-2 mb-4">
                    <Button
                      type="button"
                      variant={authMethod === 'email' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleMethodChange('email')}
                      className={authMethod === 'email' 
                        ? 'bg-pink-600 hover:bg-pink-700 flex-1' 
                        : 'border-gray-600 text-gray-300 hover:bg-gray-800 flex-1'
                      }
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </Button>
                    <Button
                      type="button"
                      variant={authMethod === 'phone' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleMethodChange('phone')}
                      className={authMethod === 'phone' 
                        ? 'bg-pink-600 hover:bg-pink-700 flex-1' 
                        : 'border-gray-600 text-gray-300 hover:bg-gray-800 flex-1'
                      }
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Phone
                    </Button>
                  </div>

                  {/* Phone Authentication */}
                  {authMethod === 'phone' ? (
                    <div className="space-y-4">
                      {/* Full Name (Signup only) */}
                      {!isLogin && (
                        <div className="space-y-2">
                          <Label htmlFor="fullName" className="text-gray-300">Full Name</Label>
                          <div className="relative">
                            <User className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                            <Input
                              id="fullName"
                              type="text"
                              placeholder="Enter your full name"
                              value={fullName}
                              onChange={(e) => setFullName(e.target.value)}
                              className={`pl-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                                errors.fullName ? "border-red-500" : ""
                              }`}
                            />
                          </div>
                          {errors.fullName && <p className="text-red-400 text-xs">{errors.fullName}</p>}
                        </div>
                      )}

                      {/* Phone Number */}
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-gray-300">Phone Number</Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                          <Input
                            id="phone"
                            type="tel"
                            placeholder="Enter your phone number"
                            value={phone}
                            onChange={(e) => setPhone(e.target.value)}
                            className={`pl-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                              errors.phone ? "border-red-500" : ""
                            }`}
                          />
                        </div>
                        {errors.phone && <p className="text-red-400 text-xs">{errors.phone}</p>}
                        <p className="text-gray-500 text-xs">We&apos;ll send you a verification code via SMS</p>
                      </div>

                      {/* Location (Signup only) */}
                      {!isLogin && (
                        <div className="space-y-2">
                          <Label className="text-gray-300">Location</Label>
                          <LocationDropdown
                            selectedState={selectedState}
                            setSelectedState={setSelectedState}
                            selectedCounty={selectedCounty}
                            setSelectedCounty={setSelectedCounty}
                            statePlaceholder="Select your state"
                            countyPlaceholder="Select your county"
                            className="w-full"
                          />
                          {errors.location && <p className="text-red-400 text-xs">{errors.location}</p>}
                        </div>
                      )}

                      {errors.general && (
                        <div className="bg-red-900/20 border border-red-500/30 rounded-md p-3">
                          <p className="text-red-400 text-sm">{errors.general}</p>
                        </div>
                      )}

                      <Button
                        onClick={handlePhoneLogin}
                        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                        disabled={isLoading}
                      >
                        {isLoading ? "Sending Code..." : (isLogin ? "Send Verification Code" : "Create Account")}
                      </Button>
                    </div>
                  ) : (
                    /* Email/Password Form */
                    <form onSubmit={handleEmailAuth} className="space-y-4">
                      {/* Full Name (Signup only) - Full width row */}
                      {!isLogin && (
                        <div className="space-y-2">
                          <Label htmlFor="fullName" className="text-gray-300">Full Name</Label>
                          <div className="relative">
                            <User className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                            <Input
                              id="fullName"
                              type="text"
                              placeholder="Enter your full name"
                              value={fullName}
                              onChange={(e) => setFullName(e.target.value)}
                              className={`pl-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                                errors.fullName ? "border-red-500" : ""
                              }`}
                            />
                          </div>
                          {errors.fullName && <p className="text-red-400 text-xs">{errors.fullName}</p>}
                        </div>
                      )}

                      {/* Email or Phone Input - Full width row */}
                      <div className="space-y-2">
                        <Label className="text-gray-300">
                          {authMethod === 'email' ? 'Email Address' : 'Phone Number'}
                        </Label>
                        <div className="relative">
                          {authMethod === 'email' ? (
                            <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                          ) : (
                            <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                          )}
                          <Input
                            type={authMethod === 'email' ? 'email' : 'tel'}
                            placeholder={authMethod === 'email' ? 'Enter your email' : 'Enter your phone number'}
                            value={authMethod === 'email' ? email : phone}
                            onChange={(e) => authMethod === 'email' ? setEmail(e.target.value) : setPhone(e.target.value)}
                            className={`pl-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                              errors[authMethod] ? "border-red-500" : ""
                            }`}
                          />
                        </div>
                        {errors[authMethod] && <p className="text-red-400 text-xs">{errors[authMethod]}</p>}
                      </div>

                      {/* Password and Confirm Password Row (Signup only) or single Password (Login) */}
                      {!isLogin ? (
                        <div className="space-y-2">
                          <Label className="text-gray-300">Password</Label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {/* Password */}
                            <div className="space-y-1">
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                                <Input
                                  type={showPassword ? 'text' : 'password'}
                                  placeholder="Create a password"
                                  value={password}
                                  onChange={(e) => setPassword(e.target.value)}
                                  className={`pl-10 pr-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                                    errors.password ? "border-red-500" : ""
                                  }`}
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute right-1 top-1 h-8 w-8 text-gray-500 hover:text-white"
                                  onClick={() => setShowPassword(!showPassword)}
                                >
                                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                </Button>
                              </div>
                              {errors.password && <p className="text-red-400 text-xs">{errors.password}</p>}
                            </div>
                            
                            {/* Confirm Password */}
                            <div className="space-y-1">
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                                <Input
                                  type={showConfirmPassword ? 'text' : 'password'}
                                  placeholder="Confirm password"
                                  value={confirmPassword}
                                  onChange={(e) => setConfirmPassword(e.target.value)}
                                  className={`pl-10 pr-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                                    errors.confirmPassword ? "border-red-500" : ""
                                  }`}
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute right-1 top-1 h-8 w-8 text-gray-500 hover:text-white"
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                </Button>
                              </div>
                              {errors.confirmPassword && <p className="text-red-400 text-xs">{errors.confirmPassword}</p>}
                            </div>
                          </div>
                          <p className="text-gray-500 text-xs">Password must be at least 8 characters long</p>
                        </div>
                      ) : (
                        /* Single Password field for Login */
                        <div className="space-y-2">
                          <Label className="text-gray-300">Password</Label>
                          <div className="relative">
                            <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                            <Input
                              type={showPassword ? 'text' : 'password'}
                              placeholder="Enter your password"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              className={`pl-10 pr-10 border-pink-500/30 bg-gray-800 text-white placeholder:text-gray-500 ${
                                errors.password ? "border-red-500" : ""
                              }`}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-1 top-1 h-8 w-8 text-gray-500 hover:text-white"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                          {errors.password && <p className="text-red-400 text-xs">{errors.password}</p>}
                        </div>
                      )}

                      {/* Location (Signup only) - Full width row */}
                      {!isLogin && (
                        <div className="space-y-2">
                          <Label className="text-gray-300">Location</Label>
                          <LocationDropdown
                            selectedState={selectedState}
                            setSelectedState={setSelectedState}
                            selectedCounty={selectedCounty}
                            setSelectedCounty={setSelectedCounty}
                            statePlaceholder="Select your state"
                            countyPlaceholder="Select your county"
                            className="w-full"
                          />
                          {errors.location && <p className="text-red-400 text-xs">{errors.location}</p>}
                        </div>
                      )}

                      {errors.general && (
                        <div className="bg-red-900/20 border border-red-500/30 rounded-md p-3">
                          <p className="text-red-400 text-sm">{errors.general}</p>
                        </div>
                      )}

                      <Button
                        type="submit"
                        className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 mt-6"
                        disabled={isLoading}
                      >
                        {isLoading 
                          ? (isLogin ? "Signing in..." : "Creating account...") 
                          : (isLogin ? "Sign In" : "Create Account")
                        }
                      </Button>
                      
                      {/* Forgot Password Link - Only show for login with email */}
                      {isLogin && authMethod === 'email' && (
                        <div className="text-center mt-4">
                          <UniversalNavLink 
                            href="/auth/forgot-password"
                            className="text-sm text-pink-400 hover:text-pink-300 hover:underline"
                          >
                            Forgot your password?
                          </UniversalNavLink>
                        </div>
                      )}
                    </form>
                  )}
                </>
              )}

              {/* Terms and Privacy */}
              {!(isLogin && authMethod === 'phone' && showOTP) && (
                <div className="mt-6 text-center">
                  <p className="text-xs text-gray-500">
                    By {isLogin ? "signing in" : "creating an account"}, you agree to our{" "}
                    <UniversalNavLink href="/terms" className="text-pink-400 hover:text-pink-300 underline">
                      Terms of Service
                    </UniversalNavLink>{" "}
                    and{" "}
                    <UniversalNavLink href="/privacy" className="text-pink-400 hover:text-pink-300 underline">
                      Privacy Policy
                    </UniversalNavLink>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

function LoginPageFallback() {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading...</p>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={<LoginPageFallback />}>
      <LoginPageContent />
    </Suspense>
  )
}
