'use client'

import { useState, useEffect, useRef, startTransition } from 'react'
import { Plus, Hash, Loader2, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { useAuthStore } from '@/lib/stores/auth-store'
import { 
  addTagToMugshot, 
  getPopularTags,
  type TagSuggestion
} from '@/lib/services/tag-service'
import { toast } from 'sonner'

interface TagInputProps {
  mugshotId: number
  onTagAdded?: (tag: { id: string; name: string; slug: string }) => void
  className?: string
  compact?: boolean
  placeholder?: string
}

export default function TagInput({ 
  mugshotId, 
  onTagAdded, 
  className = '',
  compact = false,
  placeholder = "Add a tag..."
}: TagInputProps) {
  const { user, isLoading: authLoading } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)
  
  // Input state
  const [inputValue, setInputValue] = useState('')
  const [suggestions, setSuggestions] = useState<TagSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)
  
  // Status state
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  
  // Refs
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionRefs = useRef<(HTMLButtonElement | null)[]>([])
  
  // Debounced search for suggestions
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      if (inputValue.trim().length >= 2) {
        try {
          setIsLoading(true)
          const popularTags = await getPopularTags(inputValue.trim(), 10)
          setSuggestions(popularTags)
          setShowSuggestions(true)
        } catch (error) {
          console.error('Error fetching tag suggestions:', error)
        } finally {
          setIsLoading(false)
        }
      } else {
        setSuggestions([])
        setShowSuggestions(false)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [inputValue])

  // Load initial popular suggestions when input is focused
  useEffect(() => {
    const loadInitialSuggestions = async () => {
      if (inputValue === '' && showSuggestions) {
        try {
          setIsLoading(true)
          const popularTags = await getPopularTags(undefined, 10)
          setSuggestions(popularTags)
        } catch (error) {
          console.error('Error fetching initial suggestions:', error)
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadInitialSuggestions()
  }, [showSuggestions, inputValue])

  // Handle tag submission
  const handleSubmitTag = async (tagName: string) => {
    if (!user) {
      toast.error('Please log in to add tags')
      return
    }

    if (!tagName.trim()) {
      setErrorMessage('Please enter a tag name')
      return
    }

    const cleanTagName = tagName.trim()

    // Client-side validation
    if (cleanTagName.length < 2) {
      setErrorMessage('Tag must be at least 2 characters long')
      return
    }

    if (cleanTagName.length > 20) {
      setErrorMessage('Tag must be no more than 20 characters long')
      return
    }

    if (!/^[a-zA-Z0-9\s\-]+$/.test(cleanTagName)) {
      setErrorMessage('Tag can only contain letters, numbers, spaces, and hyphens')
      return
    }

    setSubmissionStatus('submitting')
    setErrorMessage(null)

    startTransition(async () => {
      try {
        // Determine tag type: if it's already a valid tag type, use it directly
        let tagType: 'wild' | 'funny' | 'spooky'
        const normalizedInput = cleanTagName.toLowerCase().trim()
        
        if (normalizedInput === 'wild' || normalizedInput === 'funny' || normalizedInput === 'spooky') {
          tagType = normalizedInput as 'wild' | 'funny' | 'spooky'
        } else {
          // Map free-form input to predefined tag types
          const tagTypeMapping: Record<string, 'wild' | 'funny' | 'spooky'> = {
            'crazy': 'wild',
            'intense': 'wild',
            'extreme': 'wild',
            'hilarious': 'funny',
            'comedy': 'funny',
            'laugh': 'funny',
            'scary': 'spooky',
            'creepy': 'spooky',
            'eerie': 'spooky'
          }
          
          tagType = tagTypeMapping[normalizedInput] || 'funny' // Default to funny
        }
        
        const result = await addTagToMugshot(mugshotId, tagType)
        
        if (result.success) {
          setSubmissionStatus('success')
          setInputValue('')
          setSuggestions([])
          setShowSuggestions(false)
          setSelectedSuggestionIndex(-1)
          
          toast.success(result.message)
          
          // Notify parent component with properly formatted tag data
          if (onTagAdded && result.tag) {
            onTagAdded({
              id: result.tag.tagType, // Use tagType as unique identifier
              name: result.tag.tagType.charAt(0).toUpperCase() + result.tag.tagType.slice(1), // Capitalize first letter
              slug: result.tag.tagType
            })
          }
          
          // Clear success status after animation
          setTimeout(() => {
            setSubmissionStatus('idle')
          }, 2000)
          
        } else {
          throw new Error(result.message || 'Failed to add tag')
        }
        
      } catch (error) {
        console.error('Tag submission error:', error)
        setSubmissionStatus('error')
        setErrorMessage(error instanceof Error ? error.message : 'Failed to add tag')
        
        toast.error(error instanceof Error ? error.message : 'Failed to add tag')
        
        // Clear error status after delay
        setTimeout(() => {
          setSubmissionStatus('idle')
          setErrorMessage(null)
        }, 3000)
      }
    })
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleSubmitTag(inputValue)
      }
      return
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (selectedSuggestionIndex >= 0) {
          const selectedTag = suggestions[selectedSuggestionIndex]
          handleSubmitTag(selectedTag.tagType) // Use tagType for submission
        } else {
          handleSubmitTag(inputValue)
        }
        break
      case 'Escape':
        setShowSuggestions(false)
        setSelectedSuggestionIndex(-1)
        break
      default:
        break
    }
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: TagSuggestion) => {
    handleSubmitTag(suggestion.tagType) // Use tagType for submission
  }

  // Focus management for suggestions
  useEffect(() => {
    if (selectedSuggestionIndex >= 0 && suggestionRefs.current[selectedSuggestionIndex]) {
      suggestionRefs.current[selectedSuggestionIndex]?.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth'
      })
    }
  }, [selectedSuggestionIndex])

  // Character count and validation
  const characterCount = inputValue.length
  const isValidLength = characterCount >= 2 && characterCount <= 20
  const isValidFormat = /^[a-zA-Z0-9\s\-]*$/.test(inputValue)

  // Render loading state
  if (authLoading) {
    return (
      <Card className={`bg-gray-900/90 border-cyan-500/30 ${className}`}>
        <CardContent className={compact ? 'p-3' : 'p-4'}>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-32" />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Render not authenticated state
  if (!user) {
    return (
      <Card className={`bg-gray-900/90 border-gray-600/30 ${className}`}>
        <CardContent className={compact ? 'p-3' : 'p-4'}>
          <div className="text-gray-400 text-sm text-center">
            <Hash className="h-4 w-4 inline mr-1" />
            {compact ? 'Login to tag' : 'Login to add tags'}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <Card className="bg-gray-900/90 border-cyan-500/30">
        <CardContent className={compact ? 'p-3' : 'p-4'}>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4 text-cyan-400 flex-shrink-0" />
              <span className={`font-medium text-white ${compact ? 'text-sm' : 'text-base'}`}>
                Add Community Tag
              </span>
            </div>
            
            <div className="space-y-2">
              {/* Input field */}
              <div className="relative">
                <Input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onFocus={() => setShowSuggestions(true)}
                  onBlur={() => {
                    // Delay hiding suggestions to allow clicks
                    setTimeout(() => setShowSuggestions(false), 150)
                  }}
                  placeholder={placeholder}
                  disabled={submissionStatus === 'submitting'}
                  className={`
                    bg-gray-800 border-gray-600 text-white placeholder-gray-400
                    focus:border-cyan-500 focus:ring-cyan-500
                    ${!isValidFormat && inputValue ? 'border-red-500' : ''}
                    ${submissionStatus === 'error' ? 'border-red-500' : ''}
                    ${submissionStatus === 'success' ? 'border-green-500' : ''}
                  `}
                  maxLength={20}
                />
                
                {/* Status indicator */}
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {submissionStatus === 'submitting' && (
                    <Loader2 className="h-4 w-4 animate-spin text-cyan-400" />
                  )}
                  {submissionStatus === 'success' && (
                    <CheckCircle className="h-4 w-4 text-green-400" />
                  )}
                  {submissionStatus === 'error' && (
                    <AlertCircle className="h-4 w-4 text-red-400" />
                  )}
                </div>
              </div>
              
              {/* Character count and validation */}
              <div className="flex justify-between items-center text-xs">
                <div className="space-x-2">
                  <span className={`${isValidLength ? 'text-gray-400' : 'text-yellow-400'}`}>
                    {characterCount}/20
                  </span>
                  {!isValidFormat && inputValue && (
                    <span className="text-red-400">Letters, numbers, spaces, hyphens only</span>
                  )}
                </div>
                
                <Button
                  type="button"
                  size="sm"
                  onClick={() => handleSubmitTag(inputValue)}
                  disabled={!inputValue.trim() || submissionStatus === 'submitting' || !isValidFormat}
                  className="bg-cyan-600 hover:bg-cyan-700 text-white text-xs px-3 py-1 h-6"
                >
                  {submissionStatus === 'submitting' ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <>
                      <Plus className="h-3 w-3 mr-1" />
                      Add
                    </>
                  )}
                </Button>
              </div>
              
              {/* Error message */}
              {errorMessage && (
                <div className="text-red-400 text-xs">
                  {errorMessage}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Suggestions dropdown */}
      {showSuggestions && (suggestions.length > 0 || isLoading) && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-800 border-gray-600 max-h-60 overflow-y-auto">
          <CardContent className="p-2">
            {isLoading ? (
              <div className="space-y-2">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-2 p-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-8 ml-auto" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-1">
                {suggestions.map((suggestion, index) => (
                                     <button
                     key={suggestion.id}
                     ref={(el) => { suggestionRefs.current[index] = el }}
                     type="button"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className={`
                      w-full text-left p-2 rounded flex items-center justify-between
                      transition-colors duration-150
                      ${index === selectedSuggestionIndex 
                        ? 'bg-cyan-600/20 text-cyan-400' 
                        : 'text-gray-300 hover:bg-gray-700/50'
                      }
                    `}
                  >
                    <div className="flex items-center space-x-2">
                      <Hash className="h-3 w-3" />
                      <span className="text-sm">{suggestion.name}</span>
                      {suggestion.is_trending && (
                        <Badge variant="secondary" className="text-xs px-1 py-0 bg-orange-500/20 text-orange-400">
                          Trending
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">
                      {suggestion.usage_count} uses
                    </span>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
} 