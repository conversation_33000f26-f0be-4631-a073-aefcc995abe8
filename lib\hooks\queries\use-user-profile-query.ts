import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import type { DatabaseProfile } from '@/lib/types/database'

interface UserProfileQueryResponse {
  success: boolean
  data?: DatabaseProfile
  error?: string
  message?: string
}

export function useUserProfileQuery(enabled = true) {
  const { user, isAuthenticated } = useAuthStore()
  
  return useQuery({
    queryKey: ['user', 'profile', user?.id],
    queryFn: async (): Promise<DatabaseProfile | null> => {
      if (!user?.id) {
        throw new Error('User not authenticated')
      }
      
      const response = await fetch('/api/debug-profile')
      const data: UserProfileQueryResponse = await response.json()
      
      if (!response.ok) {
        if (data.error === 'UNAUTHENTICATED') {
          // Handle auth redirect with current URL
          const currentUrl = window.location.href
          window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
          return null
        }
        throw new Error(data.message || 'Failed to fetch profile')
      }
      
      if (!data.success || !data.data) {
        throw new Error(data.message || 'Profile not found')
      }
      
      return data.data
    },
    enabled: enabled && isAuthenticated && !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes - profile data doesn't change often
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry authentication errors
      if (error?.message?.includes('UNAUTHENTICATED') || 
          error?.message?.includes('not authenticated')) {
        return false
      }
      // Retry network errors up to 3 times
      return failureCount < 3
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
} 