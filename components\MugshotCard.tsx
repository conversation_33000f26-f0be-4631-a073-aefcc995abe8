"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import RatingStatistics from "./RatingStatistics"
import ReportContentDialog from "./ReportContentDialog"
import type { RatingStatistics as RatingStatsType } from "@/lib/services/rating-service"
import { getDisplayTagBadge, type TagStatistics } from "@/lib/utils/tag-utils"

interface MugshotCardProps {
  mugshot: {
    id: number
    name: string
    location: string
    rating: number
    image: string
    category: string
    votes: number
    views?: number
    arrestDate?: string
    birthDate?: string
    offenses?: string[]
    state?: string
    county?: string
    isLocal?: boolean
    rank?: number
    weekEnding?: string
    wildCount?: number
    funnyCount?: number
    spookyCount?: number
  }
  onClick: (mugshot: MugshotCardProps['mugshot']) => void
  cardSize?: "small" | "medium" | "large"
  showLocalBadge?: boolean
  showRankBadge?: boolean
  className?: string
  // Voting functionality
  showVoteButton?: boolean
  onVote?: (mugshotId: number, event: React.MouseEvent) => void
  isVoted?: boolean
  voteButtonText?: string
  votedButtonText?: string
  // Custom content
  customInfo?: React.ReactNode
  customFooter?: React.ReactNode
  // Optimized rating stats (to reduce API calls)
  ratingStats?: RatingStatsType
}

export default function MugshotCard({ 
  mugshot, 
  onClick, 
  cardSize = "large",
  showLocalBadge = false,
  showRankBadge = false,
  className = "",
  showVoteButton = false,
  onVote,
  isVoted = false,
  voteButtonText = "Vote Now",
  votedButtonText = "Voted!",
  customInfo,
  customFooter,
  ratingStats
}: MugshotCardProps) {
  
  const getCardHeight = () => {
    switch (cardSize) {
      case "small":
        return "h-48"
      case "medium":
        return "h-56"
      case "large":
        return "h-64"
      default:
        return "h-64"
    }
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return "🏆"
    if (rank === 2) return "🥈"
    if (rank === 3) return "🥉"
    if (rank <= 10) return "⭐"
    return "🎖️"
  }

  const getRankBadgeColor = (rank: number) => {
    if (rank === 1) return "bg-gradient-to-r from-yellow-400 to-yellow-600"
    if (rank === 2) return "bg-gradient-to-r from-gray-300 to-gray-500"
    if (rank === 3) return "bg-gradient-to-r from-amber-500 to-amber-700"
    if (rank <= 10) return "bg-gradient-to-r from-pink-500 to-purple-600"
    return "bg-gradient-to-r from-gray-600 to-gray-800"
  }

  return (
    <div 
      className={`mugshot-card group cursor-pointer ${className}`}
      onClick={() => onClick(mugshot)}
    >
      {/* Local Badge */}
      {showLocalBadge && mugshot.isLocal && (
        <div className="absolute top-3 left-3 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full z-10">
          LOCAL
        </div>
      )}

      {/* Rank Badge */}
      {showRankBadge && mugshot.rank && (
        <div className={`absolute top-3 left-3 ${getRankBadgeColor(mugshot.rank)} text-white text-xs font-bold px-3 py-1 rounded-full z-10 flex items-center gap-1`}>
          <span>{getRankIcon(mugshot.rank)}</span>
          <span>#{mugshot.rank}</span>
        </div>
      )}
      
      {/* Tag Badge with Emoji (replaces category badge) */}
      {(() => {
        // Create tag statistics from mugshot data
                const tagStats: TagStatistics = {
          wild: mugshot.wildCount || 0,
          funny: mugshot.funnyCount || 0,
          spooky: mugshot.spookyCount || 0,
          totalTags: (mugshot.wildCount || 0) + (mugshot.funnyCount || 0) + (mugshot.spookyCount || 0)
        }
        
        // Get the tag badge to display based on counts
        const tagBadge = getDisplayTagBadge(tagStats)
        
        // Only show badge if there are tags
        if (!tagBadge) return null
        
        return (
          <div className="absolute top-3 right-3 bg-cyan-500 text-black text-xs font-bold px-2 py-1 rounded-full z-10 flex items-center gap-1">
            <span>{tagBadge.emoji}</span>
            <span>{tagBadge.displayName}</span>
            {tagBadge.count > 1 && (
              <span className="bg-black/20 px-1 rounded text-xs">{tagBadge.count}</span>
            )}
          </div>
        )
      })()}

      {/* Mugshot Image */}
      <div className="relative overflow-hidden rounded-lg mb-4 bg-gray-800">
        <Image
          src={mugshot.image}
          alt={`${mugshot.name} mugshot`}
          width={300}
          height={400}
          className={`object-cover w-full ${getCardHeight()}`}
        />
      </div>

      {/* Mugshot Info */}
      <div className="px-4 pb-4">
        <h3 className="font-bold text-white text-lg mb-1">{mugshot.name}</h3>
        <p className="text-gray-400 text-sm mb-2">{mugshot.location}</p>
        
        {/* Custom Info */}
        {customInfo && (
          <div className="mb-3">
            {customInfo}
          </div>
        )}
        
        <div className={`${showVoteButton || customFooter ? 'mb-3' : 'mb-2'}`}>
          <RatingStatistics
            mugshotId={mugshot.id.toString()}
            compact={true}
            showDetails={false}
            preloadedStats={ratingStats}
          />
        </div>

        {/* Vote Button */}
        {showVoteButton && onVote && (
          <div className="flex justify-center">
            <Button 
              className={`w-full vote-animation ${isVoted ? 'vote-disabled' : 'vote-pulse'}`}
              onClick={(e) => onVote(mugshot.id, e)}
              disabled={isVoted}
            >
              {isVoted ? votedButtonText : voteButtonText}
            </Button>
          </div>
        )}

        {/* Report Button */}
        <div className="flex justify-center mt-2">
          <ReportContentDialog
            contentType="mugshot"
            contentId={mugshot.id.toString()}
            onReportSubmitted={() => {
              console.log(`Reported mugshot ${mugshot.id}`)
            }}
          />
        </div>

        {/* Custom Footer */}
        {customFooter && (
          <div className="text-center">
            {customFooter}
          </div>
        )}
      </div>
    </div>
  )
}