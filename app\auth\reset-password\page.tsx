"use client"

import { useState, useEffect, Suspense } from "react"
import { useRout<PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Mail, AlertCircle, CheckCircle } from "lucide-react"
import PasswordChangeForm from "@/components/PasswordChangeForm"

function ResetPasswordContent() {
  const router = useRouter()
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient()
        
        // First check if there's a session using getSession() which doesn't throw errors
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        if (sessionError) {
          console.error('Session check error:', sessionError)
          setError('Unable to verify authentication. Please try requesting a new reset link.')
          return
        }

        if (!session?.user) {
          console.log('No session found for password reset')
          setError('Authentication required. Please click the reset link from your email.')
          return
        }

        // Only call getUser if we have a valid session
        const { data: { user }, error } = await supabase.auth.getUser()
        
        if (error) {
          console.error('Auth check error:', error)
          setError('Unable to verify authentication. Please try requesting a new reset link.')
          return
        }

        if (!user) {
          console.log('User not authenticated for password reset')
          setError('Authentication required. Please click the reset link from your email.')
          return
        }

        console.log('User authenticated for password reset:', user.id, user.email)
        setUser(user)

        // Check URL fragments for auth tokens (from email link)
        const hashParams = new URLSearchParams(window.location.hash.substring(1))
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')
        const type = hashParams.get('type')

        console.log('URL hash params:', { 
          hasAccessToken: !!accessToken, 
          hasRefreshToken: !!refreshToken, 
          type 
        })

        // If we have tokens from the URL, ensure session is set
        if (accessToken && refreshToken) {
          console.log('Setting session from URL tokens...')
          const { error: sessionError } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          })

          if (sessionError) {
            console.error('Session set error:', sessionError)
            setError('Invalid reset link. Please request a new password reset.')
            return
          }
        }

      } catch (error) {
        console.error('Reset password auth check error:', error)
        setError('An unexpected error occurred. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const handlePasswordChangeSuccess = () => {
    console.log('Password reset completed successfully')
    // The PasswordChangeForm will handle logout and redirect
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-gray-600">Verifying reset link...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="w-5 h-5" />
              Password Reset Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">{error}</p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                Back to Login
              </button>
              <button
                onClick={() => router.push('/auth/forgot-password')}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                Request New Reset Link
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-600">
              <Mail className="w-5 h-5" />
              Authentication Required
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Please click the password reset link from your email to authenticate and reset your password.
            </p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                Back to Login
              </button>
              <button
                onClick={() => router.push('/auth/forgot-password')}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                Request New Reset Link
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-4">
        {/* Welcome Message */}
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <CheckCircle className="w-8 h-8 text-green-500 mx-auto" />
              <h2 className="text-lg font-semibold">Reset Your Password</h2>
              <p className="text-sm text-gray-600">
                Authenticated as: <strong>{user.email}</strong>
              </p>
              <p className="text-xs text-gray-500">
                Enter your new password below
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Password Change Form */}
        <PasswordChangeForm
          title="Set New Password"
          description="Choose a strong password for your account"
          onSuccess={handlePasswordChangeSuccess}
          autoLogout={true}
        />
      </div>
    </div>
  )
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    }>
      <ResetPasswordContent />
    </Suspense>
  )
} 