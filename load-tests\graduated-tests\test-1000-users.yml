# Test 7: 1000 Concurrent Users - Extreme Stress Test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 100
      name: "Warm-up: 100 users/sec"
    - duration: 480
      arrivalRate: 500
      name: "Extreme Stress: 500 users/sec (1000 concurrent)"
    - duration: 60
      arrivalRate: 100
      name: "Cool-down: 100 users/sec"
  
  ensure:
    - http.response_time.p95: 15000  # 95% under 15 seconds
    - http.response_time.median: 5000 # Median under 5 seconds
    - http.codes.200: 20             # 20% success rate (expect massive failures)
    - http.codes.5xx: 50             # Less than 50% server errors

  http:
    timeout: 90
    pool: 1200
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "1000 Users - Mugshots API"
    weight: 50
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            sortBy: "{{ sortBy }}"
          name: "GET /api/mugshots - 1000 users"
      - think: 0.1

  - name: "1000 Users - Details API"
    weight: 50
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 1000 users"
      - think: 0.2

processor: "../scenarios/data-generators.js"
