import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Sign in with Supabase Auth
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.trim(),
      password
    })

    if (error) {
      console.error('Signin error:', error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      )
    }

    if (!data.user || !data.session) {
      return NextResponse.json(
        { success: false, error: 'Failed to authenticate user' },
        { status: 400 }
      )
    }

    console.log('User signed in successfully:', data.user.id, data.user.email)

    // Ensure profile exists for authenticated user
    try {
      console.log('Ensuring profile exists for signed-in user...')
      
      // Use the server-side Supabase client to make the profile creation request
      const profileResult = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/auth/create-profile`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${data.session.access_token}`,
          'Content-Type': 'application/json'
        }
      })
      
      const profileData = await profileResult.json()
      
      if (profileData.success) {
        console.log('Profile verified/created successfully for signin:', profileData.message)
      } else {
        console.warn('Profile creation/check failed for signin:', profileData.error)
      }
    } catch (profileError) {
      console.error('Profile creation request failed for signin:', profileError)
    }

    // Check if user has a profile and needs location setup
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', data.user.id)
      .single()

    // If no profile or profile doesn't have location, user might need location setup
    const needsLocationSetup = !profile || !profile.state || !profile.county

    return NextResponse.json({
      success: true,
      message: 'Successfully signed in',
      user: {
        id: data.user.id,
        email: data.user.email,
        user_metadata: data.user.user_metadata
      },
      needsLocationSetup,
      profile: profile || null
    })

  } catch (error) {
    console.error('Signin API error:', error)
    return NextResponse.json(
      { success: false, error: 'An unexpected error occurred during signin' },
      { status: 500 }
    )
  }
} 