import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, message: 'Authentication required to submit ratings', error: 'UNAUTHENTICATED' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { mugshotId, rating } = body

    // Validate input
    if (!mugshotId || typeof mugshotId !== 'number') {
      return NextResponse.json(
        { success: false, message: 'Valid mugshot ID is required', error: 'INVALID_MUGSHOT_ID' },
        { status: 400 }
      )
    }

    if (!rating || typeof rating !== 'number' || rating < 1 || rating > 10) {
      return NextResponse.json(
        { success: false, message: 'Rating must be between 1 and 10', error: 'INVALID_RATING' },
        { status: 400 }
      )
    }

    // Use database function for validation and insertion
    const { error: dbError } = await supabase
      .rpc('insert_rating', {
        p_mugshot_id: mugshotId,
        p_user_id: user.id,
        p_rating: rating
      })

    if (dbError) {
      console.error('Database error submitting rating:', dbError)
      
      // Handle specific error cases
      if (dbError.message?.includes('not yet enabled')) {
        return NextResponse.json(
          { success: false, message: 'Rating not yet enabled for this mugshot', error: 'RATING_NOT_ENABLED' },
          { status: 400 }
        )
      }
      
      if (dbError.message?.includes('not found')) {
        return NextResponse.json(
          { success: false, message: 'Mugshot not found', error: 'MUGSHOT_NOT_FOUND' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { success: false, message: 'Failed to submit rating. Please try again.', error: 'DATABASE_ERROR' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Rating submitted successfully!',
      rating: {
        mugshotId,
        rating,
        userId: user.id
      }
    })

  } catch (error) {
    console.error('Rating submission error:', error)
    return NextResponse.json(
      { success: false, message: 'An unexpected error occurred. Please try again.', error: 'UNEXPECTED_ERROR' },
      { status: 500 }
    )
  }
} 