import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RefreshCw, AlertCircle, Wifi, WifiOff } from 'lucide-react'

export function MugshotsErrorFallback({ 
  resetErrorBoundary 
}: { 
  error: Error
  resetErrorBoundary: () => void 
}) {
  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <AlertCircle className="h-5 w-5" />
          Unable to load mugshots
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground">
          We&apos;re having trouble loading the mugshot data. This might be a temporary issue.
        </p>
        <div className="flex gap-2">
          <Button onClick={resetErrorBoundary} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh Page
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export function MugshotDetailErrorFallback({ 
  resetErrorBoundary 
}: { 
  error: Error
  resetErrorBoundary: () => void 
}) {
  return (
    <div className="p-6 text-center">
      <h3 className="text-lg font-semibold mb-2">Unable to load mugshot details</h3>
      <p className="text-muted-foreground mb-4">
        We couldn&apos;t load the details for this mugshot.
      </p>
      <Button onClick={resetErrorBoundary} size="sm">
        Try Again
      </Button>
    </div>
  )
}

export function RatingErrorFallback({ 
  error: _error, 
  resetErrorBoundary 
}: { 
  error: Error
  resetErrorBoundary: () => void 
}) {
  // Determine error type from message for better UX
  const getErrorInfo = () => {
    const message = _error?.message?.toLowerCase() || ''
    
    if (message.includes('network') || message.includes('fetch')) {
      return {
        icon: <WifiOff className="h-5 w-5" />,
        title: 'Connection Error',
        description: 'Please check your internet connection and try again.',
        showRefresh: true
      }
    }
    
    if (message.includes('timeout')) {
      return {
        icon: <AlertCircle className="h-5 w-5" />,
        title: 'Request Timeout',
        description: 'The request took too long. Please try again.',
        showRefresh: true
      }
    }
    
    if (message.includes('server') || message.includes('500')) {
      return {
        icon: <AlertCircle className="h-5 w-5" />,
        title: 'Server Error',
        description: 'Our servers are experiencing issues. Please try again in a moment.',
        showRefresh: true
      }
    }
    
    // Default error for any unhandled cases including JSON/HTML errors
    return {
      icon: <AlertCircle className="h-5 w-5" />,
      title: 'Loading Error',
      description: 'Unable to load rating data. Please refresh the page.',
      showRefresh: true
    }
  }
  
  const errorInfo = getErrorInfo()
  
  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-red-500/30">
      <div className="text-center space-y-3">
        <div className="flex items-center justify-center text-red-400">
          {errorInfo.icon}
        </div>
        <div>
          <h3 className="text-red-400 text-sm font-medium">{errorInfo.title}</h3>
          <p className="text-gray-400 text-xs mt-1">{errorInfo.description}</p>
        </div>
        {errorInfo.showRefresh && (
          <div className="flex gap-2 justify-center">
            <Button 
              size="sm" 
              variant="outline"
              onClick={resetErrorBoundary}
              className="text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Try Again
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export function TagErrorFallback({ 
  error: _error,
  resetErrorBoundary 
}: { 
  error: Error
  resetErrorBoundary: () => void 
}) {
  return (
    <div className="text-center p-2">
      <div className="text-red-400 text-xs mb-2">⚠️ Tags unavailable</div>
      <Button 
        size="sm" 
        variant="ghost"
        onClick={resetErrorBoundary}
        className="text-xs h-6 px-2"
      >
        Retry
      </Button>
    </div>
  )
} 

export function NetworkErrorFallback({ 
  resetErrorBoundary 
}: { 
  resetErrorBoundary: () => void 
}) {
  return (
    <Card className="m-4 border-orange-500/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-600">
          <WifiOff className="h-5 w-5" />
          Connection Issue
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground">
          Please check your internet connection and try again.
        </p>
        <div className="flex gap-2">
          <Button onClick={resetErrorBoundary} className="flex items-center gap-2">
            <Wifi className="h-4 w-4" />
            Retry Connection
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Generic fallback for any unhandled errors (including JSON/HTML errors)
export function GenericErrorFallback({ 
  error,
  resetErrorBoundary 
}: { 
  error: Error
  resetErrorBoundary: () => void 
}) {
  // Don't expose raw error objects or JSON to users
  const isProductionMode = process.env.NODE_ENV === 'production'
  
  return (
    <Card className="m-4 border-red-500/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <AlertCircle className="h-5 w-5" />
          Something went wrong
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground">
          An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
        </p>
        
        {/* Only show technical details in development */}
        {!isProductionMode && error && (
          <details className="text-xs bg-gray-100 p-2 rounded border">
            <summary className="cursor-pointer font-medium">Technical Details (Dev Mode)</summary>
            <pre className="mt-2 whitespace-pre-wrap text-xs overflow-auto max-h-32">
              {error.message}
            </pre>
          </details>
        )}
        
        <div className="flex gap-2">
          <Button onClick={resetErrorBoundary} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh Page
          </Button>
        </div>
      </CardContent>
    </Card>
  )
} 