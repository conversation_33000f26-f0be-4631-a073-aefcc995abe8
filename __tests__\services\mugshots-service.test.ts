import { describe, it, expect, beforeEach, vi, MockedFunction } from 'vitest'
import { createClient } from '@supabase/supabase-js'

// Mock Supabase client
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn()
}))

// Types
interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  category?: string[]
}

interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

interface PaginationOptions {
  page: number
  perPage: number
}

// Service class for mugshots operations
export class MugshotsService {
  private supabase: any

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  async getMugshots(
    filters: MugshotFilters = {},
    sort: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 }
  ) {
    const offset = (pagination.page - 1) * pagination.perPage
    
    // Use the search_mugshots function for complex filtering
    const { data, error } = await this.supabase.rpc('search_mugshots', {
      search_term: filters.searchTerm || null,
      state_filter: filters.state || null,
      county_filter: filters.county || null,
      date_from: filters.dateFrom || null,
      date_to: filters.dateTo || null,
      limit_count: pagination.perPage,
      offset_count: offset
    })

    if (error) throw error

    // Apply sorting (the function returns newest first by default)
    let sortedData = data || []
    if (sort.sortBy === 'top-rated') {
      // Note: Will need to join with ratings table when implemented
      sortedData = sortedData.sort((a: any, b: any) => (b.rating || 0) - (a.rating || 0))
    } else if (sort.sortBy === 'most-viewed') {
      // Note: Will need to track views when implemented
      sortedData = sortedData.sort((a: any, b: any) => (b.views || 0) - (a.views || 0))
    }

    return sortedData
  }

  async getMugshotCount(filters: MugshotFilters = {}) {
    const { data, error } = await this.supabase.rpc('count_mugshots', {
      search_term: filters.searchTerm || null,
      state_filter: filters.state || null,
      county_filter: filters.county || null,
      date_from: filters.dateFrom || null,
      date_to: filters.dateTo || null
    })

    if (error) throw error
    return data || 0
  }

  async getMugshotById(id: number) {
    const { data, error } = await this.supabase
      .from('mugshots')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  async createMugshot(mugshotData: any) {
    const { data, error } = await this.supabase
      .from('mugshots')
      .insert(mugshotData)
      .select()

    if (error) throw error
    return data?.[0]
  }
}

describe('MugshotsService', () => {
  let service: MugshotsService
  let mockSupabaseClient: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create mock Supabase client
    mockSupabaseClient = {
      rpc: vi.fn(),
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn()
          })),
          limit: vi.fn()
        })),
        insert: vi.fn(() => ({
          select: vi.fn()
        }))
      }))
    }

    // Mock createClient to return our mock
    ;(createClient as MockedFunction<typeof createClient>).mockReturnValue(mockSupabaseClient)

    service = new MugshotsService(mockSupabaseClient)
  })

  describe('getMugshots', () => {
    it('should call search_mugshots RPC with correct parameters', async () => {
      const mockData = [
        {
          id: 1,
          firstName: 'John',
          lastName: 'Doe',
          stateOfBooking: 'California',
          countyOfBooking: 'Los Angeles'
        }
      ]

      mockSupabaseClient.rpc.mockResolvedValue({ data: mockData, error: null })

      const filters = {
        searchTerm: 'john',
        state: 'California',
        county: 'Los Angeles'
      }

      const result = await service.getMugshots(filters)

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('search_mugshots', {
        search_term: 'john',
        state_filter: 'California',
        county_filter: 'Los Angeles',
        date_from: null,
        date_to: null,
        limit_count: 12,
        offset_count: 0
      })

      expect(result).toEqual(mockData)
    })

    it('should handle pagination correctly', async () => {
      const mockData: any[] = [{ id: 1 }, { id: 2 }]
      mockSupabaseClient.rpc.mockResolvedValue({ data: mockData, error: null })

      await service.getMugshots({}, { sortBy: 'newest' }, { page: 3, perPage: 24 })

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('search_mugshots', {
        search_term: null,
        state_filter: null,
        county_filter: null,
        date_from: null,
        date_to: null,
        limit_count: 24,
        offset_count: 48 // (3-1) * 24
      })
    })

    it('should handle date range filters', async () => {
      const mockData: any[] = []
      mockSupabaseClient.rpc.mockResolvedValue({ data: mockData, error: null })

      const filters = {
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31'
      }

      await service.getMugshots(filters)

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('search_mugshots', {
        search_term: null,
        state_filter: null,
        county_filter: null,
        date_from: '2024-01-01',
        date_to: '2024-01-31',
        limit_count: 12,
        offset_count: 0
      })
    })

    it('should throw error when RPC call fails', async () => {
      const mockError = new Error('Database error')
      mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: mockError })

      await expect(service.getMugshots()).rejects.toThrow('Database error')
    })

    it('should handle empty results', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({ data: [], error: null })

      const result = await service.getMugshots()

      expect(result).toEqual([])
    })

    it('should handle null data response', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: null })

      const result = await service.getMugshots()

      expect(result).toEqual([])
    })
  })

  describe('getMugshotCount', () => {
    it('should call count_mugshots RPC with correct parameters', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({ data: 42, error: null })

      const filters = {
        searchTerm: 'test',
        state: 'Texas'
      }

      const count = await service.getMugshotCount(filters)

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('count_mugshots', {
        search_term: 'test',
        state_filter: 'Texas',
        county_filter: null,
        date_from: null,
        date_to: null
      })

      expect(count).toBe(42)
    })

    it('should return 0 for null data', async () => {
      mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: null })

      const count = await service.getMugshotCount()

      expect(count).toBe(0)
    })

    it('should throw error when count fails', async () => {
      const mockError = new Error('Count failed')
      mockSupabaseClient.rpc.mockResolvedValue({ data: null, error: mockError })

      await expect(service.getMugshotCount()).rejects.toThrow('Count failed')
    })
  })

  describe('getMugshotById', () => {
    it('should fetch mugshot by ID', async () => {
      const mockMugshot = {
        id: 1,
        firstName: 'John',
        lastName: 'Doe'
      }

      const mockSelect = {
        eq: vi.fn(() => ({
          single: vi.fn().mockResolvedValue({ data: mockMugshot, error: null })
        }))
      }

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => mockSelect)
      })

      const result = await service.getMugshotById(1)

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('mugshots')
      expect(mockSelect.eq).toHaveBeenCalledWith('id', 1)
      expect(result).toEqual(mockMugshot)
    })

    it('should throw error when mugshot not found', async () => {
      const mockError = new Error('Mugshot not found')

      const mockSelect = {
        eq: vi.fn(() => ({
          single: vi.fn().mockResolvedValue({ data: null, error: mockError })
        }))
      }

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn(() => mockSelect)
      })

      await expect(service.getMugshotById(999)).rejects.toThrow('Mugshot not found')
    })
  })

  describe('createMugshot', () => {
    it('should create new mugshot', async () => {
      const mugshotData = {
        firstName: 'Jane',
        lastName: 'Smith',
        stateOfBooking: 'California',
        countyOfBooking: 'San Francisco'
      }

      const createdMugshot = { id: 1, ...mugshotData }

      const mockInsert = {
        select: vi.fn().mockResolvedValue({ data: [createdMugshot], error: null })
      }

      mockSupabaseClient.from.mockReturnValue({
        insert: vi.fn(() => mockInsert)
      })

      const result = await service.createMugshot(mugshotData)

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('mugshots')
      expect(mockInsert.select).toHaveBeenCalled()
      expect(result).toEqual(createdMugshot)
    })

    it('should throw error when creation fails', async () => {
      const mockError = new Error('Creation failed')

      const mockInsert = {
        select: vi.fn().mockResolvedValue({ data: null, error: mockError })
      }

      mockSupabaseClient.from.mockReturnValue({
        insert: vi.fn(() => mockInsert)
      })

      await expect(service.createMugshot({})).rejects.toThrow('Creation failed')
    })
  })
}) 