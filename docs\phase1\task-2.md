# Task 2: Core Query Hooks Implementation & Optimistic Mutation Hooks

## Overview

This task implements the foundational server state management layer using TanStack Query, replacing direct API calls with intelligent caching, optimistic updates, and background synchronization. This is the core transformation that moves all server state out of Zustand stores and into a dedicated query management system.

**What this accomplishes:**
- Transforms existing API endpoints into smart Query hooks with automatic caching
- Implements optimistic mutations for instant UI feedback on ratings and tags
- Establishes the foundation for realtime updates and performance improvements
- Maintains exact functional compatibility with existing components

**Why this is critical:**
- Provides the infrastructure for dramatic performance improvements through intelligent caching
- Enables optimistic updates for instant user feedback
- Creates the foundation for realtime features and background data synchronization
- Separates server state from client state for cleaner architecture

**Integration strategy:**
- Query hooks will initially work alongside existing Zustand patterns
- Components will gradually migrate to use these hooks in subsequent tasks
- Backward compatibility ensures no disruption during transition

## Prerequisites

- [ ] TanStack Query installed and configured (completed in Task 1)
- [ ] Query Client Provider set up in app layout (completed in Task 1) 
- [ ] Understanding of existing API endpoints from `/api/mugshots`, `/api/ratings/submit`, `/api/tags/toggle`
- [ ] Familiarity with current Zustand store patterns in `lib/stores/`
- [ ] Knowledge of existing authentication flow and redirect patterns

## Detailed Implementation Checklist

### 1.1 Core Mugshots Query Hook Implementation

- [ ] Create `lib/hooks/queries/use-mugshots-query.ts`
  - Implement `useMugshotsQuery(filters: MugshotFilters, pagination: PaginationOptions)` function
  - Configure query key pattern: `['mugshots', filters, pagination]` for proper cache segregation
  - Set `keepPreviousData: true` for smooth pagination without loading states
  - Configure `staleTime: 5 * 60 * 1000` (5 minutes) for optimal caching balance
  - Add comprehensive error handling with user-friendly error messages
  - Include TypeScript interfaces matching existing `MugshotFilters` and `PaginationOptions` types
  - _Requirements: 3.1, 3.2, 8.1, 8.2_

- [ ] Add intelligent query parameters handling
  - Transform filter object into URLSearchParams matching existing `/api/mugshots` format
  - Handle `includeTotal: 'true'` parameter for pagination metadata
  - Filter out undefined/null values to prevent unnecessary cache keys
  - Maintain exact compatibility with existing filter parameters (state, county, searchTerm, etc.)
  - _Requirements: 2.1, 2.2, 10.1_

- [ ] Implement response transformation and validation
  - Parse API response format: `{ success: boolean, data: { mugshots: [], pagination: {} }, message?: string }`
  - Throw structured errors for failed responses to trigger Query error boundaries
  - Return transformed data matching existing component expectations
  - Add runtime validation for critical data properties
  - _Requirements: 7.1, 7.2, 10.2_

### 1.2 Mugshot Detail Query Hook Implementation

- [ ] Create `lib/hooks/queries/use-mugshot-detail-query.ts`
  - Implement `useMugshotDetailQuery(mugshotId: string, enabled?: boolean)` function
  - Configure query key: `['mugshot', mugshotId, 'detail']` for detail-specific caching
  - Set shorter `staleTime: 1 * 60 * 1000` (1 minute) for fresher popup data
  - Add `enabled` parameter for conditional fetching when popup opens
  - Include automatic refetching when mugshot detail components mount
  - _Requirements: 3.1, 3.2, 8.1_

- [ ] Configure detail-specific API integration
  - Call existing `/api/mugshots/[id]` endpoint with proper error handling
  - Parse response format including ratings statistics and tag counts
  - Handle mugshot not found scenarios with appropriate error states
  - Ensure response data matches existing `MugshotDetailData` interface
  - _Requirements: 2.1, 2.2, 7.1_

- [ ] Add prefetching capabilities for performance
  - Export `prefetchMugshotDetail(queryClient, mugshotId)` utility function
  - Configure prefetching on hover for mugshot cards
  - Set appropriate cache time for prefetched data
  - Ensure prefetching doesn't impact initial page load performance
  - _Requirements: 8.1, 8.2, 8.5_

### 1.3 User-Specific Data Query Hook Implementation

- [ ] Create `lib/hooks/queries/use-user-mugshot-data-query.ts`
  - Implement `useUserMugshotDataQuery(mugshotId: string, enabled?: boolean)` function
  - Configure query key: `['user', 'mugshot', mugshotId, 'data']` for user-specific caching
  - Set shorter `staleTime: 30 * 1000` (30 seconds) for frequently changing user data
  - Integrate with existing `useAuthStore()` for authentication state checking
  - Add conditional enabling based on authentication status
  - _Requirements: 3.1, 4.2, 7.2_

- [ ] Implement authentication-aware API calls
  - Call existing `/api/user/mugshot/[id]/data` endpoint with proper headers
  - Handle `UNAUTHENTICATED` error responses with login redirects
  - Maintain existing URL parameter handling for post-login redirects: `/login?redirect=${encodeURIComponent(currentUrl)}`
  - Parse user rating and tag data matching existing `UserMugshotData` interface
  - _Requirements: 2.3, 2.4, 7.2_

- [ ] Add smart cache invalidation patterns
  - Configure automatic invalidation when user authentication state changes
  - Set up cache clearing on logout to prevent data leaks
  - Ensure user data refetches when authentication status becomes true
  - Add cache key patterns for bulk invalidation of user-specific data
  - _Requirements: 3.4, 4.2_

### 1.4 Rating Mutation with Optimistic Updates

- [ ] Create `lib/hooks/mutations/use-rating-mutation.ts`
  - Implement `useRatingMutation(mugshotId: string)` function returning TanStack Query mutation
  - Configure mutation function to call existing `/api/ratings/submit` endpoint
  - Add immediate authentication checking with login redirect for unauthenticated clicks
  - Include comprehensive error handling for network failures and validation errors
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] Implement immediate login redirects for unauthenticated interactions
  - Check authentication state immediately when mutation is called
  - If unauthenticated, redirect immediately to `/login?redirect=${encodeURIComponent(currentUrl)}`
  - Prevent any API calls or optimistic updates for unauthenticated users
  - Maintain existing URL parameter handling for post-login redirects
  - _Requirements: 2.3, 2.4, 7.2_

- [ ] Implement sophisticated optimistic updates for authenticated users only
  - Cancel outgoing queries in `onMutate` to prevent race conditions: `await queryClient.cancelQueries(['mugshot', mugshotId])`
  - Snapshot previous data for rollback: `const previousData = queryClient.getQueryData(['mugshot', mugshotId])`
  - Update user rating immediately: `queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], ...)`
  - Calculate new average rating optimistically based on existing total and previous user rating
  - Update total rating count if this is user's first rating
  - _Requirements: 6.1, 6.2, 6.4, 6.5_

- [ ] Add comprehensive error handling and rollback
  - Implement `onError` callback to restore previous data snapshots
  - Show user-friendly error messages for different failure scenarios
  - Log detailed error information for debugging: network errors, validation errors, auth errors
  - Add `onSettled` callback to refetch data for server truth after optimistic updates
  - _Requirements: 6.3, 7.1, 7.2_

- [ ] Configure cache invalidation patterns
  - Invalidate related queries: `['mugshot', mugshotId]`, `['user', 'mugshot', mugshotId]`
  - Update mugshots listing cache if rating affects ranking/filtering
  - Add smart invalidation for tag statistics that may be affected by rating changes
  - _Requirements: 3.4, 8.3_

### 1.5 Tag Mutation with Optimistic Updates

- [ ] Create `lib/hooks/mutations/use-tag-mutation.ts`
  - Implement `useTagMutation(mugshotId: string)` function for tag toggle operations
  - Configure mutation to call existing `/api/tags/toggle` endpoint with `{ mugshotId, tagType }`
  - Add immediate authentication checking with login redirect for unauthenticated clicks
  - Include proper error handling for tag validation and network failures
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] Implement immediate login redirects for unauthenticated tag interactions
  - Check authentication state immediately when tag mutation is called
  - If unauthenticated, redirect immediately to `/login?redirect=${encodeURIComponent(currentUrl)}`
  - Prevent any API calls or optimistic updates for unauthenticated users
  - Maintain existing URL parameter handling for post-login redirects
  - _Requirements: 2.3, 2.4, 7.2_

- [ ] Implement tag-specific optimistic updates for authenticated users only
  - Toggle user tag state immediately in `onMutate`: add/remove tag from user's tag array
  - Update tag count optimistically: increment if adding tag, decrement if removing
  - Handle edge cases: prevent negative counts, manage multiple rapid toggles
  - Snapshot both user data and mugshot tag statistics for rollback
  - _Requirements: 6.1, 6.2, 6.4, 6.5_

- [ ] Add tag-specific error handling
  - Rollback both user tag state and count statistics on error
  - Handle tag validation errors (invalid tag types, rate limiting)
  - Provide specific error messages for tag-related failures
  - Ensure consistent state across all tag displays after error recovery
  - _Requirements: 6.3, 7.1, 7.2_

- [ ] Configure tag cache invalidation
  - Invalidate mugshot detail queries: `['mugshot', mugshotId, 'detail']`
  - Invalidate user-specific data: `['user', 'mugshot', mugshotId, 'data']`
  - Update global tag statistics cache if applicable
  - Add batch invalidation for multiple mugshots if tag affects rankings
  - _Requirements: 3.4, 8.3_

### 1.6 Query Key Management and TypeScript Integration

- [ ] Create `lib/query/query-keys.ts` for centralized key management
  - Define `queryKeys` object with factory functions for all query key patterns
  - Include functions: `mugshots()`, `mugshot(id)`, `userMugshotData(id)`, `ratings(id)`, `tags(id)`
  - Add TypeScript types for all query key combinations
  - Export helper functions for query invalidation patterns
  - _Requirements: 3.1, 3.2, 8.3_

- [ ] Add comprehensive TypeScript interfaces
  - Create `types/query-types.ts` for all Query-related type definitions
  - Define interfaces matching existing API response formats exactly
  - Add mutation payload types for ratings and tags
  - Include error response type definitions
  - _Requirements: 10.2, 10.3_

- [ ] Configure development tools integration
  - Add TanStack Query DevTools configuration for debugging
  - Set up proper query key logging for development
  - Configure cache persistence options for development workflow
  - Add performance monitoring hooks for query timing
  - _Requirements: 3.3_

### 1.7 Unauthenticated User Experience Integration

- [ ] Implement proper unauthenticated user state handling in query hooks
  - All query hooks must properly detect authentication state using `useAuthStore()`
  - User-specific queries should be disabled when `user` is null/undefined
  - Query keys should not include user-specific data when unauthenticated
  - Cache invalidation should handle auth state changes properly
  - _Requirements: 4.2, 9.1, 9.2_

- [ ] Add unauthenticated popover footer behavior
  - Footer should display "Login to rate" message when user is not authenticated
  - Footer should display current user rating when authenticated and user has rated
  - Footer should display "No rating yet" when authenticated but user hasn't rated
  - Footer styling should match existing design patterns with appropriate colors
  - _Requirements: 9.1, 9.2, 10.1_

- [ ] Implement immediate redirect behavior for unauthenticated interactions
  - Rating number clicks should trigger immediate redirect to `/login?redirect=${encodeURIComponent(currentUrl)}`
  - Tag button clicks should trigger immediate redirect to `/login?redirect=${encodeURIComponent(currentUrl)}`
  - No API calls or optimistic updates should occur for unauthenticated users
  - Redirect should preserve the exact current URL including any query parameters
  - _Requirements: 2.3, 2.4, 7.2, 9.1_

- [ ] Add proper disabled state styling for unauthenticated users
  - Rating number buttons should show `opacity-50` and `cursor-not-allowed` when unauthenticated
  - Tag buttons should show disabled styling with `opacity-50` and appropriate visual feedback
  - Hover effects should be disabled for unauthenticated users
  - Visual feedback should clearly indicate that login is required for interaction
  - _Requirements: 9.1, 9.2, 10.1_

- [ ] Configure authentication state reactivity in queries
  - Queries should automatically refetch when user authentication state changes from null to authenticated
  - Cache should be cleared for user-specific data when user logs out
  - Real-time subscriptions should be enabled/disabled based on authentication state
  - Query invalidation should handle auth state transitions properly
  - _Requirements: 3.4, 4.2, 5.4_

## Acceptance Criteria Verification

- [ ] All query hooks successfully fetch data from existing API endpoints without modification
- [ ] Optimistic mutations provide instant UI feedback with proper rollback on errors
- [ ] All TypeScript interfaces match existing component expectations exactly
- [ ] Authentication redirects work identically to current implementation
- [ ] Query caching provides measurable performance improvements over direct API calls
- [ ] Error handling provides user-friendly messages while logging detailed debugging information
- [ ] All hooks integrate seamlessly with existing Zustand auth store
- [ ] Cache invalidation patterns maintain data consistency across all components

## Files to Create/Modify

**New Files to Create:**
- `lib/hooks/queries/use-mugshots-query.ts` - Main mugshots listing query hook
- `lib/hooks/queries/use-mugshot-detail-query.ts` - Individual mugshot detail query hook
- `lib/hooks/queries/use-user-mugshot-data-query.ts` - User-specific rating/tag data hook
- `lib/hooks/mutations/use-rating-mutation.ts` - Rating submission with optimistic updates
- `lib/hooks/mutations/use-tag-mutation.ts` - Tag toggle with optimistic updates
- `lib/query/query-keys.ts` - Centralized query key management
- `types/query-types.ts` - TypeScript interfaces for Query system

**Existing Files to Modify:**
- None (this task is purely additive to maintain backward compatibility)

## Implementation Notes

**Cache Configuration Pattern:**
```typescript
// Standard caching configuration for different data types
const cacheConfig = {
  mugshots: { staleTime: 5 * 60 * 1000, cacheTime: 30 * 60 * 1000 },
  mugshotDetail: { staleTime: 1 * 60 * 1000, cacheTime: 10 * 60 * 1000 },
  userData: { staleTime: 30 * 1000, cacheTime: 5 * 60 * 1000 }
}
```

**Optimistic Update Pattern:**
```typescript
// Standard pattern for all mutations
onMutate: async (newData) => {
  await queryClient.cancelQueries(relatedQueries)
  const previousData = queryClient.getQueryData(queryKey)
  queryClient.setQueryData(queryKey, optimisticUpdate)
  return { previousData }
},
onError: (err, variables, context) => {
  queryClient.setQueryData(queryKey, context.previousData)
},
onSettled: () => {
  queryClient.invalidateQueries(relatedQueries)
}
```

**Authentication Integration:**
- All hooks must integrate with existing `useAuthStore()` patterns
- Login redirects must maintain current URL parameter handling
- User-specific queries must be conditionally enabled based on auth state

**Unauthenticated User Interaction Pattern:**
```typescript
// Immediate redirect for unauthenticated interactions
const handleRatingClick = async (rating: number) => {
  if (!user) {
    // Immediate redirect - no API calls or optimistic updates
    const currentUrl = window.location.href
    window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
    return
  }
  
  // Proceed with authenticated user flow
  ratingMutation.mutate(rating)
}

const handleTagClick = async (tagType: string) => {
  if (!user) {
    // Immediate redirect - no API calls or optimistic updates  
    const currentUrl = window.location.href
    window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`
    return
  }
  
  // Proceed with authenticated user flow
  tagMutation.mutate(tagType)
}
```

**Popover Footer State Pattern:**
```typescript
// Footer display logic for different authentication states
const getFooterContent = (user: User | null, userRating: number | null) => {
  if (!user) {
    return (
      <span className="text-blue-300 font-medium">
        Login to rate
      </span>
    )
  }
  
  if (userRating) {
    return (
      <span className="text-gray-400">
        Your current rating: <span className="text-green-400 font-medium">{userRating}/10</span>
      </span>
    )
  }
  
  return (
    <span className="text-gray-500">No rating yet</span>
  )
}
```

**Query Conditional Enabling Pattern:**
```typescript
// User-specific queries should be disabled for unauthenticated users
const { data, isLoading } = useUserMugshotDataQuery(
  mugshotId, 
  !!user // Only enabled when user exists
)

// Query key should not include user data when unauthenticated
const queryKey = user 
  ? ['user', 'mugshot', mugshotId, 'data']
  : ['mugshot', mugshotId, 'public'] // Different key for public data
```

## Testing Checklist

- [ ] All query hooks return data in formats matching existing component expectations
- [ ] Optimistic mutations update UI instantly and rollback properly on errors
- [ ] Authentication redirects work with proper URL parameter preservation
- [ ] Cache invalidation maintains consistency across all related data
- [ ] Error boundaries catch and handle query failures appropriately
- [ ] TypeScript compilation passes without errors for all new hooks
- [ ] Performance testing shows improved load times compared to direct API calls
- [ ] Memory usage remains stable during extended browsing sessions
- [ ] Unauthenticated users can view mugshot content and statistics without restrictions
- [ ] Rating number clicks redirect unauthenticated users to login with proper return URL
- [ ] Tag button clicks redirect unauthenticated users to login with proper return URL
- [ ] Popover footer displays "Login to rate" for unauthenticated users
- [ ] Popover footer displays user rating when authenticated and rated
- [ ] Popover footer displays "No rating yet" when authenticated but not rated
- [ ] Disabled styling (opacity-50, cursor-not-allowed) applied correctly for unauthenticated users
- [ ] No API calls or optimistic updates occur for unauthenticated user interactions
- [ ] User-specific queries are properly disabled when user is not authenticated
- [ ] Cache properly clears user data on logout and refetches on login
- [ ] All redirect URLs preserve query parameters and fragment identifiers

## Next Steps

This task establishes the foundation for:
- **Task 5 (Enhanced Loading States)** - Query hooks provide loading states for enhanced UI feedback
- **Task 6 (Realtime Integration)** - Query cache becomes target for realtime updates
- **Task 8 (Component Migration)** - Components can now migrate from direct API calls to Query hooks
- **Task 10 (Performance Optimization)** - Query hooks enable prefetching and advanced caching strategies

The hooks created in this task will be gradually adopted by components while maintaining full backward compatibility with existing Zustand patterns. 