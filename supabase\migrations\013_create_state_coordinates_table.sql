-- Migration 013: Create state_coordinates table required by available_states_with_coordinates view
-- This table provides latitude/longitude data for US state capitals

-- Create state_coordinates table
CREATE TABLE IF NOT EXISTS public.state_coordinates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    state_name TEXT NOT NULL UNIQUE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    capital_city TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.state_coordinates ENABLE ROW LEVEL SECURITY;

-- Allow public read access (non-sensitive geographic data)
CREATE POLICY "Allow public read access to state coordinates" ON public.state_coordinates
  FOR SELECT USING (true);

-- Create index for efficient lookups
CREATE INDEX IF NOT EXISTS idx_state_coordinates_name ON public.state_coordinates(state_name);

-- Insert US state capitals with their coordinates
INSERT INTO public.state_coordinates (state_name, latitude, longitude, capital_city) VALUES
('Alabama', 32.361538, -86.279118, 'Montgomery'),
('Alaska', 58.301935, -134.419740, 'Juneau'),
('Arizona', 33.448457, -112.073844, 'Phoenix'),
('Arkansas', 34.736009, -92.331122, 'Little Rock'),
('California', 38.576668, -121.493629, 'Sacramento'),
('Colorado', 39.739236, -104.990251, 'Denver'),
('Connecticut', 41.767, -72.677, 'Hartford'),
('Delaware', 39.161921, -75.526755, 'Dover'),
('Florida', 30.4518, -84.27277, 'Tallahassee'),
('Georgia', 33.76, -84.39, 'Atlanta'),
('Hawaii', 21.30895, -157.826182, 'Honolulu'),
('Idaho', 43.613739, -116.237651, 'Boise'),
('Illinois', 39.78325, -89.650373, 'Springfield'),
('Indiana', 39.790942, -86.147685, 'Indianapolis'),
('Iowa', 41.590939, -93.620866, 'Des Moines'),
('Kansas', 39.04, -95.69, 'Topeka'),
('Kentucky', 38.197274, -84.86311, 'Frankfort'),
('Louisiana', 30.45809, -91.140229, 'Baton Rouge'),
('Maine', 44.323535, -69.765261, 'Augusta'),
('Maryland', 38.972945, -76.501157, 'Annapolis'),
('Massachusetts', 42.2352, -71.0275, 'Boston'),
('Michigan', 42.354558, -84.955255, 'Lansing'),
('Minnesota', 44.95, -93.094, 'Saint Paul'),
('Mississippi', 32.320, -90.207, 'Jackson'),
('Missouri', 38.572954, -92.189283, 'Jefferson City'),
('Montana', 46.595805, -112.027031, 'Helena'),
('Nebraska', 40.809868, -96.675345, 'Lincoln'),
('Nevada', 39.161921, -119.767403, 'Carson City'),
('New Hampshire', 43.220093, -71.549896, 'Concord'),
('New Jersey', 40.221741, -74.756138, 'Trenton'),
('New Mexico', 35.667231, -105.964575, 'Santa Fe'),
('New York', 42.659829, -73.781339, 'Albany'),
('North Carolina', 35.771, -78.638, 'Raleigh'),
('North Dakota', 46.813343, -100.779004, 'Bismarck'),
('Ohio', 39.961176, -82.998794, 'Columbus'),
('Oklahoma', 35.482309, -97.534994, 'Oklahoma City'),
('Oregon', 44.931109, -123.029159, 'Salem'),
('Pennsylvania', 40.269789, -76.875613, 'Harrisburg'),
('Rhode Island', 41.82355, -71.422132, 'Providence'),
('South Carolina', 34.000, -81.035, 'Columbia'),
('South Dakota', 44.367966, -100.336378, 'Pierre'),
('Tennessee', 36.165, -86.784, 'Nashville'),
('Texas', 30.266667, -97.75, 'Austin'),
('Utah', 40.777477, -111.888237, 'Salt Lake City'),
('Vermont', 44.26639, -72.580536, 'Montpelier'),
('Virginia', 37.54, -77.46, 'Richmond'),
('Washington', 47.042418, -122.893077, 'Olympia'),
('West Virginia', 38.349497, -81.633294, 'Charleston'),
('Wisconsin', 43.074722, -89.384444, 'Madison'),
('Wyoming', 41.145548, -104.802042, 'Cheyenne');

-- Add comment for documentation
COMMENT ON TABLE public.state_coordinates IS 'Geographic coordinates for US state capitals, used for location-based distance calculations';
COMMENT ON COLUMN public.state_coordinates.state_name IS 'Full state name matching mugshots.stateOfBooking format';
COMMENT ON COLUMN public.state_coordinates.latitude IS 'Latitude coordinate of state capital';
COMMENT ON COLUMN public.state_coordinates.longitude IS 'Longitude coordinate of state capital';
COMMENT ON COLUMN public.state_coordinates.capital_city IS 'Name of state capital city';
