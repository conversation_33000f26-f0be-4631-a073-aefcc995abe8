import "jsr:@supabase/functions-js/edge-runtime.d.ts"

interface DailyWinnerResult {
  success: boolean
  message: string
  event_id?: string
  winners?: Array<{
    mugshot_id: string
    average_rating: number
    total_ratings: number
  }>
  execution_time_ms?: number
  error?: string
}

Deno.serve(async (req: Request) => {
  try {
    // CORS headers for preflight requests
    if (req.method === 'OPTIONS') {
      return new Response('ok', { 
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        }
      })
    }

    console.log('Daily Winner Selection Edge Function started')
    const startTime = Date.now()

    // Get Supabase client with service role key for admin operations
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase environment variables')
    }

    // Import Supabase client
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2')
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get target date from request or use yesterday as default
    let targetDate: string
    
    if (req.method === 'POST') {
      try {
        const body = await req.json()
        targetDate = body.targetDate || getYesterdayDate()
      } catch {
        targetDate = getYesterdayDate()
      }
    } else {
      targetDate = getYesterdayDate()
    }

    console.log(`Processing daily winner for date: ${targetDate}`)

    // Check if daily event already exists for this date
    const { data: existingEvent, error: eventCheckError } = await supabase
      .from('events')
      .select('id, status, winners')
      .eq('event_type', 'daily')
      .eq('start_date', targetDate)
      .maybeSingle()

    if (eventCheckError) {
      console.error('Error checking existing event:', eventCheckError)
      throw new Error(`Database error: ${eventCheckError.message}`)
    }

    if (existingEvent && existingEvent.status === 'completed') {
      return new Response(
        JSON.stringify({
          success: true,
          message: `Daily winner already selected for ${targetDate}`,
          event_id: existingEvent.id,
          winners: existingEvent.winners || [],
          execution_time_ms: Date.now() - startTime
        }),
        {
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          status: 200
        }
      )
    }

    // Create new daily event if it doesn't exist
    let eventId = existingEvent?.id
    
    if (!existingEvent) {
      console.log(`Creating new daily event for ${targetDate}`)
      
      // Get eligible mugshots (arrested on target date)
      const { data: eligibleMugshots, error: mugshotsError } = await supabase
        .from('mugshots')
        .select('id')
        .eq('arrest_date', targetDate)

      if (mugshotsError) {
        console.error('Error fetching eligible mugshots:', mugshotsError)
        throw new Error(`Database error: ${mugshotsError.message}`)
      }

      const participants = eligibleMugshots?.map(m => m.id) || []
      
      if (participants.length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            message: `No mugshots found for date ${targetDate}`,
            execution_time_ms: Date.now() - startTime
          }),
          {
            headers: { 
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
            },
            status: 200
          }
        )
      }

      // Create the daily event
      const { data: newEvent, error: createError } = await supabase
        .from('events')
        .insert({
          event_type: 'daily',
          start_date: targetDate,
          end_date: targetDate,
          participants: participants,
          status: 'active',
          config: { 
            duration_hours: 24,
            rating_enabled_start: `${targetDate}T00:00:00Z`,
            rating_enabled_end: `${getNextDay(targetDate)}T00:00:00Z`
          }
        })
        .select('id')
        .single()

      if (createError) {
        console.error('Error creating event:', createError)
        throw new Error(`Database error: ${createError.message}`)
      }

      eventId = newEvent.id
      console.log(`Created new daily event with ID: ${eventId}`)
    }

    // Now compute the daily winners using the database function
    console.log(`Computing daily winners for event ${eventId}`)
    
    const { error: computeError } = await supabase
      .rpc('compute_daily_winners', { p_event_id: eventId })

    if (computeError) {
      console.error('Error computing daily winners:', computeError)
      throw new Error(`Database error: ${computeError.message}`)
    }

    // Get the updated event with winners
    const { data: completedEvent, error: fetchError } = await supabase
      .from('events')
      .select('id, status, winners')
      .eq('id', eventId)
      .single()

    if (fetchError) {
      console.error('Error fetching completed event:', fetchError)
      throw new Error(`Database error: ${fetchError.message}`)
    }

    const result: DailyWinnerResult = {
      success: true,
      message: completedEvent.winners && completedEvent.winners.length > 0 
        ? `Successfully selected ${completedEvent.winners.length} daily winner(s) for ${targetDate}`
        : `No winners qualified for ${targetDate}`,
      event_id: completedEvent.id,
      winners: completedEvent.winners || [],
      execution_time_ms: Date.now() - startTime
    }

    // Send notifications if there are winners
    if (completedEvent.winners && completedEvent.winners.length > 0) {
      try {
        await sendWinnerNotifications(supabase, targetDate, completedEvent.winners)
        console.log('Winner notifications sent successfully')
      } catch (notificationError) {
        console.error('Failed to send notifications:', notificationError)
        // Don't fail the entire operation for notification errors
      }
    }

    console.log('Daily winner selection completed:', result)

    return new Response(
      JSON.stringify(result),
      {
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 200
      }
    )

  } catch (error) {
    console.error('Edge Function error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        message: 'Daily winner selection failed',
        error: error.message,
        execution_time_ms: Date.now() - (Date.now()),
        timestamp: new Date().toISOString()
      }),
      {
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        status: 500
      }
    )
  }
})

// Helper function to get yesterday's date in YYYY-MM-DD format
function getYesterdayDate(): string {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return yesterday.toISOString().split('T')[0]
}

// Helper function to get next day's date
function getNextDay(dateString: string): string {
  const date = new Date(dateString)
  date.setDate(date.getDate() + 1)
  return date.toISOString().split('T')[0]
}

// Helper function to send winner notifications
async function sendWinnerNotifications(
  supabase: any,
  targetDate: string,
  winnerIds: string[]
): Promise<void> {
  try {
    // Get winner mugshot details
    const { data: mugshots, error: mugshotsError } = await supabase
      .from('mugshots')
      .select(`
        id,
        "firstName",
        "lastName",
        "stateOfBooking",
        "countyOfBooking",
        "imagePath"
      `)
      .in('id', winnerIds)

    if (mugshotsError || !mugshots) {
      console.error('Failed to get mugshot details for notifications:', mugshotsError)
      return
    }

    // Prepare notification data
    const notificationData = {
      type: 'daily_winner_announcement',
      date: targetDate,
      winners: mugshots.map(mugshot => ({
        id: mugshot.id,
        name: `${mugshot.firstName} ${mugshot.lastName}`,
        location: `${mugshot.stateOfBooking}, ${mugshot.countyOfBooking}`,
        image_url: mugshot.imagePath
      }))
    }

    console.log('Winner notification data prepared:', notificationData)

    // Here you would integrate with your notification system
    // For now, we'll just log the notification data
    // In a full implementation, you might:
    // 1. Send emails to users who opted in for daily winner notifications
    // 2. Send push notifications to mobile app users
    // 3. Post to social media accounts
    // 4. Update any real-time dashboard subscribers

    console.log(`Notifications prepared for ${mugshots.length} daily winner(s)`)

  } catch (error) {
    console.error('Error in sendWinnerNotifications:', error)
    throw error
  }
} 