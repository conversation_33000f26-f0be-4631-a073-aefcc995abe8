-- Migration 005: Create Events System for Competitions and Voting
-- This migration implements the events, votes, and winners tables
-- Updated to work with production mugshots table (BIGSERIAL primary key)

-- =====================================================
-- EVENTS TABLE (CORRECTED FOR BIGSERIAL)
-- =====================================================

-- Events Table (for competitions) - corrected for BIGSERIAL mugshots
CREATE TABLE IF NOT EXISTS public.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type TEXT NOT NULL CHECK (event_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE,
    voting_start TIMESTAMP WITH TIME ZONE,
    voting_end TIMESTAMP WITH TIME ZONE,
    participants BIGINT[],  -- Array of eligible mugshot_ids (BIGSERIAL)
    winners BIGINT[],  -- Array of winning mugshot_ids (BIGSERIAL)
    config JSONB,  -- Dynamic rules, e.g., {"duration_hours": 24, "vote_limit": 1}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed'))
);

-- Index for quick queries
CREATE INDEX IF NOT EXISTS idx_events_type_start ON public.events (event_type, start_date);
CREATE INDEX IF NOT EXISTS idx_events_status ON public.events (status);
CREATE INDEX IF NOT EXISTS idx_events_voting_period ON public.events (voting_start, voting_end);

-- =====================================================
-- VOTES TABLE (CORRECTED FOR BIGSERIAL)
-- =====================================================

-- Votes Table (for weekly+ competitions) - corrected for BIGSERIAL mugshots
CREATE TABLE IF NOT EXISTS public.votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    mugshot_id BIGINT NOT NULL REFERENCES public.mugshots(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Unique index to prevent multiple votes per user per event
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_vote ON public.votes (event_id, user_id);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_votes_event_id ON public.votes(event_id);
CREATE INDEX IF NOT EXISTS idx_votes_mugshot_id ON public.votes(mugshot_id);
CREATE INDEX IF NOT EXISTS idx_votes_user_id ON public.votes(user_id);
CREATE INDEX IF NOT EXISTS idx_votes_created_at ON public.votes(created_at DESC);

-- =====================================================
-- WINNERS TABLE (CORRECTED FOR BIGSERIAL)
-- =====================================================

-- Winners Table (Optional for quick access) - corrected for BIGSERIAL mugshots
CREATE TABLE IF NOT EXISTS public.winners (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    mugshot_id BIGINT NOT NULL REFERENCES public.mugshots(id) ON DELETE CASCADE,
    rank INTEGER DEFAULT 1,
    score NUMERIC,
    awarded_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_winners_event_id ON public.winners(event_id);
CREATE INDEX IF NOT EXISTS idx_winners_mugshot_id ON public.winners(mugshot_id);
CREATE INDEX IF NOT EXISTS idx_winners_rank ON public.winners(rank);
CREATE INDEX IF NOT EXISTS idx_winners_awarded_at ON public.winners(awarded_at DESC);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.winners ENABLE ROW LEVEL SECURITY;

-- RLS Policies for events
CREATE POLICY "Allow public read access to events" ON public.events
  FOR SELECT USING (true);

-- Only admins can manage events
CREATE POLICY "Admins can manage events" ON public.events
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- RLS Policies for votes
CREATE POLICY "Allow public read access to votes" ON public.votes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage own votes" ON public.votes
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for winners
CREATE POLICY "Allow public read access to winners" ON public.winners
  FOR SELECT USING (true);

-- Only admins can manage winners
CREATE POLICY "Admins can manage winners" ON public.winners
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- =====================================================
-- VOTING FUNCTIONS
-- =====================================================

-- Function to Validate and Insert Vote (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.insert_vote(p_event_id UUID, p_mugshot_id BIGINT, p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    v_start TIMESTAMP WITH TIME ZONE;
    v_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Fetch voting window
    SELECT voting_start, voting_end INTO v_start, v_end FROM public.events WHERE id = p_event_id;
    
    IF v_start IS NULL OR now() < v_start OR now() > v_end THEN
        RAISE EXCEPTION 'Voting not open for this event';
    END IF;
    
    -- Check if mugshot is a participant
    IF NOT EXISTS (SELECT 1 FROM public.events WHERE id = p_event_id AND p_mugshot_id = ANY(participants)) THEN
        RAISE EXCEPTION 'Mugshot not eligible for this event';
    END IF;
    
    -- Insert if no duplicate (unique index enforces)
    INSERT INTO public.votes (event_id, mugshot_id, user_id)
    VALUES (p_event_id, p_mugshot_id, p_user_id)
    ON CONFLICT DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- WINNER COMPUTATION FUNCTIONS
-- =====================================================

-- Function to Compute Daily Winners (Based on Average Ratings from First Day) - corrected for actual schema
CREATE OR REPLACE FUNCTION public.compute_daily_winners(p_event_id UUID)
RETURNS VOID AS $$
DECLARE
    v_start_date DATE;
    v_enabled_start TIMESTAMP WITH TIME ZONE;
    v_enabled_end TIMESTAMP WITH TIME ZONE;
BEGIN
    SELECT start_date INTO v_start_date FROM public.events WHERE id = p_event_id;
    
    -- Ratings from midnight day after to next midnight (first day only)
    v_enabled_start := (v_start_date + INTERVAL '1 day')::DATE + INTERVAL '0 hours';
    v_enabled_end := v_enabled_start + INTERVAL '1 day';
    
    -- Update winners: Mugshots with max average rating (using actual column name "dateOfBooking")
    UPDATE public.events
    SET winners = (
        SELECT ARRAY_AGG(mugshot_id)
        FROM (
            SELECT r.mugshot_id, AVG(r.rating) AS avg_rating
            FROM public.ratings r
            JOIN public.mugshots m ON r.mugshot_id = m.id
            WHERE m."dateOfBooking" = v_start_date
              AND r.created_at >= v_enabled_start
              AND r.created_at < v_enabled_end
            GROUP BY r.mugshot_id
            HAVING AVG(r.rating) = (
                SELECT MAX(sub.avg) FROM (
                    SELECT AVG(r2.rating) AS avg
                    FROM public.ratings r2 JOIN public.mugshots m2 ON r2.mugshot_id = m2.id
                    WHERE m2."dateOfBooking" = v_start_date
                      AND r2.created_at >= v_enabled_start
                      AND r2.created_at < v_enabled_end
                    GROUP BY r2.mugshot_id
                ) sub
            )
        ) AS winners
    )
    WHERE id = p_event_id;
    
    -- Set status to completed
    UPDATE public.events SET status = 'completed' WHERE id = p_event_id;
END;
$$ LANGUAGE plpgsql;

-- Function to Compute Winners for Voting-Based Events (Weekly+)
CREATE OR REPLACE FUNCTION public.compute_voting_winners(p_event_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Update winners: Mugshots with max vote count
    UPDATE public.events
    SET winners = (
        SELECT ARRAY_AGG(mugshot_id)
        FROM (
            SELECT mugshot_id, COUNT(*) AS vote_count
            FROM public.votes
            WHERE event_id = p_event_id
            GROUP BY mugshot_id
            HAVING COUNT(*) = (
                SELECT MAX(sub.cnt) FROM (
                    SELECT COUNT(*) AS cnt FROM public.votes WHERE event_id = p_event_id GROUP BY mugshot_id
                ) sub
            )
        ) AS winners
    )
    WHERE id = p_event_id;
    
    -- Set status to completed
    UPDATE public.events SET status = 'completed' WHERE id = p_event_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger to Auto-Populate Winners Table After Event Completion
CREATE OR REPLACE FUNCTION public.populate_winners()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        INSERT INTO public.winners (event_id, mugshot_id, score)
        SELECT NEW.id, unnest(NEW.winners), NULL;  -- Score can be calculated if needed
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_populate_winners
AFTER UPDATE ON public.events
FOR EACH ROW EXECUTE FUNCTION public.populate_winners();

-- =====================================================
-- PERMISSIONS
-- =====================================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.events TO anon, authenticated;
GRANT SELECT ON public.votes TO anon, authenticated;
GRANT SELECT ON public.winners TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.votes TO authenticated;

-- Grant execute permissions on functions (updated for BIGSERIAL)
GRANT EXECUTE ON FUNCTION public.insert_vote(UUID, BIGINT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.compute_daily_winners(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.compute_voting_winners(UUID) TO authenticated; 