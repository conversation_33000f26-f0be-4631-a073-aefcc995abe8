# Test 6: 500 Concurrent Users - Breaking Point Test
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 50
      name: "Warm-up: 50 users/sec"
    - duration: 420
      arrivalRate: 250
      name: "Breaking Point: 250 users/sec (500 concurrent)"
    - duration: 60
      arrivalRate: 50
      name: "Cool-down: 50 users/sec"
  
  ensure:
    - http.response_time.p95: 8000   # 95% under 8 seconds
    - http.response_time.median: 2500 # Median under 2.5 seconds
    - http.codes.200: 40             # 40% success rate (expect major failures)
    - http.codes.5xx: 30             # Less than 30% server errors

  http:
    timeout: 60
    pool: 600
    
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true

scenarios:
  - name: "500 Users - Mugshots API"
    weight: 55
    flow:
      - function: "generateRandomFilters"
      - get:
          url: "/api/mugshots"
          qs:
            page: "{{ page }}"
            perPage: "{{ perPage }}"
            state: "{{ state }}"
            county: "{{ county }}"
            search: "{{ searchTerm }}"
            tags: "{{ tags }}"
            sortBy: "{{ sortBy }}"
            dateFrom: "{{ dateFrom }}"
            dateTo: "{{ dateTo }}"
          name: "GET /api/mugshots - 500 users"
      - think: 0.3

  - name: "500 Users - Details API"
    weight: 45
    flow:
      - function: "generateRandomMugshotId"
      - get:
          url: "/api/mugshots/{{ mugshotId }}"
          name: "GET /api/mugshots/[id] - 500 users"
      - think: 0.5

processor: "../scenarios/data-generators.js"
