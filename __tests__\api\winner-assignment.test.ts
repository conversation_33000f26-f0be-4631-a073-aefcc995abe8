import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createMocks } from 'node-mocks-http'
import { NextRequest } from 'next/server'
import { POST as assignWinner, GET as getAssignments } from '@/app/api/admin/winners/assign/route'
import { POST as reverseWinner } from '@/app/api/admin/winners/reverse/route'

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: vi.fn()
  },
  from: vi.fn(),
  rpc: vi.fn()
}

vi.mock('@/lib/supabase/server', () => ({
  createClient: () => mockSupabase
}))

describe('Winner Assignment API', () => {
  const mockAdmin = {
    id: 'admin-123',
    email: '<EMAIL>'
  }

  const mockProfile = {
    role: 'admin'
  }

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>'
  }

  const mockUserProfile = {
    role: 'user'
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Default admin setup
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockAdmin }
    })
    
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: mockProfile
      }),
      insert: vi.fn().mockResolvedValue({ data: null, error: null }),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue({ data: [], error: null })
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('POST /api/admin/winners/assign', () => {
    it('should require authentication', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null }
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Authentication required')
    })

    it('should require admin role', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser }
      })
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockUserProfile
        })
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('Admin access required')
    })

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily'
          // Missing required fields
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Competition type, mugshot ID, and reason are required')
    })

    it('should validate assignment type', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment',
          assignmentType: 'invalid_type'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid assignment type')
    })

    it('should require date for daily assignments', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
          // Missing date
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Date is required for daily winner assignment')
    })

    it('should assign daily winner successfully', async () => {
      const mockRpcResponse = {
        success: true,
        assignment_id: 'assignment-123',
        new_winner: 'mugshot-123'
      }

      mockSupabase.rpc.mockResolvedValue({
        data: mockRpcResponse,
        error: null
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment',
          assignmentType: 'manual_selection'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.assignment).toEqual(mockRpcResponse)
      expect(data.message).toBe('daily winner assigned successfully')

      expect(mockSupabase.rpc).toHaveBeenCalledWith('assign_manual_daily_winner', {
        p_date: '2024-01-15',
        p_mugshot_id: 'mugshot-123',
        p_admin_id: 'admin-123',
        p_reason: 'Test assignment',
        p_assignment_type: 'manual_selection'
      })
    })

    it('should require competition ID for weekly assignments', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'weekly',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
          // Missing competitionId
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Competition ID is required for weekly winner assignment')
    })

    it('should assign weekly winner successfully', async () => {
      const mockRpcResponse = {
        success: true,
        assignment_id: 'assignment-123',
        competition_id: 'comp-123',
        new_winner: 'mugshot-123'
      }

      mockSupabase.rpc.mockResolvedValue({
        data: mockRpcResponse,
        error: null
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'weekly',
          competitionId: 'comp-123',
          mugshotId: 'mugshot-123',
          reason: 'Test weekly assignment',
          assignmentType: 'override'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.assignment).toEqual(mockRpcResponse)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('assign_manual_weekly_winner', {
        p_competition_id: 'comp-123',
        p_candidate_id: 'mugshot-123',
        p_admin_id: 'admin-123',
        p_reason: 'Test weekly assignment',
        p_assignment_type: 'override'
      })
    })

    it('should handle unsupported competition types', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'monthly',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(501)
      expect(data.error).toBe('monthly winner assignment not yet implemented')
    })

    it('should handle database RPC errors', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: new Error('Database error')
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to assign daily winner')
    })

    it('should handle unsuccessful RPC responses', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: { success: false, error: 'Custom error message' },
        error: null
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
        })
      })

      const response = await assignWinner(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Custom error message')
    })

    it('should log moderation action', async () => {
      const mockInsert = vi.fn().mockResolvedValue({ data: null, error: null })
      
      mockSupabase.rpc.mockResolvedValue({
        data: { success: true, assignment_id: 'assignment-123' },
        error: null
      })

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'moderation_actions') {
          return { insert: mockInsert }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign', {
        method: 'POST',
        body: JSON.stringify({
          competitionType: 'daily',
          date: '2024-01-15',
          mugshotId: 'mugshot-123',
          reason: 'Test assignment'
        })
      })

      await assignWinner(request)

      expect(mockInsert).toHaveBeenCalledWith({
        admin_id: 'admin-123',
        action_type: 'manual_winner_assignment',
        target_type: 'daily',
        target_id: '2024-01-15',
        reason: 'Test assignment',
        new_state: {
          mugshot_id: 'mugshot-123',
          assignment_type: 'manual_selection',
          assignment_id: 'assignment-123'
        }
      })
    })
  })

  describe('GET /api/admin/winners/assign', () => {
    const mockAssignments = [
      {
        id: 'assignment-1',
        competition_type: 'daily',
        assignment_reason: 'Test assignment',
        admin: { first_name: 'John', last_name: 'Admin' },
        new_winner: { first_name: 'Jane', last_name: 'Winner' }
      }
    ]

    it('should require authentication', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null }
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign?competitionType=daily')

      const response = await getAssignments(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Authentication required')
    })

    it('should require admin role', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser }
      })
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockUserProfile
        })
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign?competitionType=daily')

      const response = await getAssignments(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('Admin access required')
    })

    it('should require competition type parameter', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/assign')

      const response = await getAssignments(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Competition type is required')
    })

    it('should fetch assignment history successfully', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: mockAssignments,
          error: null
        })
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign?competitionType=daily&limit=10')

      const response = await getAssignments(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.assignments).toEqual(mockAssignments)
    })

    it('should handle database errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProfile }),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: null,
          error: new Error('Database error')
        })
      })

      const request = new NextRequest('http://localhost/api/admin/winners/assign?competitionType=daily')

      const response = await getAssignments(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to fetch assignment history')
    })
  })

  describe('POST /api/admin/winners/reverse', () => {
    it('should require authentication', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null }
      })

      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'assignment-123',
          reason: 'Test reversal'
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Authentication required')
    })

    it('should require admin role', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser }
      })
      
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockUserProfile
        })
      })

      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'assignment-123',
          reason: 'Test reversal'
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('Admin access required')
    })

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'assignment-123'
          // Missing reason
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Assignment ID and reason are required')
    })

    it('should check if assignment exists and is reversible', async () => {
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'manual_winner_assignments') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: null,
              error: new Error('Not found')
            })
          }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'nonexistent-assignment',
          reason: 'Test reversal'
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Assignment not found or already reversed')
    })

    it('should reverse assignment successfully', async () => {
      const mockExistingAssignment = {
        id: 'assignment-123',
        competition_type: 'daily',
        is_reversed: false
      }

      const mockRpcResponse = {
        success: true,
        assignment_id: 'assignment-123',
        reversal_reason: 'Test reversal'
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'manual_winner_assignments') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: mockExistingAssignment,
              error: null
            })
          }
        }
        if (table === 'moderation_actions') {
          return {
            insert: vi.fn().mockResolvedValue({ data: null, error: null })
          }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      mockSupabase.rpc.mockResolvedValue({
        data: mockRpcResponse,
        error: null
      })

      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'assignment-123',
          reason: 'Test reversal'
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.reversal).toEqual(mockRpcResponse)
      expect(data.message).toBe('Assignment reversed successfully')

      expect(mockSupabase.rpc).toHaveBeenCalledWith('reverse_winner_assignment', {
        p_assignment_id: 'assignment-123',
        p_admin_id: 'admin-123',
        p_reversal_reason: 'Test reversal'
      })
    })

    it('should handle RPC errors', async () => {
      const mockExistingAssignment = {
        id: 'assignment-123',
        is_reversed: false
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'manual_winner_assignments') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: mockExistingAssignment,
              error: null
            })
          }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: new Error('RPC error')
      })

      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'assignment-123',
          reason: 'Test reversal'
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to reverse assignment')
    })

    it('should handle unsuccessful RPC responses', async () => {
      const mockExistingAssignment = {
        id: 'assignment-123',
        is_reversed: false
      }

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'profiles') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({ data: mockProfile })
          }
        }
        if (table === 'manual_winner_assignments') {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: mockExistingAssignment,
              error: null
            })
          }
        }
        return {
          select: vi.fn().mockReturnThis(),
          eq: vi.fn().mockReturnThis(),
          single: vi.fn().mockResolvedValue({ data: mockProfile })
        }
      })

      mockSupabase.rpc.mockResolvedValue({
        data: { success: false, error: 'Custom reversal error' },
        error: null
      })

      const request = new NextRequest('http://localhost/api/admin/winners/reverse', {
        method: 'POST',
        body: JSON.stringify({
          assignmentId: 'assignment-123',
          reason: 'Test reversal'
        })
      })

      const response = await reverseWinner(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Custom reversal error')
    })
  })
}) 