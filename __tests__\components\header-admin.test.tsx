import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import Header from '@/components/header'
import { createClient } from '@/lib/supabase/client'

// Mock the Supabase client
vi.mock('@/lib/supabase/client')
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn()
  })
}))

const mockCreateClient = vi.mocked(createClient)

describe('Header Admin Link Visibility', () => {
  let mockSupabase: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockSupabase = {
      auth: {
        getUser: vi.fn(),
        onAuthStateChange: vi.fn(() => ({
          data: { subscription: null },
          unsubscribe: vi.fn()
        }))
      },
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn()
    }
    
    mockCreateClient.mockReturnValue(mockSupabase)
  })

  it('should show admin panel link for admin users', async () => {
    // Mock admin user and profile
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'admin-123', email: '<EMAIL>' } }
    })

    mockSupabase.single.mockResolvedValue({
      data: { 
        id: 'profile-123',
        user_id: 'admin-123',
        role: 'admin',
        full_name: 'Admin User'
      }
    })

    render(<Header />)

    // Wait for auth state to load and check for admin link
    // The exact implementation depends on how your header handles async auth state
    // You may need to wait for the component to update
    await vi.waitFor(() => {
      const adminLink = screen.queryByText('Admin Panel')
      expect(adminLink).toBeInTheDocument()
    }, { timeout: 2000 })
  })

  it('should not show admin panel link for regular users', async () => {
    // Mock regular user and profile
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'user-456', email: '<EMAIL>' } }
    })

    mockSupabase.single.mockResolvedValue({
      data: { 
        id: 'profile-456',
        user_id: 'user-456',
        role: 'user',
        full_name: 'Regular User'
      }
    })

    render(<Header />)

    // Wait for auth state to load
    await vi.waitFor(() => {
      const adminLink = screen.queryByText('Admin Panel')
      expect(adminLink).not.toBeInTheDocument()
    }, { timeout: 2000 })
  })

  it('should not show admin panel link for unauthenticated users', async () => {
    // Mock no user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null }
    })

    render(<Header />)

    // Admin link should not be visible
    const adminLink = screen.queryByText('Admin Panel')
    expect(adminLink).not.toBeInTheDocument()
  })

  it('should handle profile fetch errors gracefully', async () => {
    // Mock user but profile fetch error
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'user-error', email: '<EMAIL>' } }
    })

    mockSupabase.single.mockRejectedValue(new Error('Profile fetch failed'))

    render(<Header />)

    // Should not show admin link on error
    await vi.waitFor(() => {
      const adminLink = screen.queryByText('Admin Panel')
      expect(adminLink).not.toBeInTheDocument()
    }, { timeout: 2000 })
  })

  it('should show correct href for admin panel link', async () => {
    // Mock admin user
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'admin-789' } }
    })

    mockSupabase.single.mockResolvedValue({
      data: { 
        id: 'profile-789',
        user_id: 'admin-789',
        role: 'admin',
        full_name: 'Admin User'
      }
    })

    render(<Header />)

    // Check that admin link has correct href
    await vi.waitFor(() => {
      const adminLink = screen.getByRole('link', { name: /admin panel/i })
      expect(adminLink).toHaveAttribute('href', '/admin')
    }, { timeout: 2000 })
  })
}) 