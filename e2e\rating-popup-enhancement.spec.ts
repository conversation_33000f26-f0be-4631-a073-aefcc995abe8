import { test, expect } from '@playwright/test'

test.describe('Enhanced Rating Popup UI (Story 3.3)', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a mugshot detail page
    await page.goto('/mugshots/1')
  })

  test('should display enhanced rating popover with grid layout', async ({ page }) => {
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Find and click the Rate button
    const rateButton = page.getByRole('button', { name: /rate/i })
    await expect(rateButton).toBeVisible()
    await rateButton.click()

    // Check that popover opens with grid
    const popoverContent = page.getByTestId('rating-popover-content')
    await expect(popoverContent).toBeVisible()

    // Verify numbers 1-10 are present in grid layout
    for (let i = 1; i <= 10; i++) {
      const numberButton = page.getByRole('button', { name: i.toString() })
      await expect(numberButton).toBeVisible()
    }

    // Verify tag buttons are present
    await expect(page.getByRole('button', { name: /😂.*funny/i })).toBeVisible()
    await expect(page.getByRole('button', { name: /🤪.*wild/i })).toBeVisible()
    await expect(page.getByRole('button', { name: /😱.*scary/i })).toBeVisible()
  })

  test('should show gradient effects on hover', async ({ page }) => {
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Open rating popover
    const rateButton = page.getByRole('button', { name: /rate/i })
    await rateButton.click()

    // Hover over a number button and check for gradient
    const numberButton = page.getByRole('button', { name: '7' })
    await numberButton.hover()
    
    // Check that gradient classes are applied
    await expect(numberButton).toHaveClass(/bg-gradient-to-r/)
  })

  test('should handle unauthenticated user experience', async ({ page }) => {
    // Ensure user is not logged in (clear any existing auth)
    await page.context().clearCookies()
    
    // Navigate to mugshot page
    await page.goto('/mugshots/1')
    await page.waitForLoadState('networkidle')
    
    // Open rating popover
    const rateButton = page.getByRole('button', { name: /rate/i })
    await rateButton.click()

    // Check that login message is shown
    await expect(page.getByText(/login required to rate/i)).toBeVisible()

    // Check that number buttons are disabled
    const numberButton = page.getByRole('button', { name: '5' })
    await expect(numberButton).toBeDisabled()
  })

  test('should maintain responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate and wait for load
    await page.goto('/mugshots/1')
    await page.waitForLoadState('networkidle')
    
    // Open rating popover
    const rateButton = page.getByRole('button', { name: /rate/i })
    await rateButton.click()

    // Check that popover is responsive
    const popoverContent = page.getByTestId('rating-popover-content')
    await expect(popoverContent).toBeVisible()
    
    // Verify grid layout is still functional on mobile
    const numberButton = page.getByRole('button', { name: '3' })
    await expect(numberButton).toBeVisible()
  })

  test('should close popover after rating submission', async ({ page }) => {
    // Mock authentication (this would need proper auth setup in a real test)
    // For now, we'll just test the UI behavior
    
    await page.waitForLoadState('networkidle')
    
    // Open rating popover
    const rateButton = page.getByRole('button', { name: /rate/i })
    await rateButton.click()

    // Verify popover is open
    const popoverContent = page.getByTestId('rating-popover-content')
    await expect(popoverContent).toBeVisible()

    // Click a rating number (this might show auth error, but we're testing UI behavior)
    const numberButton = page.getByRole('button', { name: '8' })
    await numberButton.click()

    // The popover should close regardless of auth state
    // (In unauthenticated state, it shows error but still closes)
    await expect(popoverContent).not.toBeVisible()
  })
}) 