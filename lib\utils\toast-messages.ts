import { toast } from 'sonner'
import { <PERSON>, X, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fo, <PERSON> } from 'lucide-react'
import { createElement } from 'react'
import type { TagType } from '@/lib/constants'

// Toast message utilities for consistent UX
export const toastMessages = {
  // Rating-related toasts with icons
  rating: {
    saving: (rating: number) => 
      toast.loading(`Saving rating ${rating}/10...`, {
        icon: createElement(Star, { className: 'h-4 w-4 animate-pulse' }),
        duration: 10000 // 10 second max for loading states
      }),
    
    saved: (rating: number) => 
      toast.success(`Rating saved! (${rating}/10)`, {
        icon: createElement(Check, { className: 'h-4 w-4' })
      }),
    
    updated: (oldRating: number, newRating: number) => 
      toast.success(`Rating updated from ${oldRating}/10 to ${newRating}/10`, {
        icon: createElement(Check, { className: 'h-4 w-4' })
      }),
    
    failed: () => 
      toast.error('Failed to save rating. Try again.', {
        icon: createElement(X, { className: 'h-4 w-4' })
      }),
    
    loginRequired: () => 
      toast.error('Please log in to rate mugshots', {
        icon: createElement(AlertTriangle, { className: 'h-4 w-4' })
      })
  },

  // Tag-related toasts with emojis (as requested)
  tag: {
    adding: (tagType: TagType) => {
      const emoji = tagType === 'funny' ? '😂' : tagType === 'wild' ? '🤪' : '😱'
      return toast.loading(`Adding ${tagType} tag...`, {
        icon: emoji,
        duration: 10000 // 10 second max for loading states
      })
    },
    
    added: (tagType: TagType) => {
      const emoji = tagType === 'funny' ? '😂' : tagType === 'wild' ? '🤪' : '😱'
      const label = tagType.charAt(0).toUpperCase() + tagType.slice(1)
      return toast.success(`Tagged as ${label}! ${emoji}`, {
        icon: emoji
      })
    },
    
    removing: (tagType: TagType) => {
      const emoji = tagType === 'funny' ? '😂' : tagType === 'wild' ? '🤪' : '😱'
      return toast.loading(`Removing ${tagType} tag...`, {
        icon: emoji,
        duration: 10000 // 10 second max for loading states
      })
    },
    
    removed: (tagType: TagType) => {
      const label = tagType.charAt(0).toUpperCase() + tagType.slice(1)
      return toast.success(`${label} tag removed`, {
        icon: createElement(Check, { className: 'h-4 w-4' })
      })
    },
    
    failed: (action: 'add' | 'remove', tagType: TagType) => 
      toast.error(`Failed to ${action} ${tagType} tag. Try again.`, {
        icon: createElement(X, { className: 'h-4 w-4' })
      }),
    
    loginRequired: () => 
      toast.error('Please log in to add tags', {
        icon: createElement(AlertTriangle, { className: 'h-4 w-4' })
      })
  },

  // General data loading toasts with icons
  data: {
    loading: (type: 'ratings' | 'tags' | 'mugshot data') => 
      toast.loading(`Loading ${type}...`, {
        icon: createElement(Info, { className: 'h-4 w-4 animate-pulse' }),
        duration: 10000 // 10 second max for loading states
      }),
    
    loaded: (type: 'ratings' | 'tags' | 'mugshot data') => 
      toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} loaded`, {
        icon: createElement(Check, { className: 'h-4 w-4' })
      }),
    
    failed: (type: 'ratings' | 'tags' | 'mugshot data') => 
      toast.error(`Failed to load ${type}. Try again.`, {
        icon: createElement(X, { className: 'h-4 w-4' })
      })
  },

  // Utility function to dismiss specific toasts
  dismiss: (toastId: string | number) => toast.dismiss(toastId),
  
  // Utility function to dismiss all toasts
  dismissAll: () => toast.dismiss(),
  
  // Utility function to dismiss all loading toasts specifically
  dismissAllLoading: () => {
    // This will dismiss all current toasts - Sonner doesn't have type-specific dismiss
    // We use this sparingly when we need to clear stuck loading toasts
    toast.dismiss()
  }
}

// Export individual categories for easier imports
export const ratingToasts = toastMessages.rating
export const tagToasts = toastMessages.tag
export const dataToasts = toastMessages.data 