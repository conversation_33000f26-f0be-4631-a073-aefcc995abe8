import { renderHook, act, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useRatingMutation } from '@/lib/hooks/mutations/use-rating-mutation'
import { useTagMutation } from '@/lib/hooks/mutations/use-tag-mutation'
import { useMugshotsQuery } from '@/lib/hooks/queries/use-mugshots-query'
import { useMugshotDetailQuery } from '@/lib/hooks/queries/use-mugshot-detail-query'
import { useAuthStore } from '@/lib/stores/auth-store'
import { ReactNode } from 'react'

// Mock auth store
jest.mock('@/lib/stores/auth-store', () => ({
  useAuthStore: jest.fn()
}))

// Mock fetch
global.fetch = jest.fn()

const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

// Test wrapper with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('Two-Cache Update Strategy', () => {
  const mugshotId = '123'
  const testMugshot = {
    id: 123,
    firstName: 'John',
    lastName: 'Doe',
    average_rating: 7.5,
    total_ratings: 10,
    tag_counts: { wild: 5, funny: 3, spooky: 2 }
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock authenticated user
    ;(useAuthStore as jest.Mock).mockReturnValue({
      isAuthenticated: true,
      user: { id: 'user123', email: '<EMAIL>' }
    })
    
    // Mock successful API responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, data: {} })
    } as Response)
  })

  describe('Rating Mutation Two-Cache Update', () => {
    it('should update both detail and grid caches optimistically', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Pre-populate caches with test data
      queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], {
        averageRating: 7.5,
        totalRatings: 10
      })
      
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], {
        userRating: null
      })
      
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], {
        mugshots: [testMugshot, { id: 456, average_rating: 6.0, total_ratings: 5 }],
        pagination: { totalCount: 2 }
      })
      
      // Render the rating mutation hook
      const { result: ratingResult } = renderHook(
        () => useRatingMutation(mugshotId),
        { wrapper }
      )
      
      // Execute the rating mutation
      await act(async () => {
        ratingResult.current.mutate(9)
      })
      
      // Verify detail cache was updated
      const updatedRatingStats = queryClient.getQueryData(['mugshot', mugshotId, 'rating-statistics'])
      expect(updatedRatingStats).toEqual({
        averageRating: 7.64, // (7.5*10 + 9) / 11 = 84/11 ≈ 7.64
        totalRatings: 11
      })
      
      const updatedUserRating = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'rating'])
      expect(updatedUserRating).toEqual({
        userRating: 9
      })
      
      // ✅ THE MAGIC: Verify grid cache was also updated
      const updatedGridData = queryClient.getQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }])
      expect(updatedGridData).toEqual({
        mugshots: [
          {
            ...testMugshot,
            average_rating: 7.64,
            total_ratings: 11
          },
          { id: 456, average_rating: 6.0, total_ratings: 5 } // Unchanged
        ],
        pagination: { totalCount: 2 }
      })
    })

    it('should rollback both caches on error', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Mock API error
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ success: false, message: 'Server error' })
      } as Response)
      
      // Pre-populate caches
      const originalRatingStats = { averageRating: 7.5, totalRatings: 10 }
      const originalUserRating = { userRating: null }
      const originalGridData = {
        mugshots: [testMugshot],
        pagination: { totalCount: 1 }
      }
      
      queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], originalRatingStats)
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], originalUserRating)
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], originalGridData)
      
      const { result: ratingResult } = renderHook(
        () => useRatingMutation(mugshotId),
        { wrapper }
      )
      
      // Execute mutation that will fail
      await act(async () => {
        ratingResult.current.mutate(9)
      })
      
      await waitFor(() => {
        expect(ratingResult.current.isError).toBe(true)
      })
      
      // ✅ Verify both caches were rolled back
      expect(queryClient.getQueryData(['mugshot', mugshotId, 'rating-statistics']))
        .toEqual(originalRatingStats)
      expect(queryClient.getQueryData(['user', 'mugshot', mugshotId, 'rating']))
        .toEqual(originalUserRating)
      expect(queryClient.getQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }]))
        .toEqual(originalGridData)
    })
  })

  describe('Tag Mutation Two-Cache Update', () => {
    it('should update both detail and grid caches optimistically', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Pre-populate caches with test data
      queryClient.setQueryData(['mugshot', mugshotId, 'detail'], {
        id: 123,
        tags: { tagCounts: { wild: 5, funny: 3, spooky: 2 } }
      })
      
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], {
        userTags: [] // User hasn't tagged yet
      })
      
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], {
        mugshots: [testMugshot, { id: 456, tag_counts: { wild: 2, funny: 1, spooky: 0 } }],
        pagination: { totalCount: 2 }
      })
      
      // Render the tag mutation hook
      const { result: tagResult } = renderHook(
        () => useTagMutation(mugshotId),
        { wrapper }
      )
      
      // Execute the tag mutation (add 'wild' tag)
      await act(async () => {
        tagResult.current.mutate('wild')
      })
      
      // Verify detail cache was updated
      const updatedMugshotDetail = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
      expect(updatedMugshotDetail).toEqual({
        id: 123,
        tags: { tagCounts: { wild: 6, funny: 3, spooky: 2 } } // wild count increased
      })
      
      const updatedUserData = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
      expect(updatedUserData).toEqual({
        userTags: ['wild'] // User now has wild tag
      })
      
      // ✅ THE MAGIC: Verify grid cache was also updated
      const updatedGridData = queryClient.getQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }])
      expect(updatedGridData).toEqual({
        mugshots: [
          {
            ...testMugshot,
            tag_counts: { wild: 6, funny: 3, spooky: 2 } // Updated in grid too!
          },
          { id: 456, tag_counts: { wild: 2, funny: 1, spooky: 0 } } // Unchanged
        ],
        pagination: { totalCount: 2 }
      })
    })

    it('should handle tag removal optimistically', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Pre-populate caches - user already has 'funny' tag
      queryClient.setQueryData(['mugshot', mugshotId, 'detail'], {
        id: 123,
        tags: { tagCounts: { wild: 5, funny: 3, spooky: 2 } }
      })
      
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'data'], {
        userTags: ['funny'] // User has funny tag
      })
      
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], {
        mugshots: [testMugshot],
        pagination: { totalCount: 1 }
      })
      
      const { result: tagResult } = renderHook(
        () => useTagMutation(mugshotId),
        { wrapper }
      )
      
      // Execute tag removal (toggle 'funny' tag off)
      await act(async () => {
        tagResult.current.mutate('funny')
      })
      
      // Verify both caches show tag removed
      const updatedMugshotDetail = queryClient.getQueryData(['mugshot', mugshotId, 'detail'])
      expect(updatedMugshotDetail).toEqual({
        id: 123,
        tags: { tagCounts: { wild: 5, funny: 2, spooky: 2 } } // funny count decreased
      })
      
      const updatedUserData = queryClient.getQueryData(['user', 'mugshot', mugshotId, 'data'])
      expect(updatedUserData).toEqual({
        userTags: [] // User no longer has funny tag
      })
      
      const updatedGridData = queryClient.getQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }])
      expect(updatedGridData).toEqual({
        mugshots: [
          {
            ...testMugshot,
            tag_counts: { wild: 5, funny: 2, spooky: 2 } // Updated in grid too!
          }
        ],
        pagination: { totalCount: 1 }
      })
    })
  })

  describe('Cross-Cache Reactivity', () => {
    it('should update multiple grid caches with different filter combinations', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Pre-populate multiple grid caches with different filters
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], {
        mugshots: [testMugshot],
        pagination: { totalCount: 1 }
      })
      
      queryClient.setQueryData(['mugshots', { state: 'CA' }, {}, { page: 1, perPage: 12 }], {
        mugshots: [testMugshot],
        pagination: { totalCount: 1 }
      })
      
      queryClient.setQueryData(['mugshots', { tags: ['wild'] }, { sortBy: 'top-rated' }, { page: 1, perPage: 12 }], {
        mugshots: [testMugshot],
        pagination: { totalCount: 1 }
      })
      
      queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], {
        averageRating: 7.5,
        totalRatings: 10
      })
      
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], { userRating: null })
      
      const { result: ratingResult } = renderHook(
        () => useRatingMutation(mugshotId),
        { wrapper }
      )
      
      // Execute rating mutation
      await act(async () => {
        ratingResult.current.mutate(10)
      })
      
      // ✅ THE MAGIC: All grid caches should be updated
      const gridCaches = [
        ['mugshots', {}, {}, { page: 1, perPage: 12 }],
        ['mugshots', { state: 'CA' }, {}, { page: 1, perPage: 12 }],
        ['mugshots', { tags: ['wild'] }, { sortBy: 'top-rated' }, { page: 1, perPage: 12 }]
      ]
      
      gridCaches.forEach(queryKey => {
        const cacheData = queryClient.getQueryData(queryKey)
        expect(cacheData).toEqual({
          mugshots: [
            {
              ...testMugshot,
              average_rating: 7.59, // (7.5*10 + 10) / 11 ≈ 7.59
              total_ratings: 11
            }
          ],
          pagination: { totalCount: 1 }
        })
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty grid caches gracefully', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Set up empty grid cache
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], {
        mugshots: [],
        pagination: { totalCount: 0 }
      })
      
      queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], {
        averageRating: 7.5,
        totalRatings: 10
      })
      
      queryClient.setQueryData(['user', 'mugshot', mugshotId, 'rating'], { userRating: null })
      
      const { result: ratingResult } = renderHook(
        () => useRatingMutation(mugshotId),
        { wrapper }
      )
      
      // Should not throw error even with empty grid
      await act(async () => {
        ratingResult.current.mutate(8)
      })
      
      // Grid cache should remain empty
      const gridData = queryClient.getQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }])
      expect(gridData).toEqual({
        mugshots: [],
        pagination: { totalCount: 0 }
      })
    })

    it('should handle malformed grid cache data gracefully', async () => {
      const wrapper = createWrapper()
      const queryClient = (wrapper as any).queryClient || new QueryClient()
      
      // Set up malformed grid cache
      queryClient.setQueryData(['mugshots', {}, {}, { page: 1, perPage: 12 }], {
        // Missing mugshots array
        pagination: { totalCount: 0 }
      })
      
      queryClient.setQueryData(['mugshot', mugshotId, 'rating-statistics'], {
        averageRating: 7.5,
        totalRatings: 10
      })
      
      const { result: ratingResult } = renderHook(
        () => useRatingMutation(mugshotId),
        { wrapper }
      )
      
      // Should not throw error with malformed data
      await act(async () => {
        ratingResult.current.mutate(8)
      })
      
      // Should not crash the application
      expect(ratingResult.current.isError).toBe(false)
    })
  })
}) 