{"aggregate": {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 16105, "vusers.created": 107400, "errors.Undefined function \"generateRandomMugshotId\"": 16105, "http.requests": 107466, "http.codes.400": 94, "http.responses": 732, "http.downloaded_bytes": 4700813, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 94, "vusers.created_by_name.Mugshots API Load Test": 85943, "errors.Undefined function \"generateRandomFilters\"": 91325, "vusers.failed": 106734, "vusers.completed": 666, "http.codes.200": 638, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 553, "vusers.created_by_name.Realistic User Journey": 5352, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 36, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 30, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 7446, "errors.ETIMEDOUT": 9261, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 420, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 1380, "plugins.metrics-by-endpoint.Browse with filters.errors.ETIMEDOUT": 10, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 77943, "errors.ECONNREFUSED": 97471, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 14630, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 4896, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 19, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.EADDRINUSE": 1, "errors.EADDRINUSE": 2, "plugins.metrics-by-endpoint.View mugshot details.errors.ETIMEDOUT": 5, "plugins.metrics-by-endpoint.View mugshot details.errors.ECONNREFUSED": 1, "plugins.metrics-by-endpoint.Browse with filters.errors.ECONNREFUSED": 1, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.EADDRINUSE": 1}, "rates": {"http.request_rate": 144}, "firstCounterAt": 1753533307081, "firstHistogramAt": 1753533307217, "lastCounterAt": 1753534058315, "lastHistogramAt": 1753533398781, "firstMetricAt": 1753533307081, "lastMetricAt": 1753534058315, "period": 1753534050000, "summaries": {"http.response_time": {"min": 86, "max": 29712, "count": 732, "mean": 4253.9, "p50": 1408.4, "median": 1408.4, "p75": 4676.2, "p90": 10617.5, "p95": 22254.1, "p99": 28290.8, "p999": 29445.4}, "http.response_time.4xx": {"min": 86, "max": 27130, "count": 94, "mean": 3724.5, "p50": 596, "median": 596, "p75": 3678.4, "p90": 10832, "p95": 19346.7, "p99": 26643.2, "p999": 26643.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 86, "max": 27130, "count": 94, "mean": 3724.5, "p50": 596, "median": 596, "p75": 3678.4, "p90": 10832, "p95": 19346.7, "p99": 26643.2, "p999": 26643.2}, "vusers.session_length": {"min": 1607.1, "max": 30721.6, "count": 666, "mean": 5720.3, "p50": 2515.5, "median": 2515.5, "p75": 5944.6, "p90": 14332.3, "p95": 23162.4, "p99": 29445.4, "p999": 30647.1}, "http.response_time.2xx": {"min": 598, "max": 29712, "count": 638, "mean": 4331.9, "p50": 1436.8, "median": 1436.8, "p75": 4770.6, "p90": 10407.3, "p95": 24107.7, "p99": 28862.3, "p999": 29445.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 598, "max": 29712, "count": 553, "mean": 4325, "p50": 1353.1, "median": 1353.1, "p75": 4867, "p90": 10617.5, "p95": 23630.3, "p99": 28862.3, "p999": 29445.4}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 718, "max": 29160, "count": 36, "mean": 5681.6, "p50": 3197.8, "median": 3197.8, "p75": 4770.6, "p90": 15839.7, "p95": 27181.5, "p99": 27730.6, "p999": 27730.6}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 905, "max": 27886, "count": 30, "mean": 3877, "p50": 3072.4, "median": 3072.4, "p75": 3828.5, "p90": 5944.6, "p95": 6312.2, "p99": 9416.8, "p999": 9416.8}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 965, "max": 5852, "count": 19, "mean": 2692.9, "p50": 1652.8, "median": 1652.8, "p75": 3678.4, "p90": 5065.6, "p95": 5711.5, "p99": 5711.5, "p999": 5711.5}}, "histograms": {"http.response_time": {"min": 86, "max": 29712, "count": 732, "mean": 4253.9, "p50": 1408.4, "median": 1408.4, "p75": 4676.2, "p90": 10617.5, "p95": 22254.1, "p99": 28290.8, "p999": 29445.4}, "http.response_time.4xx": {"min": 86, "max": 27130, "count": 94, "mean": 3724.5, "p50": 596, "median": 596, "p75": 3678.4, "p90": 10832, "p95": 19346.7, "p99": 26643.2, "p999": 26643.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 86, "max": 27130, "count": 94, "mean": 3724.5, "p50": 596, "median": 596, "p75": 3678.4, "p90": 10832, "p95": 19346.7, "p99": 26643.2, "p999": 26643.2}, "vusers.session_length": {"min": 1607.1, "max": 30721.6, "count": 666, "mean": 5720.3, "p50": 2515.5, "median": 2515.5, "p75": 5944.6, "p90": 14332.3, "p95": 23162.4, "p99": 29445.4, "p999": 30647.1}, "http.response_time.2xx": {"min": 598, "max": 29712, "count": 638, "mean": 4331.9, "p50": 1436.8, "median": 1436.8, "p75": 4770.6, "p90": 10407.3, "p95": 24107.7, "p99": 28862.3, "p999": 29445.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 598, "max": 29712, "count": 553, "mean": 4325, "p50": 1353.1, "median": 1353.1, "p75": 4867, "p90": 10617.5, "p95": 23630.3, "p99": 28862.3, "p999": 29445.4}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 718, "max": 29160, "count": 36, "mean": 5681.6, "p50": 3197.8, "median": 3197.8, "p75": 4770.6, "p90": 15839.7, "p95": 27181.5, "p99": 27730.6, "p999": 27730.6}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 905, "max": 27886, "count": 30, "mean": 3877, "p50": 3072.4, "median": 3072.4, "p75": 3828.5, "p90": 5944.6, "p95": 6312.2, "p99": 9416.8, "p999": 9416.8}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 965, "max": 5852, "count": 19, "mean": 2692.9, "p50": 1652.8, "median": 1652.8, "p75": 3678.4, "p90": 5065.6, "p95": 5711.5, "p99": 5711.5, "p999": 5711.5}}}, "intermediate": [{"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 4, "vusers.created": 30, "errors.Undefined function \"generateRandomMugshotId\"": 4, "http.requests": 30, "http.codes.400": 4, "http.responses": 24, "http.downloaded_bytes": 153427, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 4, "vusers.created_by_name.Mugshots API Load Test": 25, "errors.Undefined function \"generateRandomFilters\"": 26, "vusers.failed": 0, "vusers.completed": 1, "http.codes.200": 20, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 19, "vusers.created_by_name.Realistic User Journey": 1, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 1}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753533307081, "firstHistogramAt": 1753533307217, "lastCounterAt": 1753533309953, "lastHistogramAt": 1753533309953, "firstMetricAt": 1753533307081, "lastMetricAt": 1753533309953, "period": "1753533300000", "summaries": {"http.response_time": {"min": 106, "max": 2381, "count": 24, "mean": 1300.2, "p50": 1200.1, "median": 1200.1, "p75": 1978.7, "p90": 2059.5, "p95": 2143.5, "p99": 2231, "p999": 2231}, "http.response_time.4xx": {"min": 106, "max": 240, "count": 4, "mean": 180, "p50": 179.5, "median": 179.5, "p75": 194.4, "p90": 194.4, "p95": 194.4, "p99": 194.4, "p999": 194.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 106, "max": 240, "count": 4, "mean": 180, "p50": 179.5, "median": 179.5, "p75": 194.4, "p90": 194.4, "p95": 194.4, "p99": 194.4, "p999": 194.4}, "vusers.session_length": {"min": 2155, "max": 2155, "count": 1, "mean": 2155, "p50": 2143.5, "median": 2143.5, "p75": 2143.5, "p90": 2143.5, "p95": 2143.5, "p99": 2143.5, "p999": 2143.5}, "http.response_time.2xx": {"min": 740, "max": 2381, "count": 20, "mean": 1524.2, "p50": 1353.1, "median": 1353.1, "p75": 1978.7, "p90": 2143.5, "p95": 2231, "p99": 2231, "p999": 2231}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 740, "max": 2381, "count": 19, "mean": 1541.8, "p50": 1495.5, "median": 1495.5, "p75": 1978.7, "p90": 2143.5, "p95": 2231, "p99": 2231, "p999": 2231}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 1190, "max": 1190, "count": 1, "mean": 1190, "p50": 1200.1, "median": 1200.1, "p75": 1200.1, "p90": 1200.1, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}}, "histograms": {"http.response_time": {"min": 106, "max": 2381, "count": 24, "mean": 1300.2, "p50": 1200.1, "median": 1200.1, "p75": 1978.7, "p90": 2059.5, "p95": 2143.5, "p99": 2231, "p999": 2231}, "http.response_time.4xx": {"min": 106, "max": 240, "count": 4, "mean": 180, "p50": 179.5, "median": 179.5, "p75": 194.4, "p90": 194.4, "p95": 194.4, "p99": 194.4, "p999": 194.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 106, "max": 240, "count": 4, "mean": 180, "p50": 179.5, "median": 179.5, "p75": 194.4, "p90": 194.4, "p95": 194.4, "p99": 194.4, "p999": 194.4}, "vusers.session_length": {"min": 2155, "max": 2155, "count": 1, "mean": 2155, "p50": 2143.5, "median": 2143.5, "p75": 2143.5, "p90": 2143.5, "p95": 2143.5, "p99": 2143.5, "p999": 2143.5}, "http.response_time.2xx": {"min": 740, "max": 2381, "count": 20, "mean": 1524.2, "p50": 1353.1, "median": 1353.1, "p75": 1978.7, "p90": 2143.5, "p95": 2231, "p99": 2231, "p999": 2231}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 740, "max": 2381, "count": 19, "mean": 1541.8, "p50": 1495.5, "median": 1495.5, "p75": 1978.7, "p90": 2143.5, "p95": 2231, "p99": 2231, "p999": 2231}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 1190, "max": 1190, "count": 1, "mean": 1190, "p50": 1200.1, "median": 1200.1, "p75": 1200.1, "p90": 1200.1, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 15, "vusers.created": 100, "errors.Undefined function \"generateRandomMugshotId\"": 15, "http.requests": 103, "http.codes.400": 13, "http.responses": 100, "http.downloaded_bytes": 654240, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 13, "vusers.failed": 0, "vusers.completed": 111, "vusers.created_by_name.Mugshots API Load Test": 82, "errors.Undefined function \"generateRandomFilters\"": 86, "http.codes.200": 87, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 82, "vusers.created_by_name.Realistic User Journey": 3, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 2, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 2, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 1}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753533310060, "firstHistogramAt": 1753533310060, "lastCounterAt": 1753533319986, "lastHistogramAt": 1753533319986, "firstMetricAt": 1753533310060, "lastMetricAt": 1753533319986, "period": "1753533310000", "summaries": {"http.response_time": {"min": 86, "max": 1298, "count": 100, "mean": 686.9, "p50": 713.5, "median": 713.5, "p75": 804.5, "p90": 837.3, "p95": 907, "p99": 1107.9, "p999": 1107.9}, "http.response_time.4xx": {"min": 86, "max": 360, "count": 13, "mean": 202.8, "p50": 202.4, "median": 202.4, "p75": 214.9, "p90": 214.9, "p95": 320.6, "p99": 320.6, "p999": 320.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 86, "max": 360, "count": 13, "mean": 202.8, "p50": 202.4, "median": 202.4, "p75": 214.9, "p90": 214.9, "p95": 320.6, "p99": 320.6, "p999": 320.6}, "vusers.session_length": {"min": 1607.1, "max": 3419.4, "count": 111, "mean": 1952.6, "p50": 1790.4, "median": 1790.4, "p75": 2101.1, "p90": 2369, "p95": 3011.6, "p99": 3197.8, "p999": 3262.4}, "http.response_time.2xx": {"min": 598, "max": 1298, "count": 87, "mean": 759.2, "p50": 727.9, "median": 727.9, "p75": 804.5, "p90": 854.2, "p95": 907, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 598, "max": 1108, "count": 82, "mean": 746.4, "p50": 727.9, "median": 727.9, "p75": 804.5, "p90": 837.3, "p95": 854.2, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 723, "max": 790, "count": 2, "mean": 756.5, "p50": 727.9, "median": 727.9, "p75": 727.9, "p90": 727.9, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 1010, "max": 1030, "count": 2, "mean": 1020, "p50": 1002.4, "median": 1002.4, "p75": 1002.4, "p90": 1002.4, "p95": 1002.4, "p99": 1002.4, "p999": 1002.4}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 1298, "max": 1298, "count": 1, "mean": 1298, "p50": 1300.1, "median": 1300.1, "p75": 1300.1, "p90": 1300.1, "p95": 1300.1, "p99": 1300.1, "p999": 1300.1}}, "histograms": {"http.response_time": {"min": 86, "max": 1298, "count": 100, "mean": 686.9, "p50": 713.5, "median": 713.5, "p75": 804.5, "p90": 837.3, "p95": 907, "p99": 1107.9, "p999": 1107.9}, "http.response_time.4xx": {"min": 86, "max": 360, "count": 13, "mean": 202.8, "p50": 202.4, "median": 202.4, "p75": 214.9, "p90": 214.9, "p95": 320.6, "p99": 320.6, "p999": 320.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 86, "max": 360, "count": 13, "mean": 202.8, "p50": 202.4, "median": 202.4, "p75": 214.9, "p90": 214.9, "p95": 320.6, "p99": 320.6, "p999": 320.6}, "vusers.session_length": {"min": 1607.1, "max": 3419.4, "count": 111, "mean": 1952.6, "p50": 1790.4, "median": 1790.4, "p75": 2101.1, "p90": 2369, "p95": 3011.6, "p99": 3197.8, "p999": 3262.4}, "http.response_time.2xx": {"min": 598, "max": 1298, "count": 87, "mean": 759.2, "p50": 727.9, "median": 727.9, "p75": 804.5, "p90": 854.2, "p95": 907, "p99": 1107.9, "p999": 1107.9}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 598, "max": 1108, "count": 82, "mean": 746.4, "p50": 727.9, "median": 727.9, "p75": 804.5, "p90": 837.3, "p95": 854.2, "p99": 907, "p999": 907}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 723, "max": 790, "count": 2, "mean": 756.5, "p50": 727.9, "median": 727.9, "p75": 727.9, "p90": 727.9, "p95": 727.9, "p99": 727.9, "p999": 727.9}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 1010, "max": 1030, "count": 2, "mean": 1020, "p50": 1002.4, "median": 1002.4, "p75": 1002.4, "p90": 1002.4, "p95": 1002.4, "p99": 1002.4, "p999": 1002.4}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 1298, "max": 1298, "count": 1, "mean": 1298, "p50": 1300.1, "median": 1300.1, "p75": 1300.1, "p90": 1300.1, "p95": 1300.1, "p99": 1300.1, "p999": 1300.1}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 85, "vusers.created": 100, "errors.Undefined function \"generateRandomFilters\"": 91, "http.requests": 107, "http.codes.200": 95, "http.responses": 109, "http.downloaded_bytes": 702773, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 85, "vusers.failed": 0, "vusers.completed": 95, "vusers.created_by_name.Mugshot Details API Load Test": 12, "errors.Undefined function \"generateRandomMugshotId\"": 12, "http.codes.400": 14, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 14, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 4, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 2, "vusers.created_by_name.Realistic User Journey": 3, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 4}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753533320077, "firstHistogramAt": 1753533320108, "lastCounterAt": 1753533329999, "lastHistogramAt": 1753533329999, "firstMetricAt": 1753533320077, "lastMetricAt": 1753533329999, "period": "1753533320000", "summaries": {"http.response_time": {"min": 149, "max": 1442, "count": 109, "mean": 771.8, "p50": 757.6, "median": 757.6, "p75": 907, "p90": 1002.4, "p95": 1200.1, "p99": 1380.5, "p999": 1408.4}, "http.response_time.2xx": {"min": 626, "max": 1442, "count": 95, "mean": 839.2, "p50": 772.9, "median": 772.9, "p75": 907, "p90": 1002.4, "p95": 1249.1, "p99": 1408.4, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 626, "max": 1442, "count": 85, "mean": 823.1, "p50": 757.6, "median": 757.6, "p75": 889.1, "p90": 982.6, "p95": 1176.4, "p99": 1408.4, "p999": 1408.4}, "vusers.session_length": {"min": 1633, "max": 13553.5, "count": 95, "mean": 2146.1, "p50": 1826.6, "median": 1826.6, "p75": 2018.7, "p90": 2322.1, "p95": 2416.8, "p99": 12968.3, "p999": 12968.3}, "http.response_time.4xx": {"min": 149, "max": 749, "count": 14, "mean": 314.3, "p50": 278.7, "median": 278.7, "p75": 301.9, "p90": 383.8, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 149, "max": 749, "count": 14, "mean": 314.3, "p50": 278.7, "median": 278.7, "p75": 301.9, "p90": 383.8, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 905, "max": 1256, "count": 4, "mean": 1089.8, "p50": 1002.4, "median": 1002.4, "p75": 1200.1, "p90": 1200.1, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 965, "max": 1059, "count": 2, "mean": 1012, "p50": 963.1, "median": 963.1, "p75": 963.1, "p90": 963.1, "p95": 963.1, "p99": 963.1, "p999": 963.1}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 718, "max": 1122, "count": 4, "mean": 845, "p50": 742.6, "median": 742.6, "p75": 804.5, "p90": 804.5, "p95": 804.5, "p99": 804.5, "p999": 804.5}}, "histograms": {"http.response_time": {"min": 149, "max": 1442, "count": 109, "mean": 771.8, "p50": 757.6, "median": 757.6, "p75": 907, "p90": 1002.4, "p95": 1200.1, "p99": 1380.5, "p999": 1408.4}, "http.response_time.2xx": {"min": 626, "max": 1442, "count": 95, "mean": 839.2, "p50": 772.9, "median": 772.9, "p75": 907, "p90": 1002.4, "p95": 1249.1, "p99": 1408.4, "p999": 1408.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 626, "max": 1442, "count": 85, "mean": 823.1, "p50": 757.6, "median": 757.6, "p75": 889.1, "p90": 982.6, "p95": 1176.4, "p99": 1408.4, "p999": 1408.4}, "vusers.session_length": {"min": 1633, "max": 13553.5, "count": 95, "mean": 2146.1, "p50": 1826.6, "median": 1826.6, "p75": 2018.7, "p90": 2322.1, "p95": 2416.8, "p99": 12968.3, "p999": 12968.3}, "http.response_time.4xx": {"min": 149, "max": 749, "count": 14, "mean": 314.3, "p50": 278.7, "median": 278.7, "p75": 301.9, "p90": 383.8, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 149, "max": 749, "count": 14, "mean": 314.3, "p50": 278.7, "median": 278.7, "p75": 301.9, "p90": 383.8, "p95": 584.2, "p99": 584.2, "p999": 584.2}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 905, "max": 1256, "count": 4, "mean": 1089.8, "p50": 1002.4, "median": 1002.4, "p75": 1200.1, "p90": 1200.1, "p95": 1200.1, "p99": 1200.1, "p999": 1200.1}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 965, "max": 1059, "count": 2, "mean": 1012, "p50": 963.1, "median": 963.1, "p75": 963.1, "p90": 963.1, "p95": 963.1, "p99": 963.1, "p999": 963.1}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 718, "max": 1122, "count": 4, "mean": 845, "p50": 742.6, "median": 742.6, "p75": 804.5, "p90": 804.5, "p95": 804.5, "p99": 804.5, "p999": 804.5}}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 13, "vusers.created": 100, "errors.Undefined function \"generateRandomMugshotId\"": 13, "http.requests": 109, "http.codes.400": 13, "http.responses": 110, "http.downloaded_bytes": 705529, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 13, "vusers.failed": 0, "vusers.completed": 98, "vusers.created_by_name.Mugshots API Load Test": 81, "errors.Undefined function \"generateRandomFilters\"": 90, "http.codes.200": 97, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 81, "vusers.created_by_name.Realistic User Journey": 6, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 6, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 6, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 4}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753533330021, "firstHistogramAt": 1753533330021, "lastCounterAt": 1753533339980, "lastHistogramAt": 1753533339980, "firstMetricAt": 1753533330021, "lastMetricAt": 1753533339980, "period": "1753533330000", "summaries": {"http.response_time": {"min": 111, "max": 1809, "count": 110, "mean": 939.7, "p50": 907, "median": 907, "p75": 1153.1, "p90": 1380.5, "p95": 1495.5, "p99": 1620, "p999": 1652.8}, "http.response_time.4xx": {"min": 111, "max": 778, "count": 13, "mean": 333.5, "p50": 242.3, "median": 242.3, "p75": 550.1, "p90": 572.6, "p95": 596, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 111, "max": 778, "count": 13, "mean": 333.5, "p50": 242.3, "median": 242.3, "p75": 550.1, "p90": 572.6, "p95": 596, "p99": 596, "p999": 596}, "vusers.session_length": {"min": 1631.8, "max": 13678.8, "count": 98, "mean": 2482.8, "p50": 1978.7, "median": 1978.7, "p75": 2231, "p90": 2416.8, "p95": 2618.1, "p99": 13497.6, "p999": 13497.6}, "http.response_time.2xx": {"min": 616, "max": 1809, "count": 97, "mean": 1020.9, "p50": 982.6, "median": 982.6, "p75": 1176.4, "p90": 1408.4, "p95": 1495.5, "p99": 1652.8, "p999": 1652.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 616, "max": 1499, "count": 81, "mean": 976.1, "p50": 925.4, "median": 925.4, "p75": 1153.1, "p90": 1353.1, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 763, "max": 1135, "count": 6, "mean": 945.2, "p50": 889.1, "median": 889.1, "p75": 944, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 1028, "max": 1809, "count": 6, "mean": 1483.5, "p50": 1495.5, "median": 1495.5, "p75": 1587.9, "p90": 1620, "p95": 1620, "p99": 1620, "p999": 1620}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 1051, "max": 1646, "count": 4, "mean": 1349.8, "p50": 1224.4, "median": 1224.4, "p75": 1495.5, "p90": 1495.5, "p95": 1495.5, "p99": 1495.5, "p999": 1495.5}}, "histograms": {"http.response_time": {"min": 111, "max": 1809, "count": 110, "mean": 939.7, "p50": 907, "median": 907, "p75": 1153.1, "p90": 1380.5, "p95": 1495.5, "p99": 1620, "p999": 1652.8}, "http.response_time.4xx": {"min": 111, "max": 778, "count": 13, "mean": 333.5, "p50": 242.3, "median": 242.3, "p75": 550.1, "p90": 572.6, "p95": 596, "p99": 596, "p999": 596}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 111, "max": 778, "count": 13, "mean": 333.5, "p50": 242.3, "median": 242.3, "p75": 550.1, "p90": 572.6, "p95": 596, "p99": 596, "p999": 596}, "vusers.session_length": {"min": 1631.8, "max": 13678.8, "count": 98, "mean": 2482.8, "p50": 1978.7, "median": 1978.7, "p75": 2231, "p90": 2416.8, "p95": 2618.1, "p99": 13497.6, "p999": 13497.6}, "http.response_time.2xx": {"min": 616, "max": 1809, "count": 97, "mean": 1020.9, "p50": 982.6, "median": 982.6, "p75": 1176.4, "p90": 1408.4, "p95": 1495.5, "p99": 1652.8, "p999": 1652.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 616, "max": 1499, "count": 81, "mean": 976.1, "p50": 925.4, "median": 925.4, "p75": 1153.1, "p90": 1353.1, "p95": 1408.4, "p99": 1436.8, "p999": 1436.8}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 763, "max": 1135, "count": 6, "mean": 945.2, "p50": 889.1, "median": 889.1, "p75": 944, "p90": 1130.2, "p95": 1130.2, "p99": 1130.2, "p999": 1130.2}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 1028, "max": 1809, "count": 6, "mean": 1483.5, "p50": 1495.5, "median": 1495.5, "p75": 1587.9, "p90": 1620, "p95": 1620, "p99": 1620, "p999": 1620}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 1051, "max": 1646, "count": 4, "mean": 1349.8, "p50": 1224.4, "median": 1224.4, "p75": 1495.5, "p90": 1495.5, "p95": 1495.5, "p99": 1495.5, "p999": 1495.5}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 80, "vusers.created": 100, "errors.Undefined function \"generateRandomFilters\"": 92, "http.requests": 110, "http.codes.200": 56, "http.responses": 64, "http.downloaded_bytes": 417901, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 45, "vusers.failed": 0, "vusers.completed": 68, "vusers.created_by_name.Mugshot Details API Load Test": 14, "errors.Undefined function \"generateRandomMugshotId\"": 14, "http.codes.400": 8, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 8, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 2, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 5, "vusers.created_by_name.Realistic User Journey": 6, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 4}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753533340011, "firstHistogramAt": 1753533340011, "lastCounterAt": 1753533349908, "lastHistogramAt": 1753533349908, "firstMetricAt": 1753533340011, "lastMetricAt": 1753533349908, "period": "1753533340000", "summaries": {"http.response_time": {"min": 211, "max": 4601, "count": 64, "mean": 1638, "p50": 1002.4, "median": 1002.4, "p75": 1720.2, "p90": 3984.7, "p95": 4147.4, "p99": 4403.8, "p999": 4403.8}, "http.response_time.2xx": {"min": 657, "max": 4601, "count": 56, "mean": 1710.4, "p50": 1002.4, "median": 1002.4, "p75": 1720.2, "p90": 4065.2, "p95": 4231.1, "p99": 4403.8, "p999": 4403.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 657, "max": 4601, "count": 45, "mean": 1605.1, "p50": 944, "median": 944, "p75": 1556.5, "p90": 3984.7, "p95": 4147.4, "p99": 4403.8, "p999": 4403.8}, "vusers.session_length": {"min": 1674.2, "max": 15524, "count": 68, "mean": 3322.6, "p50": 2059.5, "median": 2059.5, "p75": 2566.3, "p90": 5168, "p95": 13497.6, "p99": 14048.5, "p999": 14048.5}, "http.response_time.4xx": {"min": 211, "max": 3656, "count": 8, "mean": 1131.1, "p50": 478.3, "median": 478.3, "p75": 1525.7, "p90": 2101.1, "p95": 2101.1, "p99": 2101.1, "p999": 2101.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 211, "max": 3656, "count": 8, "mean": 1131.1, "p50": 478.3, "median": 478.3, "p75": 1525.7, "p90": 2101.1, "p95": 2101.1, "p99": 2101.1, "p999": 2101.1}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 1279, "max": 4291, "count": 2, "mean": 2785, "p50": 1274.3, "median": 1274.3, "p75": 1274.3, "p90": 1274.3, "p95": 1274.3, "p99": 1274.3, "p999": 1274.3}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 1032, "max": 4086, "count": 5, "mean": 2274.8, "p50": 1380.5, "median": 1380.5, "p75": 3678.4, "p90": 3678.4, "p95": 3678.4, "p99": 3678.4, "p999": 3678.4}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 915, "max": 3659, "count": 4, "mean": 1651.5, "p50": 1002.4, "median": 1002.4, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}}, "histograms": {"http.response_time": {"min": 211, "max": 4601, "count": 64, "mean": 1638, "p50": 1002.4, "median": 1002.4, "p75": 1720.2, "p90": 3984.7, "p95": 4147.4, "p99": 4403.8, "p999": 4403.8}, "http.response_time.2xx": {"min": 657, "max": 4601, "count": 56, "mean": 1710.4, "p50": 1002.4, "median": 1002.4, "p75": 1720.2, "p90": 4065.2, "p95": 4231.1, "p99": 4403.8, "p999": 4403.8}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 657, "max": 4601, "count": 45, "mean": 1605.1, "p50": 944, "median": 944, "p75": 1556.5, "p90": 3984.7, "p95": 4147.4, "p99": 4403.8, "p999": 4403.8}, "vusers.session_length": {"min": 1674.2, "max": 15524, "count": 68, "mean": 3322.6, "p50": 2059.5, "median": 2059.5, "p75": 2566.3, "p90": 5168, "p95": 13497.6, "p99": 14048.5, "p999": 14048.5}, "http.response_time.4xx": {"min": 211, "max": 3656, "count": 8, "mean": 1131.1, "p50": 478.3, "median": 478.3, "p75": 1525.7, "p90": 2101.1, "p95": 2101.1, "p99": 2101.1, "p999": 2101.1}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 211, "max": 3656, "count": 8, "mean": 1131.1, "p50": 478.3, "median": 478.3, "p75": 1525.7, "p90": 2101.1, "p95": 2101.1, "p99": 2101.1, "p999": 2101.1}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 1279, "max": 4291, "count": 2, "mean": 2785, "p50": 1274.3, "median": 1274.3, "p75": 1274.3, "p90": 1274.3, "p95": 1274.3, "p99": 1274.3, "p999": 1274.3}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 1032, "max": 4086, "count": 5, "mean": 2274.8, "p50": 1380.5, "median": 1380.5, "p75": 3678.4, "p90": 3678.4, "p95": 3678.4, "p99": 3678.4, "p999": 3678.4}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 915, "max": 3659, "count": 4, "mean": 1651.5, "p50": 1002.4, "median": 1002.4, "p75": 1043.3, "p90": 1043.3, "p95": 1043.3, "p99": 1043.3, "p999": 1043.3}}}, {"counters": {"http.codes.200": 100, "http.responses": 111, "http.downloaded_bytes": 747364, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 89, "vusers.created_by_name.Mugshots API Load Test": 77, "vusers.created": 100, "errors.Undefined function \"generateRandomFilters\"": 89, "http.requests": 107, "vusers.failed": 0, "vusers.completed": 89, "vusers.created_by_name.Mugshot Details API Load Test": 14, "errors.Undefined function \"generateRandomMugshotId\"": 14, "http.codes.400": 11, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 11, "vusers.created_by_name.Realistic User Journey": 9, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 2, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 6, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 3}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1753533350030, "firstHistogramAt": 1753533350030, "lastCounterAt": 1753533359782, "lastHistogramAt": 1753533359782, "firstMetricAt": 1753533350030, "lastMetricAt": 1753533359782, "period": "1753533350000", "summaries": {"http.response_time": {"min": 3149, "max": 6361, "count": 111, "mean": 5046.8, "p50": 5065.6, "median": 5065.6, "p75": 5378.9, "p90": 5711.5, "p95": 5944.6, "p99": 6187.2, "p999": 6187.2}, "http.response_time.2xx": {"min": 4315, "max": 6361, "count": 100, "mean": 5173.2, "p50": 5168, "median": 5168, "p75": 5378.9, "p90": 5711.5, "p95": 5944.6, "p99": 6187.2, "p999": 6187.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 4315, "max": 6235, "count": 89, "mean": 5142.8, "p50": 5168, "median": 5168, "p75": 5378.9, "p90": 5711.5, "p95": 5826.9, "p99": 6187.2, "p999": 6187.2}, "vusers.session_length": {"min": 5499.3, "max": 18316.4, "count": 89, "mean": 6410.6, "p50": 6064.7, "median": 6064.7, "p75": 6439.7, "p90": 6838, "p95": 6976.1, "p99": 16486.1, "p999": 16486.1}, "http.response_time.4xx": {"min": 3149, "max": 4837, "count": 11, "mean": 3898.1, "p50": 3905.8, "median": 3905.8, "p75": 3984.7, "p90": 4770.6, "p95": 4770.6, "p99": 4770.6, "p999": 4770.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 3149, "max": 4837, "count": 11, "mean": 3898.1, "p50": 3905.8, "median": 3905.8, "p75": 3984.7, "p90": 4770.6, "p95": 4770.6, "p99": 4770.6, "p999": 4770.6}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 5755, "max": 5852, "count": 2, "mean": 5803.5, "p50": 5711.5, "median": 5711.5, "p75": 5711.5, "p90": 5711.5, "p95": 5711.5, "p99": 5711.5, "p999": 5711.5}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 4555, "max": 5713, "count": 6, "mean": 5055.3, "p50": 4965.3, "median": 4965.3, "p75": 5168, "p90": 5168, "p95": 5168, "p99": 5168, "p999": 5168}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 5325, "max": 6361, "count": 3, "mean": 5892.3, "p50": 5944.6, "median": 5944.6, "p75": 5944.6, "p90": 5944.6, "p95": 5944.6, "p99": 5944.6, "p999": 5944.6}}, "histograms": {"http.response_time": {"min": 3149, "max": 6361, "count": 111, "mean": 5046.8, "p50": 5065.6, "median": 5065.6, "p75": 5378.9, "p90": 5711.5, "p95": 5944.6, "p99": 6187.2, "p999": 6187.2}, "http.response_time.2xx": {"min": 4315, "max": 6361, "count": 100, "mean": 5173.2, "p50": 5168, "median": 5168, "p75": 5378.9, "p90": 5711.5, "p95": 5944.6, "p99": 6187.2, "p999": 6187.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 4315, "max": 6235, "count": 89, "mean": 5142.8, "p50": 5168, "median": 5168, "p75": 5378.9, "p90": 5711.5, "p95": 5826.9, "p99": 6187.2, "p999": 6187.2}, "vusers.session_length": {"min": 5499.3, "max": 18316.4, "count": 89, "mean": 6410.6, "p50": 6064.7, "median": 6064.7, "p75": 6439.7, "p90": 6838, "p95": 6976.1, "p99": 16486.1, "p999": 16486.1}, "http.response_time.4xx": {"min": 3149, "max": 4837, "count": 11, "mean": 3898.1, "p50": 3905.8, "median": 3905.8, "p75": 3984.7, "p90": 4770.6, "p95": 4770.6, "p99": 4770.6, "p999": 4770.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 3149, "max": 4837, "count": 11, "mean": 3898.1, "p50": 3905.8, "median": 3905.8, "p75": 3984.7, "p90": 4770.6, "p95": 4770.6, "p99": 4770.6, "p999": 4770.6}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 5755, "max": 5852, "count": 2, "mean": 5803.5, "p50": 5711.5, "median": 5711.5, "p75": 5711.5, "p90": 5711.5, "p95": 5711.5, "p99": 5711.5, "p999": 5711.5}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 4555, "max": 5713, "count": 6, "mean": 5055.3, "p50": 4965.3, "median": 4965.3, "p75": 5168, "p90": 5168, "p95": 5168, "p99": 5168, "p999": 5168}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 5325, "max": 6361, "count": 3, "mean": 5892.3, "p50": 5944.6, "median": 5944.6, "p75": 5944.6, "p90": 5944.6, "p95": 5944.6, "p99": 5944.6, "p999": 5944.6}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 204, "vusers.created": 251, "errors.Undefined function \"generateRandomFilters\"": 222, "http.requests": 267, "vusers.failed": 0, "vusers.completed": 115, "http.codes.200": 103, "http.responses": 119, "http.downloaded_bytes": 719082, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 79, "http.codes.400": 16, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 16, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 8, "vusers.created_by_name.Mugshot Details API Load Test": 34, "errors.Undefined function \"generateRandomMugshotId\"": 34, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 11, "vusers.created_by_name.Realistic User Journey": 13, "plugins.metrics-by-endpoint.Browse with filters.codes.200": 5}, "rates": {"http.request_rate": 32}, "http.request_rate": null, "firstCounterAt": 1753533360023, "firstHistogramAt": 1753533360023, "lastCounterAt": 1753533369988, "lastHistogramAt": 1753533369910, "firstMetricAt": 1753533360023, "lastMetricAt": 1753533369988, "period": "1753533360000", "summaries": {"vusers.session_length": {"min": 3482.4, "max": 23487.9, "count": 115, "mean": 5477.3, "p50": 4583.6, "median": 4583.6, "p75": 5487.5, "p90": 6064.7, "p95": 6312.2, "p99": 22254.1, "p999": 23162.4}, "http.response_time": {"min": 2146, "max": 5250, "count": 119, "mean": 3444.7, "p50": 3328.3, "median": 3328.3, "p75": 3984.7, "p90": 4492.8, "p95": 4583.6, "p99": 5065.6, "p999": 5065.6}, "http.response_time.2xx": {"min": 2467, "max": 5250, "count": 103, "mean": 3514.6, "p50": 3328.3, "median": 3328.3, "p75": 4147.4, "p90": 4492.8, "p95": 4583.6, "p99": 5065.6, "p999": 5065.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 2467, "max": 4801, "count": 79, "mean": 3427.5, "p50": 3262.4, "median": 3262.4, "p75": 4065.2, "p90": 4403.8, "p95": 4492.8, "p99": 4676.2, "p999": 4676.2}, "http.response_time.4xx": {"min": 2146, "max": 3765, "count": 16, "mean": 2994.4, "p50": 2893.5, "median": 2893.5, "p75": 3605.5, "p90": 3678.4, "p95": 3752.7, "p99": 3752.7, "p999": 3752.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 2146, "max": 3765, "count": 16, "mean": 2994.4, "p50": 2893.5, "median": 2893.5, "p75": 3605.5, "p90": 3678.4, "p95": 3752.7, "p99": 3752.7, "p999": 3752.7}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 3072, "max": 4612, "count": 8, "mean": 3914.5, "p50": 4147.4, "median": 4147.4, "p75": 4316.6, "p90": 4403.8, "p95": 4403.8, "p99": 4403.8, "p999": 4403.8}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 2890, "max": 5250, "count": 11, "mean": 3677.7, "p50": 3395.5, "median": 3395.5, "p75": 3605.5, "p90": 5065.6, "p95": 5065.6, "p99": 5065.6, "p999": 5065.6}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 2839, "max": 5041, "count": 5, "mean": 3892.6, "p50": 3464.1, "median": 3464.1, "p75": 4676.2, "p90": 4676.2, "p95": 4676.2, "p99": 4676.2, "p999": 4676.2}}, "histograms": {"vusers.session_length": {"min": 3482.4, "max": 23487.9, "count": 115, "mean": 5477.3, "p50": 4583.6, "median": 4583.6, "p75": 5487.5, "p90": 6064.7, "p95": 6312.2, "p99": 22254.1, "p999": 23162.4}, "http.response_time": {"min": 2146, "max": 5250, "count": 119, "mean": 3444.7, "p50": 3328.3, "median": 3328.3, "p75": 3984.7, "p90": 4492.8, "p95": 4583.6, "p99": 5065.6, "p999": 5065.6}, "http.response_time.2xx": {"min": 2467, "max": 5250, "count": 103, "mean": 3514.6, "p50": 3328.3, "median": 3328.3, "p75": 4147.4, "p90": 4492.8, "p95": 4583.6, "p99": 5065.6, "p999": 5065.6}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 2467, "max": 4801, "count": 79, "mean": 3427.5, "p50": 3262.4, "median": 3262.4, "p75": 4065.2, "p90": 4403.8, "p95": 4492.8, "p99": 4676.2, "p999": 4676.2}, "http.response_time.4xx": {"min": 2146, "max": 3765, "count": 16, "mean": 2994.4, "p50": 2893.5, "median": 2893.5, "p75": 3605.5, "p90": 3678.4, "p95": 3752.7, "p99": 3752.7, "p999": 3752.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 2146, "max": 3765, "count": 16, "mean": 2994.4, "p50": 2893.5, "median": 2893.5, "p75": 3605.5, "p90": 3678.4, "p95": 3752.7, "p99": 3752.7, "p999": 3752.7}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 3072, "max": 4612, "count": 8, "mean": 3914.5, "p50": 4147.4, "median": 4147.4, "p75": 4316.6, "p90": 4403.8, "p95": 4403.8, "p99": 4403.8, "p999": 4403.8}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 2890, "max": 5250, "count": 11, "mean": 3677.7, "p50": 3395.5, "median": 3395.5, "p75": 3605.5, "p90": 5065.6, "p95": 5065.6, "p99": 5065.6, "p999": 5065.6}, "plugins.metrics-by-endpoint.response_time.Browse with filters": {"min": 2839, "max": 5041, "count": 5, "mean": 3892.6, "p50": 3464.1, "median": 3464.1, "p75": 4676.2, "p90": 4676.2, "p95": 4676.2, "p99": 4676.2, "p999": 4676.2}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 488, "vusers.created": 596, "errors.Undefined function \"generateRandomFilters\"": 525, "http.requests": 603, "http.codes.400": 6, "http.responses": 29, "http.downloaded_bytes": 169977, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 6, "vusers.created_by_name.Mugshot Details API Load Test": 78, "errors.Undefined function \"generateRandomMugshotId\"": 78, "http.codes.200": 23, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 22, "vusers.failed": 0, "vusers.completed": 28, "vusers.created_by_name.Realistic User Journey": 30, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 1}, "rates": {"http.request_rate": 63}, "http.request_rate": null, "firstCounterAt": 1753533370002, "firstHistogramAt": 1753533370409, "lastCounterAt": 1753533379993, "lastHistogramAt": 1753533379836, "firstMetricAt": 1753533370002, "lastMetricAt": 1753533379993, "period": "1753533370000", "summaries": {"http.response_time": {"min": 4329, "max": 12003, "count": 29, "mean": 8432.6, "p50": 8520.7, "median": 8520.7, "p75": 10617.5, "p90": 11501.8, "p95": 11734.2, "p99": 11971.2, "p999": 11971.2}, "http.response_time.4xx": {"min": 4329, "max": 12003, "count": 6, "mean": 7649.2, "p50": 5826.9, "median": 5826.9, "p75": 7865.6, "p90": 10832, "p95": 10832, "p99": 10832, "p999": 10832}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 4329, "max": 12003, "count": 6, "mean": 7649.2, "p50": 5826.9, "median": 5826.9, "p75": 7865.6, "p90": 10832, "p95": 10832, "p99": 10832, "p999": 10832}, "http.response_time.2xx": {"min": 5229, "max": 12002, "count": 23, "mean": 8637, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11274.1, "p95": 11501.8, "p99": 11734.2, "p999": 11734.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 5229, "max": 12002, "count": 22, "mean": 8601.2, "p50": 8520.7, "median": 8520.7, "p75": 10617.5, "p90": 11274.1, "p95": 11501.8, "p99": 11734.2, "p999": 11734.2}, "vusers.session_length": {"min": 6237.3, "max": 23421.1, "count": 28, "mean": 9958.4, "p50": 9607.1, "median": 9607.1, "p75": 11501.8, "p90": 12711.5, "p95": 12711.5, "p99": 12968.3, "p999": 12968.3}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 9423, "max": 9423, "count": 1, "mean": 9423, "p50": 9416.8, "median": 9416.8, "p75": 9416.8, "p90": 9416.8, "p95": 9416.8, "p99": 9416.8, "p999": 9416.8}}, "histograms": {"http.response_time": {"min": 4329, "max": 12003, "count": 29, "mean": 8432.6, "p50": 8520.7, "median": 8520.7, "p75": 10617.5, "p90": 11501.8, "p95": 11734.2, "p99": 11971.2, "p999": 11971.2}, "http.response_time.4xx": {"min": 4329, "max": 12003, "count": 6, "mean": 7649.2, "p50": 5826.9, "median": 5826.9, "p75": 7865.6, "p90": 10832, "p95": 10832, "p99": 10832, "p999": 10832}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 4329, "max": 12003, "count": 6, "mean": 7649.2, "p50": 5826.9, "median": 5826.9, "p75": 7865.6, "p90": 10832, "p95": 10832, "p99": 10832, "p999": 10832}, "http.response_time.2xx": {"min": 5229, "max": 12002, "count": 23, "mean": 8637, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11274.1, "p95": 11501.8, "p99": 11734.2, "p999": 11734.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 5229, "max": 12002, "count": 22, "mean": 8601.2, "p50": 8520.7, "median": 8520.7, "p75": 10617.5, "p90": 11274.1, "p95": 11501.8, "p99": 11734.2, "p999": 11734.2}, "vusers.session_length": {"min": 6237.3, "max": 23421.1, "count": 28, "mean": 9958.4, "p50": 9607.1, "median": 9607.1, "p75": 11501.8, "p90": 12711.5, "p95": 12711.5, "p99": 12968.3, "p999": 12968.3}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 9423, "max": 9423, "count": 1, "mean": 9423, "p50": 9416.8, "median": 9416.8, "p75": 9416.8, "p90": 9416.8, "p95": 9416.8, "p99": 9416.8, "p999": 9416.8}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 581, "vusers.created": 720, "errors.Undefined function \"generateRandomFilters\"": 610, "http.requests": 722, "vusers.created_by_name.Realistic User Journey": 28, "vusers.created_by_name.Mugshot Details API Load Test": 111, "errors.Undefined function \"generateRandomMugshotId\"": 111, "http.codes.200": 23, "http.responses": 29, "http.downloaded_bytes": 176557, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 22, "vusers.failed": 0, "vusers.completed": 27, "http.codes.400": 6, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 6, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 1}, "rates": {"http.request_rate": 73}, "http.request_rate": null, "firstCounterAt": 1753533380006, "firstHistogramAt": 1753533380108, "lastCounterAt": 1753533389995, "lastHistogramAt": 1753533389871, "firstMetricAt": 1753533380006, "lastMetricAt": 1753533389995, "period": "1753533380000", "summaries": {"http.response_time": {"min": 13337, "max": 22275, "count": 29, "mean": 17561.4, "p50": 17859.2, "median": 17859.2, "p75": 19346.7, "p90": 20958.1, "p95": 21381.5, "p99": 21381.5, "p999": 21381.5}, "http.response_time.2xx": {"min": 13337, "max": 22275, "count": 23, "mean": 17284.2, "p50": 17859.2, "median": 17859.2, "p75": 18963.6, "p90": 20958.1, "p95": 21381.5, "p99": 21381.5, "p999": 21381.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 13337, "max": 22275, "count": 22, "mean": 17346.8, "p50": 17859.2, "median": 17859.2, "p75": 18963.6, "p90": 20958.1, "p95": 21381.5, "p99": 21381.5, "p999": 21381.5}, "vusers.session_length": {"min": 14018, "max": 22537.2, "count": 27, "mean": 18345.9, "p50": 18963.6, "median": 18963.6, "p75": 20136.3, "p90": 21813.5, "p95": 22254.1, "p99": 22703.7, "p999": 22703.7}, "http.response_time.4xx": {"min": 15821, "max": 21159, "count": 6, "mean": 18624, "p50": 18220, "median": 18220, "p75": 19346.7, "p90": 19346.7, "p95": 19346.7, "p99": 19346.7, "p999": 19346.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 15821, "max": 21159, "count": 6, "mean": 18624, "p50": 18220, "median": 18220, "p75": 19346.7, "p90": 19346.7, "p95": 19346.7, "p99": 19346.7, "p999": 19346.7}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 15906, "max": 15906, "count": 1, "mean": 15906, "p50": 15839.7, "median": 15839.7, "p75": 15839.7, "p90": 15839.7, "p95": 15839.7, "p99": 15839.7, "p999": 15839.7}}, "histograms": {"http.response_time": {"min": 13337, "max": 22275, "count": 29, "mean": 17561.4, "p50": 17859.2, "median": 17859.2, "p75": 19346.7, "p90": 20958.1, "p95": 21381.5, "p99": 21381.5, "p999": 21381.5}, "http.response_time.2xx": {"min": 13337, "max": 22275, "count": 23, "mean": 17284.2, "p50": 17859.2, "median": 17859.2, "p75": 18963.6, "p90": 20958.1, "p95": 21381.5, "p99": 21381.5, "p999": 21381.5}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 13337, "max": 22275, "count": 22, "mean": 17346.8, "p50": 17859.2, "median": 17859.2, "p75": 18963.6, "p90": 20958.1, "p95": 21381.5, "p99": 21381.5, "p999": 21381.5}, "vusers.session_length": {"min": 14018, "max": 22537.2, "count": 27, "mean": 18345.9, "p50": 18963.6, "median": 18963.6, "p75": 20136.3, "p90": 21813.5, "p95": 22254.1, "p99": 22703.7, "p999": 22703.7}, "http.response_time.4xx": {"min": 15821, "max": 21159, "count": 6, "mean": 18624, "p50": 18220, "median": 18220, "p75": 19346.7, "p90": 19346.7, "p95": 19346.7, "p99": 19346.7, "p999": 19346.7}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 15821, "max": 21159, "count": 6, "mean": 18624, "p50": 18220, "median": 18220, "p75": 19346.7, "p90": 19346.7, "p95": 19346.7, "p99": 19346.7, "p999": 19346.7}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 15906, "max": 15906, "count": 1, "mean": 15906, "p50": 15839.7, "median": 15839.7, "p75": 15839.7, "p90": 15839.7, "p95": 15839.7, "p99": 15839.7, "p999": 15839.7}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 664, "vusers.created": 847, "errors.Undefined function \"generateRandomFilters\"": 708, "http.requests": 850, "vusers.created_by_name.Mugshot Details API Load Test": 139, "errors.Undefined function \"generateRandomMugshotId\"": 139, "vusers.created_by_name.Realistic User Journey": 44, "http.codes.200": 34, "http.responses": 37, "http.downloaded_bytes": 253963, "plugins.metrics-by-endpoint.GET /api/mugshots.codes.200": 29, "vusers.failed": 100, "vusers.completed": 34, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 80, "errors.ETIMEDOUT": 100, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 12, "http.codes.400": 3, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].codes.400": 3, "plugins.metrics-by-endpoint.Browse mugshots.codes.200": 4, "plugins.metrics-by-endpoint.View mugshot details.codes.200": 1, "plugins.metrics-by-endpoint.Browse with filters.errors.ETIMEDOUT": 2, "plugins.metrics-by-endpoint.View mugshot details.errors.ETIMEDOUT": 1}, "rates": {"http.request_rate": 85}, "http.request_rate": null, "firstCounterAt": 1753533390009, "firstHistogramAt": 1753533390720, "lastCounterAt": 1753533399987, "lastHistogramAt": 1753533398781, "firstMetricAt": 1753533390009, "lastMetricAt": 1753533399987, "period": "1753533390000", "summaries": {"http.response_time": {"min": 23762, "max": 29712, "count": 37, "mean": 26965.1, "p50": 27181.5, "median": 27181.5, "p75": 28290.8, "p90": 29445.4, "p95": 29445.4, "p99": 29445.4, "p999": 29445.4}, "http.response_time.2xx": {"min": 23762, "max": 29712, "count": 34, "mean": 26975.7, "p50": 27181.5, "median": 27181.5, "p75": 28290.8, "p90": 29445.4, "p95": 29445.4, "p99": 29445.4, "p999": 29445.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 23762, "max": 29712, "count": 29, "mean": 26919.4, "p50": 27181.5, "median": 27181.5, "p75": 28290.8, "p90": 29445.4, "p95": 29445.4, "p99": 29445.4, "p999": 29445.4}, "vusers.session_length": {"min": 23171.2, "max": 30721.6, "count": 34, "mean": 27737.8, "p50": 28290.8, "median": 28290.8, "p75": 28862.3, "p90": 30040.3, "p95": 30647.1, "p99": 30647.1, "p999": 30647.1}, "http.response_time.4xx": {"min": 26495, "max": 27130, "count": 3, "mean": 26844.7, "p50": 26643.2, "median": 26643.2, "p75": 26643.2, "p90": 26643.2, "p95": 26643.2, "p99": 26643.2, "p999": 26643.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 26495, "max": 27130, "count": 3, "mean": 26844.7, "p50": 26643.2, "median": 26643.2, "p75": 26643.2, "p90": 26643.2, "p95": 26643.2, "p99": 26643.2, "p999": 26643.2}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 24793, "max": 29160, "count": 4, "mean": 27156, "p50": 27181.5, "median": 27181.5, "p75": 27730.6, "p90": 27730.6, "p95": 27730.6, "p99": 27730.6, "p999": 27730.6}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 27886, "max": 27886, "count": 1, "mean": 27886, "p50": 27730.6, "median": 27730.6, "p75": 27730.6, "p90": 27730.6, "p95": 27730.6, "p99": 27730.6, "p999": 27730.6}}, "histograms": {"http.response_time": {"min": 23762, "max": 29712, "count": 37, "mean": 26965.1, "p50": 27181.5, "median": 27181.5, "p75": 28290.8, "p90": 29445.4, "p95": 29445.4, "p99": 29445.4, "p999": 29445.4}, "http.response_time.2xx": {"min": 23762, "max": 29712, "count": 34, "mean": 26975.7, "p50": 27181.5, "median": 27181.5, "p75": 28290.8, "p90": 29445.4, "p95": 29445.4, "p99": 29445.4, "p999": 29445.4}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots": {"min": 23762, "max": 29712, "count": 29, "mean": 26919.4, "p50": 27181.5, "median": 27181.5, "p75": 28290.8, "p90": 29445.4, "p95": 29445.4, "p99": 29445.4, "p999": 29445.4}, "vusers.session_length": {"min": 23171.2, "max": 30721.6, "count": 34, "mean": 27737.8, "p50": 28290.8, "median": 28290.8, "p75": 28862.3, "p90": 30040.3, "p95": 30647.1, "p99": 30647.1, "p999": 30647.1}, "http.response_time.4xx": {"min": 26495, "max": 27130, "count": 3, "mean": 26844.7, "p50": 26643.2, "median": 26643.2, "p75": 26643.2, "p90": 26643.2, "p95": 26643.2, "p99": 26643.2, "p999": 26643.2}, "plugins.metrics-by-endpoint.response_time.GET /api/mugshots/[id]": {"min": 26495, "max": 27130, "count": 3, "mean": 26844.7, "p50": 26643.2, "median": 26643.2, "p75": 26643.2, "p90": 26643.2, "p95": 26643.2, "p99": 26643.2, "p999": 26643.2}, "plugins.metrics-by-endpoint.response_time.Browse mugshots": {"min": 24793, "max": 29160, "count": 4, "mean": 27156, "p50": 27181.5, "median": 27181.5, "p75": 27730.6, "p90": 27730.6, "p95": 27730.6, "p99": 27730.6, "p999": 27730.6}, "plugins.metrics-by-endpoint.response_time.View mugshot details": {"min": 27886, "max": 27886, "count": 1, "mean": 27886, "p50": 27730.6, "median": 27730.6, "p75": 27730.6, "p90": 27730.6, "p95": 27730.6, "p99": 27730.6, "p999": 27730.6}}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 764, "vusers.created": 968, "errors.Undefined function \"generateRandomFilters\"": 817, "http.requests": 970, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 488, "errors.ETIMEDOUT": 603, "vusers.failed": 603, "vusers.created_by_name.Mugshot Details API Load Test": 152, "errors.Undefined function \"generateRandomMugshotId\"": 152, "vusers.created_by_name.Realistic User Journey": 52, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 78, "plugins.metrics-by-endpoint.Browse with filters.errors.ETIMEDOUT": 7, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 30}, "rates": {"http.request_rate": 98}, "http.request_rate": null, "firstCounterAt": 1753533400002, "lastCounterAt": 1753533409992, "firstMetricAt": 1753533400002, "lastMetricAt": 1753533409992, "period": "1753533400000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 582, "errors.ETIMEDOUT": 722, "vusers.failed": 722, "vusers.created_by_name.Mugshots API Load Test": 878, "vusers.created": 1091, "errors.Undefined function \"generateRandomFilters\"": 936, "http.requests": 1091, "vusers.created_by_name.Mugshot Details API Load Test": 155, "errors.Undefined function \"generateRandomMugshotId\"": 155, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 28, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 110, "vusers.created_by_name.Realistic User Journey": 58, "plugins.metrics-by-endpoint.Browse with filters.errors.ETIMEDOUT": 1, "plugins.metrics-by-endpoint.View mugshot details.errors.ETIMEDOUT": 1}, "rates": {"http.request_rate": 111}, "http.request_rate": null, "firstCounterAt": 1753533410001, "lastCounterAt": 1753533419994, "firstMetricAt": 1753533410001, "lastMetricAt": 1753533419994, "period": "1753533410000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 180, "vusers.created": 1220, "errors.Undefined function \"generateRandomMugshotId\"": 180, "http.requests": 1215, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 631, "errors.ETIMEDOUT": 807, "vusers.failed": 3877, "vusers.created_by_name.Mugshots API Load Test": 977, "errors.Undefined function \"generateRandomFilters\"": 1040, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 130, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 43, "vusers.created_by_name.Realistic User Journey": 63, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 2453, "errors.ECONNREFUSED": 3070, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 457, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 158, "plugins.metrics-by-endpoint.View mugshot details.errors.ETIMEDOUT": 3, "plugins.metrics-by-endpoint.View mugshot details.errors.ECONNREFUSED": 1, "plugins.metrics-by-endpoint.Browse with filters.errors.ECONNREFUSED": 1}, "rates": {"http.request_rate": 123}, "http.request_rate": null, "firstCounterAt": 1753533420009, "lastCounterAt": 1753533429999, "firstMetricAt": 1753533420009, "lastMetricAt": 1753533429999, "period": "1753533420000", "summaries": {}, "histograms": {}}, {"counters": {"http.requests": 1339, "vusers.created_by_name.Mugshots API Load Test": 1072, "vusers.created": 1337, "errors.Undefined function \"generateRandomFilters\"": 1136, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 906, "errors.ECONNREFUSED": 1132, "vusers.failed": 1132, "vusers.created_by_name.Mugshot Details API Load Test": 201, "errors.Undefined function \"generateRandomMugshotId\"": 201, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 170, "vusers.created_by_name.Realistic User Journey": 64, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 56}, "rates": {"http.request_rate": 136}, "http.request_rate": null, "firstCounterAt": 1753533430000, "lastCounterAt": 1753533439999, "firstMetricAt": 1753533430000, "lastMetricAt": 1753533439999, "period": "1753533430000", "summaries": {}, "histograms": {}}, {"counters": {"http.requests": 1468, "vusers.created_by_name.Mugshots API Load Test": 1178, "vusers.created": 1465, "errors.Undefined function \"generateRandomFilters\"": 1253, "vusers.created_by_name.Mugshot Details API Load Test": 212, "errors.Undefined function \"generateRandomMugshotId\"": 212, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 431, "errors.ECONNREFUSED": 547, "vusers.failed": 547, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 83, "vusers.created_by_name.Realistic User Journey": 75, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 33}, "rates": {"http.request_rate": 147}, "http.request_rate": null, "firstCounterAt": 1753533440000, "lastCounterAt": 1753533449995, "firstMetricAt": 1753533440000, "lastMetricAt": 1753533449995, "period": "1753533440000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Realistic User Journey": 88, "vusers.created": 1587, "errors.Undefined function \"generateRandomFilters\"": 1343, "http.requests": 1587, "vusers.created_by_name.Mugshots API Load Test": 1255, "vusers.created_by_name.Mugshot Details API Load Test": 244, "errors.Undefined function \"generateRandomMugshotId\"": 244, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 197, "errors.ETIMEDOUT": 250, "vusers.failed": 250, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 38, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 15}, "rates": {"http.request_rate": 161}, "http.request_rate": null, "firstCounterAt": 1753533450008, "lastCounterAt": 1753533459990, "firstMetricAt": 1753533450008, "lastMetricAt": 1753533459990, "period": "1753533450000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1370, "vusers.created": 1713, "errors.Undefined function \"generateRandomFilters\"": 1446, "http.requests": 1713, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 165, "errors.ETIMEDOUT": 207, "vusers.failed": 207, "vusers.created_by_name.Mugshot Details API Load Test": 267, "errors.Undefined function \"generateRandomMugshotId\"": 267, "vusers.created_by_name.Realistic User Journey": 76, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 33, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 9}, "rates": {"http.request_rate": 172}, "http.request_rate": null, "firstCounterAt": 1753533460000, "lastCounterAt": 1753533469995, "firstMetricAt": 1753533460000, "lastMetricAt": 1753533469995, "period": "1753533460000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1464, "vusers.created": 1838, "errors.Undefined function \"generateRandomFilters\"": 1554, "http.requests": 1838, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 608, "errors.ETIMEDOUT": 741, "vusers.failed": 5976, "vusers.created_by_name.Mugshot Details API Load Test": 284, "errors.Undefined function \"generateRandomMugshotId\"": 284, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 98, "vusers.created_by_name.Realistic User Journey": 90, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 4167, "errors.ECONNREFUSED": 5235, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 813, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 255, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 35}, "rates": {"http.request_rate": 184}, "http.request_rate": null, "firstCounterAt": 1753533470006, "lastCounterAt": 1753533479998, "firstMetricAt": 1753533470006, "lastMetricAt": 1753533479998, "period": "1753533470000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1531, "vusers.created": 1951, "errors.Undefined function \"generateRandomFilters\"": 1638, "http.requests": 1951, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1334, "errors.ECONNREFUSED": 1714, "vusers.failed": 1714, "vusers.created_by_name.Realistic User Journey": 107, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 98, "vusers.created_by_name.Mugshot Details API Load Test": 313, "errors.Undefined function \"generateRandomMugshotId\"": 313, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 282}, "rates": {"http.request_rate": 196}, "http.request_rate": null, "firstCounterAt": 1753533480008, "lastCounterAt": 1753533489998, "firstMetricAt": 1753533480008, "lastMetricAt": 1753533489998, "period": "1753533480000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1584, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1694, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1404, "errors.ECONNREFUSED": 1774, "vusers.failed": 1775, "vusers.created_by_name.Realistic User Journey": 110, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 101, "vusers.created_by_name.Mugshot Details API Load Test": 306, "errors.Undefined function \"generateRandomMugshotId\"": 306, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 269, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.EADDRINUSE": 1, "errors.EADDRINUSE": 1}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533490002, "lastCounterAt": 1753533499999, "firstMetricAt": 1753533490002, "lastMetricAt": 1753533499999, "period": "1753533490000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1602, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1687, "http.requests": 2000, "vusers.created_by_name.Mugshot Details API Load Test": 313, "errors.Undefined function \"generateRandomMugshotId\"": 313, "vusers.created_by_name.Realistic User Journey": 85, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 61, "errors.ETIMEDOUT": 78, "vusers.failed": 416, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 12, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 59, "errors.ECONNREFUSED": 338, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 265, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 14}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533500002, "lastCounterAt": 1753533509996, "firstMetricAt": 1753533500002, "lastMetricAt": 1753533509996, "period": "1753533500000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 200, "errors.ETIMEDOUT": 242, "vusers.failed": 1607, "vusers.created_by_name.Mugshot Details API Load Test": 305, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 305, "http.requests": 2000, "vusers.created_by_name.Mugshots API Load Test": 1584, "errors.Undefined function \"generateRandomFilters\"": 1695, "vusers.created_by_name.Realistic User Journey": 111, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 10, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 32, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1092, "errors.ECONNREFUSED": 1365, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 214, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 59}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533510002, "lastCounterAt": 1753533519999, "firstMetricAt": 1753533510002, "lastMetricAt": 1753533519999, "period": "1753533510000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 206, "errors.ECONNREFUSED": 3710, "vusers.failed": 3933, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 2957, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 547, "vusers.created_by_name.Mugshots API Load Test": 1606, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1708, "http.requests": 2000, "vusers.created_by_name.Mugshot Details API Load Test": 292, "errors.Undefined function \"generateRandomMugshotId\"": 292, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 177, "errors.ETIMEDOUT": 222, "vusers.created_by_name.Realistic User Journey": 102, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 36, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 9, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.EADDRINUSE": 1, "errors.EADDRINUSE": 1}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533520000, "lastCounterAt": 1753533529999, "firstMetricAt": 1753533520000, "lastMetricAt": 1753533529999, "period": "1753533520000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 292, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 292, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 281, "errors.ECONNREFUSED": 1883, "vusers.failed": 2232, "vusers.created_by_name.Mugshots API Load Test": 1598, "errors.Undefined function \"generateRandomFilters\"": 1708, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1498, "vusers.created_by_name.Realistic User Journey": 110, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 104, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 286, "errors.ETIMEDOUT": 349, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 50, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 13}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533530002, "lastCounterAt": 1753533539999, "firstMetricAt": 1753533530002, "lastMetricAt": 1753533539999, "period": "1753533530000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 306, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 306, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 269, "errors.ECONNREFUSED": 1737, "vusers.failed": 1737, "vusers.created_by_name.Mugshots API Load Test": 1576, "errors.Undefined function \"generateRandomFilters\"": 1694, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1366, "vusers.created_by_name.Realistic User Journey": 118, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 102}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533540005, "lastCounterAt": 1753533549999, "firstMetricAt": 1753533540005, "lastMetricAt": 1753533549999, "period": "1753533540000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 294, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 294, "http.requests": 2000, "vusers.created_by_name.Mugshots API Load Test": 1592, "errors.Undefined function \"generateRandomFilters\"": 1706, "vusers.created_by_name.Realistic User Journey": 114, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 163, "errors.ETIMEDOUT": 192, "vusers.failed": 2199, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1596, "errors.ECONNREFUSED": 2007, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 301, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 110, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 24, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533550000, "lastCounterAt": 1753533559997, "firstMetricAt": 1753533550000, "lastMetricAt": 1753533559997, "period": "1753533550000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1597, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1688, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1500, "errors.ECONNREFUSED": 1872, "vusers.failed": 2037, "vusers.created_by_name.Mugshot Details API Load Test": 312, "errors.Undefined function \"generateRandomMugshotId\"": 312, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 288, "vusers.created_by_name.Realistic User Journey": 91, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 84, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 7, "errors.ETIMEDOUT": 165, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 131, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 27}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533560001, "lastCounterAt": 1753533569997, "firstMetricAt": 1753533560001, "lastMetricAt": 1753533569997, "period": "1753533560000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1607, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1706, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1502, "errors.ECONNREFUSED": 1872, "vusers.failed": 2005, "vusers.created_by_name.Mugshot Details API Load Test": 294, "errors.Undefined function \"generateRandomMugshotId\"": 294, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 273, "vusers.created_by_name.Realistic User Journey": 99, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 97, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 109, "errors.ETIMEDOUT": 133, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 14, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 10}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533570001, "lastCounterAt": 1753533579996, "firstMetricAt": 1753533570001, "lastMetricAt": 1753533579996, "period": "1753533570000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1592, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1699, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1540, "errors.ECONNREFUSED": 1936, "vusers.failed": 2059, "vusers.created_by_name.Mugshot Details API Load Test": 301, "errors.Undefined function \"generateRandomMugshotId\"": 301, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 291, "vusers.created_by_name.Realistic User Journey": 107, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 105, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 97, "errors.ETIMEDOUT": 123, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 16, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 10}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533580001, "lastCounterAt": 1753533589996, "firstMetricAt": 1753533580001, "lastMetricAt": 1753533589996, "period": "1753533580000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1623, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1707, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1512, "errors.ECONNREFUSED": 1872, "vusers.failed": 2000, "vusers.created_by_name.Realistic User Journey": 84, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 80, "vusers.created_by_name.Mugshot Details API Load Test": 293, "errors.Undefined function \"generateRandomMugshotId\"": 293, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 280, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 24, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 7, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 97}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533590001, "lastCounterAt": 1753533599997, "firstMetricAt": 1753533590001, "lastMetricAt": 1753533599997, "period": "1753533590000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1617, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1713, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1511, "errors.ECONNREFUSED": 1872, "vusers.failed": 2000, "vusers.created_by_name.Mugshot Details API Load Test": 287, "errors.Undefined function \"generateRandomMugshotId\"": 287, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 269, "vusers.created_by_name.Realistic User Journey": 96, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 92, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 105, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 21, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533600001, "lastCounterAt": 1753533609996, "firstMetricAt": 1753533600001, "lastMetricAt": 1753533609996, "period": "1753533600000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 293, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 293, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 278, "errors.ECONNREFUSED": 1933, "vusers.failed": 1997, "vusers.created_by_name.Mugshots API Load Test": 1623, "errors.Undefined function \"generateRandomFilters\"": 1707, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1573, "vusers.created_by_name.Realistic User Journey": 84, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 82, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 52, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 10, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533610001, "lastCounterAt": 1753533619995, "firstMetricAt": 1753533610001, "lastMetricAt": 1753533619995, "period": "1753533610000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1626, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1715, "http.requests": 2000, "vusers.created_by_name.Mugshot Details API Load Test": 285, "errors.Undefined function \"generateRandomMugshotId\"": 285, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1523, "errors.ECONNREFUSED": 1874, "vusers.failed": 2002, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 111, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 13, "vusers.created_by_name.Realistic User Journey": 89, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 84, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 267, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533620001, "lastCounterAt": 1753533629998, "firstMetricAt": 1753533620001, "lastMetricAt": 1753533629998, "period": "1753533620000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1619, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1717, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1566, "errors.ECONNREFUSED": 1937, "vusers.failed": 2065, "vusers.created_by_name.Mugshot Details API Load Test": 283, "errors.Undefined function \"generateRandomMugshotId\"": 283, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 276, "vusers.created_by_name.Realistic User Journey": 98, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 95, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 106, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 18, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533630001, "lastCounterAt": 1753533639996, "firstMetricAt": 1753533630001, "lastMetricAt": 1753533639996, "period": "1753533630000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1578, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1689, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1475, "errors.ECONNREFUSED": 1872, "vusers.failed": 1939, "vusers.created_by_name.Mugshot Details API Load Test": 311, "errors.Undefined function \"generateRandomMugshotId\"": 311, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 293, "vusers.created_by_name.Realistic User Journey": 111, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 104, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 67, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 15, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533640001, "lastCounterAt": 1753533649996, "firstMetricAt": 1753533640001, "lastMetricAt": 1753533649996, "period": "1753533640000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1626, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1717, "http.requests": 1998, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 102, "errors.ETIMEDOUT": 125, "vusers.failed": 2059, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1575, "errors.ECONNREFUSED": 1934, "vusers.created_by_name.Mugshot Details API Load Test": 283, "errors.Undefined function \"generateRandomMugshotId\"": 283, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 18, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 271, "vusers.created_by_name.Realistic User Journey": 91, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 88, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533650000, "lastCounterAt": 1753533659996, "firstMetricAt": 1753533650000, "lastMetricAt": 1753533659996, "period": "1753533650000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1621, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1709, "http.requests": 2002, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1513, "errors.ECONNREFUSED": 1874, "vusers.failed": 1938, "vusers.created_by_name.Realistic User Journey": 88, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 84, "vusers.created_by_name.Mugshot Details API Load Test": 291, "errors.Undefined function \"generateRandomMugshotId\"": 291, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 277, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 54, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 7, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533660000, "lastCounterAt": 1753533669996, "firstMetricAt": 1753533660000, "lastMetricAt": 1753533669996, "period": "1753533660000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1593, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1687, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1546, "errors.ECONNREFUSED": 1935, "vusers.failed": 2063, "vusers.created_by_name.Mugshot Details API Load Test": 313, "errors.Undefined function \"generateRandomMugshotId\"": 313, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 296, "vusers.created_by_name.Realistic User Journey": 94, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 93, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 103, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 18, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 7}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533670000, "lastCounterAt": 1753533679998, "firstMetricAt": 1753533670000, "lastMetricAt": 1753533679998, "period": "1753533670000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 315, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 315, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 297, "errors.ECONNREFUSED": 1872, "vusers.failed": 1936, "vusers.created_by_name.Mugshots API Load Test": 1597, "errors.Undefined function \"generateRandomFilters\"": 1685, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1491, "vusers.created_by_name.Realistic User Journey": 88, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 84, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 49, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 12}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533680000, "lastCounterAt": 1753533689997, "firstMetricAt": 1753533680000, "lastMetricAt": 1753533689997, "period": "1753533680000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1582, "vusers.created": 2001, "errors.Undefined function \"generateRandomFilters\"": 1685, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1534, "errors.ECONNREFUSED": 1937, "vusers.failed": 2065, "vusers.created_by_name.Mugshot Details API Load Test": 315, "errors.Undefined function \"generateRandomMugshotId\"": 315, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 301, "vusers.created_by_name.Realistic User Journey": 104, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 102, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 110, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 14, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533690000, "lastCounterAt": 1753533699999, "firstMetricAt": 1753533690000, "lastMetricAt": 1753533699999, "period": "1753533690000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 298, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 298, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 286, "errors.ECONNREFUSED": 1936, "vusers.failed": 2001, "vusers.created_by_name.Mugshots API Load Test": 1600, "errors.Undefined function \"generateRandomFilters\"": 1702, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1552, "vusers.created_by_name.Realistic User Journey": 102, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 98, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 47, "errors.ETIMEDOUT": 65, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 17, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 1}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533700000, "lastCounterAt": 1753533709999, "firstMetricAt": 1753533700000, "lastMetricAt": 1753533709999, "period": "1753533700000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1587, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1688, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1484, "errors.ECONNREFUSED": 1872, "vusers.failed": 2000, "vusers.created_by_name.Mugshot Details API Load Test": 313, "errors.Undefined function \"generateRandomMugshotId\"": 313, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 293, "vusers.created_by_name.Realistic User Journey": 100, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 95, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 106, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 18}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533710000, "lastCounterAt": 1753533719999, "firstMetricAt": 1753533710000, "lastMetricAt": 1753533719999, "period": "1753533710000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1595, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1705, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1545, "errors.ECONNREFUSED": 1936, "vusers.failed": 1999, "vusers.created_by_name.Realistic User Journey": 111, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 110, "vusers.created_by_name.Mugshot Details API Load Test": 294, "errors.Undefined function \"generateRandomMugshotId\"": 294, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 281, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 47, "errors.ETIMEDOUT": 63, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 14}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533720000, "lastCounterAt": 1753533729999, "firstMetricAt": 1753533720000, "lastMetricAt": 1753533729999, "period": "1753533720000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1628, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1734, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1580, "errors.ECONNREFUSED": 1935, "vusers.failed": 1999, "vusers.created_by_name.Realistic User Journey": 105, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 100, "vusers.created_by_name.Mugshot Details API Load Test": 267, "errors.Undefined function \"generateRandomMugshotId\"": 267, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 255, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 48, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 12, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533730000, "lastCounterAt": 1753533739999, "firstMetricAt": 1753533730000, "lastMetricAt": 1753533739999, "period": "1753533730000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1598, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1696, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1496, "errors.ECONNREFUSED": 1872, "vusers.failed": 2000, "vusers.created_by_name.Mugshot Details API Load Test": 304, "errors.Undefined function \"generateRandomMugshotId\"": 304, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 286, "vusers.created_by_name.Realistic User Journey": 98, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 90, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 104, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 20, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533740000, "lastCounterAt": 1753533749999, "firstMetricAt": 1753533740000, "lastMetricAt": 1753533749999, "period": "1753533740000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1585, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1689, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1533, "errors.ECONNREFUSED": 1937, "vusers.failed": 2001, "vusers.created_by_name.Realistic User Journey": 104, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 103, "vusers.created_by_name.Mugshot Details API Load Test": 311, "errors.Undefined function \"generateRandomMugshotId\"": 311, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 301, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 49, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 13, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533750000, "lastCounterAt": 1753533759999, "firstMetricAt": 1753533750000, "lastMetricAt": 1753533759999, "period": "1753533750000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1591, "vusers.created": 2000, "errors.Undefined function \"generateRandomFilters\"": 1696, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1537, "errors.ECONNREFUSED": 1936, "vusers.failed": 2001, "vusers.created_by_name.Mugshot Details API Load Test": 304, "errors.Undefined function \"generateRandomMugshotId\"": 304, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 297, "vusers.created_by_name.Realistic User Journey": 105, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 102, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 48, "errors.ETIMEDOUT": 65, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 12, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533760000, "lastCounterAt": 1753533769999, "firstMetricAt": 1753533760000, "lastMetricAt": 1753533769999, "period": "1753533760000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 292, "vusers.created": 2000, "errors.Undefined function \"generateRandomMugshotId\"": 292, "http.requests": 2000, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 282, "errors.ECONNREFUSED": 1936, "vusers.failed": 2064, "vusers.created_by_name.Mugshots API Load Test": 1621, "errors.Undefined function \"generateRandomFilters\"": 1708, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1569, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 102, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 8, "vusers.created_by_name.Realistic User Journey": 87, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 85, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 18}, "rates": {"http.request_rate": 200}, "http.request_rate": null, "firstCounterAt": 1753533770000, "lastCounterAt": 1753533779999, "firstMetricAt": 1753533770000, "lastMetricAt": 1753533779999, "period": "1753533770000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1487, "vusers.created": 1869, "errors.Undefined function \"generateRandomFilters\"": 1591, "http.requests": 1870, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1437, "errors.ECONNREFUSED": 1806, "vusers.failed": 1869, "vusers.created_by_name.Mugshot Details API Load Test": 278, "errors.Undefined function \"generateRandomMugshotId\"": 278, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 269, "vusers.created_by_name.Realistic User Journey": 104, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 100, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 10, "errors.ETIMEDOUT": 63, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 52, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 1}, "rates": {"http.request_rate": 189}, "http.request_rate": null, "firstCounterAt": 1753533780000, "lastCounterAt": 1753533789996, "firstMetricAt": 1753533780000, "lastMetricAt": 1753533789996, "period": "1753533780000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 215, "vusers.created": 1500, "errors.Undefined function \"generateRandomMugshotId\"": 215, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 194, "errors.ECONNREFUSED": 1372, "vusers.failed": 1436, "vusers.created_by_name.Mugshots API Load Test": 1203, "errors.Undefined function \"generateRandomFilters\"": 1285, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1100, "vusers.created_by_name.Realistic User Journey": 82, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 78, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 54, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 7, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533790001, "lastCounterAt": 1753533799996, "firstMetricAt": 1753533790001, "lastMetricAt": 1753533799996, "period": "1753533790000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 217, "vusers.created": 1500, "errors.Undefined function \"generateRandomMugshotId\"": 217, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 210, "errors.ECONNREFUSED": 1436, "vusers.failed": 1500, "vusers.created_by_name.Mugshots API Load Test": 1211, "errors.Undefined function \"generateRandomFilters\"": 1283, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1158, "vusers.created_by_name.Realistic User Journey": 72, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 68, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 9, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 53, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533800001, "lastCounterAt": 1753533809995, "firstMetricAt": 1753533800001, "lastMetricAt": 1753533809995, "period": "1753533800000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Realistic User Journey": 78, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1301, "http.requests": 1500, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 73, "errors.ECONNREFUSED": 1436, "vusers.failed": 1500, "vusers.created_by_name.Mugshots API Load Test": 1223, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1171, "vusers.created_by_name.Mugshot Details API Load Test": 199, "errors.Undefined function \"generateRandomMugshotId\"": 199, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 192, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 10, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533810001, "lastCounterAt": 1753533819996, "firstMetricAt": 1753533810001, "lastMetricAt": 1753533819996, "period": "1753533810000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1196, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1264, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1142, "errors.ECONNREFUSED": 1436, "vusers.failed": 1564, "vusers.created_by_name.Realistic User Journey": 68, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 64, "vusers.created_by_name.Mugshot Details API Load Test": 236, "errors.Undefined function \"generateRandomMugshotId\"": 236, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 230, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 103, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 21, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533820001, "lastCounterAt": 1753533829996, "firstMetricAt": 1753533820001, "lastMetricAt": 1753533829996, "period": "1753533820000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1230, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1293, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1178, "errors.ECONNREFUSED": 1436, "vusers.failed": 1500, "vusers.created_by_name.Mugshot Details API Load Test": 207, "errors.Undefined function \"generateRandomMugshotId\"": 207, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 198, "vusers.created_by_name.Realistic User Journey": 63, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 60, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 53, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 7}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533830001, "lastCounterAt": 1753533839996, "firstMetricAt": 1753533830001, "lastMetricAt": 1753533839996, "period": "1753533830000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 227, "vusers.created": 1500, "errors.Undefined function \"generateRandomMugshotId\"": 227, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 213, "errors.ECONNREFUSED": 1436, "vusers.failed": 1500, "vusers.created_by_name.Mugshots API Load Test": 1177, "errors.Undefined function \"generateRandomFilters\"": 1273, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1131, "vusers.created_by_name.Realistic User Journey": 96, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 92, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 52, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 7}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533840001, "lastCounterAt": 1753533849994, "firstMetricAt": 1753533840001, "lastMetricAt": 1753533849994, "period": "1753533840000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1216, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1278, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1110, "errors.ECONNREFUSED": 1372, "vusers.failed": 1436, "vusers.created_by_name.Mugshot Details API Load Test": 222, "errors.Undefined function \"generateRandomMugshotId\"": 222, "vusers.created_by_name.Realistic User Journey": 62, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 205, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 54, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 57, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 6}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533850000, "lastCounterAt": 1753533859994, "firstMetricAt": 1753533850000, "lastMetricAt": 1753533859994, "period": "1753533850000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1213, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1286, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1163, "errors.ECONNREFUSED": 1436, "vusers.failed": 1500, "vusers.created_by_name.Mugshot Details API Load Test": 214, "errors.Undefined function \"generateRandomMugshotId\"": 214, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 52, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 203, "vusers.created_by_name.Realistic User Journey": 73, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 9, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 70, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533860000, "lastCounterAt": 1753533869994, "firstMetricAt": 1753533860000, "lastMetricAt": 1753533869994, "period": "1753533860000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Realistic User Journey": 63, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1285, "http.requests": 1500, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 60, "errors.ECONNREFUSED": 1372, "vusers.failed": 1436, "vusers.created_by_name.Mugshots API Load Test": 1222, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1116, "vusers.created_by_name.Mugshot Details API Load Test": 215, "errors.Undefined function \"generateRandomMugshotId\"": 215, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 196, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 14, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 46, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533870000, "lastCounterAt": 1753533879994, "firstMetricAt": 1753533870000, "lastMetricAt": 1753533879994, "period": "1753533870000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 228, "vusers.created": 1500, "errors.Undefined function \"generateRandomMugshotId\"": 228, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 217, "errors.ECONNREFUSED": 1436, "vusers.failed": 1564, "vusers.created_by_name.Mugshots API Load Test": 1207, "errors.Undefined function \"generateRandomFilters\"": 1272, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1157, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 17, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 106, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 5, "vusers.created_by_name.Realistic User Journey": 65, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 62}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533880000, "lastCounterAt": 1753533889993, "firstMetricAt": 1753533880000, "lastMetricAt": 1753533889993, "period": "1753533880000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1162, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1258, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1112, "errors.ECONNREFUSED": 1436, "vusers.failed": 1500, "vusers.created_by_name.Mugshot Details API Load Test": 242, "errors.Undefined function \"generateRandomMugshotId\"": 242, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 231, "vusers.created_by_name.Realistic User Journey": 96, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 93, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 50, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 11}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533890000, "lastCounterAt": 1753533899994, "firstMetricAt": 1753533890000, "lastMetricAt": 1753533899994, "period": "1753533890000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Realistic User Journey": 86, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1282, "http.requests": 1500, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 84, "errors.ECONNREFUSED": 1436, "vusers.failed": 1564, "vusers.created_by_name.Mugshot Details API Load Test": 218, "errors.Undefined function \"generateRandomMugshotId\"": 218, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 209, "vusers.created_by_name.Mugshots API Load Test": 1196, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1143, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 106, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 19}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533900000, "lastCounterAt": 1753533909994, "firstMetricAt": 1753533900000, "lastMetricAt": 1753533909994, "period": "1753533900000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 231, "vusers.created": 1500, "errors.Undefined function \"generateRandomMugshotId\"": 231, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 210, "errors.ECONNREFUSED": 1372, "vusers.failed": 1436, "vusers.created_by_name.Mugshots API Load Test": 1188, "errors.Undefined function \"generateRandomFilters\"": 1269, "vusers.created_by_name.Realistic User Journey": 81, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1089, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 73, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 11, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533910001, "lastCounterAt": 1753533919995, "firstMetricAt": 1753533910001, "lastMetricAt": 1753533919995, "period": "1753533910000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1218, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1287, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1139, "errors.ECONNREFUSED": 1397, "vusers.failed": 1461, "vusers.created_by_name.Mugshot Details API Load Test": 213, "errors.Undefined function \"generateRandomMugshotId\"": 213, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 193, "vusers.created_by_name.Realistic User Journey": 69, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 65, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 11, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 50, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 3}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533920001, "lastCounterAt": 1753533929992, "firstMetricAt": 1753533920001, "lastMetricAt": 1753533929992, "period": "1753533920000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1176, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1246, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1103, "errors.ECONNREFUSED": 1411, "vusers.failed": 1475, "vusers.created_by_name.Mugshot Details API Load Test": 254, "errors.Undefined function \"generateRandomMugshotId\"": 254, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 240, "vusers.created_by_name.Realistic User Journey": 70, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 68, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 53, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 9}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533930001, "lastCounterAt": 1753533939994, "firstMetricAt": 1753533930001, "lastMetricAt": 1753533939994, "period": "1753533930000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 247, "vusers.created": 1500, "errors.Undefined function \"generateRandomMugshotId\"": 247, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 235, "errors.ECONNREFUSED": 1436, "vusers.failed": 1564, "vusers.created_by_name.Mugshots API Load Test": 1189, "errors.Undefined function \"generateRandomFilters\"": 1253, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1139, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 21, "errors.ETIMEDOUT": 128, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 99, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 8, "vusers.created_by_name.Realistic User Journey": 64, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 62}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533940001, "lastCounterAt": 1753533949994, "firstMetricAt": 1753533940001, "lastMetricAt": 1753533949994, "period": "1753533940000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1197, "vusers.created": 1500, "errors.Undefined function \"generateRandomFilters\"": 1257, "http.requests": 1500, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1144, "errors.ECONNREFUSED": 1436, "vusers.failed": 1539, "vusers.created_by_name.Mugshot Details API Load Test": 243, "errors.Undefined function \"generateRandomMugshotId\"": 243, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 233, "vusers.created_by_name.Realistic User Journey": 60, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 59, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 79, "errors.ETIMEDOUT": 103, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 4, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 20}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533950001, "lastCounterAt": 1753533959997, "firstMetricAt": 1753533950001, "lastMetricAt": 1753533959997, "period": "1753533950000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1190, "vusers.created": 1492, "errors.Undefined function \"generateRandomFilters\"": 1268, "http.requests": 1492, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 73, "errors.ETIMEDOUT": 89, "vusers.failed": 1451, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 1085, "errors.ECONNREFUSED": 1362, "vusers.created_by_name.Mugshot Details API Load Test": 224, "errors.Undefined function \"generateRandomMugshotId\"": 224, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 205, "vusers.created_by_name.Realistic User Journey": 78, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 72, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 14, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 150}, "http.request_rate": null, "firstCounterAt": 1753533960001, "lastCounterAt": 1753533969998, "firstMetricAt": 1753533960001, "lastMetricAt": 1753533969998, "period": "1753533960000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 1054, "vusers.created": 1320, "errors.Undefined function \"generateRandomFilters\"": 1122, "http.requests": 1320, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 958, "errors.ECONNREFUSED": 1194, "vusers.failed": 1258, "vusers.created_by_name.Mugshot Details API Load Test": 198, "errors.Undefined function \"generateRandomMugshotId\"": 198, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 175, "vusers.created_by_name.Realistic User Journey": 68, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 61, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 50, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 12, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 2}, "rates": {"http.request_rate": 133}, "http.request_rate": null, "firstCounterAt": 1753533970006, "lastCounterAt": 1753533979989, "firstMetricAt": 1753533970006, "lastMetricAt": 1753533979989, "period": "1753533970000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshot Details API Load Test": 180, "vusers.created": 1090, "errors.Undefined function \"generateRandomMugshotId\"": 180, "http.requests": 1090, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 170, "errors.ECONNREFUSED": 1026, "vusers.failed": 1090, "vusers.created_by_name.Mugshots API Load Test": 865, "errors.Undefined function \"generateRandomFilters\"": 910, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 812, "vusers.created_by_name.Realistic User Journey": 45, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 44, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 53, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 10, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 1}, "rates": {"http.request_rate": 110}, "http.request_rate": null, "firstCounterAt": 1753533980003, "lastCounterAt": 1753533989988, "firstMetricAt": 1753533980003, "lastMetricAt": 1753533989988, "period": "1753533980000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 686, "vusers.created": 852, "errors.Undefined function \"generateRandomFilters\"": 735, "http.requests": 852, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 582, "errors.ECONNREFUSED": 724, "vusers.failed": 854, "vusers.created_by_name.Mugshot Details API Load Test": 117, "errors.Undefined function \"generateRandomMugshotId\"": 117, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 105, "errors.ETIMEDOUT": 130, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 19, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 99, "vusers.created_by_name.Realistic User Journey": 49, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 43, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 6}, "rates": {"http.request_rate": 87}, "http.request_rate": null, "firstCounterAt": 1753533990001, "lastCounterAt": 1753533999948, "firstMetricAt": 1753533990001, "lastMetricAt": 1753533999948, "period": "1753533990000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 502, "vusers.created": 625, "errors.Undefined function \"generateRandomFilters\"": 529, "http.requests": 625, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 395, "errors.ECONNREFUSED": 495, "vusers.failed": 621, "vusers.created_by_name.Mugshot Details API Load Test": 96, "errors.Undefined function \"generateRandomMugshotId\"": 96, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 80, "vusers.created_by_name.Realistic User Journey": 27, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 96, "errors.ETIMEDOUT": 126, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 7, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 23, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 20}, "rates": {"http.request_rate": 64}, "http.request_rate": null, "firstCounterAt": 1753534000008, "lastCounterAt": 1753534009998, "firstMetricAt": 1753534000008, "lastMetricAt": 1753534009998, "period": "1753534000000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 212, "errors.ECONNREFUSED": 259, "vusers.failed": 323, "vusers.created_by_name.Mugshots API Load Test": 309, "vusers.created": 388, "errors.Undefined function \"generateRandomFilters\"": 330, "http.requests": 388, "vusers.created_by_name.Mugshot Details API Load Test": 58, "errors.Undefined function \"generateRandomMugshotId\"": 58, "vusers.created_by_name.Realistic User Journey": 21, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 34, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 53, "errors.ETIMEDOUT": 64, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 10, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 13, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 1}, "rates": {"http.request_rate": 40}, "http.request_rate": null, "firstCounterAt": 1753534010000, "lastCounterAt": 1753534019985, "firstMetricAt": 1753534010000, "lastMetricAt": 1753534019985, "period": "1753534010000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Mugshots API Load Test": 122, "vusers.created": 149, "errors.Undefined function \"generateRandomFilters\"": 132, "http.requests": 149, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 104, "errors.ETIMEDOUT": 128, "vusers.failed": 177, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 18, "vusers.created_by_name.Realistic User Journey": 10, "plugins.metrics-by-endpoint.Browse mugshots.errors.ECONNREFUSED": 2, "errors.ECONNREFUSED": 49, "vusers.created_by_name.Mugshot Details API Load Test": 17, "errors.Undefined function \"generateRandomMugshotId\"": 17, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ECONNREFUSED": 6, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ECONNREFUSED": 41, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 6}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1753534020045, "lastCounterAt": 1753534028313, "firstMetricAt": 1753534020045, "lastMetricAt": 1753534028313, "period": "1753534020000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 105, "errors.ETIMEDOUT": 128, "vusers.failed": 128, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 16, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 7}, "rates": {}, "firstCounterAt": 1753534030381, "lastCounterAt": 1753534036553, "firstMetricAt": 1753534030381, "lastMetricAt": 1753534036553, "period": "1753534030000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 98, "errors.ETIMEDOUT": 130, "vusers.failed": 130, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 24, "plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 8}, "rates": {}, "firstCounterAt": 1753534040080, "lastCounterAt": 1753534049970, "firstMetricAt": 1753534040080, "lastMetricAt": 1753534049970, "period": "1753534040000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint.Browse mugshots.errors.ETIMEDOUT": 8, "errors.ETIMEDOUT": 101, "vusers.failed": 101, "plugins.metrics-by-endpoint.GET /api/mugshots.errors.ETIMEDOUT": 82, "plugins.metrics-by-endpoint.GET /api/mugshots/[id].errors.ETIMEDOUT": 11}, "rates": {}, "firstCounterAt": 1753534050000, "lastCounterAt": 1753534058315, "firstMetricAt": 1753534050000, "lastMetricAt": 1753534058315, "period": "1753534050000", "summaries": {}, "histograms": {}}]}