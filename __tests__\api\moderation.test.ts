import { describe, it, expect, beforeEach, vi } from 'vitest'
import { NextRequest } from 'next/server'

// Mock the Supabase client
vi.mock('@supabase/auth-helpers-nextjs', () => ({
  createServerComponentClient: vi.fn(() => ({
    auth: {
      getUser: vi.fn(() => Promise.resolve({
        data: { user: { id: 'admin_user_id' } },
        error: null
      }))
    }
  }))
}))

vi.mock('next/headers', () => ({
  cookies: vi.fn()
}))

describe('Moderation API Routes', () => {
  let mockRequest: NextRequest

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Create mock request
    mockRequest = {
      json: vi.fn(),
      url: 'http://localhost:3000/api/admin/moderation/reports',
      headers: new Headers(),
      method: 'GET'
    } as unknown as NextRequest
  })

  describe('/api/reports', () => {
    it('should create a content report', async () => {
      const { POST } = await import('@/app/api/reports/route')
      
      const requestBody = {
        content_type: 'mugshot',
        content_id: '12345',
        reason: 'inappropriate_content',
        description: 'This content violates guidelines'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.report).toBeDefined()
      expect(data.report.content_type).toBe('mugshot')
      expect(data.report.reason).toBe('inappropriate_content')
    })

    it('should reject report with missing required fields', async () => {
      const { POST } = await import('@/app/api/reports/route')
      
      const requestBody = {
        content_type: 'mugshot'
        // Missing content_id and reason
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Missing required fields')
    })

    it('should reject report with invalid content type', async () => {
      const { POST } = await import('@/app/api/reports/route')
      
      const requestBody = {
        content_type: 'invalid_type',
        content_id: '12345',
        reason: 'inappropriate_content'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid content type or reason')
    })

    it('should reject report with invalid reason', async () => {
      const { POST } = await import('@/app/api/reports/route')
      
      const requestBody = {
        content_type: 'mugshot',
        content_id: '12345',
        reason: 'invalid_reason'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid content type or reason')
    })

    it('should get user reports', async () => {
      const { GET } = await import('@/app/api/reports/route')

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.reports).toBeDefined()
      expect(Array.isArray(data.reports)).toBe(true)
    })
  })

  describe('/api/admin/moderation/reports', () => {
    it('should get all reports for admin', async () => {
      const { GET } = await import('@/app/api/admin/moderation/reports/route')

      // Mock URL with query parameters
      mockRequest.url = 'http://localhost:3000/api/admin/moderation/reports?page=1&limit=20'

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.reports).toBeDefined()
      expect(data.pagination).toBeDefined()
      expect(data.pagination.page).toBe(1)
      expect(data.pagination.limit).toBe(20)
    })

    it('should filter reports by status', async () => {
      const { GET } = await import('@/app/api/admin/moderation/reports/route')

      mockRequest.url = 'http://localhost:3000/api/admin/moderation/reports?status=pending'

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.reports).toBeDefined()
      // In a real implementation, all reports would have status 'pending'
    })

    it('should filter reports by priority', async () => {
      const { GET } = await import('@/app/api/admin/moderation/reports/route')

      mockRequest.url = 'http://localhost:3000/api/admin/moderation/reports?priority=urgent'

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.reports).toBeDefined()
    })

    it('should search reports by text', async () => {
      const { GET } = await import('@/app/api/admin/moderation/reports/route')

      mockRequest.url = 'http://localhost:3000/api/admin/moderation/reports?search=harassment'

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.reports).toBeDefined()
    })
  })

  describe('/api/admin/moderation/actions', () => {
    it('should perform moderation action on reports', async () => {
      const { POST } = await import('@/app/api/admin/moderation/actions/route')
      
      const requestBody = {
        action: 'approve',
        report_ids: ['report_1', 'report_2'],
        reason: 'Content reviewed and approved'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.results).toBeDefined()
      expect(data.results).toHaveLength(2)
    })

    it('should reject action with missing report IDs', async () => {
      const { POST } = await import('@/app/api/admin/moderation/actions/route')
      
      const requestBody = {
        action: 'approve'
        // Missing report_ids
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Missing required fields')
    })

    it('should reject invalid action type', async () => {
      const { POST } = await import('@/app/api/admin/moderation/actions/route')
      
      const requestBody = {
        action: 'invalid_action',
        report_ids: ['report_1']
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid action')
    })

    it('should handle bulk actions', async () => {
      const { POST } = await import('@/app/api/admin/moderation/actions/route')
      
      const requestBody = {
        action: 'dismiss',
        report_ids: ['report_1', 'report_2', 'report_3', 'report_4'],
        reason: 'Bulk dismissal of false reports'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.results).toHaveLength(4)
      expect(data.message).toContain('4 report(s)')
    })
  })

  describe('/api/admin/moderation/export', () => {
    it('should export reports as CSV', async () => {
      const { POST } = await import('@/app/api/admin/moderation/export/route')
      
      const requestBody = {
        filters: {
          status: 'pending',
          priority: 'high'
        }
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)

      expect(response.status).toBe(200)
      expect(response.headers.get('Content-Type')).toBe('text/csv')
      expect(response.headers.get('Content-Disposition')).toContain('attachment')
      expect(response.headers.get('Content-Disposition')).toContain('.csv')
    })

    it('should export all reports when no filters provided', async () => {
      const { POST } = await import('@/app/api/admin/moderation/export/route')
      
      const requestBody = {
        filters: {}
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)

      expect(response.status).toBe(200)
      expect(response.headers.get('Content-Type')).toBe('text/csv')
    })
  })

  describe('/api/auto-flag', () => {
    it('should analyze content and return flagging result', async () => {
      const { POST } = await import('@/app/api/auto-flag/route')
      
      const requestBody = {
        content_type: 'user_comment',
        content_id: 'comment_123',
        text_content: 'This is test content for analysis'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.flagging_result).toBeDefined()
      expect(typeof data.flagging_result.should_flag).toBe('boolean')
      expect(typeof data.flagging_result.confidence_score).toBe('number')
      expect(data.flagging_result.severity).toBeDefined()
    })

    it('should flag inappropriate content', async () => {
      const { POST } = await import('@/app/api/auto-flag/route')
      
      const requestBody = {
        content_type: 'user_comment',
        content_id: 'comment_bad',
        text_content: 'This contains harassment and threats against users'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.flagging_result.should_flag).toBe(true)
      expect(data.flagging_result.confidence_score).toBeGreaterThan(0)
    })

    it('should not flag appropriate content', async () => {
      const { POST } = await import('@/app/api/auto-flag/route')
      
      const requestBody = {
        content_type: 'user_comment',
        content_id: 'comment_good',
        text_content: 'This is perfectly normal and appropriate content'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.flagging_result.should_flag).toBe(false)
      expect(data.flagging_result.confidence_score).toBe(0)
    })

    it('should reject content with missing required fields', async () => {
      const { POST } = await import('@/app/api/auto-flag/route')
      
      const requestBody = {
        content_type: 'user_comment'
        // Missing content_id
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('content_type and content_id are required')
    })

    it('should get auto-flagging statistics', async () => {
      const { GET } = await import('@/app/api/auto-flag/route')

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.active_rules).toBeDefined()
      expect(data.statistics).toBeDefined()
      expect(data.statistics.total_rules).toBeDefined()
      expect(data.statistics.rules_by_type).toBeDefined()
      expect(data.statistics.rules_by_priority).toBeDefined()
    })
  })

  describe('Authentication and Authorization', () => {
    it('should require authentication for protected routes', async () => {
      // Mock unauthenticated user
      const mockSupabase = {
        auth: {
          getUser: vi.fn(() => Promise.resolve({
            data: { user: null },
            error: { message: 'Not authenticated' }
          }))
        }
      }

      vi.mocked(await import('@supabase/auth-helpers-nextjs')).createServerComponentClient = vi.fn(() => mockSupabase)

      const { POST } = await import('@/app/api/reports/route')
      
      const requestBody = {
        content_type: 'mugshot',
        content_id: '12345',
        reason: 'inappropriate_content'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed JSON in request body', async () => {
      const { POST } = await import('@/app/api/reports/route')

      mockRequest.json = vi.fn().mockRejectedValue(new Error('Invalid JSON'))

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })

    it('should handle database connection errors gracefully', async () => {
      // Mock database error
      const mockSupabase = {
        auth: {
          getUser: vi.fn(() => Promise.reject(new Error('Database connection failed')))
        }
      }

      vi.mocked(await import('@supabase/auth-helpers-nextjs')).createServerComponentClient = vi.fn(() => mockSupabase)

      const { GET } = await import('@/app/api/reports/route')

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('Input Validation', () => {
    it('should validate content_type enum values', async () => {
      const validContentTypes = ['mugshot', 'user_tag', 'user_comment', 'user_profile']
      
      for (const contentType of validContentTypes) {
        const { POST } = await import('@/app/api/reports/route')
        
        const requestBody = {
          content_type: contentType,
          content_id: '12345',
          reason: 'inappropriate_content'
        }

        mockRequest.json = vi.fn().mockResolvedValue(requestBody)

        const response = await POST(mockRequest)
        expect(response.status).toBe(200)
      }
    })

    it('should validate reason enum values', async () => {
      const validReasons = [
        'inappropriate_content', 'spam', 'harassment', 'copyright', 
        'violence', 'hate_speech', 'fake_content', 'privacy_violation', 'other'
      ]
      
      for (const reason of validReasons) {
        const { POST } = await import('@/app/api/reports/route')
        
        const requestBody = {
          content_type: 'mugshot',
          content_id: '12345',
          reason: reason
        }

        mockRequest.json = vi.fn().mockResolvedValue(requestBody)

        const response = await POST(mockRequest)
        expect(response.status).toBe(200)
      }
    })

    it('should sanitize and validate content IDs', async () => {
      const { POST } = await import('@/app/api/reports/route')
      
      const requestBody = {
        content_type: 'mugshot',
        content_id: '<script>alert("xss")</script>',
        reason: 'inappropriate_content'
      }

      mockRequest.json = vi.fn().mockResolvedValue(requestBody)

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      // Content ID should be treated as string, not executed
      expect(data.report.content_id).toBe('<script>alert("xss")</script>')
    })
  })
}) 