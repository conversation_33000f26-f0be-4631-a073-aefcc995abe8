import { ArrowLeft, Search } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import UniversalNavLink from '@/components/UniversalNavLink'

export default function MugshotNotFound() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <Card className="bg-gray-900/90 border-cyan-500/30 w-full max-w-md">
            <CardContent className="p-8">
              <div className="mb-6">
                <Search className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                <h1 className="text-3xl font-bold text-white mb-2">
                  Mugshot Not Found
                </h1>
                <p className="text-gray-400 text-lg">
                  The mugshot you&apos;re looking for doesn&apos;t exist or may have been removed.
                </p>
              </div>

              <div className="space-y-4">
                <p className="text-sm text-gray-500">
                  This could happen if:
                </p>
                <ul className="text-sm text-gray-400 text-left space-y-1">
                  <li>• The mugshot ID is invalid</li>
                  <li>• The record has been removed</li>
                  <li>• You followed a broken link</li>
                </ul>
              </div>

              <div className="mt-8 space-y-3">
                <UniversalNavLink href="/mugshots" className="block">
                  <Button 
                    className="w-full bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white"
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Browse All Mugshots
                  </Button>
                </UniversalNavLink>
                
                <UniversalNavLink href="/" className="block">
                  <Button 
                    variant="outline" 
                    className="w-full border-cyan-500/30 text-white hover:bg-gray-800"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Home
                  </Button>
                </UniversalNavLink>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 