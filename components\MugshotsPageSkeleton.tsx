import { Skeleton } from "@/components/ui/skeleton"

export default function MugshotsPageSkeleton() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Page Header Skeleton */}
        <div className="text-center mb-12">
          <Skeleton className="h-12 w-80 bg-gray-700 mx-auto mb-4" />
          <Skeleton className="h-6 w-96 bg-gray-700 mx-auto" />
        </div>

        {/* Filters Skeleton */}
        <div className="card-neon mb-8">
          <div className="space-y-6">
            {/* First Row - Search, Location, and Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 lg:gap-6">
              {/* Search Input Skeleton */}
              <div className="lg:col-span-2 space-y-2">
                <Skeleton className="h-4 w-24 bg-gray-700" />
                <Skeleton className="h-10 w-full bg-gray-700" />
              </div>

              {/* Location Dropdown Skeleton */}
              <div className="md:col-span-2 lg:col-span-2">
                <div className="flex flex-row gap-4 w-full">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-4 w-12 bg-gray-700" />
                      <div className="animate-spin h-3 w-3 border border-cyan-400 border-t-transparent rounded-full"></div>
                    </div>
                    <Skeleton className="h-10 w-full bg-gray-700" />
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-4 w-16 bg-gray-700" />
                      <div className="animate-spin h-3 w-3 border border-cyan-400 border-t-transparent rounded-full"></div>
                    </div>
                    <Skeleton className="h-10 w-full bg-gray-700" />
                  </div>
                </div>
              </div>

              {/* Date Range Skeleton */}
              <div className="md:col-span-2 lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20 bg-gray-700" />
                  <Skeleton className="h-10 w-full bg-gray-700" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16 bg-gray-700" />
                  <Skeleton className="h-10 w-full bg-gray-700" />
                </div>
              </div>
            </div>

            {/* Second Row - Sort & Per Page, Categories, Grid View */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 lg:gap-6 items-end">
              {/* Sort By and Per Page Skeleton */}
              <div className="lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16 bg-gray-700" />
                  <Skeleton className="h-10 w-full bg-gray-700" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20 bg-gray-700" />
                  <Skeleton className="h-10 w-full bg-gray-700" />
                </div>
              </div>

              {/* Category Filter Skeleton */}
              <div className="md:col-span-2 lg:col-span-3 space-y-2">
                <Skeleton className="h-4 w-20 bg-gray-700 mx-auto" />
                <div className="flex flex-wrap gap-2 justify-center">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className="h-8 w-20 bg-gray-700 rounded-lg" />
                  ))}
                </div>
              </div>

              {/* Grid View Skeleton */}
              <div className="lg:col-span-1 space-y-2">
                <Skeleton className="h-4 w-16 bg-gray-700 ml-auto" />
                <div className="flex gap-2 justify-center lg:justify-end">
                  <Skeleton className="h-8 w-8 bg-gray-700 rounded" />
                  <Skeleton className="h-8 w-8 bg-gray-700 rounded" />
                </div>
              </div>
            </div>

            {/* Clear All Filters Button Skeleton */}
            <div className="flex justify-center pt-4">
              <Skeleton className="h-10 w-32 bg-gray-700 rounded" />
            </div>
          </div>
        </div>

        {/* Results Count Skeleton */}
        <div className="mb-6 text-center px-4">
          <Skeleton className="h-6 w-64 bg-gray-700 mx-auto mb-2" />
          <Skeleton className="h-4 w-48 bg-gray-700 mx-auto" />
        </div>

        {/* Grid Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="w-full h-80 bg-gray-700 rounded-lg" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-3/4 bg-gray-700 mx-auto" />
                <Skeleton className="h-3 w-1/2 bg-gray-700 mx-auto" />
              </div>
              <div className="space-y-1">
                <Skeleton className="h-3 w-full bg-gray-700" />
                <Skeleton className="h-3 w-2/3 bg-gray-700" />
              </div>
              <div className="flex justify-center space-x-1">
                {Array.from({ length: 5 }).map((_, starIndex) => (
                  <Skeleton key={starIndex} className="h-4 w-4 bg-gray-700 rounded-full" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 