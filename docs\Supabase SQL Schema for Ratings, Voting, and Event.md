<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

### Supabase SQL Schema for Ratings, Voting, and Events

Based on the provided database structure requirements[^1], below is a complete, robust, and scalable SQL script for Supabase (PostgreSQL). This includes `CREATE TABLE` statements for the proposed tables (Ratings, Tags, Votes, Events, and an optional Winners table). I've used `CREATE OR REPLACE` where applicable for functions to allow updates without errors.

To ensure scalability:

- Tables use UUIDs for primary keys to handle large-scale data.
- Indexes are added for frequent queries (e.g., filtering by mugshot_id or event_type).
- Constraints enforce data integrity (e.g., unique ratings per user/mugshot).
- Triggers and functions automate validations (e.g., rating windows, one-vote limits) and winner calculations.
- JSONB is used for dynamic configurations in the Events table to allow rule changes without schema alterations.
- Assumptions: This builds on an existing `mugshots` table with columns like `id` (uuid) and `arrest_date` (date). Also assumes a users table (e.g., from Supabase auth) with `id` (uuid).

Run this script in the Supabase SQL Editor or via the `psql` CLI. For production, enable Row-Level Security (RLS) on tables to restrict access.

#### 1. Create Tables

```sql
-- Ratings Table
CREATE TABLE IF NOT EXISTS ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    mugshot_id UUID NOT NULL REFERENCES mugshots(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,  -- Assuming references auth.users(id)
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Unique index to prevent duplicate ratings
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_rating ON ratings (mugshot_id, user_id);

-- Tags Table
CREATE TABLE IF NOT EXISTS tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    mugshot_id UUID NOT NULL REFERENCES mugshots(id) ON DELETE CASCADE,
    user_id UUID,  -- Optional, if user-assigned
    tag_type TEXT NOT NULL CHECK (tag_type IN ('funny', 'wild', 'spooky')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Composite index for filtering
CREATE INDEX IF NOT EXISTS idx_tags_mugshot_type ON tags (mugshot_id, tag_type);

-- Votes Table
CREATE TABLE IF NOT EXISTS votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    mugshot_id UUID NOT NULL REFERENCES mugshots(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Unique index to prevent multiple votes per user per event
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_vote ON votes (event_id, user_id);

-- Events Table
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type TEXT NOT NULL CHECK (event_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE,
    voting_start TIMESTAMP WITH TIME ZONE,
    voting_end TIMESTAMP WITH TIME ZONE,
    participants UUID[],  -- Array of eligible mugshot_ids
    winners UUID[],  -- Array of winning mugshot_ids
    config JSONB,  -- Dynamic rules, e.g., {"duration_hours": 24, "vote_limit": 1}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed'))
);

-- Index for quick queries
CREATE INDEX IF NOT EXISTS idx_events_type_start ON events (event_type, start_date);

-- Winners Table (Optional for quick access)
CREATE TABLE IF NOT EXISTS winners (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    mugshot_id UUID NOT NULL REFERENCES mugshots(id) ON DELETE CASCADE,
    rank INTEGER DEFAULT 1,
    score NUMERIC,
    awarded_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```


#### 2. Functions

These are `CREATE OR REPLACE FUNCTION` statements for key operations like validating ratings, casting votes, and computing winners. They are designed to be called from Supabase edge functions or directly in queries.

```sql
-- Function to Validate and Insert Rating
CREATE OR REPLACE FUNCTION insert_rating(p_mugshot_id UUID, p_user_id UUID, p_rating INTEGER)
RETURNS VOID AS $$
DECLARE
    arrest_date DATE;
    enabled_time TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Fetch arrest_date from mugshots
    SELECT arrest_date INTO arrest_date FROM mugshots WHERE id = p_mugshot_id;
    
    IF arrest_date IS NULL THEN
        RAISE EXCEPTION 'Mugshot not found';
    END IF;
    
    -- Enabled at midnight the day after arrest_date
    enabled_time := (arrest_date + INTERVAL '1 day')::DATE + INTERVAL '0 hours';
    
    IF now() < enabled_time THEN
        RAISE EXCEPTION 'Rating not yet enabled for this mugshot';
    END IF;
    
    -- Insert if no duplicate (unique index will also enforce)
    INSERT INTO ratings (mugshot_id, user_id, rating)
    VALUES (p_mugshot_id, p_user_id, p_rating)
    ON CONFLICT DO NOTHING;  -- Silently ignore duplicates
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to Validate and Insert Vote
CREATE OR REPLACE FUNCTION insert_vote(p_event_id UUID, p_mugshot_id UUID, p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    v_start TIMESTAMP WITH TIME ZONE;
    v_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Fetch voting window
    SELECT voting_start, voting_end INTO v_start, v_end FROM events WHERE id = p_event_id;
    
    IF v_start IS NULL OR now() < v_start OR now() > v_end THEN
        RAISE EXCEPTION 'Voting not open for this event';
    END IF;
    
    -- Check if mugshot is a participant
    IF NOT EXISTS (SELECT 1 FROM events WHERE id = p_event_id AND p_mugshot_id = ANY(participants)) THEN
        RAISE EXCEPTION 'Mugshot not eligible for this event';
    END IF;
    
    -- Insert if no duplicate (unique index enforces)
    INSERT INTO votes (event_id, mugshot_id, user_id)
    VALUES (p_event_id, p_mugshot_id, p_user_id)
    ON CONFLICT DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to Compute Daily Winners (Based on Average Ratings from First Day)
CREATE OR REPLACE FUNCTION compute_daily_winners(p_event_id UUID)
RETURNS VOID AS $$
DECLARE
    v_start_date DATE;
    v_enabled_start TIMESTAMP WITH TIME ZONE;
    v_enabled_end TIMESTAMP WITH TIME ZONE;
BEGIN
    SELECT start_date INTO v_start_date FROM events WHERE id = p_event_id;
    
    -- Ratings from midnight day after to next midnight (first day only)
    v_enabled_start := (v_start_date + INTERVAL '1 day')::DATE + INTERVAL '0 hours';
    v_enabled_end := v_enabled_start + INTERVAL '1 day';
    
    -- Update winners: Mugshots with max average rating
    UPDATE events
    SET winners = (
        SELECT ARRAY_AGG(mugshot_id)
        FROM (
            SELECT r.mugshot_id, AVG(r.rating) AS avg_rating
            FROM ratings r
            JOIN mugshots m ON r.mugshot_id = m.id
            WHERE m.arrest_date = v_start_date
              AND r.created_at >= v_enabled_start
              AND r.created_at < v_enabled_end
            GROUP BY r.mugshot_id
            HAVING AVG(r.rating) = (
                SELECT MAX(sub.avg) FROM (
                    SELECT AVG(r2.rating) AS avg
                    FROM ratings r2 JOIN mugshots m2 ON r2.mugshot_id = m2.id
                    WHERE m2.arrest_date = v_start_date
                      AND r2.created_at >= v_enabled_start
                      AND r2.created_at < v_enabled_end
                    GROUP BY r2.mugshot_id
                ) sub
            )
        ) AS winners
    )
    WHERE id = p_event_id;
    
    -- Set status to completed
    UPDATE events SET status = 'completed' WHERE id = p_event_id;
END;
$$ LANGUAGE plpgsql;

-- Function to Compute Winners for Voting-Based Events (Weekly+)
CREATE OR REPLACE FUNCTION compute_voting_winners(p_event_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Update winners: Mugshots with max vote count
    UPDATE events
    SET winners = (
        SELECT ARRAY_AGG(mugshot_id)
        FROM (
            SELECT mugshot_id, COUNT(*) AS vote_count
            FROM votes
            WHERE event_id = p_event_id
            GROUP BY mugshot_id
            HAVING COUNT(*) = (
                SELECT MAX(sub.cnt) FROM (
                    SELECT COUNT(*) AS cnt FROM votes WHERE event_id = p_event_id GROUP BY mugshot_id
                ) sub
            )
        ) AS winners
    )
    WHERE id = p_event_id;
    
    -- Set status to completed
    UPDATE events SET status = 'completed' WHERE id = p_event_id;
END;
$$ LANGUAGE plpgsql;
```


#### 3. Triggers

These automate actions like updating timestamps or validating on insert/update.

```sql
-- Trigger for Updating Timestamps in Ratings
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_ratings_timestamp
BEFORE UPDATE ON ratings
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Trigger to Auto-Populate Winners Table After Event Completion
CREATE OR REPLACE FUNCTION populate_winners()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        INSERT INTO winners (event_id, mugshot_id, score)
        SELECT NEW.id, unnest(NEW.winners), NULL;  -- Score can be calculated if needed
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_populate_winners
AFTER UPDATE ON events
FOR EACH ROW EXECUTE FUNCTION populate_winners();
```


#### Implementation Notes

- **Automation**: Use Supabase's pg_cron extension for scheduling (e.g., daily jobs to create events and call `compute_daily_winners`). Example: `SELECT cron.schedule('0 0 * * *', 'SELECT compute_daily_winners(event_id) FROM events WHERE event_type = ''daily'' AND status = ''active'';');`
- **Dynamic Rules**: Update the `config` JSONB in Events via admin queries, e.g., to change voting durations.
- **Scalability Tips**: For high traffic, add partitioning on `created_at` for large tables like ratings/votes. Monitor with Supabase analytics and scale via read replicas if needed.
- **Testing**: Test functions with sample data, e.g., insert a mugshot, rate it, create an event, and compute winners.
- **Extensions**: If tags become votable, add a similar votes table linked to tags.

This setup enforces the required rules (e.g., rating windows, one-per-user limits, tiered events) while being flexible for changes[^1]. If your `mugshots` or users schema differs, adjust foreign keys accordingly.

<div style="text-align: center">⁂</div>

[^1]: Database-Table-Structure-for-Ratings-Voting-and.md

