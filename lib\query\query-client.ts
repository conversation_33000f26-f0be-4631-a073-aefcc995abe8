import { QueryClient } from '@tanstack/react-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // AGGRESSIVE: Longer stale times to drastically reduce API calls
      staleTime: 10 * 60 * 1000, // INCREASED: 10 minutes default
      gcTime: 60 * 60 * 1000, // INCREASED: 1 hour cache time

      // MINIMAL: Reduced retry to prevent call multiplication
      retry: 1, // REDUCED: Only 1 retry

      // SIMPLE: Fixed retry delay
      retryDelay: 2000, // FIXED: 2 second delay

      // PREVENT: All unnecessary refetching
      refetchOnWindowFocus: false,
      refetchOnReconnect: false, // DISABLED: Prevent reconnect refetching
      refetchOnMount: false, // DISABLED: Prevent mount refetching

      // DISABLE: All background refetching
      refetchInterval: false,
      refetchIntervalInBackground: false,

      // FIXED: Global error handling to prevent unhandled promise rejections
      throwOnError: false,

      // ADDED: Network mode for better offline handling
      networkMode: 'online',
      
      // Error handling is done via throwOnError: false above and individual query error handlers
    },
    mutations: {
      // Retry failed mutations once for network errors
      retry: (failureCount, error) => {
        // Don't retry authentication or validation errors
        if (error?.message?.includes('UNAUTHENTICATED') || 
            error?.message?.includes('invalid') ||
            error?.message?.includes('not found')) {
          return false
        }
        
        // Retry network and server errors once
        return failureCount < 1
      },
      
      // Faster retry for mutations
      retryDelay: 1000,
      
      // Error handling for mutations is handled individually in each mutation
    },
  },
  
  // Query client configured for production use
}) 