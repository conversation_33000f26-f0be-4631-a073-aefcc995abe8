import { getSingleMugshot, getUserMugshotData, type SingleMugshotResponse } from './api-client'
import { useRatingStore } from '@/lib/stores/rating-store'
import { useTagStore } from '@/lib/stores/tag-store'
import { useAuthStore } from '@/lib/stores/auth-store'

// Service that loads mugshot data and updates all relevant stores
export class MugshotStoreService {
  
  /**
   * Load complete mugshot data and update all stores
   * This is the main function that should be called when:
   * - Opening mugshot detail modal
   * - Loading individual mugshot page
   * - Any time you need fresh mugshot data with store updates
   */
  static async loadMugshotWithStoreUpdate(mugshotId: number): Promise<SingleMugshotResponse> {
    try {
      // Call the comprehensive mugshot API for general data
      const mugshotResponse = await getSingleMugshot(mugshotId)
      
      if (mugshotResponse.success && mugshotResponse.data) {
        // Update Rating Store with general rating stats (from mugshot data)
        MugshotStoreService.updateRatingStoreFromMugshot(mugshotId, mugshotResponse.data)
        
        // Update Tag Store with general tag stats (from mugshot data)  
        MugshotStoreService.updateTagStoreFromMugshot(mugshotId, mugshotResponse.data)
        
        // Load user-specific data separately ONLY if user is authenticated
        const authState = useAuthStore.getState()
        if (authState.isAuthenticated && authState.user) {
          try {
            const userResponse = await getUserMugshotData(mugshotId)
            if (userResponse.success && userResponse.data) {
              MugshotStoreService.updateStoresWithUserData(mugshotId, userResponse.data)
            }
          } catch (error) {
            console.log('Error loading user data for authenticated user:', error)
            // This is not critical - user can still view mugshot without their personal data
          }
        } else {
          console.log('User not authenticated, skipping user-specific data load')
        }
      }
      
      return mugshotResponse
      
    } catch (error) {
      console.error('Error in loadMugshotWithStoreUpdate:', error)
      return {
        success: false,
        message: 'Failed to load mugshot data',
        error: 'NETWORK_ERROR'
      }
    }
  }
  
  /**
   * Update Rating Store with general rating data from mugshot API response
   */
  private static updateRatingStoreFromMugshot(mugshotId: number, data: NonNullable<SingleMugshotResponse['data']>) {
    const ratingStore = useRatingStore.getState()
    
    // Extract rating statistics from the new API response format
    const ratings = data.ratings
    
    // Create rating statistics object
    const ratingStats = {
      averageRating: ratings.averageRating || 0,
      totalRatings: ratings.totalRatings || 0
    }
    
    // Update the rating store with general stats
    ratingStore.setRatingStats(mugshotId.toString(), ratingStats)
  }
  
  /**
   * Update Tag Store with general tag data from mugshot API response  
   */
  private static updateTagStoreFromMugshot(mugshotId: number, data: NonNullable<SingleMugshotResponse['data']>) {
    const tagStore = useTagStore.getState()
    
    // Extract tag statistics from the new API response format
    const tagCounts = data.tags?.tagCounts || {}
    
    // Create tag statistics object
    const tagStats = {
      wild: tagCounts.wild || 0,
      funny: tagCounts.funny || 0,
      spooky: tagCounts.spooky || 0
    }
    
    // Update tag statistics
    tagStore.setTagStatistics(mugshotId.toString(), tagStats)
  }
  
  /**
   * Update stores with user-specific data from user API response
   */
  private static updateStoresWithUserData(mugshotId: number, userData: unknown) {
    const ratingStore = useRatingStore.getState()
    const tagStore = useTagStore.getState()
    
    // Type guard for userData
    if (!userData || typeof userData !== 'object') return
    
    const data = userData as { userRating?: number | null; userTags?: string[] }
    
    // Update user's rating
    if (data.userRating !== null && data.userRating !== undefined) {
      ratingStore.setUserRating(mugshotId.toString(), data.userRating)
    }
    
    // Update user's tags
    if (data.userTags) {
      tagStore.setUserTags(mugshotId.toString(), data.userTags)
    }
  }
  
  /**
   * Convenience function for components that need to check if data is loading
   */
  static isLoading(mugshotId: string): boolean {
    const ratingStore = useRatingStore.getState()
    return ratingStore.isLoading(mugshotId)
  }
  
  /**
   * Clear cached data for a specific mugshot
   * Useful when you want to force fresh data on next load
   */
  static clearMugshotCache(mugshotId: number) {
    const ratingStore = useRatingStore.getState()
    const tagStore = useTagStore.getState()
    
    ratingStore.clearMugshotCache(mugshotId.toString())
    tagStore.clearMugshotCache(mugshotId.toString())
  }
}

// Export for easier importing
export const loadMugshotWithStoreUpdate = MugshotStoreService.loadMugshotWithStoreUpdate
export const clearMugshotCache = MugshotStoreService.clearMugshotCache 