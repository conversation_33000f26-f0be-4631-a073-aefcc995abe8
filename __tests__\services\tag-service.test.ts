import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  addTagToMugshot,
  removeTagFromMugshot,
  getMugshotTags,
  getPopularTags,
  getTrendingTags,
  searchTags,
  reportTag,
  getUserTagContributions,
  updateTrendingTags
} from '@/lib/services/tag-service'
import { createClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'

// Mock dependencies
vi.mock('@/lib/supabase/server')
vi.mock('next/cache')

describe('Tag Service', () => {
  let mockSupabase: any

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Create mock Supabase client
    mockSupabase = {
      auth: {
        getUser: vi.fn()
      },
      from: vi.fn(),
      rpc: vi.fn()
    }
    
    // Set up method chaining
    const mockQueryBuilder = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      ilike: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis()
    }
    
    mockSupabase.from.mockReturnValue(mockQueryBuilder)
    
    // Make createClient return our mock
    vi.mocked(createClient).mockResolvedValue(mockSupabase as any)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('addTagToMugshot', () => {
    it('should successfully add a tag to a mugshot', async () => {
      // Mock authenticated user
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      // Mock successful tag addition
      mockSupabase.rpc.mockResolvedValue({
        data: {
          success: true,
          tag_id: 'tag-123',
          tag_name: 'Funny',
          tag_slug: 'funny',
          usage_count: 5
        },
        error: null
      })

      const result = await addTagToMugshot(1, 'Funny')

      expect(result.success).toBe(true)
      expect(result.message).toBe('Tag added successfully!')
      expect(result.tag).toEqual({
        id: 'tag-123',
        name: 'Funny',
        slug: 'funny',
        usage_count: 5
      })
      
      expect(mockSupabase.rpc).toHaveBeenCalledWith('add_user_tag_to_mugshot', {
        p_mugshot_id: 1,
        p_tag_name: 'Funny',
        p_user_id: 'user-123'
      })
      
      expect(revalidatePath).toHaveBeenCalledWith('/mugshots/1')
    })

    it('should reject unauthenticated users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const result = await addTagToMugshot(1, 'Funny')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Authentication required to add tags')
      expect(result.error).toBe('UNAUTHENTICATED')
    })

    it('should validate tag name length', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      const result = await addTagToMugshot(1, 'A')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Tag name must be between 2 and 20 characters')
      expect(result.error).toBe('INVALID_LENGTH')
    })

    it('should validate tag name characters', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      const result = await addTagToMugshot(1, 'Tag@#$')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Tag can only contain letters, numbers, spaces, and hyphens')
      expect(result.error).toBe('INVALID_CHARACTERS')
    })

    it('should handle database errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      const result = await addTagToMugshot(1, 'Valid Tag')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Failed to add tag. Please try again.')
      expect(result.error).toBe('DATABASE_ERROR')
    })
  })

  describe('removeTagFromMugshot', () => {
    it('should successfully remove a tag from a mugshot', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      mockSupabase.rpc.mockResolvedValue({
        data: {
          success: true,
          remaining_usage: 3
        },
        error: null
      })

      const result = await removeTagFromMugshot(1, 'tag-123')

      expect(result.success).toBe(true)
      expect(result.message).toBe('Tag removed successfully!')
      expect(result.remaining_usage).toBe(3)
      
      expect(mockSupabase.rpc).toHaveBeenCalledWith('remove_user_tag_from_mugshot', {
        p_mugshot_id: 1,
        p_tag_id: 'tag-123',
        p_user_id: 'user-123'
      })
    })

    it('should reject unauthenticated users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const result = await removeTagFromMugshot(1, 'tag-123')

      expect(result.success).toBe(false)
      expect(result.message).toBe('Authentication required to remove tags')
      expect(result.error).toBe('UNAUTHENTICATED')
    })
  })

  describe('getMugshotTags', () => {
    it('should retrieve tags for a mugshot', async () => {
      const mockTags = [
        {
          id: 'tag-1',
          name: 'Funny',
          slug: 'funny',
          usage_count: 10,
          user_count: 5,
          current_user_tagged: true
        },
        {
          id: 'tag-2',
          name: 'Wild',
          slug: 'wild',
          usage_count: 7,
          user_count: 3,
          current_user_tagged: false
        }
      ]

      mockSupabase.rpc.mockResolvedValue({
        data: mockTags,
        error: null
      })

      const result = await getMugshotTags(1)

      expect(result).toEqual(mockTags)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_mugshot_tags', {
        p_mugshot_id: 1
      })
    })

    it('should handle database errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      const result = await getMugshotTags(1)

      expect(result).toEqual([])
    })
  })

  describe('getPopularTags', () => {
    it('should retrieve popular tags', async () => {
      const mockTags = [
        {
          id: 'tag-1',
          name: 'Funny',
          slug: 'funny',
          usage_count: 50,
          is_trending: true
        },
        {
          id: 'tag-2',
          name: 'Wild',
          slug: 'wild',
          usage_count: 30,
          is_trending: false
        }
      ]

      mockSupabase.rpc.mockResolvedValue({
        data: mockTags,
        error: null
      })

      const result = await getPopularTags('fun', 10)

      expect(result).toEqual(mockTags)
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_popular_tags', {
        search_term: 'fun',
        limit_count: 10
      })
    })

    it('should use default parameters', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null
      })

      await getPopularTags()

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_popular_tags', {
        search_term: null,
        limit_count: 20
      })
    })
  })

  describe('getTrendingTags', () => {
    it('should retrieve trending tags', async () => {
      const mockTags = [
        {
          id: 'tag-1',
          name: 'Viral',
          slug: 'viral',
          usage_count: 100,
          is_trending: true,
          created_at: '2024-01-01'
        }
      ]

      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue({
          data: mockTags,
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(mockQuery)

      const result = await getTrendingTags(5)

      expect(result).toEqual(mockTags)
      expect(mockSupabase.from).toHaveBeenCalledWith('user_tags')
      expect(mockQuery.eq).toHaveBeenCalledWith('is_approved', true)
      expect(mockQuery.eq).toHaveBeenCalledWith('is_trending', true)
    })
  })

  describe('reportTag', () => {
    it('should successfully report a tag', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      // Mock no existing report
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { code: 'PGRST116' } // Not found
        })
      }

      mockSupabase.from.mockReturnValueOnce(mockQuery)

      // Mock successful insert
      const mockInsertQuery = {
        insert: vi.fn().mockResolvedValue({
          error: null
        })
      }

      mockSupabase.from.mockReturnValueOnce(mockInsertQuery)

      const result = await reportTag('tag-123', 'spam', 'This is spam content')

      expect(result.success).toBe(true)
      expect(result.message).toContain('Tag reported successfully')
      
      expect(mockInsertQuery.insert).toHaveBeenCalledWith({
        tag_id: 'tag-123',
        reported_by: 'user-123',
        reason: 'spam',
        additional_info: 'This is spam content'
      })
    })

    it('should prevent duplicate reports', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { id: 'report-123' },
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(mockQuery)

      const result = await reportTag('tag-123', 'spam')

      expect(result.success).toBe(false)
      expect(result.message).toBe('You have already reported this tag')
      expect(result.error).toBe('ALREADY_REPORTED')
    })
  })

  describe('updateTrendingTags', () => {
    it('should update trending tags for admin users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin-123' } },
        error: null
      })

      // Mock admin profile check
      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { role: 'admin' },
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(mockQuery)

      // Mock successful RPC call
      mockSupabase.rpc.mockResolvedValue({
        error: null
      })

      const result = await updateTrendingTags()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Trending tags updated successfully')
      
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_trending_tags')
      expect(revalidatePath).toHaveBeenCalledWith('/tags')
    })

    it('should reject non-admin users', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })

      const mockQuery = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { role: 'user' },
          error: null
        })
      }

      mockSupabase.from.mockReturnValue(mockQuery)

      const result = await updateTrendingTags()

      expect(result.success).toBe(false)
      expect(result.message).toBe('Admin access required')
    })
  })
}) 