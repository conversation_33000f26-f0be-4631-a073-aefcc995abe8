# Current System State Documentation
**Date:** January 2025
**Purpose:** Baseline documentation before Supabase Functions → Native Queries migration

## System Overview

### Current Architecture
- **Framework**: Next.js 15.2.4 with App Router
- **Database**: Supabase (PostgreSQL) with custom functions
- **State Management**: TanStack Query v5.83.0 + Zustand v5.0.6
- **Authentication**: Supa<PERSON> Auth with profile management

### Critical Supabase Functions Currently In Use

#### 1. `search_filtered_mugshots`
**Location**: `supabase/migrations/011_filter_functions.sql`
**Usage**: Primary mugshots query with filtering, sorting, rating aggregation
**Parameters**:
- `search_term` (text) - firstName/lastName search
- `state_filter` (text) - state filtering
- `county_filter` (text) - county filtering  
- `date_from` (date) - booking date range start
- `date_to` (date) - booking date range end
- `tags_filter` (text[]) - tag filtering array
- `sort_by` (text) - 'newest' or 'top-rated'
- `limit_count` (integer) - pagination limit
- `offset_count` (integer) - pagination offset

**Returns**: Enhanced mugshot records with rating/tag aggregation

#### 2. `count_filtered_mugshots`
**Location**: `supabase/migrations/011_filter_functions.sql`
**Usage**: Count total results for pagination
**Parameters**: Same filters as search function (minus pagination)
**Returns**: Integer count

#### 3. `fast_mugshot_fallback`
**Location**: `supabase/migrations/011_filter_functions.sql`
**Usage**: Simplified fallback when main function times out
**Parameters**: Same as search function but skips tag filtering
**Returns**: Basic mugshot records without complex aggregation

#### 4. `count_fast_mugshot_fallback`
**Location**: `supabase/migrations/011_filter_functions.sql`
**Usage**: Count function for fallback queries
**Parameters**: Basic filters only
**Returns**: Integer count

### Current Data Flow

```
1. User Action (Filter/Sort/Paginate)
   ↓
2. MugshotsPageClient updates URL parameters
   ↓  
3. useMugshotsQuery triggered with new parameters
   ↓
4. API call to /api/mugshots with URLSearchParams
   ↓
5. mugshotsServiceServer.getMugshots() called
   ↓
6. getMugshotsUnified() calls search_filtered_mugshots RPC
   ↓
7. On success: Enhance with user ratings/tags via separate queries
   ↓
8. On failure: Fall back to getMugshotsFallback() → fast_mugshot_fallback RPC
   ↓
9. Transform DatabaseMugshot[] → UIMugshot[]
   ↓
10. Return formatted API response
   ↓
11. TanStack Query caches and provides to components
```

### API Response Contracts (CRITICAL - MUST NOT CHANGE)

#### GET /api/mugshots Response:
```typescript
{
  success: boolean
  data: {
    mugshots: UIMugshot[]
    pagination: {
      page: number
      perPage: number  
      totalCount?: number
      hasNextPage: boolean
      hasPreviousPage: boolean
    }
    filters: {
      applied: Record<string, unknown>
      sortBy: string
    }
    meta: {
      totalResults: number
      includedUserData: boolean
      timestamp: string
    }
  }
  message?: string
  error?: string
}
```

#### URL Parameters (CRITICAL - MUST NOT CHANGE):
- `search` - Search term for names
- `state` - State filter ('all-states' = no filter)
- `county` - County filter ('all-counties' = no filter)  
- `dateFrom` - Start date (YYYY-MM-DD)
- `dateTo` - End date (YYYY-MM-DD)
- `tags` - Comma-separated tag list
- `categories` - Legacy field (still supported)
- `sortBy` - 'newest', 'top-rated', 'most-viewed'
- `page` - Page number (1-based)
- `perPage` - Items per page (1-100)
- `includeTotal` - Include total count ('true'/'false')

### TanStack Query Integration Points

#### Query Keys Structure:
```typescript
['mugshots', filters, sortOptions, pagination]
```

#### Cache Configuration:
- **Stale Time**: 5 minutes
- **Garbage Collection**: 30 minutes  
- **Retry**: 3 attempts with exponential backoff
- **Placeholder Data**: Previous data during loading

#### Active Query Hooks:
- `useMugshotsQuery` - Main mugshots list
- `useMugshotDetailQuery` - Individual mugshot details
- `useUserMugshotDataQuery` - User-specific rating/tag data
- `useRatingStatisticsQuery` - Rating statistics
- `useTagStatisticsQuery` - Tag statistics

### Performance Characteristics

#### Current Function Performance:
- **search_filtered_mugshots**: ~500-2000ms (complex queries)
- **count_filtered_mugshots**: ~200-800ms
- **fast_mugshot_fallback**: ~100-300ms (simplified)
- **Timeout Threshold**: Functions fail after ~5 seconds

#### Fallback Strategy:
- Primary function fails → Use fast_mugshot_fallback
- Skip tag filtering in fallback to prevent timeout
- Graceful degradation maintains basic functionality

### User Experience Requirements

#### Filter Behavior:
- Real-time URL updates as filters change
- Browser back/forward button support
- Deep linking to filtered states
- Filter combination support
- User location pre-selection (authenticated users)

#### Sorting Options:
- **newest**: Sort by dateOfBooking DESC, created_at DESC
- **top-rated**: Sort by average_rating DESC, total_ratings DESC
- **most-viewed**: Currently falls back to 'newest'

#### Pagination:
- Page-based navigation
- Configurable items per page (12, 24, 48)
- Total count displayed when includeTotal=true
- Smooth transitions without loading flicker

### Database Schema Dependencies

#### Core Tables:
- `mugshots` - Primary mugshot data
- `ratings` - User ratings (1-5 stars)
- `tags` - User-applied tags (wild, funny, spooky)
- `profiles` - User profiles with location data

#### Critical Relationships:
- ratings.mugshot_id → mugshots.id
- tags.mugshot_id → mugshots.id
- ratings.user_id → profiles.user_id
- tags.user_id → profiles.user_id

### Authentication Integration

#### User Data Enhancement:
- Authenticated users get personalized rating/tag data
- `user_rating` field shows user's rating for each mugshot
- `user_tags` array shows tags user applied
- Location-based pre-filtering for authenticated users

#### Permission Model:
- Public read access to mugshots, ratings, tags
- Authenticated write access for ratings/tags
- Admin-only access for moderation functions

### Testing Requirements

#### Functional Testing:
- All filter combinations work correctly
- Pagination accurate across all scenarios  
- Sorting produces expected order
- Authentication-dependent features work
- Error handling graceful

#### Performance Testing:
- Large datasets (10k+ mugshots) perform adequately
- Complex filter combinations don't timeout
- Concurrent user load testing
- Mobile device performance

### Success Criteria for Migration

✅ **Zero Breaking Changes**:
- All existing URLs continue to work
- All filter combinations produce identical results
- API response format exactly the same
- TanStack Query hooks unchanged
- Authentication flow unaffected

✅ **Performance Equal or Better**:
- Query response times equal or faster
- No more timeout issues
- Memory usage stable or improved
- Mobile performance maintained

✅ **Maintainability Improved**:
- No more Supabase function complexity
- Direct query control for debugging
- Easier performance optimization
- Simpler testing and development

## Current Issues to Address

### Known Problems:
1. **Function Timeouts**: Complex queries sometimes timeout after 5 seconds
2. **Limited Control**: Can't optimize function queries directly
3. **Debug Difficulty**: Function debugging requires SQL knowledge
4. **Tag Performance**: Tag filtering particularly slow in functions

### Migration Goals:
1. Replace function calls with native TypeScript queries
2. Maintain exact same API contracts
3. Improve performance and reliability
4. Simplify debugging and maintenance
5. Enable future optimizations

---

**Next Steps**: Proceed with Phase 2 implementation while preserving all documented behavior. 