-- Migration 003: Create Ratings and Tags System
-- This migration implements the correct schema for ratings (1-10 attractiveness) and fixed tags (wild, funny, spooky)
-- Updated to work with production mugshots table (BIGSERIAL primary key)

-- =====================================================
-- ENABLE RLS ON EXISTING MUGSHOTS TABLE FIRST
-- =====================================================

-- Enable Row Level Security on existing mugshots table
ALTER TABLE public.mugshots ENABLE ROW LEVEL SECURITY;

-- Allow public read access to mugshots (non-sensitive public data)
CREATE POLICY "Allow public read access to mugshots" ON public.mugshots
  FOR SELECT USING (true);

-- Only allow admins to insert, update, or delete mugshots
CREATE POLICY "Allow admin full access to mugshots" ON public.mugshots
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.user_id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Add performance indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_mugshots_booking_date ON public.mugshots("dateOfBooking" DESC);
CREATE INDEX IF NOT EXISTS idx_mugshots_location ON public.mugshots("stateOfBooking", "countyOfBooking");
CREATE INDEX IF NOT EXISTS idx_mugshots_created_at ON public.mugshots(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_mugshots_names ON public.mugshots("firstName", "lastName");

-- =====================================================
-- RATINGS TABLE (CORRECTED FOR BIGSERIAL)
-- =====================================================

-- Ratings Table - Simple 1-10 attractiveness rating (corrected for BIGSERIAL id)
CREATE TABLE IF NOT EXISTS public.ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    mugshot_id BIGINT NOT NULL REFERENCES public.mugshots(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Unique index to prevent duplicate ratings
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_rating ON public.ratings (mugshot_id, user_id);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_ratings_mugshot_id ON public.ratings(mugshot_id);
CREATE INDEX IF NOT EXISTS idx_ratings_user_id ON public.ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_ratings_created_at ON public.ratings(created_at DESC);

-- =====================================================
-- TAGS TABLE (CORRECTED FOR BIGSERIAL)
-- =====================================================

-- Tags Table - Fixed categories (wild, funny, spooky) (corrected for BIGSERIAL id)
CREATE TABLE IF NOT EXISTS public.tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    mugshot_id BIGINT NOT NULL REFERENCES public.mugshots(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tag_type TEXT NOT NULL CHECK (tag_type IN ('wild', 'funny', 'spooky')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Composite index for filtering
CREATE INDEX IF NOT EXISTS idx_tags_mugshot_type ON public.tags (mugshot_id, tag_type);
CREATE INDEX IF NOT EXISTS idx_tags_user_id ON public.tags (user_id);
CREATE INDEX IF NOT EXISTS idx_tags_created_at ON public.tags (created_at DESC);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ratings
CREATE POLICY "Allow public read access to ratings" ON public.ratings
  FOR SELECT USING (true);

CREATE POLICY "Users can manage own ratings" ON public.ratings
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for tags  
CREATE POLICY "Allow public read access to tags" ON public.tags
  FOR SELECT USING (true);

CREATE POLICY "Users can manage own tags" ON public.tags
  FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- RATING FUNCTIONS
-- =====================================================

-- Function to Validate and Insert Rating (corrected for BIGSERIAL and actual column names)
CREATE OR REPLACE FUNCTION public.insert_rating(p_mugshot_id BIGINT, p_user_id UUID, p_rating INTEGER)
RETURNS VOID AS $$
DECLARE
    booking_date DATE;
    enabled_time TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Fetch dateOfBooking from mugshots (using actual column name)
    SELECT "dateOfBooking" INTO booking_date FROM public.mugshots WHERE id = p_mugshot_id;
    
    IF booking_date IS NULL THEN
        RAISE EXCEPTION 'Mugshot not found';
    END IF;
    
    -- Enabled at midnight the day after dateOfBooking
    enabled_time := (booking_date + INTERVAL '1 day')::DATE + INTERVAL '0 hours';
    
    IF now() < enabled_time THEN
        RAISE EXCEPTION 'Rating not yet enabled for this mugshot';
    END IF;
    
    -- Insert if no duplicate (unique index will also enforce)
    INSERT INTO public.ratings (mugshot_id, user_id, rating)
    VALUES (p_mugshot_id, p_user_id, p_rating)
    ON CONFLICT (mugshot_id, user_id) 
    DO UPDATE SET 
        rating = p_rating,
        updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get average rating for a mugshot (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.get_average_rating(p_mugshot_id BIGINT)
RETURNS DECIMAL(3,2) AS $$
BEGIN
    RETURN (
        SELECT COALESCE(AVG(rating), 0.0)::DECIMAL(3,2)
        FROM public.ratings 
        WHERE mugshot_id = p_mugshot_id
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get rating count for a mugshot (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.get_rating_count(p_mugshot_id BIGINT)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM public.ratings 
        WHERE mugshot_id = p_mugshot_id
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get user's rating for a mugshot (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.get_user_rating(p_mugshot_id BIGINT, p_user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT rating
        FROM public.ratings 
        WHERE mugshot_id = p_mugshot_id AND user_id = p_user_id
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TAG FUNCTIONS
-- =====================================================

-- Function to add fixed tag to mugshot (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.add_tag_to_mugshot(
    p_mugshot_id BIGINT, 
    p_user_id UUID, 
    p_tag_type TEXT
)
RETURNS JSON AS $$
DECLARE
    v_already_tagged BOOLEAN := false;
BEGIN
    -- Validate tag type
    IF p_tag_type NOT IN ('wild', 'funny', 'spooky') THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Invalid tag type. Must be wild, funny, or spooky'
        );
    END IF;
    
    -- Check if user already tagged this mugshot with this tag type
    SELECT EXISTS(
        SELECT 1 FROM public.tags 
        WHERE mugshot_id = p_mugshot_id 
          AND tag_type = p_tag_type 
          AND user_id = p_user_id
    ) INTO v_already_tagged;
    
    IF v_already_tagged THEN
        -- Remove existing tag (toggle behavior)
        DELETE FROM public.tags 
        WHERE mugshot_id = p_mugshot_id 
          AND tag_type = p_tag_type 
          AND user_id = p_user_id;
          
        RETURN json_build_object(
            'success', true,
            'action', 'removed',
            'tag_type', p_tag_type
        );
    ELSE
        -- Add new tag
        INSERT INTO public.tags (mugshot_id, user_id, tag_type)
        VALUES (p_mugshot_id, p_user_id, p_tag_type);
        
        RETURN json_build_object(
            'success', true,
            'action', 'added',
            'tag_type', p_tag_type
        );
    END IF;
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get tag counts for a mugshot (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.get_mugshot_tag_counts(p_mugshot_id BIGINT)
RETURNS TABLE(tag_type TEXT, tag_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tag_type,
        COUNT(*) as tag_count
    FROM public.tags t
    WHERE t.mugshot_id = p_mugshot_id
    GROUP BY t.tag_type
    ORDER BY tag_count DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's tags for a mugshot (corrected for BIGSERIAL)
CREATE OR REPLACE FUNCTION public.get_user_tags(p_mugshot_id BIGINT, p_user_id UUID)
RETURNS TABLE(tag_type TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT t.tag_type
    FROM public.tags t
    WHERE t.mugshot_id = p_mugshot_id 
      AND t.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger for Updating Timestamps in Ratings
CREATE OR REPLACE FUNCTION public.update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_ratings_timestamp
BEFORE UPDATE ON public.ratings
FOR EACH ROW EXECUTE FUNCTION public.update_timestamp();

-- =====================================================
-- PERMISSIONS
-- =====================================================

-- =====================================================
-- HELPER FUNCTIONS FOR PRODUCTION SCHEMA
-- =====================================================

-- Function to search mugshots with filters (adapted for BIGSERIAL id)
CREATE OR REPLACE FUNCTION public.search_mugshots(
    search_term TEXT DEFAULT NULL,
    state_filter TEXT DEFAULT NULL,
    county_filter TEXT DEFAULT NULL,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL,
    limit_count INTEGER DEFAULT 12,
    offset_count INTEGER DEFAULT 0
)
RETURNS SETOF public.mugshots AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM public.mugshots m
    WHERE 
        (search_term IS NULL OR 
         m."firstName" ILIKE '%' || search_term || '%' OR 
         m."lastName" ILIKE '%' || search_term || '%')
    AND (state_filter IS NULL OR m."stateOfBooking" = state_filter)
    AND (county_filter IS NULL OR m."countyOfBooking" = county_filter)
    AND (date_from IS NULL OR m."dateOfBooking" >= date_from)
    AND (date_to IS NULL OR m."dateOfBooking" <= date_to)
    ORDER BY m.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to count mugshots with filters
CREATE OR REPLACE FUNCTION public.count_mugshots(
    search_term TEXT DEFAULT NULL,
    state_filter TEXT DEFAULT NULL,
    county_filter TEXT DEFAULT NULL,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    result_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO result_count
    FROM public.mugshots m
    WHERE 
        (search_term IS NULL OR 
         m."firstName" ILIKE '%' || search_term || '%' OR 
         m."lastName" ILIKE '%' || search_term || '%')
    AND (state_filter IS NULL OR m."stateOfBooking" = state_filter)
    AND (county_filter IS NULL OR m."countyOfBooking" = county_filter)
    AND (date_from IS NULL OR m."dateOfBooking" >= date_from)
    AND (date_to IS NULL OR m."dateOfBooking" <= date_to);
    
    RETURN result_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- PERMISSIONS
-- =====================================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.mugshots TO anon, authenticated;
GRANT SELECT ON public.ratings TO anon, authenticated;
GRANT SELECT ON public.tags TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.ratings TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.tags TO authenticated;

-- Grant execute permissions on functions (corrected parameter types)
GRANT EXECUTE ON FUNCTION public.insert_rating(BIGINT, UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_average_rating(BIGINT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.get_rating_count(BIGINT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_rating(BIGINT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_tag_to_mugshot(BIGINT, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_mugshot_tag_counts(BIGINT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_tags(BIGINT, UUID) TO authenticated;

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION public.search_mugshots(TEXT, TEXT, TEXT, DATE, DATE, INTEGER, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.count_mugshots(TEXT, TEXT, TEXT, DATE, DATE) TO anon, authenticated; 