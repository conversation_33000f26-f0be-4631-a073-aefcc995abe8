"use client"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import LocationDropdown from "@/components/LocationDropdown"
import { MapPin, ArrowRight } from "lucide-react"

function LocationSetupContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const returnUrl = searchParams.get('returnUrl') || '/mugshots'
  
  const [selectedState, setSelectedState] = useState("")
  const [selectedCounty, setSelectedCounty] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [user, setUser] = useState<{ id: string; email?: string } | null>(null)

  useEffect(() => {
    // Check if user is authenticated
    const checkUser = async () => {
      const supabase = createClient()
      
      // First check if there's a session using getSession() which doesn't throw errors
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session?.user) {
        router.push('/login')
        return
      }
      
      // Only call getUser if we have a valid session
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        router.push('/login')
        return
      }
      
      setUser(user)
    }
    
    checkUser()
  }, [router])

  const handleLocationSubmit = async () => {
    if (!selectedState || !selectedCounty) {
      setError("Please select both state and county")
      return
    }

    if (!user) {
      setError("User not authenticated")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      console.log('Submitting location for user:', user.id, { state: selectedState, county: selectedCounty })
      
      const response = await fetch('/api/update-location', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          state: selectedState,
          county: selectedCounty
        })
      })

      const result = await response.json()

      if (!result.success) {
        console.error('Location submission failed:', result.error)
        setError(result.error || 'Failed to save location')
        return
      }

      console.log('Location saved successfully, redirecting to:', returnUrl)
      
      // Success - redirect to the original destination
      router.push(returnUrl)
    } catch (error) {
      console.error('Location update error:', error)
      setError('An unexpected error occurred while saving your location')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSkip = () => {
    // Allow user to skip location setup and proceed
    router.push(returnUrl)
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-gray-800/50 backdrop-blur-sm border-cyan-500/30 shadow-2xl">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center">
            <MapPin className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-white">
            Complete Your Profile
          </CardTitle>
          <p className="text-gray-300 text-lg">
            Help us show you mugshots from your area by selecting your location
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <div className="bg-red-500/20 border border-red-500 rounded-lg p-4">
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-4">
            <LocationDropdown
              selectedState={selectedState}
              setSelectedState={setSelectedState}
              selectedCounty={selectedCounty}
              setSelectedCounty={setSelectedCounty}
              stateLabel="Select Your State"
              countyLabel="Select Your County"
              statePlaceholder="Choose your state"
              countyPlaceholder="Choose your county"
            />
          </div>

          <div className="flex gap-4 pt-4">
            <Button
              onClick={handleSkip}
              variant="outline"
              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
              disabled={isLoading}
            >
              Skip for Now
            </Button>
            
            <Button
              onClick={handleLocationSubmit}
              disabled={!selectedState || !selectedCounty || isLoading}
              className="flex-1 bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-500 hover:to-blue-600 text-white font-semibold"
            >
              {isLoading ? (
                <>Loading...</>
              ) : (
                <>
                  Continue
                  <ArrowRight className="ml-2 w-4 h-4" />
                </>
              )}
            </Button>
          </div>

          <p className="text-xs text-gray-400 text-center">
            You can always update your location later in your profile settings
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

export default function LocationSetupPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    }>
      <LocationSetupContent />
    </Suspense>
  )
} 