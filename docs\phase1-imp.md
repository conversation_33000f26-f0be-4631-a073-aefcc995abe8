# Phase 1 Implementation: State Management Refactor - Updated Detailed Task List

## 🎯 **Project Overview**
Complete state management refactor from Zustand to TanStack Query while maintaining zero UI/API changes. This checklist incorporates schema analysis and fixes existing UI loading issues.

## 📋 **Pre-Implementation Analysis**

### 1.1 Schema Analysis (COMPLETED)
- ✅ **Mugshots Table**: `BIGSERIAL id`, proper column names with quotes (`"firstName"`, `"dateOfBooking"`, etc.)
- ✅ **Ratings Table**: References `BIGINT mugshot_id` (not UUID)
- ✅ **Tags Table**: References `BIGINT mugshot_id` (not UUID)
- ✅ **Events/Votes/Winners**: All use `BIGINT` for mugshot references
- ✅ **Database Functions**: All use `BIGINT` parameters for mugshot_id

### 1.2 Existing UI Issues Identified
- ✅ **Profile Page**: Stuck loading due to race conditions in auth state
- ✅ **Header Avatar**: Loading state never resolves
- ✅ **UserNav**: Missing dependency arrays causing infinite loops

## 🔧 **Core Infrastructure Setup**

### 2.1 TanStack Query Configuration
- [ ] Install TanStack Query dependencies
  - `@tanstack/react-query@^5.0.0`
  - `@tanstack/react-query-nextjs@^5.0.0`
- [ ] Create `lib/query-client.ts`
  - Configure global query client with proper cache times
  - Set up SSR hydration for Next.js 15
  - Configure retry strategies and stale times
- [ ] Create `app/providers.tsx`
  - Wrap app with QueryClientProvider
  - Add SSR hydration boundary
  - Include error boundary for TanStack

### 2.2 Feature Flag System
- [ ] Create `.env.local` with flags:
  ```
  NEXT_PUBLIC_STATE_REFACTOR_ENABLED=false
  NEXT_PUBLIC_USE_LEGACY_ZUSTAND=true
  ```
- [ ] Create `lib/config/feature-flags.ts`
  ```typescript
  export const FEATURE_FLAGS = {
    STATE_REFACTOR_ENABLED: process.env.NEXT_PUBLIC_STATE_REFACTOR_ENABLED === 'true',
    USE_LEGACY_ZUSTAND: process.env.NEXT_PUBLIC_USE_LEGACY_ZUSTAND !== 'false',
  }
  ```

## 🔄 **State Migration Strategy**

### 3.1 Fix Existing UI Issues First - ✅ COMPLETED

#### 3.1.1 Fix Profile Page Loading Issues - ✅ COMPLETED
- [x] **Fix `app/profile/page.tsx`**
  - [x] Add proper loading states with skeleton UI
  - [x] Implement error boundaries
  - [x] Add retry logic for failed profile loads
  - [x] Fix race condition in `checkAuthAndLoadProfile`

#### 3.1.2 Fix Header/UserNav Loading - ✅ COMPLETED
- [x] **Fix `components/UserNav.tsx`**
  - [x] Add dependency array to useEffect
  - [x] Implement proper auth state synchronization
  - [x] Add loading skeleton for avatar
  - [x] Fix infinite loop in auth listener

### 3.2 Zustand Store Refactoring (Schema-Compliant)

#### 3.2.1 Auth Store (Client-only)
- [ ] **Refactor `lib/stores/auth-store.ts`**
  - [ ] Remove all server state (profile data)
  - [ ] Keep only: auth session, theme preferences
  - [ ] Create `lib/stores/client-preferences-store.ts`
  - [ ] Test with localStorage persistence

#### 3.2.2 Rating Store → TanStack (BIGINT Compatible)
- [ ] **Create `lib/hooks/tanstack/use-ratings.ts`**
  - [ ] `useMugshotRatings(mugshotId: bigint)` - uses BIGINT
  - [ ] `useUserRating(mugshotId: bigint, userId: string)`
  - [ ] `useRatingMutation()` - handles rating submission
  - [ ] `useRatingStats(mugshotId: bigint)` - uses database functions
- [ ] **Create rating service layer**
  - [ ] `lib/services/tanstack/ratings-service.ts`
  - [ ] `submitRating(mugshotId: bigint, rating: number)` - uses `insert_rating`
  - [ ] `fetchRatingStats(mugshotId: bigint)` - uses `get_average_rating`, `get_rating_count`
  - [ ] `fetchUserRating(mugshotId: bigint, userId: string)` - uses `get_user_rating`

#### 3.2.3 Tag Store → TanStack (BIGINT Compatible)
- [ ] **Create `lib/hooks/tanstack/use-tags.ts`**
  - [ ] `useMugshotTags(mugshotId: bigint)` - uses BIGINT
  - [ ] `useUserTags(mugshotId: bigint, userId: string)`
  - [ ] `useTagMutation()` - handles tag toggling
  - [ ] `useTagStats(mugshotId: bigint)` - uses `get_mugshot_tag_counts`
- [ ] **Create tag service layer**
  - [ ] `lib/services/tanstack/tags-service.ts`
  - [ ] `submitTag(mugshotId: bigint, tagType: string)` - uses `add_tag_to_mugshot`
  - [ ] `fetchTagStats(mugshotId: bigint)` - uses `get_mugshot_tag_counts`
  - [ ] `fetchUserTags(mugshotId: bigint, userId: string)` - uses `get_user_tags`

#### 3.2.4 Filter Store → TanStack (Schema-Compliant)
- [ ] **Create `lib/hooks/tanstack/use-mugshots.ts`**
  - [ ] `useMugshotsQuery(filters, page, perPage)` - uses `search_mugshots`
  - [ ] `useMugshotCount(filters)` - uses `count_mugshots`
  - [ ] `useMugshotDetail(id: bigint)` - uses BIGINT
- [ ] **Create mugshots service layer**
  - [ ] `lib/services/tanstack/mugshots-service.ts`
  - [ ] `fetchMugshots(filters, page, perPage)` - uses `search_mugshots`
  - [ ] `fetchMugshotCount(filters)` - uses `count_mugshots`
  - [ ] `fetchMugshotDetail(id: bigint)` - direct query with BIGINT

## 🌐 **Server-Side Integration (Schema-Compliant)**

### 4.1 SSR Prefetching with BIGINT
- [ ] **Create `lib/ssr/prefetch-queries.ts`**
  - [ ] `prefetchMugshots(queryClient, filters)` - uses search_mugshots
  - [ ] `prefetchMugshotDetail(queryClient, id: bigint)` - handles BIGINT
  - [ ] `prefetchRatingStats(queryClient, mugshotId: bigint)`
  - [ ] `prefetchTagStats(queryClient, mugshotId: bigint)`

### 4.2 API Endpoint Compatibility
- [ ] **Verify all existing API endpoints work unchanged**
  - [ ] `/api/mugshots/[id]` - returns BIGINT id
  - [ ] `/api/ratings/[mugshotId]` - accepts BIGINT parameter
  - [ ] `/api/tags/[mugshotId]` - accepts BIGINT parameter
  - [ ] `/api/mugshots/search` - uses existing filters

## 🔄 **Real-time Integration (Schema-Compliant)**

### 5.1 Supabase Realtime Hooks
- [ ] **Create `lib/hooks/realtime/use-realtime-ratings.ts`**
  - [ ] Subscribe to `ratings` table changes
  - [ ] Update TanQuery cache with BIGINT mugshot_id
- [ ] **Create `lib/hooks/realtime/use-realtime-tags.ts`**
  - [ ] Subscribe to `tags` table changes
  - [ ] Update TanQuery cache with BIGINT mugshot_id
- [ ] **Create `lib/hooks/realtime/use-realtime-mugshots.ts`**
  - [ ] Subscribe to `mugshots` table changes
  - [ ] Update list queries with BIGINT ids

## 🧪 **Testing Strategy (Schema-Compliant)**

### 6.1 Regression Testing
- [ ] **Create test suite**
  - [ ] `__tests__/tanstack/auth-flow.test.ts` - test auth state
  - [ ] `__tests__/tanstack/rating-mutations.test.ts` - test BIGINT rating flow
  - [ ] `__tests__/tanstack/tag-mutations.test.ts` - test BIGINT tag flow
  - [ ] `__tests__/tanstack/mugshots-query.test.ts` - test BIGINT queries
  - [ ] `__tests__/tanstack/realtime-sync.test.ts` - test realtime with BIGINT

### 6.2 Playwright E2E Tests
- [ ] **Update E2E tests**
  - [ ] `e2e/state-refactor/auth-flow.spec.ts` - test profile loading fix
  - [ ] `e2e/state-refactor/rating-flow.spec.ts` - test BIGINT rating flow
  - [ ] `e2e/state-refactor/tag-flow.spec.ts` - test BIGINT tag flow
  - [ ] `e2e/state-refactor/filtering.spec.ts` - test search with BIGINT

## 🔄 **Migration Phases (Updated)**

### Phase 0: Fix Existing Issues (Day 1) - ✅ COMPLETED
- [x] **Fix Profile Page Loading**
  - [x] Add proper loading skeletons
  - [x] Fix race conditions in auth state
  - [x] Add error boundaries
- [x] **Fix Header/UserNav Loading**
  - [x] Fix infinite loops in auth listeners
  - [x] Add proper loading states
  - [x] Test auth flow end-to-end
- [x] **Refactor UserNav to use auth store properly**
  - [x] Eliminate duplicate auth logic
  - [x] Use existing Zustand auth store
  - [x] Fix loading state issues

### Phase 1A: Core Setup (Day 2)
- [ ] Install TanStack Query dependencies
- [ ] Set up QueryClient and providers
- [ ] Create feature flag system
- [ ] Test with existing API endpoints

### Phase 1B: Auth Store Refactor (Day 3)
- [ ] Refactor auth-store to client-only
- [ ] Test authentication flow
- [ ] Verify SSR compatibility

### Phase 1C: Rating System (Days 4-5)
- [ ] Create rating TanStack hooks with BIGINT
- [ ] Migrate rating mutations
- [ ] Implement optimistic updates
- [ ] Test with database functions

### Phase 1D: Tag System (Day 6)
- [ ] Create tag TanStack hooks with BIGINT
- [ ] Migrate tag mutations
- [ ] Test with database functions

### Phase 1E: Filter & Search (Day 7)
- [ ] Create mugshots query hooks with BIGINT
- [ ] Migrate filter state
- [ ] Test search and filtering

### Phase 1F: Realtime Integration (Day 8)
- [ ] Add Supabase realtime hooks
- [ ] Test realtime updates with BIGINT
- [ ] Validate optimistic updates

### Phase 1G: SSR & SEO (Day 9)
- [ ] Implement SSR prefetching with BIGINT
- [ ] Test SEO meta tags
- [ ] Validate server-side rendering

### Phase 1H: Testing & Validation (Day 10)
- [ ] Run full regression suite
- [ ] Performance benchmarking
- [ ] Final validation with BIGINT schema

## 🎯 **Validation Checklist (Updated)**

### 7.1 Functional Validation
- [ ] All existing features work identically
- [ ] No UI changes detected
- [ ] No API changes detected
- [ ] Authentication flow fixed (no loading stuck)
- [ ] Rating system works with BIGINT
- [ ] Tag system works with BIGINT
- [ ] Filtering works with BIGINT
- [ ] Search works with BIGINT

### 7.2 Performance Validation
- [ ] Profile page loads without stuck loading
- [ ] Header avatar loads correctly
- [ ] Faster initial page loads
- [ ] Reduced API calls
- [ ] Better caching behavior
- [ ] Smooth optimistic updates

### 7.3 Technical Validation
- [ ] SSR hydration working with BIGINT
- [ ] SEO meta tags preserved
- [ ] No console errors
- [ ] All tests passing
- [ ] Feature flag rollback working
- [ ] BIGINT schema compatibility verified

## 📝 **Implementation Notes (Updated)**

### 8.1 Code Organization (Schema-Compliant)
```
lib/
├── tanstack/
│   ├── query-client.ts
│   ├── providers.tsx
│   └── hooks/
│       ├── use-mugshots.ts (BIGINT compatible)
│       ├── use-ratings.ts (BIGINT compatible)
│       ├── use-tags.ts (BIGINT compatible)
│       └── use-auth.ts
├── stores/
│   ├── auth-store.ts (client-only)
│   └── client-preferences-store.ts
├── services/
│   ├── tanstack/
│   │   ├── mugshots-service.ts (BIGINT compatible)
│   │   ├── ratings-service.ts (BIGINT compatible)
│   │   └── tags-service.ts (BIGINT compatible)
└── config/
    └── feature-flags.ts
```

### 8.2 Testing Commands (Schema-Compliant)
```bash
# Run specific test suites
npm run test:tanstack
npm run test:e2e:state-refactor
npm run test:performance

# Feature flag testing
NEXT_PUBLIC_STATE_REFACTOR_ENABLED=true npm run dev
NEXT_PUBLIC_USE_LEGACY_ZUSTAND=true npm run dev

# Test BIGINT compatibility
npm run test:bigint-schema
```

## ✅ **Completion Criteria (Updated)**
- [ ] All existing UI loading issues fixed
- ✅ BIGINT schema compatibility verified
- [ ] Zero UI/API changes verified
- [ ] All tests passing with BIGINT
- [ ] Performance improved
- [ ] Feature flag rollback tested
- [ ] Documentation updated
- [ ] Ready for production deployment
