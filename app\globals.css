@import "tailwindcss";

@theme {
  --radius: 0.5rem;
  
  /* Light mode variables */
  --color-background: #ffffff;
  --color-foreground: #0f172a;
  --color-card: #ffffff;
  --color-card-foreground: #0f172a;
  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;
  --color-primary: #ec4899; /* Pink primary */
  --color-primary-foreground: #ffffff;
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;
  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-accent: #f1f5f9;
  --color-accent-foreground: #0f172a;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #ffffff;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #ec4899;
  
  /* Dark mode overrides */
  --color-background-dark: #0a0a0a;
  --color-foreground-dark: #ffffff;
  --color-card-dark: #1a1a1a;
  --color-card-foreground-dark: #ffffff;
  --color-popover-dark: #1a1a1a;
  --color-popover-foreground-dark: #ffffff;
  --color-primary-dark: #f472b6;
  --color-primary-foreground-dark: #0a0a0a;
  --color-secondary-dark: #262626;
  --color-secondary-foreground-dark: #ffffff;
  --color-muted-dark: #262626;
  --color-muted-foreground-dark: #a1a1aa;
  --color-accent-dark: #262626;
  --color-accent-foreground-dark: #ffffff;
  --color-destructive-dark: #dc2626;
  --color-destructive-foreground-dark: #ffffff;
  --color-border-dark: #404040;
  --color-input-dark: #404040;
  --color-ring-dark: #f472b6;
  
  /* Custom neon colors */
  --color-neon-pink: #ec4899;
  --color-neon-cyan: #06b6d4;
  --color-neon-purple: #8b5cf6;
  --color-neon-green: #10b981;
}

/* Dark mode CSS variable overrides */
.dark {
  --color-background: var(--color-background-dark);
  --color-foreground: var(--color-foreground-dark);
  --color-card: var(--color-card-dark);
  --color-card-foreground: var(--color-card-foreground-dark);
  --color-popover: var(--color-popover-dark);
  --color-popover-foreground: var(--color-popover-foreground-dark);
  --color-primary: var(--color-primary-dark);
  --color-primary-foreground: var(--color-primary-foreground-dark);
  --color-secondary: var(--color-secondary-dark);
  --color-secondary-foreground: var(--color-secondary-foreground-dark);
  --color-muted: var(--color-muted-dark);
  --color-muted-foreground: var(--color-muted-foreground-dark);
  --color-accent: var(--color-accent-dark);
  --color-accent-foreground: var(--color-accent-foreground-dark);
  --color-destructive: var(--color-destructive-dark);
  --color-destructive-foreground: var(--color-destructive-foreground-dark);
  --color-border: var(--color-border-dark);
  --color-input: var(--color-input-dark);
  --color-ring: var(--color-ring-dark);
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: system-ui, sans-serif;
    min-height: 100vh;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  /* Override body background with custom gradients */
  body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  }
  
  /* Dark mode background */
  .dark body {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 100%) !important;
  }
}

/* Custom Component Classes */
@layer components {
  .btn-neon {
    @apply bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300;
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
  }
  
  .btn-neon:hover {
    box-shadow: 0 0 30px rgba(236, 72, 153, 0.5);
  }
  
  /* Static card for sections - NO hover animation */
  .card-neon {
    @apply bg-gray-900/90 border border-pink-500/30 rounded-xl backdrop-blur-sm p-6;
  }
  
  /* Mugshot card with hover animation */
  .card-mugshot {
    @apply bg-gray-900/90 border border-pink-500/30 rounded-xl backdrop-blur-sm transition-all duration-300;
  }
  
  .card-mugshot:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
  }
  
  .input-neon {
    @apply bg-gray-800/90 border border-cyan-500/30 rounded-lg text-white placeholder:text-gray-400 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-500/20;
  }
  

  
  /* Text effects */
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
  }
  
  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  
  .shadow-glow-cyan {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }
  
  /* Mugshot card styles */
  .mugshot-card {
    @apply bg-gray-900/90 border border-pink-500/30 rounded-xl backdrop-blur-sm transition-all duration-300;
  }
  
  .mugshot-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(236, 72, 153, 0.4);
    @apply border-pink-500/50;
  }
  
  .card-neon {
    @apply bg-gray-900/90 border border-pink-500/30 rounded-xl backdrop-blur-sm;
  }
  
  .card-neon:hover {
    @apply border-pink-500/50;
    box-shadow: 0 5px 20px rgba(236, 72, 153, 0.2);
  }
  
  /* Glow effects */
  .glow-pink {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.4);
  }
  
  .glow-cyan {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
  }
  
  .glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
  }
  
  /* Button styles */
  .btn-glow-pink {
    @apply bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-semibold rounded-lg transition-all duration-300;
    box-shadow: 0 0 15px rgba(236, 72, 153, 0.3);
  }
  
  .btn-glow-pink:hover {
    box-shadow: 0 0 25px rgba(236, 72, 153, 0.5);
    transform: translateY(-1px);
  }
  
  .btn-glow-cyan {
    @apply bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-semibold rounded-lg transition-all duration-300;
    box-shadow: 0 0 15px rgba(6, 182, 212, 0.3);
  }
  
  .btn-glow-cyan:hover {
    box-shadow: 0 0 25px rgba(6, 182, 212, 0.5);
    transform: translateY(-1px);
  }
  
  /* Text glow effects */
  .text-glow-pink {
    color: #ec4899;
    text-shadow: 0 0 10px rgba(236, 72, 153, 0.5);
  }
  
  .text-glow-cyan {
    color: #06b6d4;
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
  }
  
  .text-glow-purple {
    color: #8b5cf6;
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  }
  
  /* Animated background */
  .animated-bg {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }
  
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  /* Neon border animation */
  .neon-border {
    position: relative;
    border: 2px solid transparent;
    border-radius: 12px;
  }
  
  .neon-border::before {
    content: '';
    position: absolute;
    inset: -2px;
    padding: 2px;
    background: linear-gradient(45deg, #ff006e, #8338ec, #3a86ff, #06ffa5);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
  
  /* Pulse glow animation */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }
  
  @keyframes pulseGlow {
    from {
      box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
    }
    to {
      box-shadow: 0 0 30px rgba(236, 72, 153, 0.6);
    }
  }
  
  /* Vote animation styles */
  .vote-animation {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .vote-success {
    animation: voteSuccess 0.6s ease-out;
  }
  
  .vote-disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  @keyframes voteSuccess {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    50% {
      transform: scale(1.1);
      box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
  }
  
  .vote-pulse {
    animation: votePulse 1s ease-in-out infinite;
  }
  
  @keyframes votePulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-200 dark:bg-gray-800;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  @apply bg-pink-500/50;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-pink-500/70;
}

/* Global cursor pointer for interactive elements */
button, a, select, input[type="button"], input[type="submit"], [role="button"], .cursor-pointer {
  cursor: pointer !important;
}

/* Component-specific cursor styles */
.mugshot-card {
  cursor: pointer !important;
}

.card-neon {
  cursor: pointer !important;
}

.btn-glow-pink, .btn-glow-cyan {
  cursor: pointer !important;
}

/* Vote button cursor */
.vote-animation {
  cursor: pointer !important;
}

/* Rating number outline styles */
@layer components {
  .outline-text {
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #6b7280;
    color: transparent;
    font-weight: 900;
    font-family: var(--font-m-plus-rounded);
  }
  
  .rating-gradient {
    background: linear-gradient(
      90deg,
      #3b82f6 0%,
      #06b6d4 25%,
      #10b981 50%,
      #f59e0b 75%,
      #ef4444 100%
    );
  }
}
