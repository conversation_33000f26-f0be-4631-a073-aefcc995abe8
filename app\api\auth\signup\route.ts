import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Signup API endpoint hit')
    
    const body = await request.json()
    console.log('Request body received:', body)
    
    const { email, password, fullName, selectedState, selectedCounty } = body

    // Validate required fields
    if (!email || !password || !fullName || !selectedState || !selectedCounty) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    console.log('Validation passed, attempting Supabase signup...')

    const supabase = await createClient()

    // Create user with Supabase Auth - store basic metadata for fallback
    const { data, error } = await supabase.auth.signUp({
      email: email.trim(),
      password,
      options: {
        data: {
          full_name: fullName.trim(),
          state: selectedState,
          county: selectedCounty
        }
      }
    })

    if (error) {
      console.error('Signup error:', error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      )
    }

    if (!data.user) {
      return NextResponse.json(
        { success: false, error: 'Failed to create user' },
        { status: 400 }
      )
    }

    console.log('User created successfully:', data.user.id, data.user.email)

    // Note: Profile creation will happen after email confirmation via the callback route
    // The location data is stored in user metadata and will be used when creating the profile
    console.log('User metadata stored:', {
      full_name: fullName.trim(),
      state: selectedState,
      county: selectedCounty
    })

    return NextResponse.json({
      success: true,
      message: 'Account created successfully! Please check your email to confirm your account.',
      user: {
        id: data.user.id,
        email: data.user.email
      }
    })

  } catch (error) {
    console.error('Signup API error:', error)
    return NextResponse.json(
      { success: false, error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    )
  }
} 