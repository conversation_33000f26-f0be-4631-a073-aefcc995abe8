"use client"

import { useState } from "react"
import Image from "next/image"
// import Link from "next/link" // Unused import
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Search, Menu, X } from "lucide-react"

import { ThemeToggle } from "./theme-toggle"
import UserNav from "./UserNav"
// import UserNavDebug from "./UserNavDebug" // Unused import
import AdminOnly from "./AdminOnly"
import { LogoNav } from "./UniversalNavLink"
import { NavLink } from "./NavigationClickFeedback"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()

  // Helper function to check if current path matches nav item
  const isActivePage = (path: string) => {
    if (path === "/") return pathname === "/"
    return pathname.startsWith(path)
  }

  // Get nav link classes with active state
  const getNavLinkClasses = (path: string, baseClasses: string = "") => {
    const active = isActivePage(path)
    return `${baseClasses} ${
      active 
        ? "text-pink-400 bg-pink-500/10 border-b-2 border-pink-400" 
        : "text-foreground hover:text-pink-400"
    } transition-colors px-3 py-2 rounded-t-md font-medium text-sm`
  }

  const getMobileNavLinkClasses = (path: string, baseClasses: string = "") => {
    const active = isActivePage(path)
    return `${baseClasses} ${
      active 
        ? "text-pink-400 bg-pink-500/10 border-l-4 border-pink-400 pl-4" 
        : "text-foreground hover:text-pink-400 pl-4"
    } transition-colors pr-4 py-3 rounded-r-md w-full text-left font-medium`
  }

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          {/* Logo Section - Proper flex layout */}
          <LogoNav href="/" className="flex items-center gap-3 shrink-0">
            <div className="relative z-10 flex gap-2">
              <Image
                src="/images/atm-logo.png"
                alt="America's Top Mugshots Logo"
                width={40}
                height={40}
                className="rounded-full border-2 border-pink-500 flex-shrink-0"
              />
              {/* Desktop logo text */}
              <div className="hidden sm:flex flex-col">
                <h1 className="text-lg font-bold text-foreground leading-tight">
                  America&apos;s Top <span className="text-pink-500">Mugshots</span>
                </h1>
                <p className="text-xs text-muted-foreground">Rate • Vote • Discover</p>
              </div>
              {/* Mobile-only simplified text */}
              <div className="flex sm:hidden">
                <h1 className="text-sm font-bold text-foreground">
                  ATM
                </h1>
              </div>
            </div>
          </LogoNav>

          {/* Desktop Navigation - Hidden on mobile */}
          <nav className="hidden lg:flex items-center space-x-1">
            <NavLink href="/mugshots" className={getNavLinkClasses("/mugshots")}>
              Browse All
            </NavLink>
            <NavLink href="/popular" className={getNavLinkClasses("/popular")}>
              Popular
            </NavLink>
            
            {/* Competition Navigation Dropdown */}
            <div className="relative group">
              <button className={getNavLinkClasses("/weekly-best", "flex items-center gap-1")}>
                Champions
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute top-full left-0 bg-background/95 backdrop-blur border border-border rounded-lg p-2 space-y-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none group-hover:pointer-events-auto z-50 min-w-[180px] shadow-lg">
                <NavLink href="/weekly-best" className="block px-3 py-2 hover:bg-pink-500/10 rounded text-foreground w-full text-left text-sm">
                  Weekly Best
                </NavLink>
                <NavLink href="/monthly-champions" className="block px-3 py-2 hover:bg-pink-500/10 rounded text-foreground w-full text-left text-sm">
                  Monthly Champions
                </NavLink>
                <NavLink href="/quarterly-legends" className="block px-3 py-2 hover:bg-pink-500/10 rounded text-foreground w-full text-left text-sm">
                  Quarterly Legends
                </NavLink>
              </div>
            </div>
            
            <AdminOnly>
              <NavLink href="/admin" className={getNavLinkClasses("/admin")}>
                Admin
              </NavLink>
            </AdminOnly>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2 shrink-0">
            {/* Search - Hidden on small mobile */}
            <Button variant="ghost" size="icon" className="hidden sm:flex text-foreground hover:text-pink-400">
              <Search className="h-4 w-4" />
            </Button>
            
            {/* Theme Toggle - Hidden on small mobile */}
            <div className="hidden sm:block">
              <ThemeToggle />
            </div>
            
            {/* User Navigation */}
            <UserNav />

            {/* Mobile Menu Button - Only visible on mobile */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden text-foreground hover:text-pink-400"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay - Appears on top of content */}
      {isMenuOpen && (
        <>
          {/* Backdrop overlay */}
          <div 
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            onClick={() => setIsMenuOpen(false)}
          />
          
          {/* Mobile Navigation Drawer */}
          <div className="fixed top-16 left-0 right-0 z-50 lg:hidden bg-background/95 backdrop-blur border-b border-border shadow-lg">
            <nav className="container mx-auto px-4 py-4 max-h-[calc(100vh-4rem)] overflow-y-auto">
              <div className="flex flex-col space-y-2">
                {/* Main Navigation */}
                <NavLink 
                  href="/mugshots" 
                  className={getMobileNavLinkClasses("/mugshots")} 
                  onClick={() => setIsMenuOpen(false)}
                >
                  Browse All
                </NavLink>
                <NavLink 
                  href="/popular" 
                  className={getMobileNavLinkClasses("/popular")} 
                  onClick={() => setIsMenuOpen(false)}
                >
                  Popular
                </NavLink>
                
                {/* Champions Section */}
                <div className="text-sm font-semibold text-muted-foreground mt-4 mb-2 pl-4">
                  Champions
                </div>
                <NavLink 
                  href="/weekly-best" 
                  className={getMobileNavLinkClasses("/weekly-best", "pl-8")} 
                  onClick={() => setIsMenuOpen(false)}
                >
                  Weekly Best
                </NavLink>
                <NavLink 
                  href="/monthly-champions" 
                  className={getMobileNavLinkClasses("/monthly-champions", "pl-8")} 
                  onClick={() => setIsMenuOpen(false)}
                >
                  Monthly Champions
                </NavLink>
                <NavLink 
                  href="/quarterly-legends" 
                  className={getMobileNavLinkClasses("/quarterly-legends", "pl-8")} 
                  onClick={() => setIsMenuOpen(false)}
                >
                  Quarterly Legends
                </NavLink>
                
                {/* Admin Section */}
                <AdminOnly>
                  <div className="text-sm font-semibold text-muted-foreground mt-4 mb-2 pl-4">
                    Admin
                  </div>
                  <NavLink 
                    href="/admin" 
                    className={getMobileNavLinkClasses("/admin", "pl-8")} 
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Admin Panel
                  </NavLink>
                </AdminOnly>
                
                {/* Mobile-only actions */}
                <div className="flex items-center justify-center space-x-4 mt-6 pt-4 border-t border-border">
                  <Button variant="ghost" size="icon" className="text-foreground hover:text-pink-400">
                    <Search className="h-5 w-5" />
                  </Button>
                  <ThemeToggle />
                </div>
              </div>
            </nav>
          </div>
        </>
      )}
    </>
  )
}

